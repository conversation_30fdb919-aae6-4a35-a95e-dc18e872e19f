import { createKnexInstance } from "../services/quickbooks.service.js";
import { REPORT_CONSTANTS } from "./constants/reports.constants.js";

export async function insertLines(
  reportId,
  rows,
  accountMap,
  databaseName,
  realmId = "default_realm"
) {
  if (!rows.length) return;

  // Filter only rows with values (non-zero amounts)
  const dataRows = rows.filter(
    (row) =>
      row.value != null && row.value !== 0 && row.account_name
  );

  if (!dataRows.length) return;

  // Map account_id -> account_name; fallback to existing account_name
  const prepared = dataRows.map((row) => {
    const accountName = (row.account_id && accountMap.get(row.account_id)) || row.account_name;
    return {
      path: row.path || "",
      account_name: accountName,
      account_id: row.account_id,
      amount: row.value,
      category: getCategoryForAccount(accountName),
      col_title: row.col_title,
      col_type: row.col_type,
    };
  });

  const db = await createKnexInstance(databaseName);
  
  // Get unique column information from the data
  const uniqueColumns = new Map();
  dataRows.forEach((row) => {
    if (row.col_title && row.col_type) {
      const key = `${row.col_title}_${row.col_type}`;
      if (!uniqueColumns.has(key)) {
        uniqueColumns.set(key, {
          col_title: row.col_title,
          col_type: row.col_type,
          parent_col_title: row.parent_col_title || null,
          col_order: row.column_index || 0,
        });
      }
    }
  });

  // Create columns dynamically based on the data
  const columnMap = new Map();
  for (const [key, colData] of uniqueColumns) {
    let column = await db("trial_balance_columns")
      .where({
        report_id: reportId,
        col_title: colData.col_title,
        col_type: colData.col_type,
      })
    .first();
  
    if (!column) {
      const [newColumn] = await db("trial_balance_columns")
        .insert({
          report_id: reportId,
          col_title: colData.col_title,
          col_type: colData.col_type,
          parent_col_title: colData.parent_col_title,
          col_order: colData.col_order,
          realm_id: realmId,
        })
        .returning("id");
      column = newColumn;
    }

    columnMap.set(key, column.id);
  }

  // If no columns found, create a default one
  if (columnMap.size === 0) {
    const defaultColTitle =
      dataRows[0]?.col_title || REPORT_CONSTANTS.DEFAULT_COLUMN.TITLE;
    const defaultColType =
      dataRows[0]?.col_type || REPORT_CONSTANTS.DEFAULT_COLUMN.TYPE;

    const [defaultColumn] = await db("trial_balance_columns")
      .insert({
        report_id: reportId,
        col_title: defaultColTitle,
        col_type: defaultColType,
        col_order: 0,
        realm_id: realmId,
      })
      .returning("id");
    columnMap.set("default", defaultColumn.id);
  }

  // Insert data using Knex.js into trial_balance_rows table
  const insertData = prepared.map((row) => {
    const columnKey = row.col_title && row.col_type ? `${row.col_title}_${row.col_type}` : null;
    const columnId = columnKey ? columnMap.get(columnKey) : columnMap.get("default");

    return {
      report_id: reportId,
      path: row.path,
      account_name: row.account_name,
      account_id: row.account_id,
      value: row.amount,
      category: row.category,
      realm_id: realmId,
      column_id: columnId,
    };
  });

  // Insert data in batches to avoid query size limits
  const batchSize = REPORT_CONSTANTS.BATCH_SIZE;
  try {
    for (let i = 0; i < insertData.length; i += batchSize) {
      await db("trial_balance_rows").insert(insertData.slice(i, i + batchSize));
    }
  } catch (error) {
    throw new Error(`Failed to insert Trial Balance data: ${error.message}`);
  } finally {
    await db.destroy();
  }
}

export function getCategoryForAccount(accountName) {
  if (!accountName) return null;

  const lower = accountName.toLowerCase();

  // Assets
  if (["cash", "bank", "checking", "savings"].some((term) => lower.includes(term))) {
    return "Cash & Cash Equivalents";
  }
  if (["receivable", "ar"].some((term) => lower.includes(term))) {
    return "Accounts Receivable";
  }
  if (lower.includes("inventory")) return "Inventory";
  if (lower.includes("prepaid")) return "Prepaid Expenses";
  if (
    ["equipment", "furniture", "vehicle", "building", "land"].some((term) =>
      lower.includes(term)
    )
  ) {
    return "Fixed Assets";
  }

  // Liabilities
  if (["payable", "ap"].some((term) => lower.includes(term))) {
    return "Accounts Payable";
  }
  if (lower.includes("credit card")) return "Credit Card Payable";
  if (["loan", "debt", "mortgage"].some((term) => lower.includes(term))) {
    return "Long Term Debt";
  }

  // Equity
  if (
    ["equity", "capital", "retained earnings", "owner"].some((term) =>
      lower.includes(term)
    )
  ) {
    return "Owner's Equity";
  }

  // Revenue
  if (["revenue", "income", "sales"].some((term) => lower.includes(term))) {
    return "Revenue";
  }

  // Expenses
  if (["expense", "cost", "payroll", "salary"].some((term) => lower.includes(term))) {
    return "Operating Expenses";
  }

  return "Other";
}

/**
 * Process QuickBooks Trial Balance hierarchical data structure
 * @param {Object} reportData - Raw QuickBooks report data
 * @returns {Array} Flattened array of processed rows
 */
export function processTrialBalanceHierarchy(reportData) {
  const processedRows = [];
  
  if (!reportData.Rows?.Row) return processedRows;

  function processRows(rows, parentPath = "") {
    const rowsArray = Array.isArray(rows) ? rows : [rows];

    rowsArray.forEach((row, index) => {
      const currentPath = parentPath ? `${parentPath}.${index}` : `${index}`;
      
      // Process data rows (accounts with debit/credit values)
      if (row.ColData?.length) {
        const accountName = row.ColData[0]?.value || "";
        const accountId = row.ColData[0]?.id || null;
        
        row.ColData.forEach((colData, colIndex) => {
          if (
            colIndex > 0 &&
            colData.value &&
            colData.value !== "" &&
            colData.value !== "0"
          ) {
            const numericValue = parseFloat(colData.value);
            if (!isNaN(numericValue)) {
              const isDebit = colIndex % 2 === 1;
              const monthIndex = Math.floor((colIndex - 1) / 2);
              
              processedRows.push({
                path: currentPath,
                account_name: accountName,
                account_id: accountId,
                value: numericValue,
                col_title: reportData.Columns?.Column?.[monthIndex + 1]?.ColTitle || "",
                col_type: reportData.Columns?.Column?.[monthIndex + 1]?.ColType || "Money",
                parent_col_title: isDebit ? "Debit" : "Credit",
                column_index: colIndex,
                row_type: "Data",
              });
            }
          }
        });
      }

      // Process nested rows
      if (row.Rows?.Row) processRows(row.Rows.Row, currentPath);
    });
  }

  processRows(reportData.Rows.Row);
  return processedRows;
}
