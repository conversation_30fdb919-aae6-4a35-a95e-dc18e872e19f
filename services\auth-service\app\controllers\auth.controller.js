import { createLogger } from "../utils/logger.utils.js";
import {
  loginService,
  logoutService,
  getUserProfileService,
  updateUserProfileService,
} from "../services/auth.service.js";
import {
  LOGGER_NAMES,
  LOGGER_MESSAGES,
} from "../utils/constants/log.constants.js";
import { VALIDATION_MESSAGES } from "../utils/constants/validation.constants.js";
import { HARDCODED_STRINGS } from "../utils/constants/strings.constants.js";
import { errorResponse, successResponse } from "../utils/response.util.js";
import { errorHandler } from "../utils/error.utils.js";
import { validationResult } from "express-validator";
import {
  setAccessTokenCookie,
  setRefreshTokenCookie,
  clearCookie,
} from "../utils/cookie.utils.js";
import * as status from "../utils/status_code.utils.js";

const logger = createLogger(LOGGER_NAMES.AUTH_CONTROLLER);

// HELPER FUNCTIONS

/**
 * Check validation errors from express-validator
 */
const checkValidation = (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res
      .status(status.STATUS_CODE_BAD_REQUEST)
      .json(
        errorResponse(VALIDATION_MESSAGES.VALIDATION_FAILED, errors.array())
      );
  }
  return null;
};

/**
 * Handle service response and send HTTP response
 */
const handleServiceResponse = (res, serviceResult) => {
  const { success, statusCode, message, data, error } = serviceResult;

  if (success) {
    return res.status(statusCode).json(successResponse(message, data));
  } else {
    return res.status(statusCode).json(errorResponse(message, error));
  }
};

// AUTHENTICATION ENDPOINTS

/**
 * User Login (Enhanced with MFA)
 * @route POST /auth/login
 * @access Public
 */

export const login = async (req, res) => {
  try {
    const validationError = checkValidation(req, res);
    if (validationError) return validationError;

    const loginData = {
      email: req.body.email,
      password: req.body.password,
      mfaCode: req.body.mfaCode,
      userAgent: req.headers[HARDCODED_STRINGS.USER_AGENT],
      ipAddress: req.ip,
    };

    const result = await loginService(loginData);

    // Set cookies on successful login
    if (result.success && result.data?.tokens) {
      const { access_token, refresh_token } = result.data.tokens;
      if (access_token) {
        setAccessTokenCookie(res, access_token);
      }
      if (refresh_token) {
        setRefreshTokenCookie(res, refresh_token);
      }
    }

    return handleServiceResponse(res, result);
  } catch (error) {
    logger.error(
      `${HARDCODED_STRINGS.ERROR_MESSAGES.CONTROLLER_ERROR} login in auth.controller.js:`,
      {
        error: error.message,
        stack: error.stack,
        email: req.body?.email,
        userAgent: req.headers[HARDCODED_STRINGS.USER_AGENT],
      }
    );
    return error;
  }
};

/**
 * User Logout
 * @route POST /auth/logout
 * @access Private (authenticated users only)
 */
export const logout = async (req, res) => {
  try {
    const userId = req.body.userId;
    const result = await logoutService(userId);

    // Clear cookies on successful logout
    if (result.success) {
      clearCookie(res, HARDCODED_STRINGS.ACCESS_TOKEN);
      clearCookie(res, HARDCODED_STRINGS.REFRESH_TOKEN);
    }

    return handleServiceResponse(res, result);
  } catch (error) {
    logger.error(
      `${HARDCODED_STRINGS.ERROR_MESSAGES.CONTROLLER_ERROR} logout in auth.controller.js:`,
      {
        error: error.message,
        stack: error.stack,
        userId: req.body.userId,
      }
    );
    return errorHandler(error, req, res);
  }
};

/**
 * Get User Profile
 * @route GET /auth/profile
 * @access Private (authenticated users only)
 */
export const getProfile = async (req, res) => {
  try {
    const userId = req.user.id;
    const result = await getUserProfileService(userId);
    return handleServiceResponse(res, result);
  } catch (error) {
    logger.error(
      `${HARDCODED_STRINGS.ERROR_MESSAGES.CONTROLLER_ERROR} getProfile:`,
      { error: error.message }
    );
    return errorHandler(error, req, res);
  }
};

/**
 * Update User Profile
 * @route PUT /auth/profile/:id
 * @access Private (authenticated users only)
 */
export const updateProfile = async (req, res) => {
  try {
    const validationError = checkValidation(req, res);
    if (validationError) return validationError;

    const updateData = {
      userId: req.params.id,
      updates: req.body,
      requesterId: req.user.id, // For authorization check
    };

    const result = await updateUserProfileService(updateData);
    return handleServiceResponse(res, result);
  } catch (error) {
    logger.error(
      `${HARDCODED_STRINGS.ERROR_MESSAGES.CONTROLLER_ERROR} updateProfile:`,
      { error: error.message }
    );
    return errorHandler(error, req, res);
  }
};
