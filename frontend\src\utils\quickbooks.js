/**
 * <PERSON>Books OAuth2 Utility Functions
 */

/**
 * Generate QuickBooks OAuth2 authorization URL
 * @param {Object} options - Configuration options
 * @param {string} options.clientId - QuickBooks Client ID
 * @param {string} options.redirectUri - Callback URL
 * @param {string} options.state - State parameter for security
 * @param {string} options.scope - OAuth scope (default: com.intuit.quickbooks.accounting)
 * @returns {string} Authorization URL
 */
export const generateQuickBooksAuthUrl = ({
  clientId,
  redirectUri,
  state = "your_custom_state",
  scope = "com.intuit.quickbooks.accounting",
}) => {
  const baseUrl = "https://appcenter.intuit.com/connect/oauth2";
  const params = new URLSearchParams({
    client_id: clientId,
    redirect_uri: redirectUri,
    response_type: "code",
    scope,
    state,
  });

  return `${baseUrl}?${params.toString()}`;
};

/**
 * Parse QuickBooks callback URL parameters
 * @param {string} url - The callback URL with query parameters
 * @returns {Object} Parsed parameters
 */
export const parseQuickBooksCallback = (url) => {
  try {
    const urlObj = new URL(url);
    const params = new URLSearchParams(urlObj.search);

    return {
      code: params.get("code"),
      state: params.get("state"),
      realmId: params.get("realmId"),
      error: params.get("error"),
      error_description: params.get("error_description"),
    };
  } catch (_error) {
    return null;
  }
};

/**
 * Test the QuickBooks callback API endpoint
 * @param {Object} params - Test parameters
 * @param {string} params.code - Authorization code
 * @param {string} params.realmId - QuickBooks Company ID
 * @param {string} params.state - State parameter
 * @returns {Promise<Object>} API response
 */
export const testQuickBooksCallback = async ({ code, realmId, state }) => {
  try {
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL;
    const params = new URLSearchParams({
      code,
      realmId,
      ...(state && { state }),
    });

    const response = await fetch(
      `${baseUrl}/api/quickbooks/callback?${params.toString()}`
    );
    const data = await response.json();

    return {
      success: response.ok,
      status: response.status,
      data,
    };
  } catch (error) {
    return {
      success: false,
      error: error.message,
    };
  }
};

/**
 * Validate QuickBooks environment configuration
 * @returns {Object} Validation result
 */
export const validateQuickBooksConfig = () => {
  const config = {
    clientId: process.env.NEXT_PUBLIC_QUICKBOOKS_CLIENT_ID,
    redirectUri: process.env.NEXT_PUBLIC_QUICKBOOKS_REDIRECT_URI,
    environment: process.env.QUICKBOOKS_ENVIRONMENT,
  };

  const errors = [];

  if (!config.clientId) {
    errors.push("NEXT_PUBLIC_QUICKBOOKS_CLIENT_ID is not set");
  }

  if (!config.redirectUri) {
    errors.push("NEXT_PUBLIC_QUICKBOOKS_REDIRECT_URI is not set");
  }

  if (
    config.redirectUri &&
    !config.redirectUri.includes("/api/quickbooks/callback")
  ) {
    errors.push("Redirect URI should point to /api/quickbooks/callback");
  }

  return {
    isValid: errors.length === 0,
    config,
    errors,
  };
};
