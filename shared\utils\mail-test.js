import { sendEmail } from "./email.util.js";

/**
 * Test function to send a test email using the email utility
 */
const testEmail = async () => {
  try {
    console.log("=".repeat(50));
    console.log("Email Utility Test");
    console.log("=".repeat(50));
    console.log("\nPreparing test email...");

    // Hardcoded test email parameters with deliverability improvements
    const testEmailParams = {
      to: "<EMAIL>",
      from: "<EMAIL>",
      subject: "Test Email from Email Utility",
      text: `This is a test email sent from the email utility.

Test Details:
- Timestamp: ${new Date().toISOString()}
- From Email: <EMAIL>
- To Email: <EMAIL>

If you receive this email, the email utility is working correctly!`,
      html: `
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>Test Email from Email Utility</title>
</head>
<body style="margin: 0; padding: 0; font-family: Arial, sans-serif; background-color: #f4f4f4;">
  <table role="presentation" style="width: 100%; border-collapse: collapse; background-color: #f4f4f4;">
    <tr>
      <td align="center" style="padding: 20px 0;">
        <table role="presentation" style="width: 600px; border-collapse: collapse; background-color: #ffffff; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
          <tr>
            <td style="padding: 30px; text-align: center; border-bottom: 2px solid #4CAF50;">
              <h2 style="color: #333; margin: 0; font-size: 24px;">Test Email from Email Utility</h2>
            </td>
          </tr>
          <tr>
            <td style="padding: 30px;">
              <div style="background-color: #f5f5f5; padding: 15px; border-radius: 5px; margin-bottom: 20px;">
                <p style="margin: 0; color: #333; font-size: 16px;">This is a test email sent from the email utility.</p>
              </div>
              <h3 style="color: #555; margin-top: 30px; margin-bottom: 15px; font-size: 18px;">Test Details:</h3>
              <ul style="line-height: 1.8; color: #333; padding-left: 20px;">
                <li style="margin-bottom: 10px;"><strong>Timestamp:</strong> ${new Date().toISOString()}</li>
                <li style="margin-bottom: 10px;"><strong>From Email:</strong> <EMAIL></li>
                <li style="margin-bottom: 10px;"><strong>To Email:</strong> <EMAIL></li>
              </ul>
              <div style="background-color: #e8f5e9; padding: 15px; border-radius: 5px; margin-top: 20px; border-left: 4px solid #4CAF50;">
                <p style="margin: 0; color: #2e7d32; font-size: 16px;">
                  <strong>✓ If you receive this email, the email utility is working correctly!</strong>
                </p>
              </div>
            </td>
          </tr>
          <tr>
            <td style="padding: 20px 30px; background-color: #f9f9f9; border-top: 1px solid #e0e0e0; text-align: center;">
              <p style="margin: 0; color: #666; font-size: 12px;">This is an automated test email. Please do not reply.</p>
            </td>
          </tr>
        </table>
      </td>
    </tr>
  </table>
</body>
</html>
      `,
    };

    console.log("\nEmail Configuration:");
    console.log(`  To: ${testEmailParams.to}`);
    console.log(`  From: ${testEmailParams.from}`);
    console.log(`  Subject: ${testEmailParams.subject}`);

    console.log("\nSending test email...");

    // Send the test email
    const response = await sendEmail(testEmailParams);

    console.log("\n" + "=".repeat(50));
    console.log("✅ SUCCESS! Test email sent successfully!");
    console.log("=".repeat(50));
    console.log("\nResponse:", JSON.stringify(response, null, 2));
    console.log("\nPlease check the recipient's inbox for the test email.");
    console.log("\nIf the email doesn't arrive, check:");
    console.log("  1. Spam/Junk folder");
    console.log("  2. SendGrid dashboard for delivery status");
    console.log("  3. That the 'from' email address is verified in SendGrid");

    process.exit(0);
  } catch (error) {
    console.error("\n" + "=".repeat(50));
    console.error("❌ ERROR: Failed to send test email");
    console.error("=".repeat(50));
    console.error("\nError Details:");
    console.error(`  Message: ${error.message}`);

    if (error.response) {
      console.error(`  Status Code: ${error.response.statusCode}`);
      if (error.response.body) {
        console.error(
          `  Response Body: ${JSON.stringify(error.response.body, null, 2)}`
        );
      }
    }

    console.error("\nTroubleshooting:");
    console.error("  1. Verify SENDGRID_API_KEY is correct and valid");
    console.error("  2. Check that the 'from' email is verified in SendGrid");
    console.error("  3. Ensure network connectivity");
    console.error("  4. Check SendGrid account status and API key permissions");

    process.exit(1);
  }
};

// Run the test
testEmail();
