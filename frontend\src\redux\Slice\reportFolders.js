import { createSlice } from "@reduxjs/toolkit";
import {
  fetchReportFolders,
  fetchReportFiles,
  fetchReportSummary,
} from "@/redux/Thunks/reportFolders";

const createInitialState = () => ({
  folders: [],
  folderMap: {},
  monthsByFolder: {},
  organizationId: null,
  organizationName: null,
  loadingFolders: false,
  foldersError: null,
  filesByFolder: {},
  filesLoading: {},
  filesError: {},
  summaries: {},
  summaryLoading: {},
  summaryError: {},
});

const initialState = createInitialState();

const reportFoldersSlice = createSlice({
  name: "reportFolders",
  initialState,
  reducers: {
    clearFoldersError: (state) => {
      state.foldersError = null;
    },
    resetReportFolders: () => createInitialState(),
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchReportFolders.pending, (state, action) => {
        state.loadingFolders = true;
        state.foldersError = null;
        const arg = action.meta.arg;
        state.organizationId = arg?.organizationId || arg || null;
      })
      .addCase(fetchReportFolders.fulfilled, (state, action) => {
        state.loadingFolders = false;
        state.foldersError = null;

        const payload = action.payload || {};
        const arg = action.meta.arg || {};
        const isForceRefresh = arg.forceRefresh === true;
        const orgIdChanged = state.organizationId && state.organizationId !== (payload.orgId || arg.organizationId);
        
        state.folders = payload.folders || [];
        state.folderMap = payload.folderMap || {};
        state.monthsByFolder = payload.monthsByFolder || {};
        
        // Only reset filesByFolder if organization changed or it's a forced refresh from initial load
        // Don't reset if we're just refreshing folders and files are already loaded
        if (orgIdChanged || (isForceRefresh && Object.keys(state.filesByFolder).length === 0)) {
          state.filesByFolder = {};
          state.filesLoading = {};
          state.filesError = {};
          state.summaries = {};
          state.summaryLoading = {};
          state.summaryError = {};
        }

        if (payload.orgId) {
          state.organizationId = payload.orgId;
        }
        if (payload.org) {
          state.organizationName = payload.org;
        }
      })
      .addCase(fetchReportFolders.rejected, (state, action) => {
        state.loadingFolders = false;
        state.foldersError =
          action.payload || action.error?.message || "Failed to load folders";
      })
      .addCase(fetchReportFiles.pending, (state, action) => {
        const { folder } = action.meta.arg || {};
        if (folder) {
          state.filesLoading[folder] = true;
          state.filesError[folder] = null;
        }
      })
      .addCase(fetchReportFiles.fulfilled, (state, action) => {
        const payload = action.payload || {};
        const folder = payload.folder;
        if (folder) {
          state.filesLoading[folder] = false;
          state.filesError[folder] = null;

          const files = Array.isArray(payload.files) ? payload.files : [];
          const byMonth = files.reduce((acc, file) => {
            if (file?.month) {
              acc[file.month] = file;
            }
            return acc;
          }, {});
          state.filesByFolder[folder] = {
            list: files,
            byMonth,
          };

          if (payload.orgId) {
            state.organizationId = payload.orgId;
          }
          if (payload.org) {
            state.organizationName = payload.org;
          }
        }
      })
      .addCase(fetchReportFiles.rejected, (state, action) => {
        const { folder } = action.meta.arg || {};
        if (folder) {
          state.filesLoading[folder] = false;
          state.filesError[folder] =
            action.payload ||
            action.error?.message ||
            "Failed to load report files";
        }
      })
      .addCase(fetchReportSummary.pending, (state, action) => {
        const { folder, fileName } = action.meta.arg || {};
        if (folder && fileName) {
          const key = `${folder}::${fileName}`;
          state.summaryLoading[key] = true;
          state.summaryError[key] = null;
        }
      })
      .addCase(fetchReportSummary.fulfilled, (state, action) => {
        const payload = action.payload || {};
        const folder = payload.folder;
        const file = payload.file || {};
        const fileName = file.name;
        if (folder && fileName) {
          const key = `${folder}::${fileName}`;
          state.summaryLoading[key] = false;
          state.summaryError[key] = null;

          if (!state.summaries[folder]) {
            state.summaries[folder] = {};
          }
          state.summaries[folder][fileName] = {
            summary: payload.summary || "",
            month: file.month,
            label: file.label,
          };

          if (payload.orgId) {
            state.organizationId = payload.orgId;
          }
          if (payload.org) {
            state.organizationName = payload.org;
          }
        }
      })
      .addCase(fetchReportSummary.rejected, (state, action) => {
        const { folder, fileName } = action.meta.arg || {};
        if (folder && fileName) {
          const key = `${folder}::${fileName}`;
          state.summaryLoading[key] = false;
          state.summaryError[key] =
            action.payload ||
            action.error?.message ||
            "Failed to load report summary";
        }
      });
  },
});

export const { clearFoldersError, resetReportFolders } =
  reportFoldersSlice.actions;

export default reportFoldersSlice.reducer;
