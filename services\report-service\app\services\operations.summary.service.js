import { LOGGER_NAMES } from "../utils/constants/log.constants.js";
import { createLogger } from "../utils/logger.utils.js";
import { getRecordsByDateRange } from "../utils/database.utils.js";
import {
  getStartDate,
  calculatePercentageDistribution,
  extractNameFromDisplayName,
} from "../utils/service.utils.js";
import { getOrganizationSchemaName } from "../utils/repository.utils.js";

const logger = createLogger(LOGGER_NAMES.REPORT_SERVICE);

/**
 * Get collections comparison (patients vs insurance)
 * @param {string} schemaName - Schema name
 * @param {string} startDate - Start date
 * @returns {Promise<Object>} Collections comparison data
 */
const getCollectionsComparison = async (schemaName, startDate) => {
  const collections = await getRecordsByDateRange(
    schemaName,
    "sikka_avg_daily_production",
    startDate
  );

  const patientCollection = Math.round(
    collections.reduce((sum, item) => sum + Number(item.value || 0), 0)
  );
  const insuranceCollection = Math.round(
    collections.reduce((sum, item) => sum + Number((item.value || 0) * 29), 0)
  );

  return {
    title: "Collections: Patients vs Insurance",
    comparison: [{ patientCollection, insuranceCollection }],
  };
};

/**
 * Get collections by doctor
 * @param {string} schemaName - Schema name
 * @param {string} startDate - Start date
 * @returns {Promise<Object>} Collections by doctor data
 */
const getCollectionsByDoctor = async (schemaName, startDate) => {
  const doctors = await getRecordsByDateRange(
    schemaName,
    "sikka_avg_daily_production",
    startDate
  );

  const doctorsData = doctors.map((item) => ({
    name: extractNameFromDisplayName(item.display_name, 1),
    value: Math.round(Number(item.value || 0) * 30),
  }));

  return {
    title: "Collections by Doctor",
    doctors: doctorsData,
  };
};

/**
 * Get collections by payer type
 * @param {string} schemaName - Schema name
 * @param {string} startDate - Start date
 * @returns {Promise<Object>} Collections by payer type data
 */
const getCollectionsByPayerType = async (schemaName, startDate) => {
  const payers = await getRecordsByDateRange(
    schemaName,
    "sikka_avg_daily_production",
    startDate
  );

  const payerData = payers.map((item) => {
    const dailyValue = Number(item.value || 0);
    return {
      name: extractNameFromDisplayName(item.display_name, 0),
      value: Math.round(dailyValue * 29),
    };
  });

  const categoriesWithPercentages = calculatePercentageDistribution(payerData);

  return {
    title: "Collections by Payer Type",
    categories: categoriesWithPercentages,
  };
};

/**
 * Get operations summary data for organization
 * @param {Object} params - Query parameters
 * @param {string} params.organization_id - Organization ID
 * @param {number} params.month - Month (1-12)
 * @param {number} params.year - Year
 * @returns {Promise<Object>} Operations summary data
 */
const getOperationsSummary = async ({ organization_id, month, year }) => {
  try {
    logger.info(
      `Fetching operations summary data for org: ${organization_id}, month: ${month}, year: ${year}`
    );

    const schemaName = await getOrganizationSchemaName(organization_id);
    const startDate = getStartDate(month, year);

    const [collectionsComparison, collectionsByDoctor, collectionsByPayerType] =
      await Promise.all([
        getCollectionsComparison(schemaName, startDate),
        getCollectionsByDoctor(schemaName, startDate),
        getCollectionsByPayerType(schemaName, startDate),
      ]);

    const responseData = {
      collectionsComparison,
      collectionsByDoctor,
      collectionsByPayerType,
    };

    logger.info("Operations summary data fetched successfully");
    return responseData;
  } catch (error) {
    logger.error("Error in OperationsService.getOperationsSummary:", error);
    throw error;
  }
};

export default {
  getOperationsSummary,
};
