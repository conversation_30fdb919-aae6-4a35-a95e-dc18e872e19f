// app/utils/cacheCleanup.util.js
/**
 * Cache cleanup utility
 * Periodically cleans expired cache entries to prevent memory leaks
 */
import { reportFoldersCache, envCache } from "./cache.util.js";
import logger from "../../config/logger.config.js";

// Cleanup interval: 10 minutes
const CLEANUP_INTERVAL_MS = 600000;

/**
 * Clean expired cache entries
 */
const cleanupExpiredCache = () => {
  try {
    const foldersCacheSizeBefore = reportFoldersCache.size();
    const envCacheSizeBefore = envCache.size();

    // Clean expired entries
    reportFoldersCache.cleanExpired();
    envCache.cleanExpired();

    const foldersCacheSizeAfter = reportFoldersCache.size();
    const envCacheSizeAfter = envCache.size();

    const foldersCleaned = foldersCacheSizeBefore - foldersCacheSizeAfter;
    const envCleaned = envCacheSizeBefore - envCacheSizeAfter;

    if (foldersCleaned > 0 || envCleaned > 0) {
      logger.debug(
        `Cache cleanup: Removed ${foldersCleaned} report folders entries, ${envCleaned} env entries`
      );
    }
  } catch (error) {
    logger.error(`Error cleaning cache: ${error.message}`, { error });
  }
};

/**
 * Start periodic cache cleanup
 */
export const startCacheCleanup = () => {
  // Run cleanup immediately, then periodically
  cleanupExpiredCache();
  
  setInterval(() => {
    cleanupExpiredCache();
  }, CLEANUP_INTERVAL_MS);
  
  logger.info("Cache cleanup scheduler started");
};

/**
 * Manually trigger cache cleanup
 */
export const cleanupCache = cleanupExpiredCache;

