/* CFO Insights Page Styles */

.cfo-insights-container {
  display: flex;
  flex-direction: column;
  height: 100%; /* Use 100% instead of 100vh to work within parent container */
  width: 100%;
  overflow: hidden;
  background-color: #F4F4F5; /* Soft light grey background - matches dashboard theme */
  position: relative;
  box-sizing: border-box;
}

.cfo-insights-header {
  width: 100%;
  background-color: #F3E8FF; /* Light purple background (purple-100) */
  color: #4F46E5; /* Primary purple text */
  box-shadow: 0 2px 8px -2px rgba(0, 0, 0, 0.05);
  flex-shrink: 0;
  z-index: 30; /* Higher than sidebar to ensure header stays on top */
  position: relative;
  overflow: hidden;
  border-bottom: 1px solid rgba(79, 70, 229, 0.1);
  transition: margin-right 0.3s ease, width 0.3s ease;
}

/* Adjust header width when sidebar is open */
.cfo-insights-header.sidebar-open {
  margin-right: 0;
  width: calc(100% - 20rem);
}

@media (min-width: 1024px) {
  .cfo-insights-header.sidebar-open {
    width: calc(100% - 20rem);
  }
}

@media (max-width: 1023px) {
  .cfo-insights-header.sidebar-open {
    width: 100%;
  }
}

.cfo-insights-header-bg {
  display: none;
}

.cfo-insights-header-pattern {
  display: none;
}

.cfo-insights-header-content {
  padding: 0.875rem 1.25rem; /* Reduced from 1rem */
  position: relative;
  z-index: 10;
  max-width: 100%;
}

@media (min-width: 640px) {
  .cfo-insights-header-content {
    padding: 1rem 1.5rem; /* Reduced from 1.125rem 1.75rem */
  }
}

@media (min-width: 1024px) {
  .cfo-insights-header-content {
    padding: 1rem 1.75rem; /* Reduced from 1.25rem 2.25rem */
  }
}

.cfo-insights-header-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* Header buttons - Cursor Pointer */
.cfo-insights-header button {
  cursor: pointer !important;
}

.cfo-insights-header .flex button {
  cursor: pointer !important;
}

/* Ensure all interactive elements in header have pointer */
.cfo-insights-header [role="button"],
.cfo-insights-header a {
  cursor: pointer !important;
}

.cfo-insights-header-left {
  display: flex;
  align-items: center;
  gap: 0.75rem; /* Reduced from 0.875rem */
}

.cfo-insights-header-right {
  display: flex;
  align-items: center;
  gap: 0.5rem; /* Reduced from 0.625rem */
}

.cfo-insights-logo-container {
  width: 2rem; /* Reduced from 2.25rem */
  height: 2rem; /* Reduced from 2.25rem */
  background-color: #4F46E5; /* Primary purple */
  border-radius: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  box-shadow: 0 2px 4px rgba(79, 70, 229, 0.2);
}

.cfo-insights-logo {
  width: 1rem; /* Reduced from 1.125rem */
  height: 1rem; /* Reduced from 1.125rem */
  color: white;
}

.cfo-insights-title {
  font-size: 1.25rem; /* Reduced from 1.375rem */
  font-weight: 700;
  color: #4F46E5; /* Primary purple */
  line-height: 1.3;
  margin-bottom: 0.125rem;
}

@media (min-width: 640px) {
  .cfo-insights-title {
    font-size: 1.375rem; /* Reduced from 1.625rem */
  }
}

@media (min-width: 1024px) {
  .cfo-insights-title {
    font-size: 1.5rem; /* Reduced from 1.75rem */
  }
}

.cfo-insights-subtitle {
  color: #6B7280; /* Medium grey */
  font-size: 0.8125rem;
  font-weight: 400;
  line-height: 1.4;
}

@media (min-width: 640px) {
  .cfo-insights-subtitle {
    font-size: 0.875rem;
  }
}

.cfo-insights-month-badge {
  color: #4F46E5; /* Primary purple */
  font-size: 0.6875rem;
  margin-top: 0.5rem;
  background-color: rgba(79, 70, 229, 0.1); /* Light purple background */
  padding: 0.3125rem 0.75rem;
  border-radius: 0.375rem;
  display: inline-block;
  border: 1px solid rgba(79, 70, 229, 0.2);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.cfo-insights-month-badge:hover {
  background-color: rgba(79, 70, 229, 0.15);
}

@media (min-width: 640px) {
  .cfo-insights-month-badge {
    font-size: 0.75rem;
    margin-top: 0.5rem;
  }
}

.cfo-insights-main-content {
  flex: 1;
  display: flex;
  flex-direction: row; /* Changed to row for two-column layout */
  gap: 0; /* No gap between columns */
  padding: 0; /* Remove padding - columns handle their own */
  overflow: hidden;
  background-color: #F3F4F6; /* gray-100 background */
  min-height: 0;
  max-width: 100%;
  box-sizing: border-box;
  width: 100%;
  position: relative;
  height: 100%; /* Ensure full height */
}

/* Chat Panel - Left Column (70% width) */
.cfo-insights-chat-panel {
  flex: 0 0 70%; /* w-7/10 equivalent - 70% width */
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background-color: white;
  min-height: 0;
  max-width: 70%;
  box-sizing: border-box;
  position: relative;
  height: 100%; /* Ensure full height */
}

.cfo-insights-chat-card {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background-color: white;
  min-height: 0;
  max-width: 100%;
  box-sizing: border-box;
  position: relative;
  height: 100%; /* Ensure full height */
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.06); /* Modern layered shadow-lg */
}

/* Chat Panel Header - Solid Purple Banner */
.cfo-insights-chat-header {
  flex-shrink: 0;
  background-color: #4F46E5; /* Solid Primary Purple */
  padding: 0.75rem 1.25rem;
  border-bottom: none;
}

@media (min-width: 640px) {
  .cfo-insights-chat-header {
    padding: 0.875rem 1.5rem;
  }
}

.cfo-insights-chat-header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 1rem;
  width: 100%;
}

.cfo-insights-chat-title {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 0.25rem;
  flex: 1;
  min-width: 0;
}

.cfo-insights-chat-title-row {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.cfo-insights-chat-title-text {
  font-size: 1rem;
  font-weight: 600;
  color: white; /* White text on purple banner */
  background: none; /* Remove gradient */
  background-clip: unset;
  -webkit-background-clip: unset;
}

@media (min-width: 640px) {
  .cfo-insights-chat-title-text {
    font-size: 1.125rem;
  }
}

.cfo-insights-chat-subtitle {
  font-size: 0.8125rem;
  color: rgba(255, 255, 255, 0.9); /* Slightly transparent white */
  font-weight: 400;
}

@media (min-width: 640px) {
  .cfo-insights-chat-subtitle {
    font-size: 0.875rem;
  }
}

.cfo-insights-chat-icon {
  width: 1.25rem;
  height: 1.25rem;
  background-color: rgba(255, 255, 255, 0.2); /* Semi-transparent white on purple */
  border-radius: 9999px;
  display: flex;
  align-items: center;
  justify-content: center;
}

@media (min-width: 640px) {
  .cfo-insights-chat-icon {
    width: 1.5rem;
    height: 1.5rem;
  }
}

.cfo-insights-chat-icon svg {
  width: 0.875rem;
  height: 0.875rem;
  color: white;
}

@media (min-width: 640px) {
  .cfo-insights-chat-icon svg {
    width: 1rem;
    height: 1rem;
  }
}

/* End Chat Link (X icon) in Header */
.cfo-insights-header-end-chat-link {
  background: none !important;
  border: none !important;
  padding: 0.5rem !important;
  color: rgba(255, 255, 255, 0.7) !important; /* Light gray - de-emphasized */
  cursor: pointer !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 0.375rem;
  opacity: 0.8;
}

.cfo-insights-header-end-chat-link:hover {
  opacity: 1;
  color: white !important;
  background-color: rgba(255, 255, 255, 0.1) !important;
  transform: scale(1.05);
}

.cfo-insights-header-end-chat-link:active {
  transform: scale(0.95);
}

.cfo-insights-header-end-chat-link svg {
  color: inherit;
  stroke-width: 2;
}

.cfo-insights-messages-container {
  flex: 1;
  padding: 0;
  overflow: hidden;
  min-height: 0;
  display: flex;
  flex-direction: column;
  position: relative;
  /* Ensure space for fixed input bar */
  padding-bottom: 0;
  height: 100%; /* Ensure full height */
}

/* Ensure ScrollArea takes full height */
.cfo-insights-messages-container [data-radix-scroll-area-viewport] {
  height: 100%;
  max-height: 100%;
}

.cfo-insights-messages-container [data-radix-scroll-area-root] {
  height: 100%;
  flex: 1;
  min-height: 0;
}

.cfo-insights-messages-content {
  padding: 0.875rem 1rem;
  padding-bottom: 6rem; /* Space for fixed input bar */
  min-height: 100%;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  padding-bottom: calc(6rem + 1rem);
}

@media (min-width: 640px) {
  .cfo-insights-messages-content {
    padding: 1rem 1.25rem; /* Reduced from 1.25rem 1.5rem */
    padding-bottom: calc(6rem + 1.25rem);
    gap: 0.875rem;
  }
}

.cfo-insights-empty-state {
  text-align: center;
  color: #111827; /* Dark text */
  padding: 1.5rem 0;
}

@media (min-width: 640px) {
  .cfo-insights-empty-state {
    padding: 2rem 0;
  }
}

.cfo-insights-empty-icon {
  width: 2.5rem;
  height: 2.5rem;
  margin-left: auto;
  margin-right: auto;
  margin-bottom: 0.625rem;
  color: rgb(209 213 219);
}

@media (min-width: 640px) {
  .cfo-insights-empty-icon {
    width: 3rem;
    height: 3rem;
    margin-bottom: 0.75rem;
  }
}

.cfo-insights-empty-title {
  font-size: 0.9375rem;
  font-weight: 600;
  margin-bottom: 0.25rem;
  color: #111827; /* Dark text */
}

@media (min-width: 640px) {
  .cfo-insights-empty-title {
    font-size: 1rem;
  }
}

.cfo-insights-empty-subtitle {
  font-size: 0.6875rem;
  color: #6B7280; /* Medium grey */
}

@media (min-width: 640px) {
  .cfo-insights-empty-subtitle {
    font-size: 0.75rem;
  }
}

.cfo-insights-message {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 0.25rem;
}

.cfo-insights-message.user {
  justify-content: flex-end;
}

.cfo-insights-message.ai {
  justify-content: flex-start;
}

.cfo-insights-message-bubble {
  position: relative;
  max-width: 75%; /* Reduced from 80% for better readability */
  border-radius: 0.5rem;
  padding: 0.625rem 0.875rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06); /* Modern layered shadow */
  font-size: 0.875rem;
  line-height: 1.5;
}

@media (min-width: 640px) {
  .cfo-insights-message-bubble {
    padding: 0.6875rem 0.9375rem; /* Slightly reduced from 0.75rem 1rem */
  }
}

.cfo-insights-message-bubble.user {
  background-color: #FFFFFF; /* White background - matches theme */
  color: #111827; /* Dark text */
  border: 1px solid #E5E7EB; /* Light grey border */
  margin-left: 1.5rem;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05); /* Subtle shadow for depth */
}

.cfo-insights-message-bubble.ai {
  background-color: #F3E8FF; /* Light purple background (purple-100) - matches theme */
  color: #111827; /* Dark text for readability */
  border: 1px solid rgba(79, 70, 229, 0.2); /* Light purple border */
  margin-right: 1.5rem;
  box-shadow: 0 1px 2px rgba(79, 70, 229, 0.1); /* Subtle shadow for depth */
}

@media (min-width: 640px) {
  .cfo-insights-message-bubble.user {
    margin-left: 2rem;
  }
  
  .cfo-insights-message-bubble.ai {
    margin-right: 2rem;
  }
}

.cfo-insights-message-content {
  line-height: 1.625;
  font-size: 0.9375rem;
}

.cfo-insights-message-content.user {
  color: #111827; /* Dark text */
}

.cfo-insights-message-content.ai {
  color: #111827; /* Dark text for readability */
}

.cfo-insights-message-content.ai strong {
  font-weight: 600;
  color: rgb(17 24 39);
}

.cfo-insights-message-timestamp {
  font-size: 0.625rem;
  opacity: 0.5;
  margin-top: 0.5rem;
  text-align: right;
  color: #9CA3AF; /* Light gray for subtlety */
  font-weight: 400;
}

/* Input Bar - Fixed/Sticky Footer in Chat Panel */
.cfo-insights-input-card {
  position: absolute; /* Fixed positioning */
  bottom: 0;
  left: 0;
  right: 0;
  flex-shrink: 0;
  box-shadow:
    0 -2px 8px -2px rgba(0, 0, 0, 0.1),
    0 -1px 4px -2px rgba(0, 0, 0, 0.05); /* Shadow on top */
  border-top: 1px solid rgb(229 231 235);
  border-left: none;
  border-right: none;
  border-bottom: none;
  background-color: white; /* White bar */
  border-radius: 0; /* No border radius for full-width bar */
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
  overflow: hidden;
  z-index: 10; /* Above chat messages */
  /* CRITICAL: Ensure input box is contained within column width */
  min-width: 0;
}

.cfo-insights-input-content {
  padding: 0.75rem 1rem; /* Reduced from 0.875rem 1.125rem */
  padding-top: 2rem; /* Extra space at top for character counter */
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
  position: relative; /* Ensure counter positioning context */
}

@media (min-width: 640px) {
  .cfo-insights-input-content {
    padding: 0.875rem 1.125rem; /* Reduced from 1rem 1.25rem */
    padding-top: 2.25rem; /* Extra space at top for character counter */
  }
}

.cfo-insights-input-form {
  display: flex;
  gap: 0.625rem;
  align-items: flex-end;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
  /* CRITICAL: Ensure form doesn't overflow */
  min-width: 0; /* Allow flex item to shrink */
  flex-wrap: nowrap; /* Prevent wrapping */
}

@media (min-width: 640px) {
  .cfo-insights-input-form {
    gap: 0.75rem;
  }
}

.cfo-insights-input-wrapper {
  flex: 1;
  position: relative;
  min-width: 0; /* Allow flex item to shrink below content size */
  max-width: 100%;
  box-sizing: border-box;
}

.cfo-insights-textarea {
  min-height: 2.5rem;
  max-height: 120px;
  resize: none;
  border: 1px solid #E5E7EB; /* Light gray border - subtle */
  border-radius: 0.5rem; /* rounded-lg */
  padding-right: 0.875rem; /* Space for counter moved outside */
  transition: all 0.2s ease;
  background-color: white;
  font-size: 0.875rem;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  padding-left: 0.875rem;
  line-height: 1.5;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
  overflow-wrap: break-word;
  word-wrap: break-word;
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.05); /* Subtle inner shadow */
}

@media (min-width: 640px) {
  .cfo-insights-textarea {
    min-height: 2.75rem;
    padding-right: 1rem;
    font-size: 0.9375rem;
    padding-top: 0.5625rem;
    padding-bottom: 0.5625rem;
    padding-left: 1rem;
  }
}

.cfo-insights-textarea:focus {
  border-color: #4F46E5; /* Primary purple */
  outline: none;
  box-shadow: 
    inset 0 1px 2px rgba(0, 0, 0, 0.05),
    0 0 0 3px rgba(79, 70, 229, 0.1); /* Subtle focus ring */
}

.cfo-insights-textarea:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.cfo-insights-char-count {
  position: absolute;
  top: 0.5rem; /* Positioned at top of input-content, accounting for padding */
  right: 1rem; /* Align with input padding */
  font-size: 0.6875rem;
  color: #9CA3AF; /* Theme subtle text color */
  background-color: transparent;
  padding: 0;
  border: none;
  font-weight: 400;
  z-index: 5; /* Ensure it's above other elements */
  pointer-events: none; /* Don't interfere with input interactions */
}

@media (min-width: 640px) {
  .cfo-insights-char-count {
    font-size: 0.75rem;
    right: 1.125rem; /* Match input padding */
  }
}

.cfo-insights-send-button {
  background-color: #818CF8; /* Lighter indigo - more accessible */
  color: white;
  padding: 0;
  height: 2.5rem; /* Smaller height to match reduced input */
  min-width: 2.5rem; /* Square button */
  border-radius: 0.5rem; /* Match input border radius */
  box-shadow: 0 1px 2px rgba(79, 70, 229, 0.2); /* Subtle shadow */
  transition: all 0.3s ease;
  font-weight: 500;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  cursor: pointer;
  box-sizing: border-box;
}

@media (min-width: 640px) {
  .cfo-insights-send-button {
    height: 2.75rem;
    min-width: 2.75rem;
  }
}

.cfo-insights-send-button:hover:not(:disabled) {
  background-color: #6366F1; /* Slightly darker on hover */
  box-shadow: 0 2px 4px rgba(79, 70, 229, 0.3);
  transform: translateY(-1px);
}

.cfo-insights-send-button:active:not(:disabled) {
  transform: translateY(0);
  box-shadow: 0 1px 2px rgba(79, 70, 229, 0.2);
}

.cfo-insights-send-button:disabled {
  opacity: 0.5;
  cursor: not-allowed !important;
  background-color: #C7D2FE;
  transform: none;
}

/* Ensure all buttons have pointer cursor unless disabled */
.cfo-insights-container button:not(:disabled) {
  cursor: pointer !important;
}

.cfo-insights-container button:disabled {
  cursor: not-allowed !important;
}

/* Ensure all interactive elements have pointer cursor */
.cfo-insights-container [role="button"]:not(:disabled),
.cfo-insights-container a:not([aria-disabled="true"]) {
  cursor: pointer !important;
}

/* Accordion triggers specifically - Radix UI accordion */
.cfo-insights-container [data-slot="accordion-trigger"],
.cfo-insights-container button[data-slot="accordion-trigger"] {
  cursor: pointer !important;
}

/* All Radix UI accordion triggers */
.cfo-insights-container [data-radix-accordion-trigger] {
  cursor: pointer !important;
}

.cfo-insights-send-icon {
  width: 0.875rem;
  height: 0.875rem;
}

@media (min-width: 640px) {
  .cfo-insights-send-icon {
    width: 1rem;
    height: 1rem;
  }
}

/* Hub Panel - Right Column (30% width) */
.cfo-insights-sidebar {
  flex: 0 0 30%; /* w-3/10 equivalent - 30% width */
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background-color: #F9FAFB; /* Light grey background (gray-50) to match theme */
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.06); /* Modern layered shadow-lg */
  border-left: 1px solid #E5E7EB; /* Subtle border-l separator */
  min-height: 0;
  max-width: 30%;
  box-sizing: border-box;
  position: relative;
  margin-left: 1.5rem; /* Standardized gap between panels */
}

.cfo-insights-sidebar > div {
  position: relative; /* For absolute positioning of End Chat button */
}

/* Hub panel is always visible in new layout - no transform needed */

.cfo-insights-sidebar-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 1.25rem;
  border-bottom: 1px solid rgba(229, 231, 235, 0.5);
  background-color: #F9FAFB; /* Light grey background to match sidebar */
  position: relative;
  transition: all 0.3s ease;
}

.cfo-insights-sidebar-header button {
  cursor: pointer;
}

@media (min-width: 640px) {
  .cfo-insights-sidebar-header {
    padding: 1rem 1.25rem; /* Reduced from 1.25rem 1.5rem */
  }
}

.cfo-insights-sidebar-header-bg {
  display: none;
}

.cfo-insights-sidebar-header-content {
  display: flex;
  align-items: center;
  gap: 0.875rem;
  position: relative;
  z-index: 10;
  transition: all 0.3s ease;
}

@media (min-width: 640px) {
  .cfo-insights-sidebar-header-content {
    gap: 1rem;
  }
}

.cfo-insights-sidebar-icon {
  width: 1.75rem;
  height: 1.75rem;
  background: transparent;
  border-radius: 0.375rem;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.cfo-insights-sidebar-icon:hover {
  transform: scale(1.05);
}

@media (min-width: 640px) {
  .cfo-insights-sidebar-icon {
    width: 2rem;
    height: 2rem;
  }
}

.cfo-insights-sidebar-icon svg {
  width: 1rem;
  height: 1rem;
  color: #4F46E5; /* Indigo outline icon */
}

@media (min-width: 640px) {
  .cfo-insights-sidebar-icon svg {
    width: 1.125rem;
    height: 1.125rem;
  }
}

.cfo-insights-sidebar-title {
  font-size: 0.9375rem;
  font-weight: 600;
  color: #111827;
  line-height: 1.4;
  transition: all 0.3s ease;
}

@media (min-width: 640px) {
  .cfo-insights-sidebar-title {
    font-size: 1rem;
  }
}

.cfo-insights-sidebar-content {
  flex: 1;
  padding: 1rem 1.25rem;
  overflow-y: auto;
  background-color: #F9FAFB; /* Light grey background (gray-50) to match theme */
  scrollbar-width: thin;
  scrollbar-color: rgb(203 213 225) transparent;
  transition: all 0.3s ease;
}

@media (min-width: 640px) {
  .cfo-insights-sidebar-content {
    padding: 1.25rem 1.5rem;
  }
}

.cfo-insights-sidebar-content::-webkit-scrollbar {
  width: 6px;
}

.cfo-insights-sidebar-content::-webkit-scrollbar-track {
  background: transparent;
}

.cfo-insights-sidebar-content::-webkit-scrollbar-thumb {
  background-color: rgb(203 213 225);
  border-radius: 3px;
  transition: background-color 0.3s ease;
}

.cfo-insights-sidebar-content::-webkit-scrollbar-thumb:hover {
  background-color: rgb(148 163 184);
}

/* Modernized list styles */
.cfo-insights-sidebar-content ul {
  list-style: none;
  padding-left: 0;
}

.cfo-insights-sidebar-content ul li {
  position: relative;
  padding-left: 1.25rem;
  line-height: 1.75; /* Increased line-height for better airflow */
  color: #6B7280; /* Softer medium gray for description text */
  margin-bottom: 0.5rem;
}

.cfo-insights-sidebar-content ul li::before {
  content: "•";
  position: absolute;
  left: 0;
  color: #818CF8; /* Light purple bullet to match theme */
  font-size: 1.25rem;
  line-height: 1.5;
  font-weight: 600;
}

.cfo-insights-sidebar-content ul li:hover {
  color: #111827; /* Darker on hover for better readability */
}

.cfo-insights-sidebar-section {
  width: 100%;
  background-color: transparent;
  border-radius: 0;
  padding: 0;
  box-shadow: none;
  display: flex;
  flex-direction: column;
  gap: 0;
}

/* Sidebar Typography - Section Titles */
.cfo-insights-sidebar-section [data-state="open"] button span,
.cfo-insights-sidebar-section button[data-state="open"] span {
  font-weight: 700;
  color: #111827;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Accordion trigger active state */
.cfo-insights-sidebar-section [data-state="open"] button {
  background-color: rgba(249, 250, 251, 0.5);
}

/* Sidebar Typography - Descriptive Text */
.cfo-insights-sidebar-section ul {
  color: #6B7280;
  transition: all 0.3s ease;
}

.cfo-insights-sidebar-section li {
  color: #6B7280;
  list-style-position: outside;
}

/* Accordion Triggers - Cursor Pointer */
.cfo-insights-sidebar-section button[data-state],
.cfo-insights-sidebar-section [role="button"],
.cfo-insights-sidebar-section [data-slot="accordion-trigger"],
.cfo-insights-sidebar-section button[data-slot="accordion-trigger"] {
  cursor: pointer !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.cfo-insights-sidebar-section button[data-state]:hover,
.cfo-insights-sidebar-section [role="button"]:hover {
  background-color: rgba(249, 250, 251, 0.8);
}

/* Smooth accordion content transitions */
.cfo-insights-sidebar-section [data-radix-accordion-content] {
  overflow: hidden;
  transition: max-height 0.5s cubic-bezier(0.4, 0, 0.2, 1), 
              opacity 0.4s ease-in-out,
              padding 0.3s ease-in-out;
}

.cfo-insights-sidebar-section [data-radix-accordion-content][data-state="open"] {
  animation: slideDown 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.cfo-insights-sidebar-section [data-radix-accordion-content][data-state="closed"] {
  animation: slideUp 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideUp {
  from {
    opacity: 1;
    transform: translateY(0);
  }
  to {
    opacity: 0;
    transform: translateY(-10px);
  }
}

/* Chevron rotation animation - Smooth */
.cfo-insights-sidebar-section [data-state="open"] [data-chevron],
.cfo-insights-sidebar-section button[data-state="open"] svg:last-child,
.cfo-insights-sidebar-section [data-state="open"] [data-radix-accordion-trigger] svg:last-child {
  transform: rotate(180deg);
  transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.cfo-insights-sidebar-section [data-state="closed"] [data-chevron],
.cfo-insights-sidebar-section button[data-state="closed"] svg:last-child,
.cfo-insights-sidebar-section [data-state="closed"] [data-radix-accordion-trigger] svg:last-child {
  transform: rotate(0deg);
  transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Accordion trigger icon smooth rotation */
.cfo-insights-sidebar-section [data-radix-accordion-trigger] svg {
  transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

/* All buttons in sidebar */
.cfo-insights-sidebar button {
  cursor: pointer !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* End Chat button styling */
.cfo-insights-sidebar .absolute button {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  width: 100%;
  padding: 0.625rem 1.25rem;
  font-weight: 600;
  font-size: 0.875rem;
  border-radius: 0.5rem;
  box-shadow: 0 2px 4px rgba(79, 70, 229, 0.2);
}

.cfo-insights-sidebar .absolute button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(79, 70, 229, 0.3);
}

.cfo-insights-sidebar .absolute button:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(79, 70, 229, 0.2);
}

/* Links in accordion content */
.cfo-insights-sidebar-section a {
  cursor: pointer !important;
}

/* Accordion items - ensure triggers are clickable */
.cfo-insights-sidebar-section [data-slot="accordion-item"] button,
.cfo-insights-sidebar-section [data-slot="accordion-item"] [role="button"] {
  cursor: pointer !important;
}

.cfo-insights-toggle-btn {
  position: fixed;
  right: 0;
  top: calc(80px + 50%); /* Below header, centered in remaining space */
  transform: translate(0, -50%);
  z-index: 25; /* Above sidebar */
  transition: transform 0.3s ease, opacity 0.3s ease;
  opacity: 1;
  cursor: pointer;
}

.cfo-insights-toggle-btn button {
  cursor: pointer;
}

.cfo-insights-toggle-btn.open {
  opacity: 0;
  pointer-events: none;
}

@media (min-width: 640px) {
  .cfo-insights-toggle-btn {
    top: calc(90px + 50%);
  }
}

@media (min-width: 1024px) {
  .cfo-insights-toggle-btn {
    top: calc(100px + 50%);
  }
}

@media (max-width: 1024px) {
  .cfo-insights-toggle-btn {
    top: auto;
    bottom: 2rem;
    transform: translateY(0);
  }
  
  .cfo-insights-toggle-btn.open {
    transform: translateY(0);
  }
}

.cfo-insights-loading-indicator {
  display: flex;
  gap: 0.5rem;
  justify-content: flex-start;
}

.cfo-insights-loading-bubble {
  position: relative;
  max-width: 80%;
  border-radius: 0.5rem;
  padding: 0.625rem 0.875rem;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  background-color: #F3E8FF; /* Light purple background (purple-50/100) */
  color: #111827; /* Dark text */
  border: 1px solid rgba(79, 70, 229, 0.2); /* Light purple border */
  margin-right: 1.5rem;
}

@media (min-width: 640px) {
  .cfo-insights-loading-bubble {
    padding: 0.75rem 1rem;
    margin-right: 2rem;
  }
}

.cfo-insights-loading-content {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

@media (min-width: 640px) {
  .cfo-insights-loading-content {
    gap: 0.625rem;
  }
}

.cfo-insights-loading-spinner {
  width: 0.875rem;
  height: 0.875rem;
  border: 2px solid rgb(37 99 235);
  border-top-color: transparent;
  border-radius: 9999px;
  animation: spin 1s linear infinite;
}

@media (min-width: 640px) {
  .cfo-insights-loading-spinner {
    width: 1rem;
    height: 1rem;
  }
}

.cfo-insights-loading-text {
  font-size: 0.6875rem;
  color: rgb(75 85 99);
  font-weight: 500;
}

@media (min-width: 640px) {
  .cfo-insights-loading-text {
    font-size: 0.75rem;
  }
}

/* AI Message Styling */
.ai-message h3 {
  color: rgb(31 41 55);
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  margin-top: 1rem;
}

.ai-message h3:first-child {
  margin-top: 0;
}

.ai-message h4 {
  color: rgb(55 65 81);
  font-size: 1rem;
  font-weight: 500;
  margin-bottom: 0.5rem;
  margin-top: 0.75rem;
}

.ai-message h5 {
  color: rgb(75 85 99);
  font-size: 0.875rem;
  font-weight: 500;
  margin-bottom: 0.25rem;
  margin-top: 0.5rem;
}

.ai-message ul {
  margin-bottom: 0.75rem;
}

.ai-message li {
  margin-bottom: 0.25rem;
}

.ai-message strong {
  font-weight: 600;
}

.ai-message em {
  font-style: italic;
}

/* Chat message fade-in animation */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Chat message fade-in animation - works with framer-motion */
.cfo-insights-message {
  animation: fadeInUp 0.3s ease-out;
}

/* Ensure smooth transitions for all interactive elements */
.cfo-insights-sidebar-section li {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  padding: 0.25rem 0;
  border-radius: 0.375rem;
  margin-left: -0.5rem;
  padding-left: 0.5rem;
}

.cfo-insights-sidebar-section li:hover {
  transform: translateX(4px);
  background-color: rgba(79, 70, 229, 0.05);
  padding-left: 0.75rem;
}

/* Example questions specific styling */
.cfo-insights-sidebar-section li[class*="cursor-pointer"] {
  position: relative;
}

.cfo-insights-sidebar-section li[class*="cursor-pointer"]:hover {
  color: #4F46E5;
  font-weight: 500;
}

.cfo-insights-sidebar-section li[class*="cursor-pointer"]:active {
  transform: translateX(2px) scale(0.98);
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

