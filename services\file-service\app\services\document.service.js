// app/services/document.service.js
import { Document } from "../models/index.js";
import logger from "../../config/logger.config.js";
import {
  DOCUMENT_MESSAGES,
  DOCUMENT_LOG_MESSAGES,
  DOCUMENT_SUMMARY_STATUS,
} from "../utils/constants/document.constants.js";
import { DOCUMENT_LOG_MESSAGES as DOCUMENT_SERVICE_LOG_MESSAGES } from "../utils/constants/logMessages.constants.js";
import {
  STATUS_CODE_OK,
  STATUS_CODE_CREATED,
} from "../utils/status_code.utils.js";
import {
  createServiceResponse,
  createSuccessServiceResponse,
  createBadRequestResponse,
  createInternalServerErrorResponse,
  createNotFoundResponse,
} from "../utils/serviceResponse.util.js";
import { startChatSession, getChatSummary } from "./chat.service.js";
import {
  buildBlobFilePath,
  getFullMonthName,
  MONTH_NAME_MAP,
  normalizeServiceName,
  splitBlobPathSegments,
  validateMonth,
  validateService,
  validateYear,
} from "../utils/document.utils.js";
import { ensureOrganizationExists } from "../utils/organization.util.js";
import {
  buildPowerBiReportsBlobPath,
  blobPathState,
} from "../utils/blobPath.util.js";
import { BlobServiceClient } from "@azure/storage-blob";
import { blobConfig } from "../../config/blob.config.js";
import { ONBOARDING_CONTAINER_NAME } from "../utils/constants/onboarding.constants.js";
import { REPORT_FOLDER_NAMES } from "../utils/constants/report.constants.js";
import { getAuthServiceBaseUrl, getSystemApiKey } from "../utils/env.util.js";
import { httpGet } from "../../../../shared/utils/axios.util.js";

// SERVICE FUNCTIONS

/**
 * Store document metadata
 */
export const storeDocumentService = async (documentData, requestId = null) => {
  const logPrefix = requestId ? `[${requestId}]` : "";
  logger.info(
    `${logPrefix} ${DOCUMENT_SERVICE_LOG_MESSAGES.START_STORE_SERVICE}`
  );
  logger.info(`${logPrefix} ${DOCUMENT_SERVICE_LOG_MESSAGES.INPUT_DATA}:`, {
    organization_id: documentData.organization_id,
    organization_name: documentData.organization_name,
    blob_storage_path: documentData.blob_storage_path,
    file_name: documentData.file_name,
    service: documentData.service,
    month: documentData.month,
    year: documentData.year,
    hasRefreshToken: !!documentData.refreshToken,
  });

  try {
    const {
      organization_id,
      organization_name,
      blob_storage_path,
      service,
      month,
      year,
      file_name,
      file_size,
      mime_type,
      metadata,
      refreshToken,
    } = documentData;

    // Guard clauses: validate required fields early
    if (!organization_id || !blob_storage_path || !file_name) {
      logger.warn(DOCUMENT_LOG_MESSAGES.SERVICE_MISSING_FIELDS);
      return createBadRequestResponse(
        DOCUMENT_MESSAGES.MISSING_REQUIRED_FIELDS,
        DOCUMENT_MESSAGES.ORGANIZATION_ID_REQUIRED_ERROR
      );
    }

    // Normalize and validate service using mapping pattern if provided
    let validatedService;
    if (service) {
      const normalizedService = normalizeServiceName(service);
      if (!validateService(normalizedService)) {
        logger.warn(
          `${DOCUMENT_LOG_MESSAGES.SERVICE_INVALID_SERVICE}: ${service}`
        );
        return createBadRequestResponse(
          DOCUMENT_MESSAGES.INVALID_SERVICE_TYPE,
          DOCUMENT_MESSAGES.INVALID_SERVICE_ERROR
        );
      }
      validatedService = normalizedService;
    }

    // Validate month with early return if provided
    if (month !== undefined && month !== null && !validateMonth(month)) {
      logger.warn(`${DOCUMENT_LOG_MESSAGES.SERVICE_INVALID_MONTH}: ${month}`);
      return createBadRequestResponse(
        DOCUMENT_MESSAGES.INVALID_MONTH,
        DOCUMENT_MESSAGES.INVALID_MONTH_ERROR
      );
    }

    // Validate year with early return if provided
    if (year !== undefined && year !== null && !validateYear(year)) {
      logger.warn(`${DOCUMENT_LOG_MESSAGES.SERVICE_INVALID_YEAR}: ${year}`);
      return createBadRequestResponse(
        DOCUMENT_MESSAGES.INVALID_YEAR,
        DOCUMENT_MESSAGES.INVALID_YEAR_ERROR
      );
    }

    const resolvedFileName = file_name;
    const baseBlobPath = blob_storage_path;
    const finalizedBlobPath = buildBlobFilePath(baseBlobPath, resolvedFileName);

    if (!finalizedBlobPath) {
      return createBadRequestResponse(
        DOCUMENT_MESSAGES.INVALID_BLOB_PATH,
        DOCUMENT_MESSAGES.INVALID_BLOB_PATH
      );
    }

    logger.info(
      `${logPrefix} ${DOCUMENT_SERVICE_LOG_MESSAGES.CHECKING_ORGANIZATION}: ${organization_id}`
    );
    const organizationCheck = await ensureOrganizationExists(organization_id);
    logger.info(
      `${logPrefix} ${DOCUMENT_SERVICE_LOG_MESSAGES.ORGANIZATION_CHECK_RESULT}:`,
      {
        success: organizationCheck.success,
        code: organizationCheck.code,
        message: organizationCheck.message,
      }
    );
    if (!organizationCheck.success) {
      if (organizationCheck.code === "NOT_FOUND") {
        return createNotFoundResponse(DOCUMENT_MESSAGES.ORGANIZATION_NOT_FOUND);
      }

      if (organizationCheck.code === "CLIENT_ERROR") {
        return createBadRequestResponse(
          organizationCheck.message ||
            DOCUMENT_MESSAGES.ORGANIZATION_VALIDATION_FAILED
        );
      }

      return createInternalServerErrorResponse(
        DOCUMENT_MESSAGES.ORGANIZATION_VALIDATION_FAILED,
        organizationCheck.message
      );
    }

    const pathSegments = splitBlobPathSegments(baseBlobPath);

    if (pathSegments.length < 6) {
      logger.warn(DOCUMENT_LOG_MESSAGES.SERVICE_INVALID_BLOB_PATH_STRUCTURE, {
        blob_storage_path,
      });
      return createBadRequestResponse(
        DOCUMENT_MESSAGES.INVALID_BLOB_PATH_STRUCTURE,
        DOCUMENT_MESSAGES.INVALID_BLOB_PATH_STRUCTURE
      );
    }

    const [
      pathOrganizationId,
      pathOrganizationName,
      pathStaticSegment,
      pathServiceSegment,
      pathYearSegment,
      pathMonthSegment,
    ] = pathSegments;

    if (
      !pathOrganizationId ||
      !pathOrganizationName ||
      !pathStaticSegment ||
      !pathServiceSegment ||
      !pathYearSegment ||
      !pathMonthSegment
    ) {
      logger.warn(DOCUMENT_LOG_MESSAGES.SERVICE_INVALID_BLOB_PATH_STRUCTURE, {
        blob_storage_path,
      });
      return createBadRequestResponse(
        DOCUMENT_MESSAGES.INVALID_BLOB_PATH_STRUCTURE,
        DOCUMENT_MESSAGES.INVALID_BLOB_PATH_STRUCTURE
      );
    }

    const EXPECTED_STATIC_SEGMENT = "Power BI-Reports";
    if (pathStaticSegment !== EXPECTED_STATIC_SEGMENT) {
      logger.warn(DOCUMENT_LOG_MESSAGES.SERVICE_INVALID_BLOB_PATH_STRUCTURE, {
        blob_storage_path,
      });
      return createBadRequestResponse(
        DOCUMENT_MESSAGES.INVALID_BLOB_PATH_STRUCTURE,
        DOCUMENT_MESSAGES.INVALID_BLOB_PATH_STRUCTURE
      );
    }

    if (String(pathOrganizationId) !== String(organization_id)) {
      logger.warn(DOCUMENT_LOG_MESSAGES.SERVICE_INVALID_BLOB_PATH_STRUCTURE, {
        blob_storage_path,
        providedOrganizationId: organization_id,
        pathOrganizationId,
      });
      return createBadRequestResponse(
        DOCUMENT_MESSAGES.INVALID_BLOB_PATH_STRUCTURE,
        DOCUMENT_MESSAGES.INVALID_BLOB_PATH_STRUCTURE
      );
    }

    if (validatedService) {
      const normalizedPathService = normalizeServiceName(pathServiceSegment);
      if (normalizedPathService !== validatedService) {
        logger.warn(DOCUMENT_LOG_MESSAGES.SERVICE_INVALID_BLOB_PATH_STRUCTURE, {
          blob_storage_path,
          expectedService: validatedService,
          pathService: normalizedPathService,
        });
        return createBadRequestResponse(
          DOCUMENT_MESSAGES.INVALID_BLOB_PATH_STRUCTURE,
          DOCUMENT_MESSAGES.INVALID_BLOB_PATH_STRUCTURE
        );
      }
    }

    const yearPattern = /^\d{4}$/;
    if (!yearPattern.test(pathYearSegment)) {
      logger.warn(DOCUMENT_LOG_MESSAGES.SERVICE_INVALID_BLOB_PATH_STRUCTURE, {
        blob_storage_path,
        invalidYear: pathYearSegment,
      });
      return createBadRequestResponse(
        DOCUMENT_MESSAGES.INVALID_BLOB_PATH_STRUCTURE,
        DOCUMENT_MESSAGES.INVALID_BLOB_PATH_STRUCTURE
      );
    }

    if (
      year !== undefined &&
      year !== null &&
      Number(pathYearSegment) !== year
    ) {
      logger.warn(DOCUMENT_LOG_MESSAGES.SERVICE_INVALID_BLOB_PATH_STRUCTURE, {
        blob_storage_path,
        providedYear: year,
        pathYear: pathYearSegment,
      });
      return createBadRequestResponse(
        DOCUMENT_MESSAGES.INVALID_BLOB_PATH_STRUCTURE,
        DOCUMENT_MESSAGES.INVALID_BLOB_PATH_STRUCTURE
      );
    }

    if (month !== undefined && month !== null) {
      const normalizedMonth = String(pathMonthSegment).toLowerCase();
      const acceptableMonthValues = new Set();
      acceptableMonthValues.add(String(month).toLowerCase());
      if (MONTH_NAME_MAP[month]) {
        acceptableMonthValues.add(MONTH_NAME_MAP[month]);
      }

      if (!acceptableMonthValues.has(normalizedMonth)) {
        logger.warn(DOCUMENT_LOG_MESSAGES.SERVICE_INVALID_BLOB_PATH_STRUCTURE, {
          blob_storage_path,
          providedMonth: month,
          pathMonth: pathMonthSegment,
        });
        return createBadRequestResponse(
          DOCUMENT_MESSAGES.INVALID_BLOB_PATH_STRUCTURE,
          DOCUMENT_MESSAGES.INVALID_BLOB_PATH_STRUCTURE
        );
      }
    }

    const organizationNameFromPath = pathOrganizationName;

    const organizationNameFromMetadata =
      (metadata && (metadata.organizationName || metadata.organization_name)) ||
      null;

    // Priority: payload > metadata > path
    const resolvedOrganizationName =
      organization_name ||
      organizationNameFromMetadata ||
      organizationNameFromPath;

    if (!resolvedOrganizationName) {
      logger.warn(DOCUMENT_LOG_MESSAGES.SERVICE_MISSING_ORGANIZATION_NAME);
      return createBadRequestResponse(
        DOCUMENT_MESSAGES.MISSING_ORGANIZATION_NAME,
        DOCUMENT_MESSAGES.MISSING_ORGANIZATION_NAME
      );
    }

    // Check if document already exists using composite condition
    const documentLookupWhere = {
      organization_id,
      blob_storage_path: finalizedBlobPath,
      file_name: resolvedFileName,
      is_deleted: false,
      ...(validatedService && { service: validatedService }),
      ...(month !== undefined && month !== null && { month }),
      ...(year !== undefined && year !== null && { year }),
    };

    const existingDocument = await Document.findOne({
      where: documentLookupWhere,
    });

    const combinedMetadata = {
      ...(existingDocument?.metadata || {}),
      ...(metadata || {}),
      summaryStatus: DOCUMENT_SUMMARY_STATUS.PROCESSING,
      organizationName: resolvedOrganizationName,
    };
    if (combinedMetadata.summaryError) {
      delete combinedMetadata.summaryError;
    }

    // Store/update document data in DB
    const docData = {
      organization_id,
      blob_storage_path: finalizedBlobPath,
      file_name: resolvedFileName,
      is_active: true,
      metadata: combinedMetadata,
      ...(validatedService && { service: validatedService }),
      ...(month !== undefined && month !== null && { month }),
      ...(year !== undefined && year !== null && { year }),
      ...(file_size !== undefined && file_size !== null && { file_size }),
      ...(mime_type && { mime_type }),
    };

    logger.info(
      `${logPrefix} ${
        existingDocument
          ? DOCUMENT_SERVICE_LOG_MESSAGES.UPDATING_DOCUMENT
          : DOCUMENT_SERVICE_LOG_MESSAGES.CREATING_DOCUMENT
      }`
    );
    const document = existingDocument
      ? await existingDocument.update(docData)
      : await Document.create(docData);

    logger.info(
      `${logPrefix} ${
        existingDocument
          ? DOCUMENT_SERVICE_LOG_MESSAGES.DOCUMENT_UPDATED
          : DOCUMENT_SERVICE_LOG_MESSAGES.DOCUMENT_CREATED
      }:`,
      {
        documentId: document.id,
        organization_id: document.organization_id,
        file_name: document.file_name,
        blob_storage_path: document.blob_storage_path,
      }
    );

    // Asynchronous summary generation flow (non-blocking)
    // Fire and forget - don't wait for completion
    logger.info(
      `${logPrefix} ${DOCUMENT_SERVICE_LOG_MESSAGES.STARTING_ASYNC_SUMMARY}`
    );
    logger.info(
      `${logPrefix} ${DOCUMENT_SERVICE_LOG_MESSAGES.SUMMARY_GENERATION_PARAMS}:`,
      {
        filename: resolvedFileName,
        organizationId: organization_id,
        organizationName: resolvedOrganizationName,
        year,
        month,
        hasRefreshToken: !!refreshToken,
        documentId: document.id,
      }
    );

    // Run summary generation asynchronously without blocking
    setImmediate(async () => {
      const summaryRequestId = `${requestId}-SUMMARY`;
      const summaryLogPrefix = `[${summaryRequestId}]`;

      try {
        // Step 1: Start chat session
        logger.info(
          `${summaryLogPrefix} ${DOCUMENT_SERVICE_LOG_MESSAGES.STARTING_CHAT_SESSION}`
        );
        const chatSession = await startChatSession(
          resolvedFileName,
          organization_id,
          resolvedOrganizationName,
          year,
          month,
          refreshToken,
          summaryRequestId,
          service // Pass service parameter
        );

        if (!chatSession?.success || !chatSession.sessionId) {
          logger.error(
            `${summaryLogPrefix} ${DOCUMENT_SERVICE_LOG_MESSAGES.FAILED_START_CHAT_SESSION}`,
            { response: chatSession }
          );
          throw new Error(
            DOCUMENT_SERVICE_LOG_MESSAGES.FAILED_START_CHAT_SESSION
          );
        }

        logger.info(
          `${summaryLogPrefix} ${DOCUMENT_SERVICE_LOG_MESSAGES.CHAT_SESSION_STARTED}: ${chatSession.sessionId}`
        );

        // Step 2: Get summary
        logger.info(
          `${summaryLogPrefix} ${DOCUMENT_SERVICE_LOG_MESSAGES.GETTING_SUMMARY}`
        );
        const summaryResponse = await getChatSummary(
          chatSession.sessionId,
          refreshToken,
          summaryRequestId
        );

        if (summaryResponse?.success && summaryResponse.summaryData) {
          const summaryData = summaryResponse.summaryData;
          logger.info(
            `${summaryLogPrefix} ${DOCUMENT_SERVICE_LOG_MESSAGES.SUMMARY_RECEIVED}:`,
            {
              hasTitle: !!summaryData.title,
              sectionsCount: summaryData.sections?.length,
            }
          );

          // Step 3: Update document with summary
          await document.update({
            summary: summaryData,
            metadata: {
              ...combinedMetadata,
              summaryStatus: DOCUMENT_SUMMARY_STATUS.COMPLETED,
              summaryError: null,
            },
          });

          logger.info(
            `${summaryLogPrefix} ${DOCUMENT_SERVICE_LOG_MESSAGES.UPDATING_DOCUMENT_WITH_SUMMARY}`
          );
        } else {
          logger.error(
            `${summaryLogPrefix} ${DOCUMENT_SERVICE_LOG_MESSAGES.FAILED_GET_SUMMARY}`,
            { response: summaryResponse }
          );
          throw new Error(DOCUMENT_SERVICE_LOG_MESSAGES.FAILED_GET_SUMMARY);
        }
      } catch (error) {
        // Build detailed error message
        const errorDetails = {
          message: error.message,
          status: error.response?.status,
          statusText: error.response?.statusText,
          responseData: error.response?.data,
          url: error.config?.url,
          method: error.config?.method,
        };

        // Create comprehensive error message
        let summaryError;
        if (error.response) {
          const status = error.response.status;
          const responseMessage =
            error.response.data?.message ||
            error.response.data?.error ||
            error.message;
          summaryError = `HTTP ${status}: ${responseMessage}`;

          // Add more context if available
          if (error.response.data?.details) {
            summaryError += ` | Details: ${JSON.stringify(
              error.response.data.details
            )}`;
          }
        } else if (error.request) {
          summaryError = `Network Error: ${
            error.message || "Request failed - no response received"
          }`;
        } else {
          summaryError = `Error: ${error.message}`;
        }

        logger.error(
          `${summaryLogPrefix} ${DOCUMENT_SERVICE_LOG_MESSAGES.SUMMARY_GENERATION_FAILED}:`,
          {
            error: error.message,
            errorDetails,
            stack: error.stack,
          }
        );

        // Update document with error status
        await document.update({
          metadata: {
            ...combinedMetadata,
            summaryStatus: DOCUMENT_SUMMARY_STATUS.FAILED,
            summaryError: summaryError,
          },
        });
      }
    });

    // Reload document to get latest state
    await document.reload();

    logger.info(
      `${logPrefix} ${DOCUMENT_SERVICE_LOG_MESSAGES.END_STORE_SERVICE}`
    );
    return createServiceResponse(
      true,
      existingDocument ? STATUS_CODE_OK : STATUS_CODE_CREATED,
      existingDocument
        ? DOCUMENT_MESSAGES.UPDATED_SUCCESSFULLY
        : DOCUMENT_MESSAGES.STORED_SUCCESSFULLY,
      document
    );
  } catch (error) {
    logger.error(
      `${logPrefix} ${DOCUMENT_SERVICE_LOG_MESSAGES.STORE_SERVICE_ERROR}:`,
      {
        error: error.message,
        stack: error.stack,
      }
    );
    return createInternalServerErrorResponse(
      DOCUMENT_MESSAGES.STORAGE_FAILED,
      error.message
    );
  }
};

/**
 * Get document by ID (for status checking)
 */
export const getDocumentByIdService = async (documentId) => {
  try {
    const document = await Document.findOne({
      where: {
        id: documentId,
        is_deleted: false,
      },
      attributes: [
        "id",
        "organization_id",
        "file_name",
        "blob_storage_path",
        "summary",
        "metadata",
        "created_at",
        "updated_at",
      ],
    });

    if (!document) {
      return createNotFoundResponse(DOCUMENT_MESSAGES.DOCUMENT_NOT_FOUND);
    }

    // Extract summary status from metadata
    const summaryStatus = document.metadata?.summaryStatus || null;
    const summaryError = document.metadata?.summaryError || null;
    const hasSummary = !!document.summary;

    return createServiceResponse(
      true,
      STATUS_CODE_OK,
      DOCUMENT_MESSAGES.RETRIEVED_SUCCESSFULLY,
      {
        id: document.id,
        organizationId: document.organization_id,
        fileName: document.file_name,
        blobStoragePath: document.blob_storage_path,
        summaryStatus,
        summaryError,
        hasSummary,
        summary: document.summary, // Include summary if available
        createdAt: document.created_at,
        updatedAt: document.updated_at,
      }
    );
  } catch (error) {
    logger.error(`Error getting document by ID: ${error.message}`, {
      stack: error.stack,
    });
    return createInternalServerErrorResponse(
      DOCUMENT_MESSAGES.RETRIEVAL_FAILED,
      error.message
    );
  }
};

/**
 * Get documents by organization and filters
 */
export const getDocumentsService = async (organization_id, filters = {}) => {
  logger.info(DOCUMENT_LOG_MESSAGES.SERVICE_GETTING_DOCUMENTS);

  try {
    // Build where clause using object spread pattern
    const whereClause = {
      organization_id,
      is_deleted: false,
    };

    // Add filters only if provided (guard clause pattern)
    if (filters.service) {
      // Normalize service name for consistent filtering
      whereClause.service = normalizeServiceName(filters.service);
    }
    if (filters.month) {
      whereClause.month = filters.month;
    }
    if (filters.year) {
      whereClause.year = filters.year;
    }
    if (filters.blob_storage_path) {
      whereClause.blob_storage_path = filters.blob_storage_path;
    }

    const documents = await Document.findAll({
      where: whereClause,
      order: [["created_at", "DESC"]],
    });

    return createServiceResponse(
      true,
      STATUS_CODE_OK,
      DOCUMENT_MESSAGES.RETRIEVED_SUCCESSFULLY,
      documents
    );
  } catch (error) {
    logger.error(
      `${DOCUMENT_LOG_MESSAGES.SERVICE_RETRIEVAL_ERROR}: ${error.message}`,
      { stack: error.stack }
    );
    return createInternalServerErrorResponse(
      DOCUMENT_MESSAGES.RETRIEVAL_FAILED,
      error.message
    );
  }
};

/**
 * Update document by ID (internal use)
 */
export const updateDocumentByIdService = async (documentId, updates = {}) => {
  const requestId = `UPD-${Date.now()}-${Math.random()
    .toString(36)
    .substr(2, 9)}`;
  logger.info(
    `${requestId} [UPDATE_DOCUMENT] ===== START: updateDocumentByIdService =====`
  );
  logger.info(`${requestId} [UPDATE_DOCUMENT] Input:`, {
    documentId,
    updates: {
      ...updates,
      summary: updates.summary
        ? typeof updates.summary === "object"
          ? "JSON_OBJECT"
          : "STRING"
        : "NULL",
      summaryPreview: updates.summary
        ? JSON.stringify(updates.summary).substring(0, 200)
        : "null",
    },
  });

  try {
    const document = await Document.findOne({
      where: {
        id: documentId,
        is_deleted: false,
      },
    });

    if (!document) {
      logger.warn(
        `${requestId} [UPDATE_DOCUMENT] Document not found: ${documentId}`
      );
      return createNotFoundResponse(DOCUMENT_MESSAGES.DOCUMENT_NOT_FOUND);
    }

    logger.info(`${requestId} [UPDATE_DOCUMENT] Document found:`, {
      documentId: document.id,
      currentSummary: document.summary
        ? typeof document.summary === "object"
          ? "JSON_OBJECT"
          : "STRING"
        : "NULL",
    });

    const {
      summary,
      summaryStatus,
      metadata: metadataUpdates,
      summaryError,
      ...restUpdates
    } = updates;

    logger.info(`${requestId} [UPDATE_DOCUMENT] Processing updates:`, {
      hasSummary: !!summary,
      summaryType: typeof summary,
      summaryStatus,
      hasMetadataUpdates: !!metadataUpdates,
      summaryError,
    });

    const nextMetadata = {
      ...(document.metadata || {}),
    };

    logger.info(`${requestId} [UPDATE_DOCUMENT] Current metadata:`, {
      currentMetadata: document.metadata,
      metadataUpdates,
    });

    if (metadataUpdates && typeof metadataUpdates === "object") {
      Object.assign(nextMetadata, metadataUpdates);
    }

    if (summaryStatus) {
      nextMetadata.summaryStatus = summaryStatus;
      if (summaryStatus === DOCUMENT_SUMMARY_STATUS.COMPLETED) {
        delete nextMetadata.summaryError;
      }
    }

    if (summaryError) {
      nextMetadata.summaryError = summaryError;
    } else if (
      !summaryError &&
      summaryStatus !== DOCUMENT_SUMMARY_STATUS.FAILED
    ) {
      delete nextMetadata.summaryError;
    }

    const updateData = {};

    if (summary !== undefined) {
      updateData.summary = summary;
    }

    if (Object.keys(nextMetadata).length > 0) {
      updateData.metadata = nextMetadata;
    }

    // Allow specific fields to be updated explicitly if provided
    const allowedFields = ["is_active", "file_size", "mime_type"];
    allowedFields.forEach((field) => {
      if (restUpdates[field] !== undefined) {
        updateData[field] = restUpdates[field];
      }
    });

    if (Object.keys(updateData).length === 0) {
      return createServiceResponse(
        true,
        STATUS_CODE_OK,
        DOCUMENT_MESSAGES.UPDATED_SUCCESSFULLY,
        document
      );
    }

    const updatedDocument = await document.update(updateData);

    return createServiceResponse(
      true,
      STATUS_CODE_OK,
      DOCUMENT_MESSAGES.UPDATED_SUCCESSFULLY,
      updatedDocument
    );
  } catch (error) {
    logger.error(
      `${DOCUMENT_LOG_MESSAGES.SERVICE_STORAGE_ERROR}: ${error.message}`,
      { stack: error.stack }
    );
    return createInternalServerErrorResponse(
      DOCUMENT_MESSAGES.STORAGE_FAILED,
      error.message
    );
  }
};

/**
 * Initialize blob storage for PDF storage
 */
const initializeBlobStorage = () => {
  if (blobPathState.isConfigured) {
    return;
  }

  try {
    blobConfig.validate();
    const blobServiceClient = BlobServiceClient.fromConnectionString(
      blobConfig.connectionString
    );
    blobPathState.containerClient = blobServiceClient.getContainerClient(
      ONBOARDING_CONTAINER_NAME
    );
    blobPathState.isConfigured = true;
  } catch (error) {
    logger.error(
      `${DOCUMENT_LOG_MESSAGES.SERVICE_PDF_STORAGE_FAILED}: ${error.message}`,
      { error }
    );
    blobPathState.isConfigured = false;
  }
};

/**
 * Ensure blob container exists
 */
const ensureContainerExists = async () => {
  if (!blobPathState.containerClient) {
    throw new Error(DOCUMENT_MESSAGES.PDF_STORAGE_FAILED);
  }

  if (blobPathState.containerEnsured) {
    return;
  }

  logger.info("[PDF_STORAGE] Ensuring Azure container exists");
  await blobPathState.containerClient.createIfNotExists();
  blobPathState.containerEnsured = true;
};

/**
 * Fetch organization name from auth service
 * @param {string} orgId - Organization ID
 * @returns {Promise<{success: boolean, orgName?: string, error?: string}>}
 */
const fetchOrganizationNameForPdf = async (orgId) => {
  try {
    const baseUrl = getAuthServiceBaseUrl();
    const url = `${baseUrl}/organization/${orgId}`;

    const headers = {};
    try {
      const apiKey = getSystemApiKey();
      headers["x-api-key"] = apiKey;
    } catch {
      logger.warn(
        "[PDF_STORAGE] System API key not configured for organization lookup"
      );
    }

    logger.info(`[PDF_STORAGE] Fetching organization name for: ${orgId}`);
    const response = await httpGet(url, {
      headers,
      timeout: 5000,
    });

    const organizationName =
      response?.data?.data?.name ?? response?.data?.name ?? null;

    if (organizationName) {
      logger.info(
        `[PDF_STORAGE] Organization name fetched: ${organizationName}`
      );
      return { success: true, orgName: organizationName };
    }

    logger.warn(`[PDF_STORAGE] Organization name not found for: ${orgId}`);
    return { success: false, error: "Organization name not found" };
  } catch (error) {
    logger.error(
      `[PDF_STORAGE] Failed to fetch organization name: ${error.message}`,
      {
        orgId,
        error: error.response?.data || error.message,
      }
    );
    return { success: false, error: error.message };
  }
};

/**
 * Store PDF buffer to Azure Blob Storage
 * @param {Object} params - Storage parameters
 * @param {string} params.organization_id - Organization ID
 * @param {string} params.organization_name - Organization name (optional, will be fetched if not provided)
 * @param {string} params.service - Service type (finance, payroll, operations)
 * @param {number} params.month - Month (1-12)
 * @param {number} params.year - Year
 * @param {Buffer} params.pdfBuffer - PDF file buffer
 * @param {string} params.fileName - Optional custom file name
 * @returns {Promise<Object>} Service response with blob URL and path
 */
export const storePdfToBlob = async ({
  organization_id,
  organization_name,
  service,
  month,
  year,
  pdfBuffer,
  fileName,
}) => {
  const requestId = `PDF-${Date.now()}-${Math.random()
    .toString(36)
    .substr(2, 9)}`;
  logger.info(
    `[${requestId}] ${DOCUMENT_LOG_MESSAGES.SERVICE_PDF_STORAGE_START}`,
    {
      organization_id,
      service,
      month,
      year,
      hasBuffer: !!pdfBuffer,
      bufferSize: pdfBuffer?.length,
    }
  );

  try {
    // Validate required fields
    if (!organization_id || !service || !month || !year) {
      logger.warn(`[${requestId}] Missing required PDF storage parameters`);
      return createBadRequestResponse(
        DOCUMENT_MESSAGES.MISSING_PDF_PARAMS,
        DOCUMENT_MESSAGES.MISSING_PDF_PARAMS
      );
    }

    if (!pdfBuffer || !Buffer.isBuffer(pdfBuffer)) {
      logger.warn(`[${requestId}] PDF buffer is required`);
      return createBadRequestResponse(
        DOCUMENT_MESSAGES.PDF_BUFFER_REQUIRED,
        DOCUMENT_MESSAGES.PDF_BUFFER_REQUIRED
      );
    }

    // Validate service
    const normalizedService = normalizeServiceName(service);
    if (!validateService(normalizedService)) {
      logger.warn(`[${requestId}] Invalid service: ${service}`);
      return createBadRequestResponse(
        DOCUMENT_MESSAGES.INVALID_SERVICE_TYPE,
        DOCUMENT_MESSAGES.INVALID_SERVICE_ERROR
      );
    }

    // Validate month
    if (!validateMonth(month)) {
      logger.warn(`[${requestId}] Invalid month: ${month}`);
      return createBadRequestResponse(
        DOCUMENT_MESSAGES.INVALID_MONTH,
        DOCUMENT_MESSAGES.INVALID_MONTH_ERROR
      );
    }

    // Validate year
    if (!validateYear(year)) {
      logger.warn(`[${requestId}] Invalid year: ${year}`);
      return createBadRequestResponse(
        DOCUMENT_MESSAGES.INVALID_YEAR,
        DOCUMENT_MESSAGES.INVALID_YEAR_ERROR
      );
    }

    // Initialize blob storage
    initializeBlobStorage();

    if (!blobPathState.isConfigured || !blobPathState.containerClient) {
      logger.error(`[${requestId}] Blob storage not configured`);
      return createInternalServerErrorResponse(
        DOCUMENT_MESSAGES.PDF_STORAGE_FAILED,
        "Blob storage is not configured"
      );
    }

    await ensureContainerExists();

    // Resolve organization name if not provided
    let resolvedOrgName = organization_name;
    if (!resolvedOrgName) {
      logger.info(`[${requestId}] Organization name not provided, fetching...`);
      const orgNameResult = await fetchOrganizationNameForPdf(organization_id);
      if (!orgNameResult.success || !orgNameResult.orgName) {
        return createBadRequestResponse(
          DOCUMENT_MESSAGES.MISSING_ORGANIZATION_NAME,
          orgNameResult.error || "Organization name not found"
        );
      }
      resolvedOrgName = orgNameResult.orgName;
    }

    // Build file name
    const monthName = getFullMonthName(Number(month));
    const serviceMap = {
      financial: REPORT_FOLDER_NAMES.FINANCE,
      operational: REPORT_FOLDER_NAMES.OPERATIONS,
      payroll: REPORT_FOLDER_NAMES.PAYROLL,
    };
    const serviceFolderName = serviceMap[normalizedService] || service;
    const defaultFileName = `${serviceFolderName}_Report_${monthName}_${year}.pdf`;
    const finalFileName = fileName || defaultFileName;

    // Build blob path
    logger.info(
      `[${requestId}] ${DOCUMENT_LOG_MESSAGES.SERVICE_PDF_BUILDING_PATH}`
    );
    const blobPath = buildPowerBiReportsBlobPath(
      organization_id,
      resolvedOrgName,
      service,
      year,
      month,
      finalFileName
    );

    logger.info(`[${requestId}] Uploading PDF to blob path: ${blobPath}`);

    // Upload to blob storage
    const blockBlobClient =
      blobPathState.containerClient.getBlockBlobClient(blobPath);
    await blockBlobClient.uploadData(pdfBuffer, {
      blobHTTPHeaders: {
        blobContentType: "application/pdf",
      },
    });

    logger.info(
      `[${requestId}] ${DOCUMENT_LOG_MESSAGES.SERVICE_PDF_STORAGE_SUCCESS}`,
      {
        blobPath,
        blobUrl: blockBlobClient.url,
        size: pdfBuffer.length,
      }
    );

    // Store document metadata in Documents table
    logger.info(
      `[${requestId}] Storing document metadata to Documents table`
    );
    
    const documentMetadata = {
      organization_id,
      organization_name: resolvedOrgName,
      blob_storage_path: blobPath,
      service: normalizedService,
      month: Number(month),
      year: Number(year),
      file_name: finalFileName,
      file_size: pdfBuffer.length,
      mime_type: "application/pdf",
    };

    const documentResult = await storeDocumentService(documentMetadata, requestId);
    
    if (!documentResult.success) {
      logger.warn(
        `[${requestId}] Failed to store document metadata: ${documentResult.message}`
      );
      // Don't fail the whole operation since blob was uploaded successfully
    } else {
      logger.info(
        `[${requestId}] Document metadata stored successfully`,
        {
          documentId: documentResult.data?.id
        }
      );
    }

    return createSuccessServiceResponse(
      STATUS_CODE_CREATED,
      DOCUMENT_MESSAGES.PDF_STORED_SUCCESSFULLY,
      {
        blobPath,
        blobUrl: blockBlobClient.url,
        fileName: finalFileName,
        size: pdfBuffer.length,
        contentType: "application/pdf",
        organizationId: organization_id,
        organizationName: resolvedOrgName,
        service: normalizedService,
        month: Number(month),
        year: Number(year),
        documentId: documentResult.data?.id,
      }
    );
  } catch (error) {
    logger.error(
      `[${requestId}] ${DOCUMENT_LOG_MESSAGES.SERVICE_PDF_STORAGE_FAILED}`,
      {
        error: error.message,
        stack: error.stack,
      }
    );
    return createInternalServerErrorResponse(
      DOCUMENT_MESSAGES.PDF_STORAGE_FAILED,
      error.message
    );
  }
};
