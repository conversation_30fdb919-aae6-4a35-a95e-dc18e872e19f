// DOCUMENT CONSTANTS
// Static messages and validation constants for Document Management

export const DOCUMENT_MESSAGES = {
  // Success Messages
  STORED_SUCCESSFULLY: "Document stored successfully",
  UPDATED_SUCCESSFULLY: "Document updated successfully",
  RETRIEVED_SUCCESSFULLY: "Documents retrieved successfully",
  PDF_STORED_SUCCESSFULLY: "PDF stored successfully to blob storage",

  // Error Messages
  STORAGE_FAILED: "Failed to store document",
  RETRIEVAL_FAILED: "Failed to retrieve documents",
  MISSING_REQUIRED_FIELDS:
    "Missing required fields: organization_id, blob_storage_path, service, month, and year are required",
  MISSING_ORGANIZATION_ID: "organization_id is required",
  ORGANIZATION_ID_REQUIRED_ERROR:
    "Required fields: organization_id, blob_storage_path, service, month, year",
  MISSING_FILE_NAME: "file_name is required to generate document summary",
  MISSING_REFRESH_TOKEN:
    "Authentication token is required to generate document summary",
  INVALID_BLOB_PATH: "Unable to resolve blob storage path for the document",
  MISSING_ORGANIZATION_NAME:
    "Organization name could not be determined from the provided path or metadata",
  INVALID_BLOB_PATH_STRUCTURE:
    "Blob storage path must follow 'organizationId/organizationName/Power BI-Reports/service/year/month/' format",
  ORGANIZATION_NOT_FOUND: "Organization does not exist",
  ORGANIZATION_VALIDATION_FAILED: "Failed to validate organization",
  SUMMARY_GENERATION_FAILED: "Failed to generate document summary",
  PDF_STORAGE_FAILED: "Failed to store PDF to blob storage",
  PDF_BUFFER_REQUIRED: "PDF buffer is required",
  MISSING_PDF_PARAMS:
    "Missing required fields: organization_id, service, month, year are required",

  // Validation Messages
  INVALID_SERVICE_TYPE:
    "Invalid service type. Must be one of: financial, operational, payroll (case-insensitive, accepts: Finance/Financial, Operations/Operational, Payroll/PMS)",
  INVALID_MONTH: "Month must be between 1 and 12",
  INVALID_YEAR: "Year must be between 2000 and 3000",

  // General Error Labels
  INVALID_SERVICE_ERROR: "Invalid service",
  INVALID_MONTH_ERROR: "Invalid month",
  INVALID_YEAR_ERROR: "Invalid year",
  FETCH_FAILED: "Failed to fetch documents",

  // Document Status
  DOCUMENT_EXISTS:
    "Document already exists for this organization, service, month, and year",
  DOCUMENT_NOT_FOUND: "Document not found",
};

export const DOCUMENT_SUMMARY_STATUS = {
  PROCESSING: "PROCESSING",
  COMPLETED: "COMPLETED",
  FAILED: "FAILED",
};

export const DOCUMENT_LOG_MESSAGES = {
  // Service Log Messages
  SERVICE_START_STORAGE: "Starting document storage",
  SERVICE_MISSING_FIELDS: "Missing required fields",
  SERVICE_INVALID_SERVICE: "Invalid service",
  SERVICE_INVALID_MONTH: "Invalid month",
  SERVICE_INVALID_YEAR: "Invalid year",
  SERVICE_MISSING_ORGANIZATION_NAME:
    "Organization name missing from path and metadata",
  SERVICE_INVALID_BLOB_PATH_STRUCTURE: "Invalid blob storage path structure",
  SERVICE_DOCUMENT_EXISTS: "Document already exists, updating existing record",
  SERVICE_DOCUMENT_UPDATED: "Document updated successfully",
  SERVICE_DOCUMENT_STORED: "Document stored successfully",
  SERVICE_STORAGE_ERROR: "Error storing document",
  SERVICE_GETTING_DOCUMENTS: "Getting documents",
  SERVICE_RETRIEVAL_ERROR: "Error retrieving documents",
  SERVICE_SUMMARY_START: "Generating document summary",
  SERVICE_SUMMARY_SUCCESS: "Document summary generated successfully",
  SERVICE_SUMMARY_FAILED: "Document summary generation failed",
  SERVICE_INVALID_BLOB_PATH_STRUCTURE:
    "Blob storage path does not match expected structure",
  SERVICE_ORGANIZATION_NOT_FOUND:
    "Organization lookup failed - organization not found",
  SERVICE_ORGANIZATION_VALIDATION_FAILED:
    "Organization lookup failed due to an unexpected error",
  // PDF Storage Log Messages
  SERVICE_PDF_STORAGE_START: "[PDF_STORAGE] Starting PDF storage to blob",
  SERVICE_PDF_STORAGE_SUCCESS: "[PDF_STORAGE] PDF stored successfully to blob",
  SERVICE_PDF_STORAGE_FAILED: "[PDF_STORAGE] PDF storage failed",
  SERVICE_PDF_BUILDING_PATH: "[PDF_STORAGE] Building blob path for PDF",

  // Controller Log Messages
  CONTROLLER_START_STORAGE: "Starting document storage request",
  CONTROLLER_FIELD_VALIDATION: "Missing required fields",
  CONTROLLER_STORAGE_SUCCESS: "Document stored successfully",
  CONTROLLER_STORAGE_FAILED: "Document storage failed",
  CONTROLLER_STORAGE_UNEXPECTED_ERROR: "Unexpected error",
  CONTROLLER_GET_REQUEST: "Get documents request",
  CONTROLLER_GET_ERROR: "Error getting documents",
  CONTROLLER_PDF_STORAGE_START: "[PDF_STORAGE] Starting PDF storage request",
  CONTROLLER_PDF_STORAGE_SUCCESS: "[PDF_STORAGE] PDF storage completed",
  CONTROLLER_PDF_STORAGE_FAILED: "[PDF_STORAGE] PDF storage failed",
};

export const DOCUMENT_VALIDATION_RULES = {
  MONTH_MIN: 1,
  MONTH_MAX: 12,
  YEAR_MIN: 2000,
  YEAR_MAX: 3000,
};

export const DOCUMENT_SERVICE_LIST = ["financial", "operational", "payroll"];

export default {
  DOCUMENT_MESSAGES,
  DOCUMENT_LOG_MESSAGES,
  DOCUMENT_SERVICE_LIST,
  DOCUMENT_VALIDATION_RULES,
  DOCUMENT_SUMMARY_STATUS,
};
