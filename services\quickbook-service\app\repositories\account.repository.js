import { GLAccountMaster } from "../models/index.js";
import { create } from "../utils/database.utils.js";

export const accountsRepository = {
  /**
   * Store account info into GL account master model
   * @param {Object} accountData - Account data to store
   * @returns {Promise<Object>} Created account
   */
  async storeAccountInfo(accountData) {
    return await create(GLAccountMaster, accountData);
  },
};

export default accountsRepository;
