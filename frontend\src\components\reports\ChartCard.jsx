"use client";

import clsx from "clsx";
import { Card } from "antd";

export function ChartCard({ title, children, className, variant = "default" }) {
  const chartHeight = variant === "pie" ? "h-80 lg:h-96" : "h-64 lg:h-72";

  return (
    <Card
      className={clsx("card-surface h-full border-0 text-center", className)}
      styles={{ body: { padding: 24 } }}
    >
      <div className="mb-4 text-xl font-semibold text-slate-900">{title}</div>
      <div className="h-64 lg:h-72 flex items-center justify-center">
        {children}
      </div>
    </Card>
  );
}
