import { withDb } from "../utils/db.util.js";

/**
 * Create a new audit log entry
 */
export const createAuditLog = async (data) => {
  return await withDb(async (client) => {
    const query = `
      INSERT INTO audit_logs
        (service_name, organization_id, module, sync_start_time, sync_end_time, execution_status, records_processed, error_details, created_at)
      VALUES
        ($1, $2, $3, $4, $5, $6, $7, $8, NOW())
      RETURNING *;
    `;
    const values = [
      data.serviceName,
      data.organizationId,
      data.module,
      data.syncStartTime,
      data.syncEndTime,
      data.executionStatus,
      data.recordsProcessed,
      data.errorDetails,
    ];

    const result = await client.query(query, values);
    console.log("✅ Audit log created:", result.rows[0]);
    return result.rows[0];
  });
};
