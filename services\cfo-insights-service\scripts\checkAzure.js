import { chatWithContext } from "../app/utils/azureOpenAI.js";

async function run() {
  try {
    const answer = await chatWithContext({
      contextText: "The correct reply is: pong.",
      userQuestion: "ping",
      history: [],
      maxTokens: 100
    });
    process.stdout.write(
      `Chat OK. Sample answer: ${JSON.stringify(answer)}\n`
    );
  } catch (e) {
    process.stderr.write(`Chat FAILED: ${e?.message || e}\n`);
    process.exit(1);
  }
}

run();
