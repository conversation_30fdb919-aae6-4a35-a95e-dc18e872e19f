import { createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";
import { CHAT_API_CONFIG, CHAT_ERRORS } from "../../utils/constants/chat";
import { startChatSessionRequest } from "@shared/utils/chatSession.util.js";

// Create optimized axios instance for CFO Insights Service (singleton pattern)
let chatApiClient = null;

const getChatApiClient = () => {
  if (!chatApiClient) {
    chatApiClient = axios.create({
      baseURL: CHAT_API_CONFIG.BASE_URL,
      headers: {
        "Content-Type": "application/json",
      },
      timeout: CHAT_API_CONFIG.TIMEOUT,
    });
  }
  return chatApiClient;
};

/**
 * Extract year and month from selectedMonth string or use defaults
 */
const parseYearAndMonth = (selectedMonth) => {
  const currentYear = new Date().getFullYear();
  const monthNameMap = {
    January: 1, February: 2, March: 3, April: 4, May: 5, June: 6,
    July: 7, August: 8, September: 9, October: 10, November: 11, December: 12
  };
  
  if (monthNameMap[selectedMonth]) {
    return { year: currentYear, month: monthNameMap[selectedMonth] };
  }
  
  const numericMonth = parseInt(selectedMonth, 10);
  if (!isNaN(numericMonth) && numericMonth >= 1 && numericMonth <= 12) {
    return { year: currentYear, month: numericMonth };
  }

  return { year: currentYear, month: new Date().getMonth() + 1 };
};

/**
 * Start a chat session for a specific document
 * POST /api/chat/start
 */
export const startChatSession = createAsyncThunk(
  "chat/startChatSession",
  async (payload, { rejectWithValue }) => {
    try {
      const { filename, orgId, orgName, organizationId, organizationName, year, month, selectedMonth, service, isFinancialSelected, isOperationsSelected, isPayrollSelected } = payload || {};

      if (!filename?.trim()) {
        throw new Error(CHAT_ERRORS.VALIDATION_ERRORS.FILENAME_REQUIRED);
      }

      // Resolve orgId and orgName (support both camelCase and full names)
      const resolvedOrgId = orgId || organizationId;
      const resolvedOrgName = orgName || organizationName;


      let resolvedYear = year;
      let resolvedMonth = month;
      
      if (selectedMonth && (!resolvedMonth || !resolvedYear)) {
        const parsed = parseYearAndMonth(selectedMonth);
        resolvedMonth = resolvedMonth || parsed.month;
        resolvedYear = resolvedYear || parsed.year;
      }
      
      resolvedYear = resolvedYear || new Date().getFullYear();
      resolvedMonth = resolvedMonth || new Date().getMonth() + 1;

      let resolvedService = service;
      
      if (resolvedService) {
        const serviceLower = resolvedService.toLowerCase();
        if (serviceLower === "financial" || serviceLower === "finance") {
          resolvedService = "Finance";
        } else if (serviceLower === "operational" || serviceLower === "operations") {
          resolvedService = "Operations";
        } else if (serviceLower === "payroll") {
          resolvedService = "Payroll";
        }
      } else {
        if (isFinancialSelected) {
          resolvedService = "Finance";
        } else if (isOperationsSelected) {
          resolvedService = "Operations";
        } else if (isPayrollSelected) {
          resolvedService = "Payroll";
        } else {
          // Default to Finance if nothing is selected
          resolvedService = "Finance";
        }
      }

      const apiClient = getChatApiClient();
      const { data, message } = await startChatSessionRequest({
        baseUrl: CHAT_API_CONFIG.BASE_URL,
        filename: filename.trim(),
        orgId: resolvedOrgId,
        orgName: resolvedOrgName,
        year: resolvedYear,
        month: resolvedMonth,
        service: resolvedService,
        client: apiClient,
        useBlobPath: true,
      });

      const sessionId = data?.sessionId;
      if (!sessionId) {
        throw new Error(message || CHAT_ERRORS.VALIDATION_ERRORS.SESSION_ID_INVALID);
      }

      return {
        sessionId,
        filename: data?.filename || filename,
        organizationId: data?.organizationId || resolvedOrgId,
        organizationName: data?.organizationName || resolvedOrgName,
        year: resolvedYear,
        month: resolvedMonth,
      };
    } catch (error) {
      if (error.code === "ECONNABORTED") {
        return rejectWithValue(CHAT_ERRORS.NETWORK_ERRORS.TIMEOUT);
      }
      if (error.response?.status === 404) {
        return rejectWithValue(CHAT_ERRORS.NETWORK_ERRORS.SERVICE_UNAVAILABLE);
      }
      if (error.response?.status >= 500) {
        return rejectWithValue(CHAT_ERRORS.NETWORK_ERRORS.SERVER_ERROR);
      }
      return rejectWithValue(
        error.response?.data?.message || error.message || CHAT_ERRORS.START_SESSION_FAILED
      );
    }
  }
);

/**
 * Send a message in an existing chat session
 * POST /api/chat/message
 * Automatically retries by creating a new session if the current one is expired
 */
export const sendChatMessage = createAsyncThunk(
  "chat/sendChatMessage",
  async (payload, { rejectWithValue, dispatch, getState }) => {
    try {
      const {
        sessionId,
        message,
        organization,
        organizationId,
        organizationName,
        filename, // Optional: filename to use for auto-retry if session expires
      } = payload;

      // Enhanced validation
      if (
        !sessionId ||
        typeof sessionId !== "string" ||
        sessionId.trim().length === 0
      ) {
        throw new Error(CHAT_ERRORS.VALIDATION_ERRORS.SESSION_ID_REQUIRED);
      }
      if (
        !message ||
        typeof message !== "string" ||
        message.trim().length === 0
      ) {
        throw new Error(CHAT_ERRORS.VALIDATION_ERRORS.MESSAGE_EMPTY);
      }
      if (message.length > 2000) {
        throw new Error(CHAT_ERRORS.VALIDATION_ERRORS.MESSAGE_TOO_LONG);
      }

      const apiClient = getChatApiClient();
      let response;

      try {
        response = await apiClient.post(CHAT_API_CONFIG.ENDPOINTS.MESSAGE, {
          sessionId: sessionId.trim(),
          message: message.trim(),
        });
      } catch (error) {
        // Check if it's specifically the "Session not found or expired" error with 404 status
        // Only retry for this exact error, not for any other 404 or error
        const errorMessage =
          error.response?.data?.message || error.response?.data?.error || "";
        const errorMessageLower = errorMessage.toLowerCase();
        const isSessionExpired =
          error.response?.status === 404 &&
          (errorMessageLower === "session not found or expired" ||
            errorMessageLower.includes("session not found or expired") ||
            (errorMessageLower.includes("session") &&
              errorMessageLower.includes("not found") &&
              errorMessageLower.includes("expired")));

        // Auto-retry: Create a new session ONLY if it's the specific "Session not found or expired" error with 404
        if (isSessionExpired && filename) {
          try {
            // Start a new session with the filename
            const startResult = await dispatch(
              startChatSession({
                filename,
                organizationId,
                organizationName,
                service: payload.service,
                isFinancialSelected: payload.isFinancialSelected,
                isOperationsSelected: payload.isOperationsSelected,
                isPayrollSelected: payload.isPayrollSelected,
                selectedMonth: payload.selectedMonth,
              })
            ).unwrap();

            if (startResult && startResult.sessionId) {
              // Retry message request with new session ID
              response = await apiClient.post(
                CHAT_API_CONFIG.ENDPOINTS.MESSAGE,
                {
                  sessionId: startResult.sessionId,
                  message: message.trim(),
                }
              );
              // Continue with normal response handling below
            } else {
              throw new Error(CHAT_ERRORS.START_SESSION_FAILED);
            }
          } catch (retryError) {
            // If retry also fails, throw the original error
            throw error;
          }
        } else {
          // Re-throw the original error if we can't retry
          throw error;
        }
      }

      // Handle JSON response format (for regular chat)
      const responseData = response.data?.data || response.data;
      const successFlag = response.data?.status === "success" || response.data?.success === true;

      if (successFlag && responseData) {
        // Check for JSON answer format (responseType, narrative, comparisonData)
        if (responseData.responseType) {
          return {
            jsonAnswer: responseData,
            answer: responseData.narrative || JSON.stringify(responseData),
            filename: responseData.filename || filename,
            organizationId: responseData.organizationId || organizationId,
            organizationName: responseData.organizationName || organization || organizationName,
          };
        }

        // Check for plainAnswer (fallback)
        if (responseData.plainAnswer) {
          return {
            answer: responseData.plainAnswer,
            filename: responseData.filename || filename,
            organizationId: responseData.organizationId || organizationId,
            organizationName: responseData.organizationName || organization || organizationName,
          };
        }
      }

      throw new Error(
        responseData?.message || responseData?.error || CHAT_ERRORS.SEND_MESSAGE_FAILED
      );
    } catch (error) {
      // Enhanced error handling
      if (error.code === "ECONNABORTED") {
        return rejectWithValue(CHAT_ERRORS.NETWORK_ERRORS.TIMEOUT);
      }
      if (error.response?.status === 404) {
        return rejectWithValue(CHAT_ERRORS.NETWORK_ERRORS.SESSION_NOT_FOUND);
      }
      if (error.response?.status === 429) {
        return rejectWithValue(CHAT_ERRORS.NETWORK_ERRORS.RATE_LIMITED);
      }
      if (error.response?.status >= 500) {
        return rejectWithValue(CHAT_ERRORS.NETWORK_ERRORS.SERVER_ERROR);
      }

      return rejectWithValue(
        error.response?.data?.message ||
          error.message ||
          CHAT_ERRORS.SEND_MESSAGE_FAILED
      );
    }
  }
);

/**
 * Send a summary request in an existing chat session (summary mode)
 * POST /api/chat/summary
 * New format: Only sessionId required (and optional message)
 */
export const sendSummaryMessage = createAsyncThunk(
  "chat/sendSummaryMessage",
  async (payload, { rejectWithValue, dispatch }) => {
    try {
      const { sessionId, message, filename, orgId, orgName, organizationId, organizationName, selectedMonth } = payload;

      if (!sessionId?.trim()) {
        throw new Error(CHAT_ERRORS.VALIDATION_ERRORS.SESSION_ID_REQUIRED);
      }
      if (message && message.length > 2000) {
        throw new Error(CHAT_ERRORS.VALIDATION_ERRORS.MESSAGE_TOO_LONG);
      }

      const apiClient = getChatApiClient();
      const body = { sessionId: sessionId.trim() };
      if (message?.trim()) {
        body.message = message.trim();
      }

      let response;
      try {
        response = await apiClient.post(CHAT_API_CONFIG.ENDPOINTS.SUMMARY, body);
      } catch (error) {
        const errorMessage = error.response?.data?.message || error.response?.data?.error || "";
        const isSessionExpired = error.response?.status === 404 && 
          errorMessage.toLowerCase().includes("session not found");

        // Auto-retry: Create new session if expired
        if (isSessionExpired && filename) {
          const resolvedOrgId = orgId || organizationId;
          const resolvedOrgName = orgName || organizationName;
          const startResult = await dispatch(
            startChatSession({ filename, orgId: resolvedOrgId, orgName: resolvedOrgName, selectedMonth })
          ).unwrap();

          if (startResult?.sessionId) {
            response = await apiClient.post(CHAT_API_CONFIG.ENDPOINTS.SUMMARY, {
              sessionId: startResult.sessionId,
              ...(message?.trim() && { message: message.trim() }),
            });
          } else {
            throw error;
          }
        } else {
          throw error;
        }
      }

      const responseData = response.data?.data || response.data;
      if (!responseData?.summaryData) {
        throw new Error(CHAT_ERRORS.VALIDATION_ERRORS.NO_AI_RESPONSE);
      }

      // Return summaryData (JSON object) instead of plainAnswer
      return {
        answer: responseData.summaryData, // JSON object with title and sections
        summaryData: responseData.summaryData,
        filename: responseData.filename,
        organizationId: responseData.orgId || organizationId,
        organizationName: responseData.orgName || organizationName,
        year: responseData.year,
        month: responseData.month,
      };
    } catch (error) {
      if (error.code === "ECONNABORTED") {
        return rejectWithValue(CHAT_ERRORS.NETWORK_ERRORS.TIMEOUT);
      }
      if (error.response?.status === 404) {
        return rejectWithValue(CHAT_ERRORS.NETWORK_ERRORS.SESSION_NOT_FOUND);
      }
      if (error.response?.status === 429) {
        return rejectWithValue(CHAT_ERRORS.NETWORK_ERRORS.RATE_LIMITED);
      }
      if (error.response?.status >= 500) {
        return rejectWithValue(CHAT_ERRORS.NETWORK_ERRORS.SERVER_ERROR);
      }
      return rejectWithValue(
        error.response?.data?.message || error.message || CHAT_ERRORS.GET_SUMMARY_FAILED
      );
    }
  }
);
