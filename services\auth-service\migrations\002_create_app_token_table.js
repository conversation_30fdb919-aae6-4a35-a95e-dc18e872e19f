"use strict";

const SCHEMA_NAME = "Authentication";
const TABLE_NAME = "app_token";
const TABLE_REFERENCE = { tableName: TABLE_NAME, schema: SCHEMA_NAME };
const TOKEN_TYPE_ENUM = "enum_app_token_token_type";
const TOKEN_TYPE_ENUM_WITH_SCHEMA = `enum_${SCHEMA_NAME}_${TABLE_NAME}_token_type`;

const ensureSchemaExists = async (queryInterface) => {
  await queryInterface.sequelize.query(
    `CREATE SCHEMA IF NOT EXISTS "${SCHEMA_NAME}"`
  );
};

const dropTokenTypeEnum = async (queryInterface) => {
  await queryInterface.sequelize.query(
    `DO $$
    BEGIN
      IF EXISTS (SELECT 1 FROM pg_type WHERE typname = '${TOKEN_TYPE_ENUM}') THEN
        DROP TYPE "${TOKEN_TYPE_ENUM}";
      ELSIF EXISTS (SELECT 1 FROM pg_type WHERE typname = '${TOKEN_TYPE_ENUM_WITH_SCHEMA}') THEN
        DROP TYPE "${TOKEN_TYPE_ENUM_WITH_SCHEMA}";
      END IF;
    END$$;`
  );
};

export const up = async (queryInterface, Sequelize) => {
  const { Op } = Sequelize;

  await ensureSchemaExists(queryInterface);

  await queryInterface.createTable(
    TABLE_REFERENCE,
    {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true,
      },
      organization_id: {
        type: Sequelize.UUID,
        allowNull: false,
      },
      user_id: {
        type: Sequelize.UUID,
        allowNull: true,
      },
      token_type: {
        type: Sequelize.ENUM(
          "access",
          "refresh",
          "reset",
          "invite",
          "email_verification",
          "mfa_temp"
        ),
        allowNull: false,
      },
      token_hash: {
        type: Sequelize.TEXT,
      },
      access_token: {
        type: Sequelize.TEXT,
      },
      refresh_token: {
        type: Sequelize.TEXT,
      },
      metadata: {
        type: Sequelize.JSONB,
      },
      expires_at: {
        type: Sequelize.DATE,
      },
      last_used_at: {
        type: Sequelize.DATE,
      },
      revoked: {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
      },
      revoked_at: {
        type: Sequelize.DATE,
      },
      revoked_by: {
        type: Sequelize.UUID,
      },
      used: {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
      },
      used_at: {
        type: Sequelize.DATE,
      },
      user_agent: {
        type: Sequelize.TEXT,
      },
      ip_address: {
        type: Sequelize.INET,
      },
      device_fingerprint: {
        type: Sequelize.TEXT,
      },
      created_at: {
        type: Sequelize.DATE,
        defaultValue: Sequelize.NOW,
      },
      created_by: {
        type: Sequelize.UUID,
      },
      updated_at: {
        type: Sequelize.DATE,
      },
      updated_by: {
        type: Sequelize.UUID,
      },
    },
    {
      schema: SCHEMA_NAME,
      timestamps: true,
      createdAt: "created_at",
      updatedAt: "updated_at",
    }
  );

  const indexDefinitions = [
    {
      name: "idx_token_user",
      fields: ["user_id"],
    },
    {
      name: "idx_token_organization",
      fields: ["organization_id"],
    },
    {
      name: "idx_token_type",
      fields: ["token_type"],
    },
    {
      name: "idx_token_hash",
      fields: ["token_hash"],
    },
    {
      name: "idx_token_expires",
      fields: ["expires_at"],
    },
    {
      name: "unique_access_token_per_user",
      unique: true,
      fields: ["user_id", "access_token"],
      where: {
        revoked: false,
        access_token: { [Op.ne]: null },
      },
    },
  ];

  await Promise.all(
    indexDefinitions.map((definition) =>
      queryInterface.addIndex(TABLE_REFERENCE, definition)
    )
  );
};

export const down = async (queryInterface) => {
  await queryInterface.dropTable(TABLE_REFERENCE);
  await dropTokenTypeEnum(queryInterface);
};
