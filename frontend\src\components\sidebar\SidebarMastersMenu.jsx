"use client";

import { useState, useEffect } from "react";
import { usePathname } from "next/navigation";
import { BarChart3, ChevronDown } from "lucide-react";
import { NAVIGATION_CONSTANTS } from "@/utils/constants/navigation";
import "./sidebar.css";

const SidebarMastersMenu = ({ isAdmin, onNavigate }) => {
  const pathname = usePathname();
  const [mastersOpen, setMastersOpen] = useState(false);
  const mastersSubmenu = NAVIGATION_CONSTANTS.MAIN_MENU.MASTERS.SUBMENU;

  // Keep Masters menu open if current pathname matches any of its children
  useEffect(() => {
    const isOnMastersPage = mastersSubmenu.some(
      (item) => pathname === item.path || pathname.startsWith(`${item.path}/`)
    );
    setMastersOpen(isOnMastersPage);
  }, [pathname, mastersSubmenu]);

  if (!isAdmin) return null;

  return (
    <div>
      <button
        onClick={() => setMastersOpen((v) => !v)}
        className={`sidebar-masters-button ${
          mastersOpen ? "sidebar-masters-button--open" : ""
        }`}
      >
        <BarChart3 size={20} className="text-white" />
        <span className="font-semibold">
          {NAVIGATION_CONSTANTS.MAIN_MENU.MASTERS.LABEL}
        </span>
        <span className="ml-auto">
          <ChevronDown 
            size={18} 
            className="text-white sidebar-masters-chevron"
          />
        </span>
      </button>

      <div
        className={`sidebar-masters-submenu ${
          mastersOpen ? "sidebar-masters-submenu--open" : ""
        }`}
      >
        {mastersSubmenu.map((item, index) => {
            const isActive =
              pathname === item.path ||
              pathname.startsWith(`${item.path}/`);
            return (
              <button
                key={item.label}
                onClick={() => onNavigate(item.path)}
                className={`sidebar-masters-submenu-item ${
                  isActive ? "sidebar-masters-submenu-item--active" : ""
                }`}
              style={{ animationDelay: `${index * 0.05}s` }}
              >
                {item.label}
              </button>
            );
          })}
        </div>
    </div>
  );
};

export default SidebarMastersMenu;

