"use client";

import React, {
  createContext,
  useContext,
  useState,
  useCallback,
  useRef,
  useMemo,
} from "react";

/**
 * Sidebar Context for persisting sidebar state across route changes
 * This prevents the sidebar from losing its open/closed states when navigating
 */
const SidebarContext = createContext(null);

export const useSidebarContext = () => {
  const context = useContext(SidebarContext);
  if (!context) {
    throw new Error("useSidebarContext must be used within SidebarProvider");
  }
  return context;
};

// Create a separate context for stable sidebar data (excludes months)
const StableSidebarContext = createContext(null);

export const useStableSidebarContext = () => {
  const context = useContext(StableSidebarContext);
  if (!context) {
    throw new Error(
      "useStableSidebarContext must be used within SidebarProvider"
    );
  }
  return context;
};

/**
 * Sidebar Provider - Wraps the app and provides persistent sidebar state
 * This ensures sidebar state persists across route changes and component re-renders
 */
export const SidebarProvider = ({ children, initialReportFolders = [] }) => {
  // Internal state that persists across re-renders
  const [isFinancialSelected, setIsFinancialSelected] = useState(true);
  const [isOperationsSelected, setIsOperationsSelected] = useState(false);
  const [isPayrollSelected, setIsPayrollSelected] = useState(false);
  const [months, setMonths] = useState(null);

  // Use refs to track values without causing re-renders
  const monthsRef = useRef(months);
  const listenersRef = useRef(new Set());
  const initializedRef = useRef(false);

  // Update months ref when state changes
  monthsRef.current = months;

  // Stable setMonths function that updates state and notifies listeners
  const setMonthsStable = useCallback((newMonths) => {
    setMonths(newMonths);
    monthsRef.current = newMonths;
    // Notify all registered listeners (like Dashboard component)
    listenersRef.current.forEach((listener) => listener(newMonths));
  }, []);

  // Function to register/unregister listeners for month changes
  const subscribeToMonthChanges = useCallback((listener) => {
    listenersRef.current.add(listener);
    return () => listenersRef.current.delete(listener);
  }, []);

  // Function to get current months without subscribing to changes
  const getCurrentMonths = useCallback(() => monthsRef.current, []);

  // Report folders from API (updated externally)
  const [reportFolders, setReportFolders] = useState(
    Array.isArray(initialReportFolders) ? initialReportFolders : []
  );
  const [loadingFolders, setLoadingFolders] = useState(false);
  const [foldersError, setFoldersError] = useState(null);

  // Stable setter functions that don't change between renders
  const setFinancialSelected = useCallback((value) => {
    setIsFinancialSelected(value);
  }, []);

  const setOperationsSelected = useCallback((value) => {
    setIsOperationsSelected(value);
  }, []);

  const setPayrollSelected = useCallback((value) => {
    setIsPayrollSelected(value);
  }, []);

  const updateReportFolders = useCallback((folders) => {
    if (Array.isArray(folders)) {
      setReportFolders(folders);
    } else {
      setReportFolders([]);
    }
  }, []);

  const updateLoadingFolders = useCallback((loading) => {
    setLoadingFolders(loading);
  }, []);

  const updateFoldersError = useCallback((error) => {
    setFoldersError(error);
  }, []);

  // Memoize main context value (includes months - for Dashboard)
  const contextValue = useMemo(
    () => ({
      // Section states
      isFinancialSelected,
      isOperationsSelected,
      isPayrollSelected,
      setFinancialSelected,
      setOperationsSelected,
      setPayrollSelected,

      // Month selection
      months,
      setMonths: setMonthsStable,

      // Report folders data
      reportFolders,
      loadingFolders,
      foldersError,
      updateReportFolders,
      updateLoadingFolders,
      updateFoldersError,

      // Month utilities
      getCurrentMonths,
      subscribeToMonthChanges,

      // Initialization flag
      initialized: initializedRef.current,
      setInitialized: (value) => {
        initializedRef.current = value;
      },
    }),
    [
      isFinancialSelected,
      isOperationsSelected,
      isPayrollSelected,
      months,
      reportFolders,
      loadingFolders,
      foldersError,
      setFinancialSelected,
      setOperationsSelected,
      setPayrollSelected,
      setMonthsStable,
      updateReportFolders,
      updateLoadingFolders,
      updateFoldersError,
      getCurrentMonths,
      subscribeToMonthChanges,
    ]
  );

  // Create truly stable context value using refs - this object never changes
  const stableContextValueRef = useRef();

  if (!stableContextValueRef.current) {
    stableContextValueRef.current = {
      // Getters for current values
      getIsFinancialSelected: () => isFinancialSelected,
      getIsOperationsSelected: () => isOperationsSelected,
      getIsPayrollSelected: () => isPayrollSelected,
      getReportFolders: () => reportFolders,
      getLoadingFolders: () => loadingFolders,
      getFoldersError: () => foldersError,

      // Setters (stable references)
      setFinancialSelected,
      setOperationsSelected,
      setPayrollSelected,
      updateReportFolders,
      updateLoadingFolders,
      updateFoldersError,

      // Month utilities (without reactive months)
      setMonths: setMonthsStable,
      getCurrentMonths,

      // Initialization flag
      getInitialized: () => initializedRef.current,
      setInitialized: (value) => {
        initializedRef.current = value;
      },
    };
  }

  // Update getters to return current values (but don't change the object reference)
  const updateStableGetters = () => {
    stableContextValueRef.current.getIsFinancialSelected = () =>
      isFinancialSelected;
    stableContextValueRef.current.getIsOperationsSelected = () =>
      isOperationsSelected;
    stableContextValueRef.current.getIsPayrollSelected = () =>
      isPayrollSelected;
    stableContextValueRef.current.getReportFolders = () => reportFolders;
    stableContextValueRef.current.getLoadingFolders = () => loadingFolders;
    stableContextValueRef.current.getFoldersError = () => foldersError;
  };

  updateStableGetters();

  return (
    <SidebarContext.Provider value={contextValue}>
      <StableSidebarContext.Provider value={stableContextValueRef.current}>
        {children}
      </StableSidebarContext.Provider>
    </SidebarContext.Provider>
  );
};
