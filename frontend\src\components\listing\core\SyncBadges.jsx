"use client";

import { useMemo, useCallback } from "react";
import { Check<PERSON>ircle2, <PERSON>ader2, AlertCircle } from "lucide-react";
import { cn } from "@/utils/methods/cn";
import { formatTimeAgo } from "@/utils/constants/dateFormats";
import { BOOKCLOSURE_CONSTANTS } from "@/utils/const";
import { LISTING_CONSTANTS } from "@/utils/constants/listing";
import { getMinTimestamp } from "@/utils/methods/helpers";
import "@/styles/listing.css";

const SYNC_THRESHOLD_MINUTES = 5;

const CATEGORY_CLASS_MAP = {
  operational: {
    badge: "listing-sync-badge-operational",
    icon: "listing-icon-operational",
    text: "listing-text-operational",
  },
  payroll: {
    badge: "listing-sync-badge-payroll",
    icon: "listing-icon-payroll",
    text: "listing-text-payroll",
  },
  financial: {
    badge: "listing-sync-badge-financial",
    icon: "listing-icon-financial",
    text: "listing-text-financial",
  },
};

const SYNC_DATA_CONFIG = [
  {
    name: "Operational",
    timestampKey: "sikka_last_synced_at",
    key: "operational",
  },
  { name: "Payroll", timestampKey: "adp_last_synced_at", key: "payroll" },
  { name: "Financial", timestampKey: "qb_last_synced_at", key: "financial" },
];

const getSyncStatus = (timestamp) => {
  if (!timestamp) {
    return {
      status: "not_synced",
      icon: AlertCircle,
      label: "Never Synced",
      badgeClass: "listing-sync-badge-not-synced",
      textClass: "listing-text-not-synced",
      iconClass: "listing-icon-not-synced",
    };
  }
  const diffInMinutes = Math.floor(
    (Date.now() - new Date(timestamp).getTime()) / 60000
  );
  if (diffInMinutes < SYNC_THRESHOLD_MINUTES) {
    return {
      status: "syncing",
      icon: Loader2,
      label: "Syncing",
      badgeClass: "listing-sync-badge-syncing",
      textClass: "listing-text-syncing",
      iconClass: "listing-icon-syncing",
      isSpinning: true,
    };
  }
  return {
    status: "synced",
    icon: CheckCircle2,
    label: "Synced",
    badgeClass: "listing-sync-badge-synced",
    textClass: "listing-text-synced",
    iconClass: "listing-icon-synced",
  };
};

const formatFullTimestamp = (timestamp) => {
  if (!timestamp) return "Never synced";
  try {
    return new Date(timestamp).toLocaleString("en-US", {
      month: "short",
      day: "numeric",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  } catch {
    return "Invalid date";
  }
};

export default function SyncBadges({ client }) {
  const clientServices = useMemo(
    () =>
      Array.isArray(client?.services)
        ? new Set(client.services.map((s) => s.toLowerCase()))
        : new Set(),
    [client?.services]
  );

  // Calculate the oldest sync time to highlight critical statuses
  const getOldestSyncTime = useCallback(() => {
    const timestamps = SYNC_DATA_CONFIG.map(
      (item) => client?.[item.timestampKey]
    ).filter(Boolean);
    return getMinTimestamp(timestamps);
  }, [client]);

  const oldestSyncTime = useMemo(() => getOldestSyncTime(), [getOldestSyncTime]);

  const renderBadge = useCallback(
    (item) => {
      const timestamp = client?.[item.timestampKey];
      const isSelected = clientServices.has(item.key);
      const status = getSyncStatus(timestamp);
      const Icon = status.icon;
      const relativeTime = timestamp ? formatTimeAgo(timestamp) : null;
      // Optimize text format for better readability and clarity
      const getDisplayText = () => {
        // If service is not selected/configured, show "Inactive"
        if (!isSelected) {
          return "Inactive";
        }
        // If service is selected but has timestamp, show relative time
        if (timestamp && relativeTime) {
          return relativeTime;
        }
        // If service is selected but never synced, show "Never Synced"
        return status.label;
      };
      const displayText = getDisplayText();
      const categoryClasses = CATEGORY_CLASS_MAP[item.key] || {};
      const categoryBadgeClass = categoryClasses.badge || status.badgeClass;
      const categoryIconClass = categoryClasses.icon || status.iconClass;
      const categoryTextClass = categoryClasses.text || status.textClass;
      
      // Determine if this is a critical status (oldest sync, not synced, or very old)
      // Consider syncs older than threshold days as critical
      const criticalThreshold = Date.now() - (LISTING_CONSTANTS.SYNC.CRITICAL_THRESHOLD_DAYS * 24 * 60 * 60 * 1000);
      const timestampMs = timestamp ? new Date(timestamp).getTime() : null;
      const isNotSynced = !timestamp;
      const isVeryOld = timestampMs && timestampMs < criticalThreshold;
      const isOldest = timestampMs && oldestSyncTime && timestampMs === oldestSyncTime;
      const isCritical = isNotSynced || isVeryOld || isOldest;
      
      const tooltipText = timestamp
        ? `Last synced: ${formatFullTimestamp(
            timestamp
          )}\nClick to sync now (coming soon)`
        : "Not synced yet\nClick to sync now (coming soon)";

      return (
        <div
          key={item.key}
          className={cn("listing-sync-badge", categoryBadgeClass)}
          title={tooltipText}
          onClick={() => {}}
        >
          <Icon
            className={cn(
              "listing-sync-icon",
              timestamp ? categoryIconClass : status.iconClass,
              status.isSpinning && "listing-sync-icon-spinning",
              isCritical && "listing-sync-icon-critical"
            )}
          />
          <div className="listing-badge-content">
            <span className={cn(
              "listing-category-name", 
              categoryTextClass,
              isCritical && "listing-category-name-critical"
            )}>
              {item.name}
            </span>
            <span className={cn("listing-separator", categoryTextClass)}>
              :
            </span>
            {!isSelected ? (
              <span
                className={cn(
                  "listing-status-text",
                  categoryTextClass
                )}
              >
                {BOOKCLOSURE_CONSTANTS.UPLOAD_SECTION.NOT_SELECTED_LABEL}
              </span>
            ) : timestamp ? (
              <span
                className={cn(
                  "listing-status-text", 
                  categoryTextClass,
                  isCritical && "listing-status-text-critical"
                )}
              >
                {displayText}
              </span>
            ) : (
              <span
                className={cn(
                  "listing-status-text", 
                  categoryTextClass,
                  isNotSynced && "listing-status-text-critical"
                )}
              >
                {displayText}
              </span>
            )}
          </div>
        </div>
      );
    },
    [client, clientServices, oldestSyncTime]
  );

  return (
    <div className="listing-sync-badges-container">
      {SYNC_DATA_CONFIG.map(renderBadge)}
    </div>
  );
}
