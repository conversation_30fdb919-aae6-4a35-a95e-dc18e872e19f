"use client";

import { useRef, useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "antd";
import { FaFileDownload } from "react-icons/fa";

import { useSelector } from "react-redux";
import ExistingDashboard from "@/components/dashboard/Dashboard";
import KPIBox from "../shared/KPIBox";
import { ChartCard } from "../../ChartCard";
import { TotalVsDoctorChart } from "../../TotalVsDoctorChart";
import { SalaryByCategoryChart } from "../../SalaryByCategoryChart";
import { DeductionsDonutChart } from "../../DeductionsDonutChart";
import { TaxBreakdownChart } from "../../TaxBreakdownChart";
import { HeadcountChart } from "../../HeadcountChart";
import { AverageSalaryChart } from "../../AverageSalaryChart";
import {
  mapPayrollKpiData,
  mapPayrollTaxBreakdownData,
  mapPayrollDeductionsBreakdownData,
  mapPayrollSalaryByDepartmentData,
  mapPayrollDoctorSalaryComparisonData,
  mapPayrollAverageSalaryByRoleData,
  mapPayrollHeadcountByRoleData,
} from "@/utils/methods/payrollReports";
import { formatCardValue } from "@/utils/methods/formatters";
import { downloadReportFromBlob } from "@/utils/methods/pdfExport";

import payrollSummary from "./dummyData/payrollSummary.json";
import doctorSalary from "./dummyData/doctorSalary.json";
import averageSalary from "./dummyData/averageSalary.json";
import deductions from "./dummyData/deductions.json";
import headcount from "./dummyData/headcount.json";
import salaryByCategory from "./dummyData/salaryByCategory.json";
import taxes from "./dummyData/taxes.json";

const MONTHS = [
  "January",
  "February",
  "March",
  "April",
  "May",
  "June",
  "July",
  "August",
  "September",
  "October",
  "November",
  "December",
];

const MONTHS_SHORT = [
  "Jan",
  "Feb",
  "Mar",
  "Apr",
  "May",
  "Jun",
  "Jul",
  "Aug",
  "Sep",
  "Oct",
  "Nov",
  "Dec",
];

function ChartFallback({ message }) {
  return (
    <div className="flex h-full items-center justify-center text-sm text-slate-400">
      {message}
    </div>
  );
}

export default function PayrollDashboard({
  organizationId,
  month,
  year,
  organizationName,
}) {
  const {
    payrollKpisData,
    payrollTaxBreakdownData,
    payrollDeductionsBreakdownData,
    payrollSalaryByDepartmentData,
  } = useSelector((state) => state.reports);

  const dashboardRef = useRef(null);
  const parsedMonthIndex =
    month && Number(month) >= 1 && Number(month) <= 12
      ? Number(month) - 1
      : null;
  const monthFullName =
    parsedMonthIndex !== null ? MONTHS[parsedMonthIndex] : null;
  const monthShortName =
    parsedMonthIndex !== null ? MONTHS_SHORT[parsedMonthIndex] : null;
  const yearLabel = year || new Date().getFullYear();
  const headerPeriod =
    monthFullName && yearLabel ? `${monthFullName} - ${yearLabel}` : null;
  const chartPeriod =
    monthShortName && yearLabel
      ? `${monthShortName}-${String(yearLabel).slice(-2)}`
      : doctorSalary?.comparison?.[0]?.period;

  const doctorSalaryWithPeriod = doctorSalary
    ? {
        ...doctorSalary,
        comparison: doctorSalary.comparison.map((item) => ({
          ...item,
          period: chartPeriod || item.period,
        })),
      }
    : null;

  const cards = mapPayrollKpiData(payrollKpisData);

  const doctorSalaryComparisonData = mapPayrollDoctorSalaryComparisonData(
    payrollKpisData,
    chartPeriod
  );

  const taxesData = mapPayrollTaxBreakdownData(payrollTaxBreakdownData);
  console.log("taxesData", taxesData);

  const deductionsData = mapPayrollDeductionsBreakdownData(payrollDeductionsBreakdownData);

  const salaryByCategoryData = mapPayrollSalaryByDepartmentData(payrollSalaryByDepartmentData);

  const averageSalaryData = mapPayrollAverageSalaryByRoleData(payrollSalaryByDepartmentData);

  const headcountData = mapPayrollHeadcountByRoleData(payrollSalaryByDepartmentData);

  const [isExporting, setIsExporting] = useState(false);

  const handleExportPdf = () => {
    downloadReportFromBlob({
      organizationId,
      organizationName,
      month,
      year,
      serviceType: "payroll",
      onLoadingChange: setIsExporting,
    });
  };

  return (
    <div className="flex flex-col gap-5 w-full py-3" ref={dashboardRef}>
      <section className="card-surface w-full flex flex-col gap-1 p-5 lg:p-6 text-center relative">
        <p className="text-xs font-semibold uppercase tracking-wide text-slate-500">
          {organizationName ||
            payrollSummary?.organization ||
            "Corbin Dental Center"}
        </p>
        <h2 className="text-4xl font-semibold text-slate-900">
          Payroll Summary For{" "}
          {headerPeriod || payrollSummary?.period || "August-2025"}
        </h2>
        <div className="absolute top-6 right-6">
          <Tooltip title="Export to PDF" placement="bottom">
            <Button
              icon={<FaFileDownload style={{ fontSize: "18px" }} />}
              onClick={handleExportPdf}
              size="large"
              loading={isExporting}
              shape="circle"
              style={{
                backgroundColor: "#dc2626",
                borderColor: "#dc2626",
                color: "white",
              }}
            />
          </Tooltip>
        </div>
      </section>
      {cards ? (
        <div className="w-full grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-5 gap-3 sm:gap-4 items-stretch">
          {cards.map((card) => (
            <div key={card.id} className="w-full h-full">
              <KPIBox
                title={card.title}
                amount={formatCardValue(card.value, card.format)}
                pmValue={formatCardValue(card.previousMonth, card.format)}
                ytdValue={formatCardValue(card.yearToDate, card.format)}
                change={card.previousMonthChange?.formatted}
                isPositive={card.previousMonthChange?.isPositive}
              />
            </div>
          ))}
        </div>
      ) : null}

      <div className="w-full grid grid-cols-1 md:grid-cols-2 gap-3 sm:gap-4">
        <ChartCard title="Total VS Doctor Salary Comparison">
          {doctorSalaryComparisonData ? (
            <TotalVsDoctorChart data={doctorSalaryComparisonData} />
          ) : (
            <ChartFallback message="Doctor salary data unavailable" />
          )}
        </ChartCard>
        <ChartCard title="Salary By Staff Category">
          {salaryByCategoryData ? (
            <SalaryByCategoryChart data={salaryByCategoryData} />
          ) : (
            <ChartFallback message="Salary category data unavailable" />
          )}
        </ChartCard>
        <ChartCard title="Deductions Breakdown">
          {deductionsData ? (
            <DeductionsDonutChart data={deductionsData} />
          ) : (
            <ChartFallback message="Deduction data unavailable" />
          )}
        </ChartCard>
        <ChartCard title="Tax Breakdown">
          {taxesData ? (
            <TaxBreakdownChart data={taxesData} />
          ) : (
            <ChartFallback message="Tax data unavailable" />
          )}
        </ChartCard>
        <ChartCard title="Headcount By Role">
          {headcountData ? (
            <HeadcountChart data={headcountData} />
          ) : (
            <ChartFallback message="Headcount data unavailable" />
          )}
        </ChartCard>
        <ChartCard title="Average Salary By Role">
          {averageSalaryData ? (
            <AverageSalaryChart data={averageSalaryData} />
          ) : (
            <ChartFallback message="Average salary data unavailable" />
          )}
        </ChartCard>
      </div>
    </div>
  );
}
