"use client";

import { useMemo } from "react";
import { Select, Button } from "antd";
import "./reports.css";

const DEFAULT_MONTH_OPTIONS = [
  { label: "January", value: "01" },
  { label: "February", value: "02" },
  { label: "March", value: "03" },
  { label: "April", value: "04" },
  { label: "May", value: "05" },
  { label: "June", value: "06" },
  { label: "July", value: "07" },
  { label: "August", value: "08" },
  { label: "September", value: "09" },
  { label: "October", value: "10" },
  { label: "November", value: "11" },
  { label: "December", value: "12" },
];

const buildYearOptions = () => {
  const currentYear = new Date().getFullYear();
  return Array.from({ length: 6 }, (_, idx) => {
    const year = currentYear - idx;
    return { label: `${year}`, value: `${year}` };
  });
};

export default function ReportsFilters({
  organizations = [],
  selectedOrganization,
  onOrganizationChange,
  selectedService = null,
  onServicesChange,
  selectedMonth,
  onMonthChange,
  selectedYear,
  onYearChange,
  onGenerateReport,
  onViewInsights,
  showViewInsights = false,
  loading = false,
  organizationsLoading = false,
  availableServices = [],
  servicesLoading = false,
  monthOptions: monthOptionsProp,
  yearOptions: yearOptionsProp,
  disableOrganizationSelect = false,
}) {
  const organizationOptions = useMemo(
    () =>
      organizations.map((org) => ({
        label: org.organizationName || org.name || "Unknown Organization",
        value: org.id,
      })),
    [organizations]
  );

  const servicesOptions = useMemo(
    () =>
      Array.isArray(availableServices)
        ? availableServices.map((service) => ({
            label: service.charAt(0).toUpperCase() + service.slice(1),
            value: service,
          }))
        : [],
    [availableServices]
  );

  const monthOptions = monthOptionsProp || DEFAULT_MONTH_OPTIONS;
  const yearOptions = yearOptionsProp || buildYearOptions();

  const handleGenerate = () => {
    const payload = {
      organization: selectedOrganization,
      service: selectedService,
      month: selectedMonth,
      year: selectedYear,
    };
    onGenerateReport(payload);
  };

  const filterOption = (input, option) =>
    (option?.label ?? "").toLowerCase().includes(input.toLowerCase());

  return (
    <div className="flex flex-wrap lg:flex-nowrap items-end gap-4">
      <div className="w-full lg:w-auto lg:max-w-[220px]">
        <label className="block text-xs font-semibold uppercase tracking-wide text-slate-700 mb-1">
          Select Organization
        </label>
        <Select
          aria-label="Select Organization"
          className="w-full"
          size="large"
          allowClear
          showSearch
          placeholder={
            organizationsLoading
              ? "Loading organizations..."
              : organizationOptions.length === 0
                ? "No organizations available"
                : "Choose organization..."
          }
          options={organizationOptions}
          loading={organizationsLoading}
          disabled={
            loading ||
            organizationsLoading ||
            organizationOptions.length === 0 ||
            disableOrganizationSelect
          }
          value={selectedOrganization || undefined}
          onChange={onOrganizationChange}
          filterOption={filterOption}
        />
      </div>

      <div className="w-full lg:w-auto lg:max-w-[160px]">
        <label className="block text-xs font-semibold uppercase tracking-wide text-slate-700 mb-1">
          Select Services
        </label>
        <Select
          aria-label="Select Services"
          className="w-full"
          size="large"
          allowClear
          showSearch
          placeholder={
            servicesLoading
              ? "Loading services..."
              : servicesOptions.length === 0
                ? "No services available"
                : "Choose service..."
          }
          options={servicesOptions}
          loading={servicesLoading}
          disabled={
            !selectedOrganization ||
            servicesLoading ||
            servicesOptions.length === 0
          }
          value={selectedService || undefined}
          onChange={onServicesChange}
          filterOption={filterOption}
        />
      </div>

      <div className="w-full lg:w-auto lg:max-w-[100px]">
        <label className="block text-xs font-semibold uppercase tracking-wide text-slate-700 mb-1">
          Year
        </label>
        <Select
          aria-label="Select Year"
          className="w-full"
          size="large"
          placeholder="Select year..."
          options={yearOptions}
          disabled={!selectedOrganization}
          value={selectedYear || undefined}
          onChange={onYearChange}
          filterOption={filterOption}
        />
      </div>

      <div className="w-full lg:w-auto lg:max-w-[120px]">
        <label className="block text-xs font-semibold uppercase tracking-wide text-slate-700 mb-1">
          Month
        </label>
        <Select
          aria-label="Select Month"
          className="w-full"
          size="large"
          placeholder="Select month..."
          options={monthOptions}
          disabled={!selectedOrganization}
          value={selectedMonth || undefined}
          onChange={onMonthChange}
          filterOption={filterOption}
        />
      </div>

      <div className="w-full lg:w-auto lg:ml-auto flex gap-3">
        {showViewInsights && (
          <Button
            type="default"
            size="large"
            className="w-full lg:w-auto px-6 h-11 font-semibold rounded-lg"
            style={{ backgroundColor: '#4F46E5', color: 'white' }}
            onClick={onViewInsights}
          >
            View Insights
          </Button>
        )}
        <Button
          type="primary"
          size="large"
          className="w-full lg:w-auto px-6 h-11 font-semibold rounded-lg"
          disabled={!selectedOrganization || !selectedMonth || !selectedYear || loading}
          onClick={handleGenerate}
        >
          Generate Report
        </Button>
      </div>
    </div>
  );
}
