export const API_CONFIG = {
  // Default pagination settings
  DEFAULT_PAGE_SIZE: 10,
  MAX_PAGE_SIZE: 100,

  // Request timeouts
  DEFAULT_TIMEOUT: 10000,
  UPLOAD_TIMEOUT: 60000,

  // Retry settings
  DEFAULT_RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000,

  // Cache settings
  DEFAULT_CACHE_TIME: 5 * 60 * 1000, // 5 minutes
  DEFAULT_STALE_TIME: 2 * 60 * 1000, // 2 minutes
};

export const SERVICE_PORTS = {
  AUTH: process.env.NEXT_PUBLIC_AUTH_SERVICE_URL,
  TENANT: process.env.NEXT_PUBLIC_TENANT_SERVICE_URL,
  USER: process.env.NEXT_PUBLIC_USER_SERVICE_URL,
  SIKKA: process.env.NEXT_PUBLIC_SIKKA_SERVICE_URL,
  QUICKBOOK: process.env.NEXT_PUBLIC_QUICKBOOK_SERVICE_URL,
  ADP: process.env.NEXT_PUBLIC_ADP_SERVICE_URL,
  REPORT: process.env.NEXT_PUBLIC_REPORT_SERVICE_URL,
  FILE: process.env.NEXT_PUBLIC_FILE_SERVICE_URL,
};
