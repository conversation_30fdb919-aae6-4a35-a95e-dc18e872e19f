import { NextResponse } from "next/server";

/**
 * <PERSON>B<PERSON>s OAuth2 Callback Handler
 *
 * This API route handles the OAuth2 callback from QuickBooks after user authorization.
 * It extracts the authorization code and realmId, then calls your backend API.
 *
 * Expected URL format:
 * /api/quickbooks/callback?code=xxx&state=xxx&realmId=xxx
 */

export async function GET(request) {
  try {
    // Extract query parameters from the request URL
    const { searchParams } = new URL(request.url);

    const code = searchParams.get("code");
    const state = searchParams.get("state");
    const realmId = searchParams.get("realmId");

    // QuickBooks OAuth Callback received

    // Extract organization data from state parameter
    let orgData = {
      schemaName: "aritek",
      email: "<EMAIL>",
      organization_id: 1,
    };

    if (state) {
      try {
        const parsedState = JSON.parse(decodeURIComponent(state));
        if (parsedState.orgData) {
          orgData = {
            schemaName:
              parsedState.orgData.schemaName ||
              parsedState.orgData.name?.toLowerCase().replace(/\s+/g, "_") ||
              "aritek",
            email: parsedState.orgData.email || "<EMAIL>",
            organization_id: parsedState.orgData.id || 1,
          };
        }
      } catch (error) {
        // Error parsing state parameter, using default organization data
      }
    }

    // Validate required parameters
    if (!code) {
      return NextResponse.json(
        {
          success: false,
          error: "MISSING_CODE",
          message: "Authorization code is required",
          timestamp: new Date().toISOString(),
        },
        { status: 400 }
      );
    }

    if (!realmId) {
      return NextResponse.json(
        {
          success: false,
          error: "MISSING_REALM_ID",
          message: "QuickBooks Company ID (realmId) is required",
          timestamp: new Date().toISOString(),
        },
        { status: 400 }
      );
    }

    // Call your backend API

    // REFACTORED: No fallback operator - assumes env var is validated externally
    const backendUrl = process.env.BACKEND_API_URL;

    // Guard clause: validate environment variable
    if (!backendUrl) {
      throw new Error("BACKEND_API_URL environment variable is not configured");
    }
    const apiEndpoint = `${backendUrl}/api/v1/quickbooks/add`;

    // Check if we should use mock response (for testing)
    const useMockResponse = process.env.USE_MOCK_QUICKBOOKS_RESPONSE === "true";

    if (useMockResponse) {
      const redirectUrl = new URL("/masters/org", request.url);
      redirectUrl.searchParams.set("quickbooks", "success");
      redirectUrl.searchParams.set(
        "message",
        "QuickBooks connected successfully (mock)"
      );
      return NextResponse.redirect(redirectUrl);
    }

    // Use GET method with query parameters as per the original implementation
    const queryParams = new URLSearchParams({
      code: code,
      realmId: realmId,
      company_name: orgData.schemaName,
      email: orgData.email,
      organization_id: orgData.organization_id,
    });

    const fullUrl = `${apiEndpoint}?${queryParams.toString()}`;

    try {
      const backendResponse = await fetch(fullUrl, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
        signal: AbortSignal.timeout(10000), // 10 second timeout
      });

      const backendData = await backendResponse.json();

      if (!backendResponse.ok) {
        // Redirect back with error message
        const redirectUrl = new URL("/masters/org", request.url);
        redirectUrl.searchParams.set("quickbooks", "error");
        redirectUrl.searchParams.set("message", "Failed to connect QuickBooks");

        return NextResponse.redirect(redirectUrl);
      }

      // Redirect back to masters/org page after successful processing
      const redirectUrl = new URL("/masters/org", request.url);
      redirectUrl.searchParams.set("quickbooks", "success");
      redirectUrl.searchParams.set(
        "message",
        "QuickBooks connected successfully"
      );

      return NextResponse.redirect(redirectUrl);
    } catch (error) {
      return NextResponse.json(
        {
          success: false,
          error: "CALLBACK_ERROR",
          message: "Failed to process QuickBooks callback",
          details:
            process.env.NODE_ENV === "development" ? error.message : undefined,
          timestamp: new Date().toISOString(),
        },
        { status: 500 }
      );
    }
  } catch (error) {
    return NextResponse.json(
      {
        success: false,
        error: "CALLBACK_ERROR",
        message: "Failed to process QuickBooks callback",
        details:
          process.env.NODE_ENV === "development" ? error.message : undefined,
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

/**
 * Handle POST requests (optional - for form submissions)
 */
export async function POST(request) {
  try {
    const body = await request.json();

    return NextResponse.json(
      {
        success: false,
        error: "METHOD_NOT_ALLOWED",
        message:
          "POST method not supported for QuickBooks callback. Use GET instead.",
        receivedData: body,
        timestamp: new Date().toISOString(),
      },
      { status: 405 }
    );
  } catch (error) {
    return NextResponse.json(
      {
        success: false,
        error: "INVALID_REQUEST",
        message: "Invalid request format",
        timestamp: new Date().toISOString(),
      },
      { status: 400 }
    );
  }
}
