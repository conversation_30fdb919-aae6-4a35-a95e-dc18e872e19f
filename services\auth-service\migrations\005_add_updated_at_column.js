"use strict";

const SCHEMA_NAME = "Authentication";
const TABLE_NAME = "app_user";
const COLUMN_NAME = "updated_at";
const TABLE_REFERENCE = { tableName: TABLE_NAME, schema: SCHEMA_NAME };

export const up = async (queryInterface, Sequelize) => {
  const tableDescription = await queryInterface.describeTable(
    TABLE_REFERENCE
  );

  if (!tableDescription[COLUMN_NAME]) {
    await queryInterface.addColumn(
      TABLE_REFERENCE,
      COLUMN_NAME,
      {
        type: Sequelize.DATE,
        defaultValue: Sequelize.NOW,
        allowNull: true,
      }
    );
  }
};

export const down = async (queryInterface) => {
  const tableDescription = await queryInterface.describeTable(
    TABLE_REFERENCE
  );

  if (tableDescription[COLUMN_NAME]) {
    await queryInterface.removeColumn(
      TABLE_REFERENCE,
      COLUMN_NAME
    );
  }
};
