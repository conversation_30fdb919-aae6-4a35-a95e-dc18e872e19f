import FinanceService from "../services/finance.service.js";
import BalanceSheetService from "../services/balance_sheet.service.js";
import KpiService from "../services/kpi.service.js";
import IncomeExpenseStatementService from "../services/income_expense_statement.service.js";
import CashFlowService from "../services/cash_flow.service.js";
import RevenueExpenseService from "../services/revenue_expense.service.js";
import { LOGGER_NAMES } from "../utils/constants/log.constants.js";
import { createLogger } from "../utils/logger.utils.js";
import {
  validateRequiredParams,
  handleControllerError,
  sendSuccessResponse,
} from "../utils/controller.utils.js";

const logger = createLogger(LOGGER_NAMES.REPORT_CONTROLLER);

const getFinanceReport = async (req, res) => {
  try {
    const reportData = await FinanceService.getFinanceReport(req.query);
    res.status(200).json({
      success: true,
      message: "Finance report fetched successfully",
      data: reportData,
    });
  } catch (error) {
    logger.error("Error fetching finance report:", error);
    res.status(500).json({
      success: false,
      message: "Error fetching finance report",
      error: error.message,
    });
  }
};

/**
 * Calculate combined finance data by aggregating all finance endpoints
 * (balance sheet, KPIs, income/expense statement, cash flow, revenue/expense)
 * using the same query parameters.
 *
 * Expected query params (union of all): organization_id, org_id, month, year
 * Note: balance sheet uses org_id, others use organization_id.
 */
const calculateFinance = async (req, res) => {
  try {
    const { organization_id, org_id, month, year } = req.query;

    logger.info(
      `Calculating finance data for org: ${
        organization_id || org_id
      }, month: ${month}, year: ${year}`
    );

    // Validate required parameters for the common case (organization_id/month/year)
    const validationError = validateRequiredParams(req.query, [
      "month",
      "year",
    ]);
    if (validationError) {
      return res.status(400).json(validationError);
    }

    const balanceSheetPayload = {
      org_id,
      organization_id,
      month,
      year,
    };

    const commonOrgPayload = {
      organization_id,
      month,
      year,
    };

    // Call all underlying services in parallel
    const [
      balanceSheetData,
      kpiData,
      incomeExpenseData,
      cashFlowData,
      revenueExpenseData,
    ] = await Promise.all([
      BalanceSheetService.getBalanceSheetData(balanceSheetPayload),
      KpiService.getKpiData(commonOrgPayload),
      IncomeExpenseStatementService.getIncomeExpenseStatement(commonOrgPayload),
      CashFlowService.getCashFlowByGroup(commonOrgPayload),
      RevenueExpenseService.getRevenueExpenseData(commonOrgPayload),
    ]);

    const combinedData = {
      balanceSheet: balanceSheetData,
      kpis: kpiData,
      incomeExpenseStatement: incomeExpenseData,
      cashFlow: cashFlowData,
      revenueExpense: revenueExpenseData,
    };

    sendSuccessResponse(
      res,
      "Finance data calculated successfully",
      combinedData
    );
  } catch (error) {
    logger.error("Error calculating finance data:", error);
    handleControllerError(error, res, "Error calculating finance data");
  }
};

export default {
  getFinanceReport,
  calculateFinance,
};
