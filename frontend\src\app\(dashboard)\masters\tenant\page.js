"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import PageWrapper from "@/components/common/PageWrapper";
import {
  renderStatusBadge,
  renderActionButtons,
} from "@/components/common/TableRenderers";
import DeleteModal from "@/components/common/DeleteModal";
import { TENANT_CONSTANTS } from "@/utils/constants/tenant";
import { tenantsData } from "@/utils/data/tenants";
import { useToast } from "@/components/ui/toast";

export default function TenantPage() {
  const router = useRouter();
  const { addToast } = useToast();
  const [deleteModal, setDeleteModal] = useState({ isOpen: false, item: null });
  const [isDeleting, setIsDeleting] = useState(false);
  // Stats configuration
  const stats = [
    {
      title: TENANT_CONSTANTS.STATS.TOTAL_TENANTS.TITLE,
      value: tenantsData.length.toString(),
      icon: TENANT_CONSTANTS.STATS.TOTAL_TENANTS.ICON,
      bgColor: TENANT_CONSTANTS.STATS.TOTAL_TENANTS.BG_COLOR,
      iconColor: TENANT_CONSTANTS.STATS.TOTAL_TENANTS.ICON_COLOR,
    },
    {
      title: TENANT_CONSTANTS.STATS.ACTIVE.TITLE,
      value: tenantsData
        .filter((t) => t.status === TENANT_CONSTANTS.STATUS.ACTIVE)
        .length.toString(),
      icon: TENANT_CONSTANTS.STATS.ACTIVE.ICON,
      bgColor: TENANT_CONSTANTS.STATS.ACTIVE.BG_COLOR,
      iconColor: TENANT_CONSTANTS.STATS.ACTIVE.ICON_COLOR,
    },
    {
      title: TENANT_CONSTANTS.STATS.INACTIVE.TITLE,
      value: tenantsData
        .filter((t) => t.status === TENANT_CONSTANTS.STATUS.INACTIVE)
        .length.toString(),
      icon: TENANT_CONSTANTS.STATS.INACTIVE.ICON,
      bgColor: TENANT_CONSTANTS.STATS.INACTIVE.BG_COLOR,
      iconColor: TENANT_CONSTANTS.STATS.INACTIVE.ICON_COLOR,
    },
    {
      title: TENANT_CONSTANTS.STATS.TOTAL_USERS.TITLE,
      value: tenantsData.reduce((sum, t) => sum + t.users, 0).toString(),
      icon: TENANT_CONSTANTS.STATS.TOTAL_USERS.ICON,
      bgColor: TENANT_CONSTANTS.STATS.TOTAL_USERS.BG_COLOR,
      iconColor: TENANT_CONSTANTS.STATS.TOTAL_USERS.ICON_COLOR,
    },
  ];

  // Action handlers
  const handleView = (item) => {
    router.push(`/masters/tenant/view/${item.id}`);
  };

  const handleEdit = (item) => {
    router.push(`/masters/tenant/edit/${item.id}`);
  };

  const handleDelete = (item) => {
    setDeleteModal({ isOpen: true, item });
  };

  const confirmDelete = async () => {
    setIsDeleting(true);
    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000));
      setDeleteModal({ isOpen: false, item: null });
    } catch (error) {
      addToast(TENANT_CONSTANTS.TOAST_MESSAGES.DELETE_FAILURE, "error");
    } finally {
      setIsDeleting(false);
    }
  };

  // Table columns configuration
  const columns = [
    {
      key: "name",
      title: TENANT_CONSTANTS.TABLE_HEADERS.TENANT_NAME,
      width: "20%",
    },
    {
      key: "domain",
      title: TENANT_CONSTANTS.TABLE_HEADERS.DOMAIN,
      width: "15%",
    },
    {
      key: "status",
      title: TENANT_CONSTANTS.TABLE_HEADERS.STATUS,
      width: "10%",
      render: (value) => renderStatusBadge(value),
    },
    {
      key: "users",
      title: TENANT_CONSTANTS.TABLE_HEADERS.USERS,
      width: "10%",
    },
    {
      key: "plan",
      title: TENANT_CONSTANTS.TABLE_HEADERS.PLAN,
      width: "10%",
    },
    {
      key: "createdAt",
      title: TENANT_CONSTANTS.TABLE_HEADERS.CREATED,
      width: "15%",
    },
    {
      key: "actions",
      title: TENANT_CONSTANTS.TABLE_HEADERS.ACTIONS,
      width: "15%",
      sortable: false,
      render: (_, item) =>
        renderActionButtons(item, handleView, handleEdit, handleDelete),
    },
  ];

  // Filters configuration
  const filters = [
    {
      key: "status",
      options: [
        TENANT_CONSTANTS.FILTERS.STATUS.ALL,
        TENANT_CONSTANTS.FILTERS.STATUS.ACTIVE,
        TENANT_CONSTANTS.FILTERS.STATUS.INACTIVE,
      ],
      defaultValue: TENANT_CONSTANTS.FILTERS.STATUS.ALL,
    },
  ];

  return (
    <>
      <PageWrapper
        title={TENANT_CONSTANTS.PAGE_TITLE}
        subtitle={TENANT_CONSTANTS.PAGE_SUBTITLE}
        searchPlaceholder={TENANT_CONSTANTS.SEARCH_PLACEHOLDER}
        addButtonText={TENANT_CONSTANTS.ADD_BUTTON_TEXT}
        addButtonPath={TENANT_CONSTANTS.ADD_BUTTON_PATH}
        stats={stats}
        data={tenantsData}
        columns={columns}
        filters={filters}
        itemsPerPage={6}
      />

      <DeleteModal
        isOpen={deleteModal.isOpen}
        onClose={() => setDeleteModal({ isOpen: false, item: null })}
        onConfirm={confirmDelete}
        title={TENANT_CONSTANTS.DELETE_MODAL.TITLE}
        description={TENANT_CONSTANTS.DELETE_MODAL.DESCRIPTION}
        itemName={deleteModal.item?.name}
        isLoading={isDeleting}
      />
    </>
  );
}
