// Since shared.config.js uses ES6 modules, we need to create a CommonJS wrapper
require("dotenv").config();

// Manually recreate the database config for Sequelize CLI
const parseEnvValue = (value, type = "string", defaultValue = null) => {
  if (!value) return defaultValue;

  switch (type) {
    case "number":
      const num = parseInt(value, 10);
      return isNaN(num) ? defaultValue : num;
    case "boolean":
      return value.toLowerCase() === "true";
    default:
      return value;
  }
};

const getDatabaseConfig = () => {
  // Allow explicit control over SSL via DB_SSL env var.
  // If DB_SSL is not provided, default to true for production, false for development.
  const defaultSslEnabled = process.env.NODE_ENV === "production";
  const dbSslEnabled = process.env.DB_SSL
    ? parseEnvValue(process.env.DB_SSL, "boolean", defaultSslEnabled)
    : defaultSslEnabled;
  
  return {
    database: process.env.DB_NAME,
    username: process.env.DB_USER,
    password: process.env.DB_PASS,
    host: process.env.DB_HOST,
    port: parseEnvValue(process.env.DB_PORT, "number", 5432),
    dialect: "postgres",
    logging: process.env.NODE_ENV === "development" ? console.log : false,
    pool: {
      max: process.env.NODE_ENV === "production" ? 10 : 5,
      min: 0,
      acquire: 30000,
      idle: 10000,
    },
    dialectOptions: dbSslEnabled
      ? {
          ssl: {
            require: true,
            rejectUnauthorized: false,
          },
        }
      : {},
  };
};

// Get database configuration from shared utilities
const dbConfig = getDatabaseConfig();

module.exports = {
  development: {
    username: dbConfig.username,
    password: dbConfig.password,
    database: dbConfig.database,
    host: dbConfig.host,
    port: dbConfig.port,
    dialect: dbConfig.dialect,
    dialectOptions: dbConfig.dialectOptions,
    logging: dbConfig.logging,
    pool: dbConfig.pool,
    define: {
      timestamps: true,
      underscored: true,
      freezeTableName: true,
    },
  },
  test: {
    username: dbConfig.username,
    password: dbConfig.password,
    database: dbConfig.database,
    host: dbConfig.host,
    port: dbConfig.port,
    dialect: dbConfig.dialect,
    dialectOptions: dbConfig.dialectOptions,
    logging: false,
    pool: dbConfig.pool,
    define: {
      timestamps: true,
      underscored: true,
      freezeTableName: true,
    },
  },
  production: {
    username: dbConfig.username,
    password: dbConfig.password,
    database: dbConfig.database,
    host: dbConfig.host,
    port: dbConfig.port,
    dialect: dbConfig.dialect,
    dialectOptions: dbConfig.dialectOptions,
    logging: false,
    pool: dbConfig.pool,
    define: {
      timestamps: true,
      underscored: true,
      freezeTableName: true,
    },
  },
};
