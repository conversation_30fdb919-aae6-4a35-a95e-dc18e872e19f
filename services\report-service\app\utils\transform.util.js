import { formatCurrency } from "./constants/pdf.constants.js";

/**
 * Transform balance sheet data from service format to HTML generator format
 * Service format: [{ section: "...", rows: [{ account: "...", amount: ... }, { account: "...", children: [...] }] }]
 * HTML format: [{ section: "...", subsections: [{ name: "...", total: ..., accounts: [...], nestedSubsections: [...] }] }]
 * 
 * @param {Array} balanceSheetData - Balance sheet data from service
 * @returns {Array} Transformed balance sheet data for HTML generation
 */
export const transformBalanceSheetData = (balanceSheetData) => {
  if (!balanceSheetData || !Array.isArray(balanceSheetData)) {
    return [];
  }

  /**
   * Recursively process a row and format it with totals
   * @param {Object} row - Row object with potential children
   * @returns {Object} Formatted row with accounts, nested subsections, and summary
   */
  const processRow = (row) => {
    if (row.type === "summary") {
      return {
        name: row.account || "Total",
        total: row.amount || 0,
        total_formatted: formatCurrency(row.amount || 0),
        accounts: [],
        nestedSubsections: [],
        summary: {
          name: row.account || "Total",
          amount: row.amount || 0,
          amount_formatted: formatCurrency(row.amount || 0),
        },
      };
    }

    const result = {
      name: row.account || "Item",
      accounts: [],
      nestedSubsections: [],
      summary: null,
    };

    if (row.children?.length > 0) {
      row.children.forEach((child) => {
        if (child.type === "summary") {
          result.summary = {
            name: child.account || "Total",
            amount: child.amount || 0,
            amount_formatted: formatCurrency(child.amount || 0),
          };
        } else if (child.children?.length > 0) {
          result.nestedSubsections.push(processRow(child));
        } else if (child.account !== undefined && child.amount !== undefined) {
          result.accounts.push({
            name: child.account || "N/A",
            amount: child.amount || 0,
            amount_formatted: formatCurrency(child.amount || 0),
          });
        }
      });
    } else if (row.account !== undefined && row.amount !== undefined) {
      result.accounts.push({
        name: row.account || "N/A",
        amount: row.amount || 0,
        amount_formatted: formatCurrency(row.amount || 0),
      });
    }

    const total = result.summary
      ? result.summary.amount
      : result.accounts.reduce((sum, acc) => sum + (acc.amount || 0), 0) +
        result.nestedSubsections.reduce((sum, nested) => sum + (nested.total || 0), 0);

    return {
      ...result,
      total,
      total_formatted: formatCurrency(total),
    };
  };

  return balanceSheetData.map((section) => {
    const subsections = [];
    const rows = section.rows || [];
    let sectionSummary = null;

    rows.forEach((row) => {
      if (row.type === "summary" && !row.children?.length) {
        sectionSummary = {
          name: row.account || "Total",
          amount: row.amount || 0,
          amount_formatted: formatCurrency(row.amount || 0),
        };
        return;
      }
      subsections.push(processRow(row));
    });

    return {
      section: section.section || "Section",
      subsections: subsections,
      summary: sectionSummary,
    };
  });
};

/**
 * Transform income expense statement data from service format to HTML generator format
 * Service format: [{ section: "...", rows: [{ account: "...", amount: ... }, { type: "summary", account: "...", amount: ... }] }]
 * HTML format: [{ category: "...", amount: ..., items: [{ account_name: "...", amount: ... }] }]
 * 
 * @param {Array} incomeExpenseData - Income expense statement data from service
 * @returns {Array} Transformed income expense data for HTML generation
 */
export const transformIncomeExpenseData = (incomeExpenseData) => {
  if (!incomeExpenseData || !Array.isArray(incomeExpenseData)) {
    return [];
  }

  return incomeExpenseData.map((section) => {
    const rows = section.rows || [];
    const items = [];
    let categoryTotal = 0;

    const summaryRow = rows.find((row) => row.type === "summary");
    if (summaryRow) {
      categoryTotal = summaryRow.amount || 0;
    }

    rows.forEach((row) => {
      if (row.type === "summary") {
        return;
      }

      if (row.children && Array.isArray(row.children) && row.children.length > 0) {
        row.children.forEach((child) => {
          if (child.type === "summary") {
            return;
          }

          if (child.account !== undefined && child.amount !== undefined) {
            items.push({
              account_name: child.account || "N/A",
              name: child.account || "N/A",
              amount: child.amount || 0,
            });
          } else if (child.children && Array.isArray(child.children)) {
            child.children.forEach((nestedChild) => {
              if (
                nestedChild.type !== "summary" &&
                nestedChild.account !== undefined &&
                nestedChild.amount !== undefined
              ) {
                items.push({
                  account_name: nestedChild.account || "N/A",
                  name: nestedChild.account || "N/A",
                  amount: nestedChild.amount || 0,
                });
              }
            });
          }
        });
      } else if (row.account !== undefined && row.amount !== undefined) {
        items.push({
          account_name: row.account || "N/A",
          name: row.account || "N/A",
          amount: row.amount || 0,
        });
      }
    });

    if (!summaryRow && items.length > 0) {
      categoryTotal = items.reduce((sum, item) => sum + (item.amount || 0), 0);
    }

    return {
      category: section.section || "Category",
      amount: categoryTotal,
      items: items,
    };
  });
};

export default {
  transformBalanceSheetData,
  transformIncomeExpenseData,
};

