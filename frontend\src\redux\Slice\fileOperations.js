import { createSlice } from "@reduxjs/toolkit";
import {
  downloadFile,
  uploadFile,
  convertFileToBase64,
  validateFile as validateFileUtil,
  removeUploadedFile as removeFileUtil,
  clearUploadedFiles as clearFilesUtil,
} from "@/redux/Thunks/fileOperations";

const initialState = {
  // File operation states
  uploading: false,
  downloading: false,
  uploadProgress: 0,
  uploadedFiles: [],

  // Error handling
  error: null,

  // File validation rules
  validationRules: {
    allowedTypes: [],
    maxSize: Infinity,
    minSize: 0,
  },
};

const fileOperationsSlice = createSlice({
  name: "fileOperations",
  initialState,
  reducers: {
    // Clear error
    clearError: (state) => {
      state.error = null;
    },

    // Set validation rules
    setValidationRules: (state, action) => {
      state.validationRules = { ...state.validationRules, ...action.payload };
    },

    // Update upload progress
    setUploadProgress: (state, action) => {
      state.uploadProgress = action.payload;
    },

    // Add uploaded file
    addUploadedFile: (state, action) => {
      state.uploadedFiles.push(action.payload);
    },

    // Remove uploaded file
    removeUploadedFile: (state, action) => {
      state.uploadedFiles = state.uploadedFiles.filter(
        (_, index) => index !== action.payload
      );
    },

    // Clear all uploaded files
    clearUploadedFiles: (state) => {
      state.uploadedFiles = [];
    },

    // Reset all states
    resetFileOperations: (state) => {
      state.uploading = false;
      state.downloading = false;
      state.uploadProgress = 0;
      state.uploadedFiles = [];
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    // Download file
    builder
      .addCase(downloadFile.pending, (state) => {
        state.downloading = true;
        state.error = null;
      })
      .addCase(downloadFile.fulfilled, (state) => {
        state.downloading = false;
      })
      .addCase(downloadFile.rejected, (state, action) => {
        state.downloading = false;
        state.error = action.payload;
      });

    // Upload file
    builder
      .addCase(uploadFile.pending, (state) => {
        state.uploading = true;
        state.uploadProgress = 0;
        state.error = null;
      })
      .addCase(uploadFile.fulfilled, (state, action) => {
        state.uploading = false;
        state.uploadProgress = 100;
        state.uploadedFiles.push(action.payload);
      })
      .addCase(uploadFile.rejected, (state, action) => {
        state.uploading = false;
        state.uploadProgress = 0;
        state.error = action.payload;
      });

    // Convert file to base64
    builder.addCase(convertFileToBase64.rejected, (state, action) => {
      state.error = action.payload;
    });

    // Validate file
    builder.addCase(validateFileUtil.rejected, (state, action) => {
      state.error = action.payload;
    });
  },
});

export const {
  clearError,
  setValidationRules,
  setUploadProgress,
  addUploadedFile,
  removeUploadedFile,
  clearUploadedFiles,
  resetFileOperations,
} = fileOperationsSlice.actions;

export default fileOperationsSlice.reducer;
