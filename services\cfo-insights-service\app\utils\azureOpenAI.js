// app/utils/azureOpenAI.js
import dotenv from "dotenv";
dotenv.config();

import OpenAI from "openai";
import { ERROR_MESSAGES } from "./constants/error.constants.js";
import {
  buildSummarySystemPrompt,
  buildFinancialDataPrompt,
  buildFinancialJSONPrompt,
  buildChatSystemPrompt,
  buildOperationsDataPrompt,
  buildOperationsJSONPrompt,
  buildOperationsSystemPrompt,
  buildPayrollDataPrompt,
  buildPayrollJSONPrompt,
  buildPayrollSystemPrompt,
} from "../helpers/prompts.js";
import { parseJsonSafely, cleanJsonString } from "./jsonCleaner.util.js";

/**
 * Configuration constants
 */
const DEFAULT_TEMPERATURE = {
  SUMMARY: 0.1,
  CHAT: 0.2,
};

const DEFAULT_TOP_P = 0.95;
const DEFAULT_FREQUENCY_PENALTY = 0.3;

/**
 * Azure OpenAI client configuration
 * Uses environment variables for endpoint, deployment name, API key, and version.
 * Lazy-loaded to ensure environment variables are available.
 */
let client = null;

function getClient() {
  if (!client) {
    const apiKey = process.env.AZURE_OPENAI_API_KEY;
    const endpoint = process.env.AZURE_OPENAI_ENDPOINT;
    const deploymentName = process.env.AZURE_OPENAI_DEPLOYMENT_NAME;
    const apiVersion = process.env.AZURE_OPENAI_API_VERSION;

    if (!apiKey) {
      throw new Error(ERROR_MESSAGES.GENERAL.AZURE_OPENAI_API_KEY_MISSING);
    }
    if (!endpoint || !deploymentName || !apiVersion) {
      throw new Error(
        ERROR_MESSAGES.GENERAL.AZURE_OPENAI_CONFIGURATION_INCOMPLETE
      );
    }

    client = new OpenAI({
      apiKey,
      baseURL: `${endpoint}openai/deployments/${deploymentName}`,
      defaultQuery: { "api-version": apiVersion },
      defaultHeaders: { "api-key": apiKey },
      // No timeout - will wait for response indefinitely
    });
  }
  return client;
}

/**
 * Core chat handler for FinChat Assistant.
 * Provides conversational, financial-analysis-style answers using only the provided context.
 * @param {string} contextText - Document/context text
 * @param {string} userQuestion - User's question
 * @param {Array} history - Chat history
 * @param {number} maxTokens - Maximum tokens
 * @param {boolean} summaryMode - If true, use summary prompt; if false, use conversational chat prompt
 */
export async function chatWithContext({
  contextText,
  userQuestion,
  history = [],
  maxTokens = 3000,
  summaryMode = false,
  organization,
  service = "",
}) {
  // Build messages array
  // If summaryMode=true: Uses summary.prompt.js (fallback for two-step approach)
  // If summaryMode=false: Uses chat.prompt.js (regular conversational chat)
  // Determine the correct system prompt builder based on service
  let systemPromptBuilder = buildSummarySystemPrompt;
  const srv = String(service || "").toLowerCase();

  if (srv === "operations") systemPromptBuilder = buildOperationsSystemPrompt;
  else if (srv === "payroll") systemPromptBuilder = buildPayrollSystemPrompt;

  const systemPrompt = summaryMode
    ? systemPromptBuilder(organization, service)
    : buildChatSystemPrompt(organization);

  // OPTIMIZATION: Limit history to last 6 messages to reduce token usage and improve speed
  const limitedHistory = history.slice(-6);

  // OPTIMIZATION: Combine organization into system prompt instead of separate message
  const enhancedSystemPrompt = organization
    ? `${systemPrompt}\n\nOrganization Context: ${organization}`
    : systemPrompt;

  const messages = [
    { role: "system", content: enhancedSystemPrompt },
    {
      role: "user",
      content: `---DOCUMENT CONTEXT START---\n${contextText}\n---DOCUMENT CONTEXT END---`,
    },
    ...limitedHistory,
    { role: "user", content: `Question: ${userQuestion}` },
  ];

  const openAIClient = getClient();
  const temperature = summaryMode
    ? DEFAULT_TEMPERATURE.SUMMARY
    : DEFAULT_TEMPERATURE.CHAT;

  try {
    const response = await openAIClient.chat.completions.create({
      model: process.env.AZURE_OPENAI_DEPLOYMENT_NAME,
      messages,
      max_tokens: maxTokens,
      temperature,
      top_p: DEFAULT_TOP_P,
      frequency_penalty: DEFAULT_FREQUENCY_PENALTY,
      stream: false,
    });

    const content = response.choices?.[0]?.message?.content?.trim();

    if (!content) {
      return ERROR_MESSAGES.GENERAL.OPENAI_EMPTY_RESPONSE;
    }

    // If summaryMode is false (regular chat), parse JSON response
    if (!summaryMode) {
      try {
        const jsonData = parseJsonSafely(content);

        // Validate required structure
        if (!jsonData.responseType || !jsonData.narrative) {
          throw new Error(
            "Invalid JSON structure: missing responseType or narrative"
          );
        }

        return jsonData;
      } catch (parseError) {
        throw new Error(
          `${ERROR_MESSAGES.GENERAL.INVALID_JSON_FORMAT}: ${parseError.message}`
        );
      }
    }

    // For summaryMode=true, return content as-is (may be JSON or text depending on prompt)
    return content;
  } catch (error) {
    throw error;
  }
}

/**
 * Two-step summary generation returning JSON format:
 * STEP 1: Extract financial data as JSON (using financialData.prompt.js)
 * STEP 2: Convert JSON to structured JSON output (using financialJSON.prompt.js)
 *
 * This is used when JSON format is required instead of HTML.
 *
 * @param {string} contextText - Document/context text
 * @param {string} organization - Organization name
 * @param {number} maxTokens - Maximum tokens per step
 * @returns {Promise<Object>} - Valid JSON object matching the required structure
 */
export async function generateFinancialSummaryJSON({
  contextText,
  organization = "",
  service = "",
  maxTokens = 2000,
}) {
  const openAIClient = getClient();

  // Determine prompt builders based on service
  const srv = String(service || "").toLowerCase();
  let dataPromptBuilder = buildFinancialDataPrompt;
  let jsonPromptBuilder = buildFinancialJSONPrompt;

  if (srv === "operations") {
    dataPromptBuilder = buildOperationsDataPrompt;
    jsonPromptBuilder = buildOperationsJSONPrompt;
  } else if (srv === "payroll") {
    dataPromptBuilder = buildPayrollDataPrompt;
    jsonPromptBuilder = buildPayrollJSONPrompt;
  }

  // STEP 1: Extract data as JSON
  const dataPrompt = dataPromptBuilder(organization, service);
  const dataMessages = [
    { role: "system", content: dataPrompt },
    { role: "user", content: contextText },
  ];

  let jsonData;
  try {
    const dataResponse = await openAIClient.chat.completions.create({
      model: process.env.AZURE_OPENAI_DEPLOYMENT_NAME,
      messages: dataMessages,
      max_tokens: maxTokens,
      temperature: 0.1, // Low temperature for accurate data extraction
      top_p: DEFAULT_TOP_P,
      frequency_penalty: DEFAULT_FREQUENCY_PENALTY,
      stream: false, // Important - ensures full JSON output
    });

    const dataContent = dataResponse.choices?.[0]?.message?.content?.trim();

    if (!dataContent) {
      throw new Error(ERROR_MESSAGES.GENERAL.NO_DATA_EXTRACTED);
    }

    // Parse JSON with automatic cleaning
    try {
      jsonData = parseJsonSafely(dataContent);
    } catch (parseError) {
      throw new Error(
        `${ERROR_MESSAGES.GENERAL.INVALID_JSON_FORMAT}: ${parseError.message}`
      );
    }
  } catch (error) {
    throw new Error(
      `${ERROR_MESSAGES.GENERAL.FAILED_EXTRACT_FINANCIAL_DATA}: ${error.message}`
    );
  }

  // STEP 2: Convert JSON to structured JSON output
  const jsonPrompt = jsonPromptBuilder(organization, service);
  const jsonMessages = [
    { role: "system", content: jsonPrompt },
    { role: "user", content: JSON.stringify(jsonData, null, 2) },
  ];

  let jsonOutput;
  try {
    const jsonResponse = await openAIClient.chat.completions.create({
      model: process.env.AZURE_OPENAI_DEPLOYMENT_NAME,
      messages: jsonMessages,
      max_tokens: maxTokens,
      temperature: 0.2, // Low temperature for consistent JSON structure
      top_p: DEFAULT_TOP_P,
      frequency_penalty: DEFAULT_FREQUENCY_PENALTY,
      stream: false, // Important - ensures full JSON output
    });

    const jsonContent = jsonResponse.choices?.[0]?.message?.content?.trim();

    if (!jsonContent) {
      throw new Error(ERROR_MESSAGES.GENERAL.NO_JSON_GENERATED);
    }

    // Parse JSON with automatic cleaning
    try {
      jsonOutput = parseJsonSafely(jsonContent);
    } catch (parseError) {
      throw new Error(
        `${ERROR_MESSAGES.GENERAL.INVALID_JSON_FORMAT}: ${parseError.message}`
      );
    }

    // Validate that all required sections are present
    if (!jsonOutput.sections || !Array.isArray(jsonOutput.sections)) {
      throw new Error(ERROR_MESSAGES.GENERAL.MISSING_SECTIONS_ARRAY);
    }

    const serviceLower = String(service || "")
      .toLowerCase()
      .trim();
    const highlightsHeading =
      serviceLower === "operations" || serviceLower === "operational"
        ? "Operations Highlights"
        : serviceLower === "payroll"
        ? "Payroll Highlights"
        : "Financial Highlights";

    const requiredHeadings = [
      highlightsHeading,
      "Key Insights",
      "Strategic Recommendations",
      "Executive Takeaway",
    ];

    const foundHeadings = jsonOutput.sections.map((s) => s.heading);
    const missingHeadings = requiredHeadings.filter(
      (h) => !foundHeadings.includes(h)
    );

    if (missingHeadings.length > 0) {
      throw new Error(
        `${
          ERROR_MESSAGES.GENERAL.MISSING_REQUIRED_SECTIONS
        }: ${missingHeadings.join(", ")}`
      );
    }
  } catch (error) {
    throw new Error(
      `${ERROR_MESSAGES.GENERAL.FAILED_GENERATE_JSON}: ${error.message}`
    );
  }

  return jsonOutput;
}
