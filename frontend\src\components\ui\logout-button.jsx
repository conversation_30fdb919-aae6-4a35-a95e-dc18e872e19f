import { logout } from "@/redux/Thunks/Authentication.js";
import { LogOut } from "lucide-react";
import { useRouter } from "next/navigation";
import { useDispatch } from "react-redux";

const LogoutButton = () => {
  const dispatch = useDispatch();
  const router = useRouter();

  const handleSignOut = () => {
    dispatch(logout());
    router.push("/login");
  };
  return (
    <button
      onClick={handleSignOut}
      className="w-full flex items-center gap-3 px-4 py-3 rounded-xl text-white hover:bg-red-500/20 hover:text-red-300 transition-all duration-200 group border border-transparent hover:border-red-400/30"
    >
      <LogOut size={18} className="text-red-300 group-hover:text-red-200 transition-colors duration-200" />
      <span className="font-medium text-sm">Sign Out</span>
    </button>
  );
};

export default LogoutButton;
