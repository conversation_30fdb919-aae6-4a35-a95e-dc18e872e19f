// app/utils/queryParams.util.js
/**
 * Decode URL-encoded string, handling both single and double encoding
 * @param {string} value - URL-encoded string
 * @returns {string} Decoded string
 */
const decodeUrlParam = (value) => {
  if (!value) return null;
  
  try {
    // First decode
    let decoded = decodeURIComponent(value);
    
    // Check if still contains encoded characters (double-encoded)
    // Try decoding again if % is present
    if (decoded.includes('%')) {
      try {
        decoded = decodeURIComponent(decoded);
      } catch (error) {
        // If second decode fails, return first decoded value
      }
    }
    
    return decoded;
  } catch (error) {
    // If decoding fails, return original value
    return value;
  }
};

/**
 * Extract and decode organization ID from query parameters
 * Supports: orgnId, orgId
 * Handles URL encoding/decoding (single and double encoding)
 */
export const getOrganizationId = (query) => {
  let orgId = null;
  
  if (query.orgnId) {
    orgId = query.orgnId;
  } else if (query.orgId) {
    orgId = query.orgId;
  }
  
  return decodeUrlParam(orgId);
};

/**
 * Extract and decode organization name from query parameters
 * Supports: oorgname, orgname
 * Handles URL encoding/decoding (including double-encoded values)
 * Example: Children%2520Health%2520Partner%27s -> Children Health Partner's
 */
export const getOrganizationName = (query) => {
  let orgName = null;

  if (query.oorgname) {
    orgName = query.oorgname;
  } else if (query.orgname) {
    orgName = query.orgname;
  } else if (query.organizationName) {
    orgName = query.organizationName;
  }

  return decodeUrlParam(orgName);
};

