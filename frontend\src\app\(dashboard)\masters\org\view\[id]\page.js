"use client";

import { useEffect } from "react";
import { useRouter, useParams } from "next/navigation";
import { useDispatch, useSelector } from "react-redux";
import AddForm from "@/components/common/AddForm";
import { organizationFields } from "@/utils/data/organizations";
import { ORGANIZATION_CONSTANTS } from "@/utils/constants/organization";
import { getOrganizationById } from "@/redux/Thunks/organization.js";
import CustomSpinner from "@/components/common/CustomSpinner";

export default function ViewOrganizationPage() {
  const router = useRouter();
  const params = useParams();
  const dispatch = useDispatch();
  const orgId = params.id; // Keep as string since it's a UUID

  // Get organization data from Redux store
  const { organization, loading, error } = useSelector((state) => {
    // Try different possible data structures
    const orgData =
      state.organizations?.currentOrganization?.data ||
      state.organizations?.currentOrganization ||
      state.organizations?.data;

    return {
      organization: orgData,
      loading: state.organizations?.loading,
      error: state.organizations?.error,
    };
  });

  // Fetch organization data when component mounts
  useEffect(() => {
    if (orgId) {
      dispatch(getOrganizationById(orgId));
    } else {
    }
  }, [dispatch, orgId]);

  // Loading state
  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <CustomSpinner tip="Loading organization details..." />
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-red-600">
            Error Loading Organization
          </h2>
          <p className="text-gray-600 mt-2">{error?.message}</p>
          <button
            onClick={() => router.push("/masters/org")}
            className="mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
          >
            Back to Organizations
          </button>
        </div>
      </div>
    );
  }

  // Organization not found
  if (!organization) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900">
            {ORGANIZATION_CONSTANTS.ERROR_MESSAGES.NOT_FOUND_TITLE}
          </h2>
          <p className="text-gray-600 mt-2">
            {ORGANIZATION_CONSTANTS.ERROR_MESSAGES.NOT_FOUND_MESSAGE}
          </p>
          <button
            onClick={() => router.push("/masters/org")}
            className="mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
          >
            Back to Organizations
          </button>
        </div>
      </div>
    );
  }

  const handleEdit = () => {
    router.push(`/masters/org/edit/${orgId}`);
  };

  return (
    <div>
      {/* Temporary debug display */}
      {/* <div className="mb-4 p-4 bg-yellow-100 border border-yellow-400 rounded">
        <h3 className="font-bold text-yellow-800">Debug Information:</h3>
        <p>
          <strong>Loading:</strong> {loading ? "true" : "false"}
        </p>
        <p>
          <strong>Error:</strong> {error ? JSON.stringify(error) : "null"}
        </p>
        <p>
          <strong>Organization:</strong>{" "}
          {organization ? JSON.stringify(organization, null, 2) : "null"}
        </p>
      </div> */}

      <AddForm
        heading={ORGANIZATION_CONSTANTS.VIEW_PAGE.HEADING}
        subTitle={ORGANIZATION_CONSTANTS.VIEW_PAGE.SUBTITLE}
        onBack={() => router.push("/masters/org")}
        backLabel={ORGANIZATION_CONSTANTS.VIEW_PAGE.BACK_LABEL}
        title={ORGANIZATION_CONSTANTS.VIEW_PAGE.TITLE}
        fields={organizationFields}
        initialValues={organization}
        mode="view"
        onEdit={handleEdit}
      />
    </div>
  );
}
