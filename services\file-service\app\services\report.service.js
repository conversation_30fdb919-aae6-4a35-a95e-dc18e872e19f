// app/services/report.service.js
import blobStorageService from "./blobStorage.service.js";
import logger from "../../config/logger.config.js";
import { STATUS_CODE_OK } from "../utils/status_code.utils.js";
import {
  createServiceResponse,
  createBadRequestResponse,
  createInternalServerErrorResponse,
  createNotFoundResponse,
} from "../utils/serviceResponse.util.js";
import {
  FILE_MESSAGES,
  FILE_LOG_MESSAGES,
  REPORT_MESSAGES,
  CATEGORY_NAMES,
} from "../utils/constants/file.constants.js";
import {
  MONTH_MAP,
  FULL_MONTH_TO_ABBR,
} from "../utils/constants/report.constants.js";
import { normalizeCategory } from "../utils/normalizers.util.js";
import { validateCategory, validateMonth } from "../utils/validators.util.js";
import { reportFoldersCache } from "../utils/cache.util.js";
import { Document } from "../models/index.js";
import { Op } from "sequelize";

// Cache for combined report data
const combinedReportsCache = new Map();
const COMBINED_REPORTS_CACHE_TTL = 300000; // 5 minutes

const parseMonthLabel = (label) => {
  try {
    const [month, year] = label.split("-");
    return { month, year: parseInt(year, 10) };
  } catch {
    return { month: label, year: new Date().getFullYear() % 100 };
  }
};

const formatMonthLabel = (monthAbbr, year) => {
  try {
    const monthFullName = MONTH_MAP[monthAbbr] || monthAbbr;
    const monthAbbreviation = FULL_MONTH_TO_ABBR[monthFullName] || monthAbbr;
    const yearValue = year || new Date().getFullYear();
    return `${monthAbbreviation}-${yearValue.toString().slice(-2)}`;
  } catch {
    return `${monthAbbr}-${new Date().getFullYear() % 100}`;
  }
};

const formatMonthDisplayLabel = (label) => {
  try {
    const { month: monthAbbr, year } = parseMonthLabel(label);
    const monthFullName = MONTH_MAP[monthAbbr] || monthAbbr;
    const normalizedYear =
      typeof year === "number"
        ? year >= 100
          ? year
          : 2000 + year
        : new Date().getFullYear();
    return `${monthFullName} ${normalizedYear}`;
  } catch {
    return label;
  }
};

const MONTH_ORDER = Object.keys(MONTH_MAP);

const sortMonthsChronologically = (months) => {
  if (!months?.length) return months;
  try {
    return [...months].sort((a, b) => {
      const aParsed = parseMonthLabel(a);
      const bParsed = parseMonthLabel(b);
      if (aParsed.year !== bParsed.year) return aParsed.year - bParsed.year;
      const aIndex = MONTH_ORDER.indexOf(aParsed.month);
      const bIndex = MONTH_ORDER.indexOf(bParsed.month);
      if (aIndex === -1 || bIndex === -1) {
        if (aIndex === -1 && bIndex === -1) return 0;
        return aIndex === -1 ? 1 : -1;
      }
      return aIndex - bIndex;
    });
  } catch {
    return months;
  }
};

/**
 * Get report folders structure by organization ID
 * @param {string} organizationId - Organization ID
 * @param {string} organizationName - Organization name (optional)
 */
export const getReportFoldersService = async (
  organizationId,
  organizationName
) => {
  try {
    // Handle optional organizationId
    const trimmedOrgId = organizationId ? organizationId.trim() : null;
    const trimmedOrgName = organizationName ? organizationName.trim() : "";

    // If no organizationId provided, return empty folders
    if (!trimmedOrgId) {
      return createServiceResponse(
        true,
        STATUS_CODE_OK,
        REPORT_MESSAGES.FOLDERS_RETRIEVED,
        {
          organizationId: null,
          organizationName: trimmedOrgName ? trimmedOrgName : null,
          folders: {
            Finance: [],
            Operations: [],
            Payroll: [],
          },
        }
      );
    }

    // Include organizationName in cache key to avoid cache conflicts
    const cacheKey = `reportFolders:${trimmedOrgId}:${trimmedOrgName}`;
    const cachedResult = reportFoldersCache.get(cacheKey);
    if (cachedResult !== null) {
      logger.info(`Cache hit for report folders: ${trimmedOrgId}`);
      // Include organizationId and organizationName in cached response
      const cachedResponseData = {
        organizationId: trimmedOrgId,
        organizationName: trimmedOrgName ? trimmedOrgName : null,
        folders: cachedResult,
      };
      return createServiceResponse(
        true,
        STATUS_CODE_OK,
        REPORT_MESSAGES.FOLDERS_RETRIEVED,
        cachedResponseData
      );
    }

    const prefix = trimmedOrgName
      ? `${trimmedOrgId}/${trimmedOrgName}/Power BI-Reports/`
      : `${trimmedOrgId}/Power BI-Reports/`;

    logger.info(`Fetching report folders with prefix: ${prefix}`);
    const blobs = await blobStorageService.listByPrefix(prefix);
    logger.info(`Found ${blobs.length} blobs with prefix: ${prefix}`);

    const folders = {
      [CATEGORY_NAMES.FINANCE]: [],
      [CATEGORY_NAMES.OPERATIONS]: [],
      [CATEGORY_NAMES.PAYROLL]: [],
    };

    // Set to track unique months per category
    const seenMonths = {
      [CATEGORY_NAMES.FINANCE]: new Set(),
      [CATEGORY_NAMES.OPERATIONS]: new Set(),
      [CATEGORY_NAMES.PAYROLL]: new Set(),
    };

    for (const blob of blobs) {
      const pathParts = blob.name.split("/").filter(Boolean);

      // Path structure with orgName: {orgId}/{orgName}/Power BI-Reports/{Category}/{Year}/{Month}/
      // Path structure without orgName: {orgId}/Power BI-Reports/{Category}/{Year}/{Month}/
      // Minimum required parts: 5 (without orgName) or 6 (with orgName)
      const minPartsWithoutOrg = 5;
      const minPartsWithOrg = 6;

      if (pathParts.length < minPartsWithoutOrg) continue;

      try {
        // Determine if organization name is in path
        // Structure: [orgId, orgName?, "Power BI-Reports", category, year, month]
        const hasOrgName =
          pathParts.length >= minPartsWithOrg &&
          pathParts[1] !== "Power BI-Reports";

        // Calculate indices based on path structure
        // With orgName: [0=orgId, 1=orgName, 2="Power BI-Reports", 3=category, 4=year, 5=month]
        // Without orgName: [0=orgId, 1="Power BI-Reports", 2=category, 3=year, 4=month]
        const categoryIndex = hasOrgName ? 3 : 2;
        const yearIndex = hasOrgName ? 4 : 3;
        const monthIndex = hasOrgName ? 5 : 4;

        // Validate indices are within bounds
        if (
          categoryIndex >= pathParts.length ||
          yearIndex >= pathParts.length ||
          monthIndex >= pathParts.length
        ) {
          logger.warn(`Path parts out of bounds for blob: ${blob.name}`, {
            pathParts,
            hasOrgName,
          });
          continue;
        }

        const category = pathParts[categoryIndex];
        const year = pathParts[yearIndex];
        const monthAbbr = pathParts[monthIndex];

        // Skip if "Power BI-Reports" is not in expected position
        const powerBiReportsIndex = hasOrgName ? 2 : 1;
        if (pathParts[powerBiReportsIndex] !== "Power BI-Reports") {
          logger.warn(`Unexpected path structure for blob: ${blob.name}`, {
            pathParts,
            expectedPowerBiIndex: powerBiReportsIndex,
          });
          continue;
        }

        const normalizedCategory = normalizeCategory(category);
        if (!folders[normalizedCategory]) continue;

        const monthLabel = formatMonthLabel(monthAbbr, year);
        if (!seenMonths[normalizedCategory].has(monthLabel)) {
          folders[normalizedCategory].push(monthLabel);
          seenMonths[normalizedCategory].add(monthLabel);
        }
      } catch (error) {
        logger.warn(`Error parsing blob path: ${error.message}`, {
          blobName: blob.name,
          error: error.stack,
        });
        continue;
      }
    }

    for (const category of Object.keys(folders)) {
      try {
        folders[category] = sortMonthsChronologically(folders[category]);
      } catch (error) {
        logger.error(
          `Error sorting months for category ${category}: ${error.message}`
        );
      }
    }

    logger.info(
      `${FILE_LOG_MESSAGES.REPORT_FOLDERS_COMPLETE}: ${trimmedOrgId}`
    );
    reportFoldersCache.set(cacheKey, folders);

    // Include organizationId and organizationName in response
    const responseData = {
      organizationId: trimmedOrgId,
      organizationName: trimmedOrgName ? trimmedOrgName : null,
      folders,
    };

    return createServiceResponse(
      true,
      STATUS_CODE_OK,
      REPORT_MESSAGES.FOLDERS_RETRIEVED,
      responseData
    );
  } catch (error) {
    logger.error(
      `${FILE_LOG_MESSAGES.REPORT_FOLDERS_ERROR}: ${error.message}`,
      {
        stack: error.stack,
      }
    );
    return createInternalServerErrorResponse(
      REPORT_MESSAGES.FOLDERS_RETRIEVAL_FAILED,
      error.message
    );
  }
};

/**
 * Get report blob URL by organization ID, category, and month
 * @param {string} organizationId - Organization ID
 * @param {string} organizationName - Organization name (optional)
 * @param {string} category - Category (Finance, Operations, Payroll)
 * @param {string} month - Month label (e.g., "Aug-25")
 */
const getReportBlobUrlServiceInternal = async (
  organizationId,
  organizationName,
  category,
  month
) => {
  try {
    // Validate required fields
    if (!organizationId) {
      return createBadRequestResponse(
        FILE_MESSAGES.ORGANIZATION_ID_REQUIRED,
        FILE_MESSAGES.VALID_ORGANIZATION_ID
      );
    }

    if (!validateCategory(category)) {
      return createBadRequestResponse(
        FILE_MESSAGES.CATEGORY_REQUIRED,
        FILE_MESSAGES.VALID_CATEGORY
      );
    }

    if (!validateMonth(month)) {
      return createBadRequestResponse(
        FILE_MESSAGES.MONTH_REQUIRED,
        FILE_MESSAGES.VALID_MONTH
      );
    }

    // Normalize and sanitize inputs
    const normalizedCategory = normalizeCategory(category);
    const trimmedOrgId = organizationId.trim();
    const trimmedOrgName = organizationName ? organizationName.trim() : "";
    const parsedMonth = parseMonthLabel(month);
    const year = `20${parsedMonth.year}`;
    const monthAbbr = parsedMonth.month;
    const monthFolder = MONTH_MAP[monthAbbr] || monthAbbr;

    // Construct blob path
    const blobPathPrefix = trimmedOrgName
      ? `${trimmedOrgId}/${trimmedOrgName}/Power BI-Reports/${normalizedCategory}/${year}/${monthFolder}/`
      : `${trimmedOrgId}/Power BI-Reports/${normalizedCategory}/${year}/${monthFolder}/`;

    // Fetch blob list
    const blobs = await blobStorageService.listByPrefix(blobPathPrefix);
    const pdfBlob = blobs.find((blob) =>
      blob.name.toLowerCase().endsWith(".pdf")
    );

    if (!pdfBlob) {
      return createBadRequestResponse(
        REPORT_MESSAGES.REPORT_FILE_NOT_FOUND,
        `${REPORT_MESSAGES.NO_PDF_FOUND} for ${normalizedCategory} - ${month}`
      );
    }

    // Generate blob URL
    const blobPath = pdfBlob.name;
    const blobUrl = await blobStorageService.getBlobSasUrl(blobPath);
    const pathParts = blobPath.split("/");
    const fileName = pathParts[pathParts.length - 1] || blobPath;

    logger.info(`${FILE_LOG_MESSAGES.REPORT_BLOB_URL_COMPLETE}: ${blobPath}`);

    // Query document summary from database (optimized with single query)
    let summary = null;
    try {
      const doc = await Document.findOne({
        where: {
          organization_id: trimmedOrgId,
          blob_storage_path: blobPath,
          is_deleted: false,
        },
        attributes: ["summary"],
        raw: true, // Faster - returns plain object
      });

      if (doc?.summary) {
        summary = doc.summary;
      }
    } catch (error) {
      logger.warn(`Failed to fetch summary for blob path ${blobPath}: ${error.message}`);
    }

    // Construct response
    const responseData = {
      organizationId: trimmedOrgId,
      organizationName: trimmedOrgName || null,
      blobUrl,
      blobStoragePath: blobPath,
      fileName,
      chatFilename: blobPath,
      category: normalizedCategory,
      monthLabel: month,
      summary,
    };

    const serviceResponse = createServiceResponse(
      true,
      200,
      REPORT_MESSAGES.BLOB_URL_RETRIEVED,
      responseData
    );

    return serviceResponse;
  } catch (error) {
    logger.error(
      `${FILE_LOG_MESSAGES.REPORT_BLOB_URL_ERROR}: ${error.message}`,
      { stack: error.stack }
    );
    return createInternalServerErrorResponse(
      REPORT_MESSAGES.BLOB_URL_RETRIEVAL_FAILED,
      error.message
    );
  }
};

// Batch fetch summaries for multiple blob paths
const batchFetchSummaries = async (organizationId, blobPaths) => {
  if (!blobPaths?.length) return new Map();

  try {
    const docs = await Document.findAll({
      where: {
        organization_id: organizationId,
        blob_storage_path: { [Op.in]: blobPaths },
        is_deleted: false,
      },
      attributes: ["blob_storage_path", "summary"],
      raw: true,
    });

    const summaryMap = new Map();
    docs.forEach((doc) => {
      if (doc.summary) {
        summaryMap.set(doc.blob_storage_path, doc.summary);
      }
    });

    return summaryMap;
  } catch (error) {
    logger.warn(`Failed to batch fetch summaries: ${error.message}`);
    return new Map();
  }
};

export const getCombinedReportDataService = async (
  organizationId,
  organizationName
) => {
  try {
    // Check cache first
    const cacheKey = `combinedReports:${organizationId}:${organizationName || ""}`;
    const cached = combinedReportsCache.get(cacheKey);
    if (cached && Date.now() < cached.expiresAt) {
      logger.info(`Cache hit for combined reports: ${organizationId}`);
      return cached.data;
    }

    const foldersResponse = await getReportFoldersService(
      organizationId,
      organizationName
    );

    if (!foldersResponse.success) {
      return foldersResponse;
    }

    logger.info(FILE_LOG_MESSAGES.COMBINED_REPORTS_START, {
      organizationId,
      organizationName,
    });

    const { folders, organizationId: orgId, organizationName: orgName } =
      foldersResponse.data || {};

    if (!folders || typeof folders !== "object") {
      const emptyResponse = createServiceResponse(
        true,
        STATUS_CODE_OK,
        REPORT_MESSAGES.FOLDERS_RETRIEVED,
        {
          organizationId: orgId,
          organizationName: orgName,
          reports: {},
        }
      );
      // Cache empty response
      combinedReportsCache.set(cacheKey, {
        data: emptyResponse,
        expiresAt: Date.now() + COMBINED_REPORTS_CACHE_TTL,
      });
      return emptyResponse;
    }

    const categoryEntries = Object.entries(folders);
    const reports = {};

    // Process all categories in parallel
    const categoryPromises = categoryEntries.map(async ([category, months]) => {
      if (!Array.isArray(months) || months.length === 0) {
        return { category, results: [] };
      }

      // Process all months in parallel for this category
      const monthResults = await Promise.all(
        months.map(async (month) => {
          try {
            const reportResponse = await getReportBlobUrlServiceInternal(
              orgId,
              orgName,
              category,
              month
            );

            if (reportResponse.success) {
              const { data } = reportResponse;
              return {
                month,
                blobUrl: data?.blobUrl || null,
                blobStoragePath: data?.blobStoragePath || null,
                fileName: data?.fileName || null,
                summary: data?.summary || null,
                category: data?.category || category,
              };
            }

            return {
              month,
              error: reportResponse.message || REPORT_MESSAGES.BLOB_URL_RETRIEVAL_FAILED,
            };
          } catch (error) {
            logger.warn(`Error processing month ${month} for category ${category}: ${error.message}`);
            return {
              month,
              error: REPORT_MESSAGES.BLOB_URL_RETRIEVAL_FAILED,
            };
          }
        })
      );

      return { category, results: monthResults };
    });

    // Wait for all categories to complete
    const categoryResults = await Promise.all(categoryPromises);
    
    // Build reports object
    categoryResults.forEach(({ category, results }) => {
      reports[category] = results;
    });

    logger.info(FILE_LOG_MESSAGES.COMBINED_REPORTS_COMPLETE, {
      organizationId: orgId,
      categories: Object.keys(reports),
    });

    const response = createServiceResponse(
      true,
      STATUS_CODE_OK,
      REPORT_MESSAGES.COMBINED_REPORTS_RETRIEVED,
      {
        organizationId: orgId,
        organizationName: orgName || null,
        reports,
      }
    );

    // Cache the response
    combinedReportsCache.set(cacheKey, {
      data: response,
      expiresAt: Date.now() + COMBINED_REPORTS_CACHE_TTL,
    });

    return response;
  } catch (error) {
    logger.error(FILE_LOG_MESSAGES.COMBINED_REPORTS_ERROR, {
      organizationId,
      organizationName,
      error: error.message,
      stack: error.stack,
    });
    return createInternalServerErrorResponse(
      REPORT_MESSAGES.COMBINED_REPORTS_RETRIEVAL_FAILED,
      error.message
    );
  }
};

const normalizeFolderKey = (reports = {}, folder = "") => {
  if (!folder || typeof folder !== "string") return null;
  const target = folder.trim().toLowerCase();
  return Object.keys(reports).find((key) => key.toLowerCase() === target) || null;
};

export const getAllReportsData = async (organizationId, organizationName) => {
  return getCombinedReportDataService(organizationId, organizationName);
};

export const getReportBlobUrlService = async (
  organizationId,
  organizationName,
  category,
  month
) => {
  const serviceResponse = await getReportBlobUrlServiceInternal(
    organizationId,
    organizationName,
    category,
    month
  );

  if (!serviceResponse.success) {
    return serviceResponse;
  }

  const data = serviceResponse.data || {};
  const { summary, ...pdfData } = data || {};

  return createServiceResponse(
    true,
    serviceResponse.statusCode ?? STATUS_CODE_OK,
    REPORT_MESSAGES.BLOB_URL_RETRIEVED,
    pdfData
  );
};

export const getReportSummaryService = async (
  organizationId,
  organizationName,
  category,
  month
) => {
  const serviceResponse = await getReportBlobUrlServiceInternal(
    organizationId,
    organizationName,
    category,
    month
  );

  if (!serviceResponse.success) {
    return serviceResponse;
  }

  const data = serviceResponse.data || {};

  const summaryData = {
    organizationId: data?.organizationId || organizationId || null,
    organizationName: data?.organizationName || organizationName || null,
    category: data?.category || category || null,
    monthLabel: data?.monthLabel || month || null,
    summary: data?.summary ?? null,
  };

  return createServiceResponse(
    true,
    serviceResponse.statusCode ?? STATUS_CODE_OK,
    REPORT_MESSAGES.SUMMARY_RETRIEVED,
    summaryData
  );
};

export const getReportFoldersOnly = async (
  organizationId,
  organizationName
) => {
  const combinedResponse = await getAllReportsData(
    organizationId,
    organizationName
  );

  if (!combinedResponse.success) {
    return combinedResponse;
  }

  const data = combinedResponse.data || {};
  const reports = data.reports || {};

  const folderNames = Object.keys(reports);
  const folderOptions = folderNames.map((folderName) => {
    const entries = Array.isArray(reports[folderName]) ? reports[folderName] : [];
    const months = entries
      .map((entry) => entry?.month || entry?.monthLabel || null)
      .filter((month) => typeof month === "string" && month.trim().length > 0);
    const uniqueMonths = Array.from(new Set(months));

    const monthOptions = uniqueMonths.map((monthLabel) => ({
      value: monthLabel,
      label: formatMonthDisplayLabel(monthLabel),
    }));

    return {
      key: folderName,
      label: folderName,
      ...(monthOptions.length > 0 ? { months: monthOptions } : {}),
    };
  });

  return createServiceResponse(true, combinedResponse.statusCode ?? STATUS_CODE_OK, FILE_MESSAGES.REPORT_FOLDERS_SUCCESS, {
    organization: {
      id: data.organizationId || organizationId || null,
      name: data.organizationName || organizationName || null,
    },
    folders: folderOptions,
  });
};

export const getReportFilesForFolder = async (
  organizationId,
  organizationName,
  folder,
  monthsFilter,
  filename = null
) => {
  if (!folder || typeof folder !== "string" || folder.trim().length === 0) {
    return createBadRequestResponse(
      FILE_MESSAGES.FOLDER_REQUIRED,
      FILE_MESSAGES.FOLDER_REQUIRED
    );
  }

  const combinedResponse = await getAllReportsData(
    organizationId,
    organizationName
  );

  if (!combinedResponse.success) {
    return combinedResponse;
  }

  const data = combinedResponse.data || {};
  const reports = data.reports || {};
  const normalizedFolder = normalizeFolderKey(reports, folder);

  if (!normalizedFolder) {
    return createNotFoundResponse(
      FILE_MESSAGES.REPORT_NOT_FOUND,
      `Folder "${folder}" not found`
    );
  }

  // Optimize: Build allowedMonths set only if filter provided
  let allowedMonths = null;
  if (monthsFilter?.trim()) {
    const monthArray = monthsFilter
      .split(",")
      .map((value) => value.trim())
      .filter(Boolean);
    
    if (monthArray.length > 0) {
      allowedMonths = new Set(monthArray.map((value) => value.toLowerCase()));
    }
  }

  const folderEntries = reports[normalizedFolder] || [];
  const files = [];

  // If filename is provided, try to resolve specific file
  if (filename) {
    const filenameLower = filename.toLowerCase().trim();
    
    // Try to find exact match first
    for (const entry of folderEntries) {
      if (!entry || entry.error) continue;

      const entryFileName = entry?.fileName;
      if (!entryFileName) continue;

      // Check for exact match or partial match
      if (entryFileName.toLowerCase().includes(filenameLower) || 
          filenameLower.includes(entryFileName.toLowerCase())) {
        const monthValue = entry?.month || entry?.monthLabel;
        const blobUrl = entry?.blobUrl;

        if (blobUrl) {
          return createServiceResponse(
            true,
            combinedResponse.statusCode ?? STATUS_CODE_OK,
            FILE_MESSAGES.REPORT_FILES_SUCCESS,
            {
              orgId: data.organizationId || organizationId || null,
              org: data.organizationName || organizationName || null,
              folder: normalizedFolder,
              files: [{
                month: monthValue || null,
                label: monthValue ? formatMonthDisplayLabel(monthValue) : null,
                name: entryFileName,
                url: blobUrl,
                blobStoragePath: entry?.blobStoragePath || null,
              }],
            }
          );
        }
      }
    }

    // If not found in cache, try direct blob storage lookup
    try {
      const filenameWithoutExt = filename.replace(/\.pdf$/i, "").trim();
      const monthYearMatch = filenameWithoutExt.match(/(\w+)\s+(\w+)[-\s](\d{4})/i);
      
      if (monthYearMatch && organizationId) {
        const service = monthYearMatch[1];
        const month = monthYearMatch[2];
        const year = monthYearMatch[3];
        
        // Normalize service name
        const normalizedService = normalizeCategory(service);
        
        // Normalize month name
        const monthLower = month.toLowerCase();
        const monthNameMap = {
          jan: "January", january: "January",
          feb: "February", february: "February",
          mar: "March", march: "March",
          apr: "April", april: "April",
          may: "May",
          jun: "June", june: "June",
          jul: "July", july: "July",
          aug: "August", august: "August",
          sep: "September", september: "September",
          oct: "October", october: "October",
          nov: "November", november: "November",
          dec: "December", december: "December",
        };
        const normalizedMonth = monthNameMap[monthLower] || month;
        
        // Construct blob path
        const trimmedOrgId = organizationId.trim();
        const trimmedOrgName = organizationName ? organizationName.trim() : "";
        const blobPathPrefix = trimmedOrgName
          ? `${trimmedOrgId}/${trimmedOrgName}/Power BI-Reports/${normalizedService}/${year}/${normalizedMonth}/`
          : `${trimmedOrgId}/Power BI-Reports/${normalizedService}/${year}/${normalizedMonth}/`;
        
        // List blobs in that path
        const blobs = await blobStorageService.listByPrefix(blobPathPrefix);
        const matchingBlob = blobs.find((blob) => {
          const blobName = blob.name.toLowerCase();
          return blobName.includes(filenameLower) || filenameLower.includes(blobName.split('/').pop().toLowerCase());
        });
        
        if (matchingBlob) {
          const blobUrl = await blobStorageService.getBlobSasUrl(matchingBlob.name);
          const pathParts = matchingBlob.name.split("/");
          const fileName = pathParts[pathParts.length - 1] || matchingBlob.name;
          const monthAbbr = FULL_MONTH_TO_ABBR[normalizedMonth] || normalizedMonth;
          const monthLabel = `${monthAbbr}-${year.slice(-2)}`;
          
          return createServiceResponse(
            true,
            STATUS_CODE_OK,
            FILE_MESSAGES.REPORT_FILES_SUCCESS,
            {
              orgId: trimmedOrgId,
              org: trimmedOrgName || null,
              folder: normalizedService,
              files: [{
                month: monthLabel,
                label: formatMonthDisplayLabel(monthLabel),
                name: fileName,
                url: blobUrl,
                blobStoragePath: matchingBlob.name,
              }],
            }
          );
        }
      }
    } catch (blobError) {
      logger.warn(`Error looking up blob directly: ${blobError.message}`, {
        filename,
        organizationId,
      });
    }
  }

  // Normal flow: return all files matching filters
  for (const entry of folderEntries) {
    if (!entry || entry.error) continue;

    const monthValue = entry?.month || entry?.monthLabel;
    if (!monthValue) continue;

    // Apply month filter if provided
    if (allowedMonths && !allowedMonths.has(monthValue.toLowerCase())) {
      continue;
    }

    const fileName = entry?.fileName;
    const blobUrl = entry?.blobUrl;

    if (!fileName || !blobUrl) continue;

    files.push({
      month: monthValue,
      label: formatMonthDisplayLabel(monthValue),
      name: fileName,
      url: blobUrl,
      blobStoragePath: entry?.blobStoragePath || null,
    });
  }

  return createServiceResponse(
    true,
    combinedResponse.statusCode ?? STATUS_CODE_OK,
    FILE_MESSAGES.REPORT_FILES_SUCCESS,
    {
      orgId: data.organizationId || organizationId || null,
      org: data.organizationName || organizationName || null,
      folder: normalizedFolder,
      files,
    }
  );
};

export const getReportSummaryForFile = async (
  organizationId,
  organizationName,
  folder,
  fileName
) => {
  if (!folder || typeof folder !== "string" || folder.trim().length === 0) {
    return createBadRequestResponse(
      FILE_MESSAGES.FOLDER_REQUIRED,
      FILE_MESSAGES.FOLDER_REQUIRED
    );
  }

  if (!fileName || typeof fileName !== "string" || fileName.trim().length === 0) {
    return createBadRequestResponse(
      FILE_MESSAGES.FILE_NAME_REQUIRED,
      FILE_MESSAGES.FILE_NAME_REQUIRED
    );
  }

  const combinedResponse = await getAllReportsData(
    organizationId,
    organizationName
  );

  if (!combinedResponse.success) {
    return combinedResponse;
  }

  const data = combinedResponse.data || {};
  const reports = data.reports || {};
  const normalizedFolder = normalizeFolderKey(reports, folder);

  if (!normalizedFolder) {
    return createNotFoundResponse(
      FILE_MESSAGES.REPORT_NOT_FOUND,
      `Folder "${folder}" not found`
    );
  }

  const entries = reports[normalizedFolder] || [];
  const decodedFileName = (() => {
    try {
      return decodeURIComponent(fileName);
    } catch {
      return fileName;
    }
  })();

  const searchKey = decodedFileName.trim().toLowerCase();
  const targetEntry = entries.find(
    (entry) => (entry?.fileName || "").toLowerCase() === searchKey
  );

  if (!targetEntry || targetEntry.error) {
    return createNotFoundResponse(
      FILE_MESSAGES.REPORT_NOT_FOUND,
      `File "${decodedFileName}" not found in folder "${normalizedFolder}"`
    );
  }

  const monthValue = targetEntry?.month || targetEntry?.monthLabel || null;
  const monthLabel = monthValue ? formatMonthDisplayLabel(monthValue) : null;

  return createServiceResponse(
    true,
    combinedResponse.statusCode ?? STATUS_CODE_OK,
    FILE_MESSAGES.REPORT_SUMMARY_SUCCESS,
    {
      orgId: data.organizationId || organizationId || null,
      org: data.organizationName || organizationName || null,
      folder: normalizedFolder,
      file: {
        name: targetEntry.fileName || decodedFileName,
        month: monthValue || undefined,
        label: monthLabel || undefined,
      },
      summary: targetEntry.summary ?? undefined,
    }
  );
};
