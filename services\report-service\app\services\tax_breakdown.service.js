import TaxBreakdownRepository from "../repository/tax_breakdown.repository.js";
import { LOGGER_NAMES } from "../utils/constants/log.constants.js";
import { createLogger } from "../utils/logger.utils.js";
import { isValidMonth, isValidYear, parseNumericValue, formatToKWithDecimals } from "../utils/format.utils.js";
import { getMonthDateRange } from "../utils/repository.utils.js";

const logger = createLogger(LOGGER_NAMES.REPORT_SERVICE);

/**
 * Get tax breakdown data for organization
 * @param {Object} params - Query parameters
 * @param {string} params.organization_id - Organization ID
 * @param {number} params.month - Month (1-12)
 * @param {number} params.year - Year
 * @returns {Promise<Object>} Tax breakdown data
 */
const getTaxBreakdownData = async ({ organization_id, month, year }) => {
  try {
    logger.info(
      `Fetching tax breakdown for org: ${organization_id}, month: ${month}, year: ${year}`
    );

    // Validate parameters
    if (!organization_id) {
      throw new Error("Organization ID is required");
    }

    if (!month || !isValidMonth(month)) {
      throw new Error("Valid month (1-12) is required");
    }

    if (!year || !isValidYear(year)) {
      throw new Error("Valid year is required");
    }

    // Convert to numbers
    const monthNum = parseInt(month, 10);
    const yearNum = parseInt(year, 10);

    // Get organization schema name
    const schemaName = await TaxBreakdownRepository.getOrganizationSchemaName(
      organization_id
    );

    if (!schemaName) {
      logger.warn(`Schema not found for organization: ${organization_id}`);
      throw new Error("Organization not found or schema not configured");
    }

    // Calculate date range from month and year
    const { startDate, endDate } = getMonthDateRange(monthNum, yearNum);
    
    logger.info(
      `Calculated date range for month ${monthNum}, year ${yearNum}: ${startDate} to ${endDate}`
    );

    // Fetch tax breakdown
    const taxData = await TaxBreakdownRepository.getTaxBreakdown(
      schemaName,
      startDate,
      endDate
    );

    // Parse and format values
    const sit = parseNumericValue(taxData.sit);
    const ftt = parseNumericValue(taxData.ftt);
    const socsec = parseNumericValue(taxData.socsec);
    const medicare = parseNumericValue(taxData.medicare);

    logger.info(
      `Tax breakdown data formatted successfully - SIT: ${sit}, FTT: ${ftt}, SOCSEC: ${socsec}, MEDICARE: ${medicare}`
    );

    // Return formatted response
    return {
      tax_breakdown: {
        SIT: formatToKWithDecimals(sit),
        FTT: formatToKWithDecimals(ftt),
        SOCSEC: formatToKWithDecimals(socsec),
        MEDICARE: formatToKWithDecimals(medicare),
      },
    };
  } catch (error) {
    logger.error("Error in TaxBreakdownService.getTaxBreakdownData:", error);
    throw error;
  }
};

export default {
  getTaxBreakdownData,
};
