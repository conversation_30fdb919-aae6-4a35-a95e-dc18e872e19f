import { listPdfBlobNames } from "../app/utils/pdfLoader.js";
import { config } from "../app/config/env.js";

async function run() {
  try {
    process.stdout.write(
      `Account: ${
        (config.AZURE_BLOB_CONNECTION_STRING || "").includes(
          "SharedAccessSignature="
        )
          ? "SAS"
          : "AccountKey"
      }\n`
    );
    process.stdout.write(
      `Container: ${config.AZURE_BLOB_CONTAINER_NAME || "<unset>"}\n`
    );
    const names = await listPdfBlobNames();
    process.stdout.write(`Blob OK. PDF count: ${names.length}\n`);
    process.stdout.write(`Samples: ${JSON.stringify(names.slice(0, 10))}\n`);
  } catch (e) {
    const msg = e?.message || String(e);
    process.stderr.write(`Blob FAILED: ${msg}\n`);
    if (msg.includes("not authorized") || msg.includes("AuthorizationFailure") || e?.statusCode === 403) {
      process.stderr.write(
        "Hints: \n- If using SAS, regenerate a SAS with List (l) and Read (r) permissions and a future expiry (se).\n- If using AccountKey, ensure 'Allow storage account key access' is enabled and networking allows your IP.\n- Verify AZURE_BLOB_CONTAINER_NAME matches an existing container.\n"
      );
    }
    process.exit(1);
  }
}

run();
