const path = require("path");
const CaseSensitivePathsPlugin = require("case-sensitive-paths-webpack-plugin");

/** @type {import('next').NextConfig} */
const nextConfig = {
  // Image optimization and caching
  images: {
    // Enable image optimization
    unoptimized: false,
    // Cache images for 1 year by default
    minimumCacheTTL: 31536000,
    // Allow external domains if needed
    domains: [],
  },

  // Headers for better caching control
  async headers() {
    return [
      {
        // Apply to all static assets
        source: "/public/:path*",
        headers: [
          {
            key: "Cache-Control",
            value: "public, max-age=31536000, immutable",
          },
        ],
      },
      {
        // Apply to images specifically
        source: "/:path*\\.(png|jpg|jpeg|gif|svg|ico|webp)",
        headers: [
          {
            key: "Cache-Control",
            value: "public, max-age=86400, must-revalidate",
          },
        ],
      },
      {
        // Apply to PDF worker file
        source: "/pdf.worker.min.js",
        headers: [
          {
            key: "Cache-Control",
            value: "public, max-age=31536000, immutable",
          },
          {
            key: "Content-Type",
            value: "application/javascript",
          },
        ],
      },
    ];
  },

  // Add this to ignore 'canvas' module in frontend builds
  webpack: (config) => {
    config.resolve.fallback = {
      ...config.resolve.fallback,
      canvas: false,
    };
    config.resolve.alias = {
      ...config.resolve.alias,
      "@": path.resolve(__dirname, "src"),
      "@shared": path.resolve(__dirname, "../shared"),
    };
    // Add case-sensitive paths plugin to catch casing issues early
    config.plugins.push(new CaseSensitivePathsPlugin());
    return config;
  },
};

module.exports = nextConfig;
