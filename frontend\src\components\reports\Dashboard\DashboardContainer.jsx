"use client";

import FinanceDashboard from "./finance/FinanceDashboard";
import PayrollDashboard from "./payroll/PayrollDashboard";
import OperationalDashboard from "./operations/OperationalDashboard";
import ComingSoon from "./ComingSoon";

export default function DashboardContainer({
  serviceType,
  organizationId,
  organizationName,
  month,
  year,
}) {
  if (serviceType === "financial")
    return (
      <FinanceDashboard
        organizationId={organizationId}
        organizationName={organizationName}
        month={month}
        year={year}
      />
    );
  if (serviceType === "payroll")
    return (
      <PayrollDashboard
        organizationId={organizationId}
        organizationName={organizationName}
        month={month}
        year={year}
      />
    );
  if (serviceType === "operational")
    return (
      <OperationalDashboard
        organizationId={organizationId}
        organizationName={organizationName}
        month={month}
        year={year}
      />
    );
  return <ComingSoon title={`${serviceType || "Dashboard"}`} />;
}
