import RevenueExpenseService from "../services/revenue_expense.service.js";
import { LOGGER_NAMES } from "../utils/constants/log.constants.js";
import { createLogger } from "../utils/logger.utils.js";
import { validateRequiredParams, handleControllerError, sendSuccessResponse } from "../utils/controller.utils.js";

const logger = createLogger(LOGGER_NAMES.REPORT_CONTROLLER);

/**
 * Get revenue and expense data for organization by month and year
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getRevenueExpense = async (req, res) => {
  try {
    const { organization_id, month, year } = req.query;

    logger.info(
      `Fetching revenue/expense data for org: ${organization_id}, month: ${month}, year: ${year}`
    );

    // Validate required parameters
    const validationError = validateRequiredParams(req.query, ['organization_id', 'month', 'year']);
    if (validationError) {
      return res.status(400).json(validationError);
    }

    // Fetch revenue/expense data
    const revenueExpenseData = await RevenueExpenseService.getRevenueExpenseData({
      organization_id,
      month,
      year,
    });

    // Return successful response
    sendSuccessResponse(res, "Revenue and expense data fetched successfully", revenueExpenseData);
  } catch (error) {
    logger.error("Error fetching revenue/expense data:", error);
    handleControllerError(error, res, "Error fetching revenue/expense data");
  }
};

export default {
  getRevenueExpense,
};
