import { createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";
import tokenStorage from "@/lib/tokenStorage";
import { REPORT_FOLDERS_CONSTANTS } from "@/utils/constants";

// File Service API Configuration
const FILE_SERVICE_URL = process.env.NEXT_PUBLIC_FILE_SERVICE_URL;
const SYSTEM_API_KEY = process.env.NEXT_PUBLIC_SYSTEM_API_KEY;

const MONTH_NAME_MAP = {
  Jan: "January",
  Feb: "February",
  Mar: "March",
  Apr: "April",
  May: "May",
  Jun: "June",
  Jul: "July",
  Aug: "August",
  Sep: "September",
  Oct: "October",
  Nov: "November",
  Dec: "December",
};

const formatMonthLabel = (value) => {
  if (!value || typeof value !== "string") return value;

  const parts = value.split("-");
  if (parts.length !== 2) return value;

  const [abbr, yearPart] = parts;
  const monthName = MONTH_NAME_MAP[abbr] || abbr;
  const year = yearPart?.length === 2 ? `20${yearPart}` : yearPart || "";

  return `${monthName} ${year}`.trim();
};

const normalizeMonths = (months = []) => {
  if (!Array.isArray(months)) return [];

  return months
    .map((month) => {
      if (!month) return null;

      if (typeof month === "string") {
        return {
          value: month,
          label: formatMonthLabel(month),
        };
      }

      const value = month?.value || month?.month || null;
      if (!value) return null;

      return {
        value,
        label: month?.label || formatMonthLabel(value),
      };
    })
    .filter((entry) => entry?.value && entry?.label);
};

// Create axios instance for file service
const getFileServiceClient = () => {
  if (!FILE_SERVICE_URL) {
    throw new Error(
      "NEXT_PUBLIC_FILE_SERVICE_URL environment variable is not configured"
    );
  }

  const instance = axios.create({
    baseURL: FILE_SERVICE_URL,
    headers: {
      "Content-Type": "application/json",
      ...(SYSTEM_API_KEY ? { "x-api-key": SYSTEM_API_KEY } : {}),
    },
    withCredentials: true,
    timeout: 30000,
  });

  instance.interceptors.request.use(
    (config) => {
      const accessToken = tokenStorage.getAccessToken();
      if (accessToken && !tokenStorage.isTokenExpired(accessToken)) {
        config.headers.Authorization = `Bearer ${accessToken}`;
      }
      return config;
    },
    (error) => Promise.reject(error)
  );

  return instance;
};

const buildFolderMap = (folders = []) => {
  return folders.reduce((acc, folder) => {
    if (folder?.key) {
      acc[folder.key] = folder;
    }
    return acc;
  }, {});
};

const buildMonthsByFolder = (folders = []) => {
  return folders.reduce((acc, folder) => {
    if (folder?.key) {
      acc[folder.key] = Array.isArray(folder.months)
        ? folder.months.map((month) => month.value)
        : [];
    }
    return acc;
  }, {});
};

const validateOrganizationId = (organizationId) => {
  if (
    !organizationId ||
    typeof organizationId !== "string" ||
    organizationId.trim().length === 0
  ) {
    throw new Error(REPORT_FOLDERS_CONSTANTS.ERROR.ORG_ID_REQUIRED);
  }
  return organizationId.trim();
};

const handleApiError = (error, rejectWithValue) => {
  // Handle network errors (connection refused, timeout, etc.)
  if (!error.response) {
    const isConnectionError =
      error.message?.includes("ERR_CONNECTION_REFUSED") ||
      error.message?.includes("ERR_CONNECTION_RESET") ||
      error.message?.includes("Network Error") ||
      error.code === "ECONNREFUSED" ||
      error.code === "ETIMEDOUT";

    if (isConnectionError) {
      return rejectWithValue(
        "File service is unavailable. Please check if the service is running."
      );
    }
  }

  if (error.response?.status === 404) {
    return rejectWithValue(REPORT_FOLDERS_CONSTANTS.ERROR.NOT_FOUND);
  }
  if (error.response?.status === 401 || error.response?.status === 403) {
    return rejectWithValue(REPORT_FOLDERS_CONSTANTS.ERROR.UNAUTHORIZED);
  }
  return rejectWithValue(
    error.response?.data?.message ||
      error.message ||
      REPORT_FOLDERS_CONSTANTS.ERROR.FETCH_FAILED
  );
};

export const fetchReportFolders = createAsyncThunk(
  "reportFolders/fetchReportFolders",
  async (
    { organizationId, organizationName, forceRefresh = false },
    { rejectWithValue }
  ) => {
    try {
      const trimmedOrgId = validateOrganizationId(organizationId);

      const apiClient = getFileServiceClient();
      const params = {
        orgId: trimmedOrgId,
      };

      if (organizationName && organizationName.trim()) {
        params.orgname = organizationName.trim();
      }

      if (forceRefresh) {
        params._t = Date.now();
      }

      const response = await apiClient.get("/files/report-folders", { params });

      if (response.data?.success && response.data?.data) {
        const responseData = response.data.data || {};
        const organization = responseData.organization || {};
        const foldersFromApi = Array.isArray(responseData.folders)
          ? responseData.folders
          : [];

        const normalizedFolders = foldersFromApi
          .map((folder) => {
            if (!folder) return null;
            const key = folder.key || folder.label || null;
            if (!key) return null;
            return {
              key,
              label: folder.label || folder.key || key,
              months: normalizeMonths(folder.months),
            };
          })
          .filter(Boolean);

        const folderMap = buildFolderMap(normalizedFolders);
        const monthsByFolder = buildMonthsByFolder(normalizedFolders);

        return {
          orgId: organization.id || trimmedOrgId,
          org:
            organization.name ||
            (organizationName ? organizationName.trim() : null),
          folders: normalizedFolders,
          folderMap,
          monthsByFolder,
        };
      }

      throw new Error(
        response.data?.message || REPORT_FOLDERS_CONSTANTS.ERROR.FETCH_FAILED
      );
    } catch (error) {
      // Handle network errors (connection refused, timeout, etc.)
      if (!error.response) {
        const isConnectionError =
          error.message?.includes("ERR_CONNECTION_REFUSED") ||
          error.message?.includes("ERR_CONNECTION_RESET") ||
          error.message?.includes("Network Error") ||
          error.code === "ECONNREFUSED" ||
          error.code === "ETIMEDOUT";

        if (isConnectionError) {
          return rejectWithValue(
            "File service is unavailable. Please check if the service is running."
          );
        }
      }

      if (error.response?.status === 404) {
        return rejectWithValue(REPORT_FOLDERS_CONSTANTS.ERROR.NOT_FOUND);
      }
      if (error.response?.status === 401 || error.response?.status === 403) {
        return rejectWithValue(REPORT_FOLDERS_CONSTANTS.ERROR.UNAUTHORIZED);
      }
      return rejectWithValue(
        error.response?.data?.message ||
          error.message ||
          REPORT_FOLDERS_CONSTANTS.ERROR.FETCH_FAILED
      );
    }
  }
);

const validateFolder = (folder) => {
  if (!folder || typeof folder !== "string" || folder.trim().length === 0) {
    throw new Error(REPORT_FOLDERS_CONSTANTS.ERROR.FOLDER_REQUIRED);
  }
  return folder.trim();
};

export const fetchReportFiles = createAsyncThunk(
  "reportFolders/fetchReportFiles",
  async (
    { organizationId, organizationName, folder, months },
    { rejectWithValue }
  ) => {
    try {
      const trimmedOrgId = validateOrganizationId(organizationId);
      const trimmedFolder = validateFolder(folder);

      const apiClient = getFileServiceClient();
      const params = {
        orgId: trimmedOrgId,
        folder: trimmedFolder,
      };

      if (organizationName?.trim()) {
        params.orgname = organizationName.trim();
      }

      if (months?.trim()) {
        params.months = months
          .split(",")
          .map((value) => value.trim())
          .filter(Boolean)
          .join(",");
      }

      const response = await apiClient.get("/files/report-files", { params });

      if (response.data?.success && response.data?.data) {
        const data = response.data.data || {};
        const files = Array.isArray(data.files) ? data.files : [];

        const normalizedFiles = files
          .map((file) => {
            if (!file) return null;
            const monthValue = file.month || file.value || null;
            const label = file.label || formatMonthLabel(monthValue);
            const name = file.name || file.fileName || null;
            const url = file.url || file.blobUrl || null;
            if (!monthValue || !name || !url) return null;
            return {
              month: monthValue,
              label,
              name,
              url,
            };
          })
          .filter(Boolean);

        return {
          orgId: data.orgId || trimmedOrgId,
          org: data.org || organizationName?.trim() || null,
          folder: data.folder || trimmedFolder,
          files: normalizedFiles,
        };
      }

      throw new Error(
        response.data?.message || REPORT_FOLDERS_CONSTANTS.ERROR.FETCH_FAILED
      );
    } catch (error) {
      return handleApiError(error, rejectWithValue);
    }
  }
);

const validateFileName = (fileName) => {
  if (
    !fileName ||
    typeof fileName !== "string" ||
    fileName.trim().length === 0
  ) {
    throw new Error(REPORT_FOLDERS_CONSTANTS.ERROR.FILE_NAME_REQUIRED);
  }
  return fileName.trim();
};

export const fetchReportSummary = createAsyncThunk(
  "reportFolders/fetchReportSummary",
  async (
    { organizationId, organizationName, folder, fileName, forceRefresh = true },
    { rejectWithValue }
  ) => {
    try {
      const trimmedOrgId = validateOrganizationId(organizationId);
      const trimmedFolder = validateFolder(folder);
      const trimmedFileName = validateFileName(fileName);

      const apiClient = getFileServiceClient();
      
      // Build query string manually to ensure %20 encoding (not +) for spaces
      const queryParams = new URLSearchParams();
      queryParams.append("orgId", trimmedOrgId);
      queryParams.append("folder", trimmedFolder);
      queryParams.append("fileName", trimmedFileName); // Will be properly encoded
      
      if (organizationName?.trim()) {
        queryParams.append("orgname", organizationName.trim());
      }
      
      // Add cache-busting timestamp to always get fresh data
      if (forceRefresh) {
        queryParams.append("_t", Date.now().toString());
      }
      
      // Convert URLSearchParams to string and replace + with %20 for proper encoding
      let queryString = queryParams.toString().replace(/\+/g, "%20");
      console.log("queryString", queryString);
      const response = await apiClient.get(`/files/report-summary?${queryString}`);

      if (response.data?.success && response.data?.data) {
        const data = response.data.data || {};
        const file = data.file || {};
        const monthValue = file.month || null;
        const label = file.label || formatMonthLabel(monthValue);

        return {
          orgId: data.orgId || trimmedOrgId,
          org: data.org || organizationName?.trim() || null,
          folder: data.folder || trimmedFolder,
          file: {
            name: file.name || trimmedFileName,
            month: monthValue || undefined,
            label: label || undefined,
          },
          summary: data.summary || "",
        };
      }

      throw new Error(
        response.data?.message || REPORT_FOLDERS_CONSTANTS.ERROR.FETCH_FAILED
      );
    } catch (error) {
      return handleApiError(error, rejectWithValue);
    }
  }
);
