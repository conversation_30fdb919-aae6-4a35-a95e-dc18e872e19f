import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";
import { dirname } from "path";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

/**
 * Creates a JSON file from JSON data and returns the file path
 * @param {Object|Array} jsonData - The JSON data to convert to file
 * @param {string} filename - Optional filename (default: 'data.json')
 * @returns {string} The file path of the created JSON file
 */
export const createJsonFile = (jsonData, filename = "data.json") => {
  try {
    // Ensure filename has .json extension
    const jsonFilename = filename.endsWith(".json")
      ? filename
      : `${filename}.json`;

    // Create temp directory if it doesn't exist
    const tempDir = path.join(__dirname, "../../temp");
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
    }

    // Create unique filename with timestamp to avoid conflicts
    const timestamp = Date.now();
    const uniqueFilename = `${timestamp}_${jsonFilename}`;
    const filePath = path.join(tempDir, uniqueFilename);

    // Write JSON data to file
    fs.writeFileSync(filePath, JSON.stringify(jsonData, null, 2), "utf8");

    return filePath;
  } catch (error) {
    console.error("Error creating JSON file:", error);
    throw error;
  }
};

/**
 * Creates a JSON file from JSON data and returns the file path
 * @param {Object|Array} jsonData - The JSON data to convert to file
 * @param {string} filename - Optional filename (default: 'data.json')
 * @returns {string} The file path of the created JSON file
 */
export const sendJsonFile = (jsonData, filename = "data.json") => {
  try {
    // Ensure filename has .json extension
    const jsonFilename = filename.endsWith(".json")
      ? filename
      : `${filename}.json`;

    // Create temp directory if it doesn't exist
    const tempDir = path.join(__dirname, "../../temp");
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
    }

    // Create unique filename with timestamp to avoid conflicts
    const timestamp = Date.now();
    const uniqueFilename = `${timestamp}_${jsonFilename}`;
    const filePath = path.join(tempDir, uniqueFilename);

    // Write JSON data to file
    fs.writeFileSync(filePath, JSON.stringify(jsonData, null, 2), "utf8");

    return filePath;
  } catch (error) {
    console.error("Error creating JSON file:", error);
    throw error;
  }
};

export default {
  sendJsonFile,
  createJsonFile,
};
