import React from "react";

// Modern semantic color styles using new palette
const statusStyles = {
  Active: "bg-[#10B981]/10 text-semantic-success border border-semantic-success/30",
  Inactive: "bg-[#EF4444]/10 text-semantic-danger border border-semantic-danger/30",
  Pending: "bg-[#F59E0B]/10 text-semantic-warning border border-semantic-warning/30",
  Draft: "bg-text-subtle/10 text-text-body border border-border",
  Published: "bg-[#3B82F6]/10 text-semantic-info border border-semantic-info/30",
  Archived: "bg-primary/10 text-primary border border-primary/30",
};

const typeStyles = {
  Department: "bg-[#3B82F6]/10 text-semantic-info",
  Team: "bg-[#10B981]/10 text-semantic-success",
  Division: "bg-primary/10 text-primary",
  Admin: "bg-[#EF4444]/10 text-semantic-danger",
  Manager: "bg-[#3B82F6]/10 text-semantic-info",
  User: "bg-text-subtle/10 text-text-body",
  Enterprise: "bg-primary/10 text-primary",
  Professional: "bg-[#3B82F6]/10 text-semantic-info",
  Basic: "bg-text-subtle/10 text-text-body",
};

export default function StatusBadge({
  status,
  type = "status", // "status" or "type"
  className = "",
  size = "sm", // "xs", "sm", "md", "lg"
  variant = "default", // "default", "outline", "solid"
}) {
  const sizeClasses = {
    xs: "px-2 py-0.5 text-xs",
    sm: "px-3 py-1 text-sm",
    md: "px-4 py-2 text-sm",
    lg: "px-6 py-3 text-base",
  };

  // Modern pill style with new semantic colors
  const pillStyles = {
    Active: "bg-[#10B981]/10 text-semantic-success border border-semantic-success/30",
    Inactive: "bg-[#EF4444]/10 text-semantic-danger border border-semantic-danger/30",
    Pending: "bg-[#F59E0B]/10 text-semantic-warning border border-semantic-warning/30",
    Draft: "bg-text-subtle/10 text-text-body border border-border",
    Published: "bg-[#3B82F6]/10 text-semantic-info border border-semantic-info/30",
    Archived: "bg-primary/10 text-primary border border-primary/30",
  };

  const baseClasses = `
    ${sizeClasses[size]} 
    rounded-full font-medium transition-all duration-200 
    inline-flex items-center justify-center
  `;

  let colorClasses = "";

  if (type === "status") {
    colorClasses = pillStyles[status] || pillStyles.Draft;
  } else if (type === "type") {
    colorClasses = typeStyles[status] || typeStyles.User;
  }

  if (variant === "outline") {
    // Border already included in pillStyles
  } else if (variant === "solid") {
    // For solid variant, use solid backgrounds
    if (status === "Active") colorClasses = "bg-semantic-success text-white border-0";
    else if (status === "Inactive") colorClasses = "bg-semantic-danger text-white border-0";
    else if (status === "Pending") colorClasses = "bg-semantic-warning text-white border-0";
    else colorClasses = "bg-text-body text-white border-0";
  }

  return (
    <span className={`${baseClasses} ${colorClasses} ${className}`}>
      {status}
    </span>
  );
}
