import { DataTypes } from "sequelize";

const QbBalanceSheetLineItemModel = (sequelize) => {
  const BalanceSheetLineItem = sequelize.define(
    "BalanceSheetLineItem",
    {
      id: {
        type: DataTypes.BIGINT,
        primaryKey: true,
        autoIncrement: true,
      },
      report_id: {
        type: DataTypes.BIGINT,
        allowNull: false,
        references: {
          model: "qb_balance_sheet_reports",
          key: "id",
        },
      },
      path: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      account_name: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      account_id: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      amount: {
        type: DataTypes.DECIMAL(18, 2),
        allowNull: true,
        defaultValue: 0,
      },
      section: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      subsection: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      account_type: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      realm_id: {
        type: DataTypes.STRING,
        allowNull: false,
      },
    },
    {
      tableName: "qb_balance_sheet_line_items",
      timestamps: false,
    }
  );
  return BalanceSheetLineItem;
};

export default QbBalanceSheetLineItemModel;
