﻿# AUTH SERVICE ENVIRONMENT CONFIGURATION EXAMPLE
# Copy this file to .env and update the values
 
# SERVER CONFIGURATION
NODE_ENV=development
AUTH_SERVICE_PORT=3001
 
# CLIENT CONFIGURATION
USER_SERVICE_URL=your_user_service_url
ALLOWED_ORIGINS=your_allowed_origins

 
# DATABASE CONFIGURATION
DB_HOST=your_db_host
DB_PORT=your_db_port
DB_NAME=your_db_name
DB_USER=your_db_user
DB_PASS=your_db_password
DB_SSL=false
 
# AUTHENTICATION & SECURITY
# JWT Configuration
JWT_SECRET=your_jwt_secret
JWT_ACCESS_SECRET=your_jwt_access_secret
JWT_REFRESH_SECRET=your_jwt_refresh_secret
JWT_RESET_SECRET=your_jwt_reset_secret
JWT_ACCESS_EXPIRY=30d
JWT_REFRESH_EXPIRY=60d
JWT_RESET_EXPIRY=1h
 
# Session Configuration
SESSION_SECRET=your_session_secret
 
# COOKIE CONFIGURATION
COOKIE_MAX_AGE=3600000
COOKIE_DOMAIN=your_cookie_domain
 
# LOGGING CONFIGURATION
LOG_LEVEL=info
LOG_FORMAT=YYYY-MM-DD HH:mm:ss
LOG_FILE_PATH=logs/auth-service.log
LOG_MAX_SIZE=20m
LOG_MAX_FILES=14
LOG_ENABLE_CONSOLE=true

# EXTERNAL SERVICES (Optional)
FINANCIAL_SERVICE_URL=http://localhost:3005
OPERATIONAL_SERVICE_URL=http://localhost:3004
PAYROLL_SERVICE_URL=http://localhost:3006
