"use client";

import { memo } from "react";
import CustomSpinner from "../../common/CustomSpinner";
import MobileMenu from "./MobileMenu";
import DashboardSidebar from "../../sidebar/DashboardSidebar";

const MemoizedMobileMenu = memo(MobileMenu);
const MemoizedSidebar = DashboardSidebar;

export const DashboardLayout = memo(function DashboardLayout({
  children,
  header,
  isMobileMenuOpen,
  onMobileMenuToggle,
}) {
  return (
    <div className="flex flex-col lg:flex-row w-full h-full min-h-0 h-screen">
      <MemoizedMobileMenu
        isOpen={isMobileMenuOpen}
        onToggle={onMobileMenuToggle}
      >
        <MemoizedSidebar />
      </MemoizedMobileMenu>
      <div className="hidden lg:block w-56 xl:w-64 flex-shrink-0">
        <MemoizedSidebar />
      </div>
      <div className="flex-1 flex flex-col min-w-0 h-full">
        {header && <div className="flex-shrink-0">{header}</div>}
        <div className="flex-1 min-h-0 overflow-hidden">{children}</div>
      </div>
    </div>
  );
});

export const EmptyState = memo(function EmptyState({
  message,
  className = "",
}) {
  return (
    <div
      className={`flex-1 h-full flex flex-col items-center justify-center bg-gradient-to-br from-gray-50 to-gray-100 min-w-0 p-4 sm:p-6 ${className}`}
    >
      <div className="text-gray-600 text-xl font-semibold text-center max-w-md">
        {message}
      </div>
    </div>
  );
});

export const LoadingState = memo(function LoadingState({ tip = "" }) {
  return (
    <div className="flex-1 h-full flex flex-col items-center justify-center bg-gradient-to-br from-gray-50 to-gray-100 min-w-0 p-4 sm:p-6">
      <CustomSpinner tip={tip} size="large" />
    </div>
  );
});
