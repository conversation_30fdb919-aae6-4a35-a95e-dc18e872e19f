import {
  SIKKA_API,
  <PERSON>I<PERSON><PERSON>_MESSAGES,
  LOG_ACTIONS,
  ERROR_MESSAGES,
  MODEL_FIELDS,
  METHOD_TYPES,
  DATABASE_ERRORS,
  LOGGER_NAMES,
  API_URLS,
  SYNC_CONSTANTS,
  LOG_MESSAGES,
  CONFIG_DEFAULTS,
  SIKKA_CONSTANTS,
  CONTROLLER_MESSAGES,
} from "../utils/constants.util.js";
import {
  sikkaApiCall,
  validateAndThrowIfInvalid,
  findPracticeByOfficeId,
  validateAndGetPracticeCredentials,
  createRequestKeyPayload,
  formatRequestKeyResponse,
  fetchAuthorizedPractices,
  getModelsDirectory,
  getModelFiles,
  processModelFile,
} from "../utils/methods.util.js";
import { createLogger } from "../utils/logger.util.js";
import { sikkaRepository } from "../repositories/sikka.repository.js";
import { fileURLToPath } from "url";
import path from "path";
import sequelize from "../../config/postgres.js";
import { apiRequest } from "../../../../shared/utils/axios.util.js";
import { sendSikkaKpiReportsSyncFailureEmail } from "../utils/sikka.util.js";
import { BATCH_PROCESSING } from "../utils/constants.util.js";

const logger = createLogger(LOGGER_NAMES.SIKKA_SERVICE);
const app_id = process.env.SIKKA_APP_ID;
const app_key = process.env.SIKKA_APP_KEY;

const auth_url = process.env.AUTH_SERVICE_URL;

/**
 * Get authorized practices from Sikka API
 * @param {string|null} office_id - Optional office ID to find and validate specific practice
 * @returns {Object} Response with practices data and optionally the found practice
 */
export const getAuthorizedPractices = async (office_id) => {
  const practices_data = await fetchAuthorizedPractices(app_id, app_key);

  const practice = findPracticeByOfficeId(practices_data.data.items, office_id);
  if (!practice) {
    throw new Error(SIKKA_MESSAGES.NO_AUTHORIZED_PRACTICES);
  }

  return practice;
};

export const checkOfficeIdAssociationService = async (office_id) => {
  const response = await apiRequest(
    METHOD_TYPES.GET,
    `${auth_url}${API_URLS.ORGANIZATION_CHECK_OFFICE_ID_ASSOCIATION}${office_id}`
  );
  return response.data;
};

/**
 * Generate and store a new request key
 * This method encapsulates the complete business logic for request key generation
 * @param {string} office_id - Office ID
 * @param {string|null} schema_name - Optional schema name
 * @returns {Object} Created request key record
 */
export const generateRequestKey = async (office_id, schema_name) => {
  logger.info(LOG_ACTIONS.REQUESTING_KEY);

  // Validate credentials
  validateAndThrowIfInvalid(app_id, app_key);

  // Get authorized practices and find/validate practice
  const practice = await getAuthorizedPractices(office_id);
  const secret_key = validateAndGetPracticeCredentials(practice);

  // Create request key payload and call API
  const request_key_payload = createRequestKeyPayload({
    office_id,
    secret_key,
    app_id,
    app_key,
  });
  const request_key_data = await callRequestKeyAPI(request_key_payload);
  const formatted_data = formatRequestKeyResponse(request_key_data);
  const request_key_record = await storeRequestKey(formatted_data, schema_name);

  logger.info(LOG_ACTIONS.REQUEST_KEY_SUCCESS, {
    [MODEL_FIELDS.OFFICE_ID]: office_id,
  });

  return request_key_record;
};

/**
 * Call Sikka request key API
 * @param {Object} request_data - Request payload
 * @returns {Object} Response with request key data
 */
export const callRequestKeyAPI = (request_data) => {
  return sikkaApiCall(METHOD_TYPES.POST, SIKKA_API.ENDPOINTS.REQUEST_KEY, {
    data: request_data,
    successMessage: LOG_ACTIONS.REQUEST_KEY_SUCCESS,
    errorMessage: ERROR_MESSAGES.REQUEST_KEY_API_FAILED,
  });
};

/**
 * Store Sikka request key data in the database
 * @param {Object} params - { request_key, start_time, end_time, expires_in }
 * @param {string|null} schema_name - Optional schema name
 * @returns {Promise<Object>} Created or updated record
 */
export const storeRequestKey = async (params, schema_name) => {
  const existing_record = await sikkaRepository
    .findRequestKey(
      {
        office_id: params.office_id,
      },
      schema_name
    )
    .catch((error) => {
      // If table doesn't exist, log and return null to create new record
      logger.info(
        `${SIKKA_MESSAGES.COULD_NOT_FIND_REQUEST_KEY} ${error.message}`
      );
      return null;
    });

  if (existing_record) {
    return await sikkaRepository.updateRequestKey(
      existing_record.id,
      params,
      schema_name
    );
  }

  return await sikkaRepository.createRequestKey(params, schema_name);
};

/**
 * Get request key from database
 * @param {string} office_id - Office ID
 * @param {string|null} schema_name - Optional schema name
 * @returns {Promise<Object>} Request key data or empty object if not found
 */
export const getRequestKey = async (office_id, schema_name) => {
  const request_key_record = await sikkaRepository
    .findRequestKey({ office_id }, schema_name)
    .catch((error) => {
      // If table doesn't exist in schema, return null (triggers key generation)
      // This is expected behavior when querying a schema for the first time
      if (
        error.message &&
        error.message.includes(DATABASE_ERRORS.RELATION) &&
        error.message.includes(DATABASE_ERRORS.DOES_NOT_EXIST)
      ) {
        logger.info(
          `${SIKKA_MESSAGES.REQUEST_KEY_TABLE_NOT_EXIST} ${schema_name}, ${SIKKA_MESSAGES.WILL_GENERATE_NEW_KEY}`
        );
        return null;
      }
      // For other errors, rethrow
      throw error;
    });

  if (!request_key_record) {
    return {};
  }

  return {
    request_key: request_key_record.get(MODEL_FIELDS.REQUEST_KEY),
    end_time: request_key_record.get(MODEL_FIELDS.END_TIME),
  };
};

/**
 * Ensure request key is available and valid, generating a new one if needed
 * @param {string} office_id - Office ID
 * @param {string|null} schema_name - Optional schema name
 * @returns {Promise<void>}
 */
export const ensureValidRequestKey = async (office_id, schema_name) => {
  logger.info(CONTROLLER_MESSAGES.CHECKING_REQUEST_KEY_AVAILABILITY);
  const { request_key, end_time } = await getRequestKey(office_id, schema_name);

  // Calculate expiry buffer time to proactively refresh key before expiration
  const expiry_buffer_time = new Date(
    Date.now() + CONFIG_DEFAULTS.EXPIRY_BUFFER_MS
  );

  // Generate new key if: no key exists, expired, or expires within buffer time
  if (!request_key || !end_time || end_time < expiry_buffer_time) {
    logger.info(SIKKA_CONSTANTS.REQUEST_KEY_EXPIRED_MESSAGE);
    await generateRequestKey(office_id, schema_name);
    logger.info(CONTROLLER_MESSAGES.REQUEST_KEY_GENERATED_SUCCESSFULLY);
  }
};

/**
 * Fetch information about all Sequelize models in the tables directory
 * @returns {Promise<Object>} Models information including table names, columns, types, and associations
 */
export const fetchAllModelsInfo = async () => {
  logger.info(LOG_ACTIONS.GETTING_MODELS_INFO);

  const __filename = fileURLToPath(import.meta.url);
  const __dirname = path.dirname(__filename);
  const models_dir = getModelsDirectory(__dirname);

  const model_files = getModelFiles(models_dir);
  const models_info = {};

  for (const model_file_name of model_files) {
    const model_info = await processModelFile(
      models_dir,
      model_file_name,
      sequelize,
      logger
    );
    if (model_info) {
      models_info[model_file_name] = model_info;
    }
  }

  return {
    [MODEL_FIELDS.TOTAL_MODELS]: Object.keys(models_info).length,
    [MODEL_FIELDS.MODELS]: models_info,
    [MODEL_FIELDS.TIMESTAMP]: new Date().toISOString(),
  };
};

/**
 * Process KPI batch sync results and send notifications
 * @param {Array} results - Array of KPI sync results
 * @returns {Promise<Object>} Object with success_count, fail_count, and failed_apis
 */
export const processKpiBatchSyncResults = async (results) => {
  const success_count = results.filter(
    (result) => result.status === BATCH_PROCESSING.STATUS_SUCCESS
  ).length;

  const fail_count = results.filter(
    (result) => result.status === BATCH_PROCESSING.STATUS_FAIL
  ).length;

  const failed_apis = results
    .filter((result) => result.status === BATCH_PROCESSING.STATUS_FAIL)
    .map((result) => ({
      [BATCH_PROCESSING.FIELD_NAME]: result.name,
      [BATCH_PROCESSING.FIELD_ERROR]: result.error,
    }));

  if (fail_count > 0) {
    await sendSikkaKpiReportsSyncFailureEmail(failed_apis);
  }

  return {
    success_count,
    fail_count,
    failed_apis,
  };
};

/**
 * Sync organization status after successful KPI sync
 * @param {string} office_id - Office ID
 * @param {string} end_date - End date for lastSyncedAt
 * @returns {Promise<void>}
 */
export const syncOrganizationStatus = async (
  last_synced_at,
  organization_id
) => {
  // Update organization sync status
  await apiRequest(
    METHOD_TYPES.PUT,
    `${auth_url}${API_URLS.ORGANIZATION_SYNC_BASE}${organization_id}`,
    {
      type: SYNC_CONSTANTS.TYPE_SIKKA,
      lastSyncedAt: last_synced_at,
    }
  );

  logger.info(
    `${LOG_MESSAGES.ORGANIZATION_SYNC_STATUS_UPDATED} ${organization_id} ${LOG_MESSAGES.WITH_LAST_SYNCED_AT} ${last_synced_at}`
  );
};
