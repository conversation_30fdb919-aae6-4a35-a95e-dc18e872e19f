import { createAsyncThunk } from "@reduxjs/toolkit";
import api from "@/redux/ApiService/ApiService";
import { SERVICE_PORTS } from "@/utils/constants/api";

const axios = api(SERVICE_PORTS.REPORT);

export const fetchBalanceSheet = createAsyncThunk(
  "reports/fetchBalanceSheet",
  async ({ org_id, month, year }, { rejectWithValue }) => {
    try {
      if (!org_id || !month || !year) {
        return rejectWithValue("org_id, month, and year are required");
      }

      const params = { org_id, month, year };
      const response = await axios.get("/report/finance/balance-sheet", {
        params,
      });
      return response.data;
    } catch (error) {
      const message =
        error.response?.data?.message ||
        error.message ||
        "Failed to fetch balance sheet";
      return rejectWithValue(message);
    }
  }
);

export const fetchIncomeExpenseData = createAsyncThunk(
  "reports/fetchIncomeExpenseData",
  async ({ org_id, month, year }, { rejectWithValue }) => {
    try {
      if (!org_id || !month || !year) {
        return rejectWithValue("org_id, month, and year are required");
      }

      const params = { organization_id: org_id, month, year };
      const response = await axios.get("/report/finance/income-expense-statement", {
        params,
      });
      return response.data;
    } catch (error) {
      const message =
        error.response?.data?.message ||
        error.message ||
        "Failed to fetch balance sheet";
      return rejectWithValue(message);
    }
  }
);

export const fetchKpisData = createAsyncThunk(
  "reports/kpisData",
  async ({ org_id, month, year }, { rejectWithValue }) => {
    try {
      if (!org_id || !month || !year) {
        return rejectWithValue("org_id, month, and year are required");
      }

      const params = { organization_id: org_id, month, year };
      const response = await axios.get("/report/finance/kpis", {
        params,
      });
      return response.data;
    } catch (error) {
      const message =
        error.response?.data?.message ||
        error.message ||
        "Failed to fetch balance sheet";
      return rejectWithValue(message);
    }
  }
);

export const fetchCashFlowData = createAsyncThunk(
  "reports/fetchCashFlowData",
  async ({ org_id, month, year }, { rejectWithValue }) => {
    try {
      if (!org_id || !month || !year) {
        return rejectWithValue("org_id, month, and year are required");
      }

      const params = { organization_id: org_id, month, year };
      const response = await axios.get("/report/finance/cash-flow", {
        params,
      });
      return response.data;
    } catch (error) {
      const message =
        error.response?.data?.message ||
        error.message ||
        "Failed to fetch balance sheet";
      return rejectWithValue(message);
    }
  }
);

export const fetchRevenueExpenseData = createAsyncThunk(
  "reports/fetchRevenueExpenseData",
  async ({ org_id, month, year }, { rejectWithValue }) => {
    try {
      if (!org_id || !month || !year) {
        return rejectWithValue("org_id, month, and year are required");
      }

      const params = { organization_id: org_id, month, year };
      const response = await axios.get("/report/finance/revenue-expense", {
        params,
      });
      return response.data;
    } catch (error) {
      const message =
        error.response?.data?.message ||
        error.message ||
        "Failed to fetch balance sheet";
      return rejectWithValue(message);
    }
  }
);

export const fetchExpenseBreakdownData = createAsyncThunk(
  "reports/fetchExpenseBreakdownData",
  async ({ org_id, month, year }, { rejectWithValue }) => {
    try {
      if (!org_id || !month || !year) {
        return rejectWithValue("org_id, month, and year are required");
      }

      const params = { organization_id: org_id, month, year };
      const response = await axios.get("/report/finance/expense-breakdown", {
        params,
      });
      return response.data;
    } catch (error) {
      const message =
        error.response?.data?.message ||
        error.message ||
        "Failed to fetch balance sheet";
      return rejectWithValue(message);
    }
  }
);

export const fetchPayrollKpis = createAsyncThunk(
  "reports/fetchPayrollKpis",
  async ({ org_id, month, year }, { rejectWithValue }) => {
    try {
      if (!org_id || !month || !year) {
        return rejectWithValue("org_id, month, and year are required");
      }

      const params = { organization_id: org_id, month, year };
      const response = await axios.get("/report/payroll/kpis", {
        params,
      });
      return response.data;
    } catch (error) {
      const message =
        error.response?.data?.message ||
        error.message ||
        "Failed to fetch payroll KPIs";
      return rejectWithValue(message);
    }
  }
);

export const fetchPayrollTaxBreakdown = createAsyncThunk(
  "reports/fetchPayrollTaxBreakdown",
  async ({ org_id, month, year }, { rejectWithValue }) => {
    try {
      if (!org_id || !month || !year) {
        return rejectWithValue("org_id, month, and year are required");
      }

      const params = { organization_id: org_id, month, year };
      const response = await axios.get("/report/payroll/tax-breakdown", {
        params,
      });
      return response.data;
    } catch (error) {
      const message =
        error.response?.data?.message ||
        error.message ||
        "Failed to fetch tax breakdown";
      return rejectWithValue(message);
    }
  }
);

export const fetchPayrollDeductionsBreakdown = createAsyncThunk(
  "reports/fetchPayrollDeductionsBreakdown",
  async ({ org_id, month, year }, { rejectWithValue }) => {
    try {
      if (!org_id || !month || !year) {
        return rejectWithValue("org_id, month, and year are required");
      }

      const params = { organization_id: org_id, month, year };
      const response = await axios.get("/report/payroll/deductions-breakdown", {
        params,
      });
      return response.data;
    } catch (error) {
      const message =
        error.response?.data?.message ||
        error.message ||
        "Failed to fetch deductions breakdown";
      return rejectWithValue(message);
    }
  }
);

export const fetchPayrollSalaryByDepartment = createAsyncThunk(
  "reports/fetchPayrollSalaryByDepartment",
  async ({ org_id, month, year }, { rejectWithValue }) => {
    try {
      if (!org_id || !month || !year) {
        return rejectWithValue("org_id, month, and year are required");
      }

      const params = { organization_id: org_id, month, year };
      const response = await axios.get("/report/payroll/salary-by-department", {
        params,
      });
      return response.data;
    } catch (error) {
      const message =
        error.response?.data?.message ||
        error.message ||
        "Failed to fetch salary by department";
      return rejectWithValue(message);
    }
  }
);

export default fetchBalanceSheet;
