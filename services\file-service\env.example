# Azure Blob Storage Configuration
AZURE_STORAGE_CONNECTION_STRING=your_azure_storage_connection_string
AZURE_BLOB_CONTAINER_NAME=your_azure_blob_container_name

# Server Configuration
PORT=3008
NODE_ENV=development

FILE_SERVICE_URL=your_file_service_url

# Chat Service Configuration
CHAT_SERVICE_API_URL=http://localhost:3007/api

# Power BI Workflow URLs (optional full URLs including signatures per service)
POWER_BI_FINANCE_WORKFLOW_URL=
POWER_BI_OPERATIONS_WORKFLOW_URL=
POWER_BI_PAYROLL_WORKFLOW_URL=

# System API Key (used for Power BI workflow authentication)
SYSTEM_API_KEY=

# Auth Service Configuration
AUTH_SERVICE_BASE_URL=http://localhost:3001

# Database Configuration
DB_HOST=your_db_host
DB_PORT=your_db_port
DB_NAME=your_db_name
DB_PASS=your_db_password
DB_USER=your_db_user
DB_DIALECT=postgres

# CORS Configuration
CORS_ORIGIN=your_cors_origin

# Logging Configuration
LOG_LEVEL=info
 