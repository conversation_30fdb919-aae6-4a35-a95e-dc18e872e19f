"use client";

import {
  createContext,
  useContext,
  useEffect,
  useMemo,
  useCallback,
  useRef,
} from "react";
import { useSelector, useDispatch } from "react-redux";
import { useRouter } from "next/navigation";
import {
  login as loginThunk,
  logout as logoutThunk,
  refreshAuth,
  getProfile,
} from "@/redux/Thunks/Authentication";
import { getUserById } from "@/redux/Thunks/userThunks";
import {
  addFailedUserId,
  isFailedUserId,
  clearFailedUserIdsCache,
} from "@/redux/ApiService/ApiService";
import { clearError } from "@/redux/Slice/Authentication";
import { clearCurrentUser } from "@/redux/Slice/userSlice";
import tokenStorage from "@/lib/tokenStorage";
import { ROLES } from "@/utils/const";
import { useToast } from "@/components/ui/toast";

const AuthContext = createContext({});

export const AuthProvider = ({ children }) => {
  const dispatch = useDispatch();
  const router = useRouter();
  const { addToast } = useToast();

  // Add safety checks for Redux state - handle cases where state might not be initialized
  const authState = useSelector((state) => state?.auth);
  const usersState = useSelector((state) => state?.users);

  const { user, loading, error } = authState || {};
  const {
    currentUser: fullUserDetails,
    loading: usersLoading,
    error: usersError,
  } = usersState || {};

  // Get user data from JWT token if available
  const userFromToken = tokenStorage.getUserDataFromToken();
  const sourceUser = userFromToken || user;
  const userId = sourceUser?.id;
  const isFullUserDetailsValid = fullUserDetails?.id === userId;

  // Use full user details only if it matches current userId, otherwise use token/auth data
  const currentUser = isFullUserDetailsValid
    ? fullUserDetails
    : userFromToken || user;
  const currentIsAuthenticated = !!(
    tokenStorage.getAccessToken() && currentUser
  );

  // Clear stale fullUserDetails when userId changes
  useEffect(() => {
    if (fullUserDetails && userId && fullUserDetails.id !== userId) {
      // Clear stale user details when userId changes
      dispatch(clearCurrentUser());
    }
  }, [dispatch, userId, fullUserDetails]);

  const lastRequestedUserIdRef = useRef(null);
  const failedUserIdsRef = useRef(new Set());

  useEffect(() => {
    const hasToken = tokenStorage.getAccessToken();
    const shouldFetch =
      hasToken &&
      userId &&
      !isFullUserDetailsValid &&
      !loading &&
      !usersLoading;

    if (!shouldFetch) return;

    // Check both local and global cache BEFORE making request
    if (failedUserIdsRef.current.has(userId) || isFailedUserId(userId)) {
      return;
    }

    // Check if we already requested this userId and got an error
    if (lastRequestedUserIdRef.current === userId && usersError) {
      const isNotFoundError =
        usersError &&
        (usersError.includes("not found") ||
          usersError.includes("404") ||
          usersError === "User not found");
      if (isNotFoundError) {
        failedUserIdsRef.current.add(userId);
        addFailedUserId(userId);
        return;
      }
    }

    // Prevent duplicate requests for the same userId
    if (lastRequestedUserIdRef.current === userId) {
      return;
    }

    lastRequestedUserIdRef.current = userId;

    // Use unwrap() to properly catch errors and prevent console errors
    dispatch(getUserById(userId))
      .unwrap()
      .catch((error) => {
        // Silently handle 404 errors - user doesn't exist
        if (
          error?.includes("not found") ||
          error?.includes("404") ||
          error === "User not found"
        ) {
          failedUserIdsRef.current.add(userId);
          addFailedUserId(userId);
        }
      });
  }, [
    dispatch,
    userId,
    isFullUserDetailsValid,
    loading,
    usersLoading,
    usersError,
  ]);

  // Only check profile if we have a token but no user data from token
  useEffect(() => {
    const hasToken = tokenStorage.getAccessToken();
    const shouldFetchProfile =
      hasToken && !userFromToken && !loading && !isFullUserDetailsValid;

    if (shouldFetchProfile) {
      dispatch(getProfile());
    }
  }, [dispatch, userFromToken, loading, isFullUserDetailsValid]);

  const login = useCallback(
    async (credentials) => {
      try {
        const result = await dispatch(loginThunk(credentials)).unwrap();
        // Fetch full user details after successful login
        if (result?.id) {
          dispatch(getUserById(result.id));
        }
        return { success: true, data: result };
      } catch (error) {
        return { success: false, message: error };
      }
    },
    [dispatch]
  );

  const logout = useCallback(async () => {
    try {
      await dispatch(logoutThunk());
      // Clear full user details on logout
      dispatch(clearCurrentUser());
      // Clear failed user IDs cache on logout
      clearFailedUserIdsCache();
      router.push("/login");
    } catch (_error) {
      addToast(
        "We couldn't contact the server. You've been logged out locally.",
        "warning"
      );
      // Force logout even if API call fails
      dispatch(clearCurrentUser());
      clearFailedUserIdsCache();
      router.push("/login");
    }
  }, [dispatch, router, addToast]);

  const refreshToken = useCallback(async () => {
    try {
      await dispatch(refreshAuth()).unwrap();
      // After successful refresh, get updated user data from new token
      const updatedUserFromToken = tokenStorage.getUserDataFromToken();
      // Fetch full user details if user ID is available
      if (updatedUserFromToken?.id) {
        dispatch(getUserById(updatedUserFromToken.id));
      }
      return true;
    } catch (_error) {
      return false;
    }
  }, [dispatch]);

  const clearAuthError = useCallback(() => {
    dispatch(clearError());
  }, [dispatch]);

  // Get current user role
  const userRole = useMemo(() => {
    return currentUser?.role || null;
  }, [currentUser]);

  const isAdmin = useMemo(() => {
    return userRole === ROLES.ADMIN;
  }, [userRole]);

  const authValue = useMemo(
    () => ({
      user: currentUser,
      userRole,
      isAuthenticated: currentIsAuthenticated,
      isAdmin,
      loading,
      error,
      login,
      logout,
      refreshToken,
      clearAuthError,
    }),
    [
      currentUser,
      userRole,
      currentIsAuthenticated,
      isAdmin,
      loading,
      error,
      login,
      logout,
      refreshToken,
      clearAuthError,
    ]
  );

  return (
    <AuthContext.Provider value={authValue}>{children}</AuthContext.Provider>
  );
};

export const useAuthContext = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error("useAuthContext must be used within an AuthProvider");
  }
  return context;
};

export default AuthProvider;
