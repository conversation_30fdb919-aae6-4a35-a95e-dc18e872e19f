/**
 * Chat Constants
 * Centralized configuration for chat functionality
 */

export const CFO_ENDPOINT = process.env.NEXT_PUBLIC_CFO_SERVICE_URL;

// API Configuration
// REFACTORED: No fallback operators - assumes env vars are validated externally
export const CHAT_API_CONFIG = {
  BASE_URL: process.env.NEXT_PUBLIC_CFO_SERVICE_URL,
  TIMEOUT: 30000,
  RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000,
  ENDPOINTS: {
    START: "/chat/start",
    MESSAGE: "/chat/message",
    SUMMARY: "/chat/summary",
  },
};

// Default Values
export const CHAT_DEFAULTS = {
  ORGANIZATION: "SmileCare Dental Group",
  DASHBOARD_SUMMARY_MESSAGE: "Provide the summary of this dashboard.",
  DEFAULT_MONTH: "August",
};

export const CHAT_DATE_CONSTANTS = {
  CURRENT_YEAR: new Date().getFullYear(),
  DEFAULT_YEAR: new Date().getFullYear(),
};

// Message Types
export const MESSAGE_TYPES = {
  USER: "user",
  AI: "ai",
  SYSTEM: "system",
};

// Error Messages
export const CHAT_ERRORS = {
  START_SESSION_FAILED: "Failed to start chat session",
  SEND_MESSAGE_FAILED: "Failed to send message",
  GET_SUMMARY_FAILED: "Failed to get dashboard summary",
  GENERIC_ERROR:
    "Sorry, I encountered an error while processing your question. Please try again.",
  VALIDATION_ERRORS: {
    FILENAME_REQUIRED: "Valid filename is required to start chat session",
    SESSION_ID_REQUIRED: "Valid session ID is required",
    MESSAGE_EMPTY: "Message cannot be empty",
    MESSAGE_TOO_LONG:
      "Message is too long. Please keep it under 2000 characters.",
    FILENAME_INVALID: "Unable to determine filename for chat session",
    SESSION_ID_INVALID: "Invalid session ID received from server",
    NO_AI_RESPONSE: "No response received from AI",
  },
  NETWORK_ERRORS: {
    TIMEOUT: "Request timeout. Please try again.",
    SERVICE_UNAVAILABLE: "Chat service not available. Please try again later.",
    SERVER_ERROR: "Server error. Please try again later.",
    SESSION_NOT_FOUND: "Chat session not found. Please start a new chat.",
    RATE_LIMITED:
      "Too many requests. Please wait a moment before trying again.",
  },
};

// Static Messages
export const CHAT_MESSAGES = {
  WELCOME: `Welcome to CFO Insights! 👋

I'm analyzing **{organizationName}**{possessive} {serviceType} Dashboard – **{selectedMonth} {currentYear}**.

How can I assist you in exploring your {serviceTypeLower} insights today?`,

  DASHBOARD_SUMMARY_REQUEST: "Provide the summary of this dashboard or file",
  PLACEHOLDER: "Ask a question about your financial documents...",

  BUTTON_TEXT: {
    SEND: "Send",
    FINISH_CHAT: "Finish Chat",
  },

  UI_TEXT: {
    CHARACTER_COUNT: "{current}/{max}",
    TYPING: "Typing...",
    THINKING: "Thinking...",
    START_CONVERSATION:
      "Start a conversation by asking a question about your financial documents",
  },
};

export const CHAT_MONTH_MAP = {
  ABBREVIATIONS_TO_FULL: {
    Jan: "January",
    Feb: "February",
    Mar: "March",
    Apr: "April",
    May: "May",
    Jun: "June",
    Jul: "July",
    Aug: "August",
    Sep: "September",
    Oct: "October",
    Nov: "November",
    Dec: "December",
  },
  NUMERIC_TO_FULL: {
    "01": "January", "1": "January",
    "02": "February", "2": "February",
    "03": "March", "3": "March",
    "04": "April", "4": "April",
    "05": "May", "5": "May",
    "06": "June", "6": "June",
    "07": "July", "7": "July",
    "08": "August", "8": "August",
    "09": "September", "9": "September",
    "10": "October",
    "11": "November",
    "12": "December",
  },
};

export const CHAT_REGEX = {
  YEAR_SUFFIX: /^\d{2,4}$/,
  INLINE_YEAR: /\b20\d{2}\b/,
  DIGIT_SEQUENCE: /\d+/g,
  COMPARISON_KEYWORDS: /(compare|vs|versus|competitor|benchmark|against)/i,
  ORG_NAME_WRAPPING_QUOTES: /^['"]|['"]$/g,
};
