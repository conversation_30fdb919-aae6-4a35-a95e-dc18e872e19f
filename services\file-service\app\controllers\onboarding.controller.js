import logger from "../../config/logger.config.js";
import {
  ONBOARDING_LOG_MESSAGES,
  ONBOARDING_MESSAGES,
} from "../utils/constants/onboarding.constants.js";
import {
  uploadOrganizationLogoService,
  uploadOrganizationFileService,
} from "../services/onboarding.service.js";
import {
  handleControllerError,
  handleServiceResponse,
} from "../utils/controllerHandler.util.js";

/**
 * Unified file upload controller
 * Handles both upload types from single route:
 * 1. Logo uploads: type: "logo"
 * 2. Report uploads: type: "report"
 */
export const uploadFile = async (req, res) => {
  logger.info(ONBOARDING_LOG_MESSAGES.CONTROLLER_UPLOAD_START);

  try {
    const { orgId, type, orgName, service, year, month } = req.body;

    // Determine upload type (case-insensitive)
    const isLogoUpload = type?.toLowerCase?.() === "logo";

    if (isLogoUpload) {
      // Logo upload
      const serviceResponse = await uploadOrganizationLogoService({
        orgId,
        file: req.file,
      });

      return handleServiceResponse(
        serviceResponse,
        res,
        ONBOARDING_LOG_MESSAGES.CONTROLLER_UPLOAD_SUCCESS,
        ONBOARDING_LOG_MESSAGES.CONTROLLER_UPLOAD_FAILED
      );
    } else {
      // Report upload
      const serviceResponse = await uploadOrganizationFileService({
        orgId,
        orgName,
        service,
        year,
        month,
        file: req.file,
      });

      return handleServiceResponse(
        serviceResponse,
        res,
        ONBOARDING_LOG_MESSAGES.CONTROLLER_UPLOAD_SUCCESS,
        ONBOARDING_LOG_MESSAGES.CONTROLLER_UPLOAD_FAILED
      );
    }
  } catch (error) {
    return handleControllerError(
      error,
      res,
      ONBOARDING_LOG_MESSAGES.CONTROLLER_UPLOAD_ERROR,
      ONBOARDING_MESSAGES.FILE_UPLOAD_FAILED
    );
  }
};
