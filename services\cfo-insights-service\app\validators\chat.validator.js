import { CHAT_CONSTANTS } from "../utils/constants/chat.constants.js";
import { ERROR_MESSAGES } from "../utils/constants/error.constants.js";
import {
  HTTP_STATUS,
  buildErrorResponse,
} from "../utils/constants/http.constants.js";

export const validateStartChat = (req, res, next) => {
  const {
    filename,
    orgId,
    orgid,
    orgName,
    organization,
    service,
    month,
    year,
    blobStoragePath,
    blobBasePath,
  } = req.body;

  const hasBlobPath = !!blobStoragePath || !!blobBasePath;

  if (!filename || typeof filename !== "string" || !filename.trim()) {
    return res
      .status(HTTP_STATUS.BAD_REQUEST)
      .json(buildErrorResponse(ERROR_MESSAGES.CHAT.FILENAME_REQUIRED, HTTP_STATUS.BAD_REQUEST));
  }

  if (!hasBlobPath) {
    const resolvedOrgId = (orgId || orgid || "").trim();
    const resolvedOrgName = (orgName || organization || "").trim();

    if (!resolvedOrgId) {
      return res
        .status(HTTP_STATUS.BAD_REQUEST)
        .json(buildErrorResponse(ERROR_MESSAGES.VALIDATION.ORG_ID_REQUIRED, HTTP_STATUS.BAD_REQUEST));
    }

    if (!resolvedOrgName) {
      return res
        .status(HTTP_STATUS.BAD_REQUEST)
        .json(buildErrorResponse(ERROR_MESSAGES.VALIDATION.ORG_NAME_REQUIRED, HTTP_STATUS.BAD_REQUEST));
    }

    if (!service || typeof service !== "string" || !service.trim()) {
      return res
        .status(HTTP_STATUS.BAD_REQUEST)
        .json(buildErrorResponse(ERROR_MESSAGES.VALIDATION.FOLDER_REQUIRED, HTTP_STATUS.BAD_REQUEST));
    }

    if (
      month === undefined ||
      month === null ||
      Number.isNaN(Number.parseInt(month, 10))
    ) {
      return res
        .status(HTTP_STATUS.BAD_REQUEST)
        .json(buildErrorResponse(ERROR_MESSAGES.VALIDATION.MONTH_REQUIRED, HTTP_STATUS.BAD_REQUEST));
    }

    if (
      year === undefined ||
      year === null ||
      Number.isNaN(Number.parseInt(year, 10))
    ) {
      return res
        .status(HTTP_STATUS.BAD_REQUEST)
        .json(buildErrorResponse(ERROR_MESSAGES.VALIDATION.YEAR_REQUIRED, HTTP_STATUS.BAD_REQUEST));
    }

    const monthNum = Number.parseInt(month, 10);
    const yearNum = Number.parseInt(year, 10);

    if (monthNum < CHAT_CONSTANTS.MONTH_MIN || monthNum > CHAT_CONSTANTS.MONTH_MAX) {
      return res
        .status(HTTP_STATUS.BAD_REQUEST)
        .json(buildErrorResponse(ERROR_MESSAGES.VALIDATION.MONTH_INVALID, HTTP_STATUS.BAD_REQUEST));
    }

    if (yearNum < CHAT_CONSTANTS.YEAR_MIN || yearNum > CHAT_CONSTANTS.YEAR_MAX) {
      return res
        .status(HTTP_STATUS.BAD_REQUEST)
        .json(buildErrorResponse(ERROR_MESSAGES.VALIDATION.YEAR_INVALID, HTTP_STATUS.BAD_REQUEST));
    }
  }

  return next();
};

