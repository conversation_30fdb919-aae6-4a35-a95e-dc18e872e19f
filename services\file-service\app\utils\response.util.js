// app/utils/response.util.js

/**
 * Create a success response
 * @param {string} message - Success message
 * @param {any} data - Response data (optional)
 * @returns {Object} Success response object
 */
export const successResponse = (message, data = null) => {
  const response = {
    success: true,
    message,
    timestamp: new Date().toISOString(),
  };
  
  // Only include data if it's not null or empty string
  if (data !== null && data !== undefined && data !== "") {
    response.data = data;
  }
  
  return response;
};

/**
 * Create an error response
 * @param {string} message - Error message
 * @param {any} error - Error details
 * @returns {Object} Error response object
 */
export const errorResponse = (message, error = null) => {
  const response = {
    success: false,
    message,
    timestamp: new Date().toISOString(),
  };
  
  if (error) {
    response.error = error;
  }
  
  return response;
};

