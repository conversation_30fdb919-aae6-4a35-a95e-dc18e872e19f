import express from "express";
import * as sikkaController from "../controllers/sikka.controller.js";

const router = express.Router();

// Check connection to Sikka Account
router.post("/check-connection", sikkaController.checkConnection);

// Request API key from Sikka
router.post("/request-key", sikkaController.requestKey);

// Get information about all Sequelize models
router.get("/tables", sikkaController.getTables);

// Sync all KPI reports
router.post("/sync-all-reports", sikkaController.syncAllKpiReports);

export default router;
