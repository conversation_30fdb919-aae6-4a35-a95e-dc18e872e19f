import multer from "multer";

export const createMemoryUploadMiddleware = ({
  fieldName,
  allowedMimeTypes = new Set(),
  fileSizeLimitBytes,
  invalidTypeMessage = "Invalid file type",
  fileTooLargeMessage = "File is too large",
  statusCodeBadRequest = 400,
  errorResponseBuilder = (message, error) => ({
    success: false,
    message,
    error,
  }),
}) => {
  const upload = multer({
    storage: multer.memoryStorage(),
    limits: fileSizeLimitBytes ? { fileSize: fileSizeLimitBytes } : undefined,
    fileFilter: (_req, file, cb) => {
      if (!allowedMimeTypes.size || allowedMimeTypes.has(file.mimetype)) {
        cb(null, true);
      } else {
        cb(new Error(invalidTypeMessage), false);
      }
    },
  });

  return (req, res, next) => {
    upload.single(fieldName)(req, res, (err) => {
      if (err) {
        const message =
          err.code === "LIMIT_FILE_SIZE"
            ? fileTooLargeMessage
            : invalidTypeMessage;
        return res
          .status(statusCodeBadRequest)
          .json(errorResponseBuilder(message, err.message));
      }
      next();
    });
  };
};
