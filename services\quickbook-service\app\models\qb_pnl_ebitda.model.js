import { DataTypes } from "sequelize";

const QbPnlEbitdaModel = (sequelize) => {
  const PnlEbitda = sequelize.define(
    "PnlEbitda",
    {
      id: { type: DataTypes.BIGINT, primaryKey: true, autoIncrement: true },
      report_id: { type: DataTypes.BIGINT, allowNull: false },
      net_income: { type: DataTypes.DECIMAL(18, 2), defaultValue: 0 },
      interest_expense: { type: DataTypes.DECIMAL(18, 2), defaultValue: 0 },
      income_taxes: { type: DataTypes.DECIMAL(18, 2), defaultValue: 0 },
      depreciation: { type: DataTypes.DECIMAL(18, 2), defaultValue: 0 },
      amortization: { type: DataTypes.DECIMAL(18, 2), defaultValue: 0 },
      ebit: { type: DataTypes.DECIMAL(18, 2), defaultValue: 0 },
      ebitda: { type: DataTypes.DECIMAL(18, 2), defaultValue: 0 },
      created_at: { type: DataTypes.DATE, defaultValue: DataTypes.NOW },
    },
    {
      tableName: "qb_pnl_ebitda",
      timestamps: false,
    }
  );

  return PnlEbitda;
};

export default QbPnlEbitdaModel;

