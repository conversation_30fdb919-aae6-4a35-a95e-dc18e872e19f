import { chromium } from "playwright";
import { createLogger } from "./logger.utils.js";
import { LOGGER_NAMES } from "./constants/log.constants.js";
import { MONTHS, formatCurrency } from "./constants/pdf.constants.js";
import {
  convertAbsValueToThousands,
  parseNumericString,
} from "./numeric.util.js";
import {
  generateChartTableHTML,
  generateTaxTableHTML,
  generateSalaryCompTableHTML,
  generateHeadcountTableHTML,
  generateOperationsTableHTML,
  generateBalanceSheetHTML,
  generateIncomeExpenseHTML,
  generateTableWithBody,
} from "./html.util.js";
import {
  generateChartConfig,
  generateChartCanvas,
  generateChartInitScript,
} from "./chart.util.js";
import {
  transformBalanceSheetData,
  transformIncomeExpenseData,
} from "./transform.util.js";

const logger = createLogger(LOGGER_NAMES.REPORT_SERVICE);

const generateHTML = (data) => {
  const month = MONTHS[parseInt(data.month) - 1] || "";
  const kpi = data.kpi || {};

  const cashFlow = data.cashFlow || [];
  const chart1Labels = cashFlow.map((cashFlowItem) => cashFlowItem.category);
  const chart1Data = cashFlow.map((cashFlowItem) =>
    convertAbsValueToThousands(cashFlowItem.total)
  );

  const expenseBreakdown = data.expenseBreakdown || [];
  const revenueExpense = data.revenueExpense || {};
  const chart2Labels = ["Revenue", "Expenses", "Net Income"];
  const chart2Data = [
    parseNumericString(revenueExpense.revenue, "0"),
    parseNumericString(revenueExpense.expense, "0"),
    parseNumericString(revenueExpense.net_income, "0"),
  ];
  const chart3Labels = expenseBreakdown
    .slice(0, 5)
    .map((expenseItem) => expenseItem.category);
  const chart3Data = expenseBreakdown
    .slice(0, 5)
    .map((expenseItem) => parseNumericString(expenseItem.amount, "0"));

  const chart1TableHTML = generateChartTableHTML(chart1Labels, chart1Data, [
    "Category",
    "Amount",
  ]);
  const chart2TableHTML = generateChartTableHTML(chart2Labels, chart2Data, [
    "Item",
    "Amount",
  ]);
  const chart3TableHTML = generateChartTableHTML(chart3Labels, chart3Data, [
    "Category",
    "Amount",
  ]);

  // Transform data structures to match HTML generator expectations
  const transformedBalanceSheet = transformBalanceSheetData(data.balanceSheet || []);
  const transformedIncomeExpense = transformIncomeExpenseData(data.incomeExpense || []);

  const balanceSheetHTML = generateBalanceSheetHTML(
    transformedBalanceSheet,
    formatCurrency
  );
  const incomeExpenseHTML = generateIncomeExpenseHTML(
    transformedIncomeExpense,
    formatCurrency
  );

  const chart1Colors = cashFlow.map((cashFlowItem) =>
    cashFlowItem.total < 0 ? "#ef4444" : "#10b981"
  );
  const chart1Config = generateChartConfig(
    "bar",
    chart1Labels,
    chart1Data,
    chart1Colors,
    {
      plugins: {
        legend: { display: false },
        datalabels: {
          color: "#1e293b",
          font: { weight: "bold", size: 13 },
          formatter: (value) => "$" + value.toFixed(1) + "K",
        },
      },
      scales: {
        y: {
          beginAtZero: true,
          ticks: {
            font: { size: 12 },
            callback: (tickValue) => "$" + tickValue + "K",
          },
        },
        x: { ticks: { font: { size: 12 } } },
      },
    }
  );

  const chart2Config = generateChartConfig(
    "bar",
    chart2Labels,
    chart2Data,
    ["#10b981", "#ef4444", "#3b82f6"],
    {
      plugins: {
        legend: { display: false },
        datalabels: {
          color: "#1e293b",
          font: { weight: "bold", size: 13 },
          formatter: (value) => value.toFixed(1) + "K",
        },
      },
      scales: {
        y: { beginAtZero: true, ticks: { font: { size: 12 } } },
        x: { ticks: { font: { size: 12 } } },
      },
    }
  );

  const chart3Config = generateChartConfig(
    "doughnut",
    chart3Labels,
    chart3Data,
    ["#3b82f6", "#10b981", "#f59e0b", "#ef4444", "#8b5cf6"],
    {
      plugins: {
        legend: {
          position: "bottom",
          labels: { font: { size: 12 }, boxWidth: 14 },
        },
        datalabels: {
          color: "#1e293b",
          font: { weight: "bold", size: 12 },
          formatter: (value) => "$" + value.toFixed(1) + "K",
        },
      },
    }
  );

  const chart1Canvas = generateChartCanvas("chart1", chart1Config);
  const chart2Canvas = generateChartCanvas("chart2", chart2Config);
  const chart3Canvas = generateChartCanvas("chart3", chart3Config);
  const chartInitScript = generateChartInitScript();

  const kpiCard = (title, value, pm, ytd, change) => `
    <div class="kpi-card">
      <h3>${title}</h3>
      <div class="kpi-value">${value}</div>
      <div class="kpi-details">
        <div>PM: ${pm} ${change ? `(${change})` : ""}</div>
        <div>YTD: ${ytd}</div>
      </div>
    </div>`;

  return `<!DOCTYPE html>
<html><head><meta charset="UTF-8"><title>Finance Report</title>
<script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.umd.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-datalabels@2.2.0/dist/chartjs-plugin-datalabels.min.js"></script>
<style>
*{margin:0;padding:0;box-sizing:border-box}
body{font-family:-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,Arial,sans-serif;padding:20px;background:#f5f7fa;-webkit-user-select:text;-moz-user-select:text;-ms-user-select:text;user-select:text}
.container{max-width:1000px;margin:0 auto;background:#fff;padding:30px;border-radius:8px}
.header{text-align:center;margin-bottom:25px;border-bottom:3px solid#2563eb;padding-bottom:15px}
.header h1{font-size:26px;color:#1e293b;margin-bottom:6px;font-weight:600}
.header h2{font-size:16px;color:#64748b;text-transform:uppercase;font-weight:500}
.kpi-grid{display:grid;grid-template-columns:repeat(5,1fr);gap:12px;margin-bottom:25px}
.kpi-card{background:linear-gradient(135deg,#667eea 0%,#764ba2 100%);padding:16px;border-radius:6px;color:#fff}
.kpi-card h3{font-size:13px;margin-bottom:8px;opacity:0.9;text-transform:uppercase;font-weight:600}
.kpi-value{font-size:24px;font-weight:bold;margin-bottom:6px}
.kpi-details{font-size:11px;opacity:0.9}
.kpi-details div{margin-bottom:3px}
.charts-grid{display:grid;grid-template-columns:repeat(2,1fr);gap:15px;margin-bottom:20px;max-width:900px;margin-left:auto;margin-right:auto}
.chart-box{background:#f8fafc;padding:15px;border-radius:6px;border:1px solid#e2e8f0;position:relative;max-width:100%}
.chart-box h4{font-size:15px;font-weight:600;margin-bottom:10px;color:#1e293b}
.chart-container{height:250px;margin-bottom:10px;max-width:100%}
.chart-data-table{width:95%;font-size:11px;border-collapse:collapse;margin-top:8px;background:#fff;margin-left:auto;margin-right:auto}
.chart-data-table th{text-align:left;padding:4px 6px;background:#e2e8f0;font-weight:600;font-size:10px}
.chart-data-table td{padding:3px 6px;border-bottom:1px solid#e2e8f0;color:#475569}
.chart-data-table .amount{text-align:right;font-weight:500}
.tables-grid{display:grid;grid-template-columns:1fr 1fr;gap:15px;max-width:900px;margin-left:auto;margin-right:auto}
.table-box{background:#f8fafc;padding:15px;border-radius:6px;border:1px solid#e2e8f0;max-width:100%}
.table-box h4{font-size:15px;font-weight:600;margin-bottom:10px;color:#1e293b;border-bottom:2px solid#2563eb;padding-bottom:6px}
table{width:95%;font-size:12px;border-collapse:collapse;margin-left:auto;margin-right:auto}
th{text-align:left;padding:6px;background:#e2e8f0;font-weight:600}
td{padding:5px 6px;border-bottom:1px solid#e2e8f0;color:#475569}
tr:last-child td{border-bottom:none}
.amount{text-align:right;font-weight:500}
.category-header{font-weight:700;background:#f1f5f9;font-size:11px;text-transform:uppercase}
.subcategory{padding-left:12px!important;font-weight:600}
.item-indent{padding-left:24px!important}
.negative{color:#ef4444}
</style></head><body><div class="container">
<div class="header"><h2>Financial ${
    data.organization || "Organization"
  }</h2><h1>Summary For ${month} - ${data.year}</h1></div>
<div class="kpi-grid">
  ${kpiCard(
    "Revenue",
    formatCurrency(kpi.revenue?.total),
    formatCurrency(kpi.revenue?.pm),
    formatCurrency(kpi.revenue?.ytd),
    kpi.revenue?.pm_change
  )}
  ${kpiCard(
    "Expenses",
    formatCurrency(kpi.expense?.total),
    formatCurrency(kpi.expense?.pm),
    formatCurrency(kpi.expense?.ytd),
    kpi.expense?.pm_change
  )}
  ${kpiCard(
    "Net Income",
    formatCurrency(kpi.income?.total),
    formatCurrency(kpi.income?.pm),
    formatCurrency(kpi.income?.ytd),
    kpi.income?.pm_change
  )}
  ${kpiCard(
    "Net Cashflow",
    formatCurrency(kpi.cashflow?.net_cashflow),
    formatCurrency(kpi.cashflow?.pm),
    formatCurrency(kpi.cashflow?.ytd),
    kpi.cashflow?.pm_change
  )}
  ${kpiCard(
    "Profit Margin",
    `${kpi.profit_margin?.profit_margin || 0}%`,
    `${kpi.profit_margin?.pm || 0}%`,
    `${kpi.profit_margin?.ytd || 0}%`,
    kpi.profit_margin?.pm_change
  )}
</div>
<div class="charts-grid">
  <div class="chart-box"><h4>Cashflow by Group</h4><div class="chart-container">${chart1Canvas}</div><table class="chart-data-table">${chart1TableHTML}</table></div>
  <div class="chart-box"><h4>Revenue vs Expenses</h4><div class="chart-container">${chart2Canvas}</div><table class="chart-data-table">${chart2TableHTML}</table></div>
</div>
<div class="charts-grid">
  <div class="chart-box"><h4>Expense Breakdown</h4><div class="chart-container">${chart3Canvas}</div><table class="chart-data-table">${chart3TableHTML}</table></div>
</div>
<div class="tables-grid">
  <div class="table-box"><h4>Balance Sheet</h4>${generateTableWithBody(
    [{ text: "ITEM" }, { text: "AMOUNT", className: "amount" }],
    balanceSheetHTML
  )}</div>
  <div class="table-box"><h4>Income & Expense Statement</h4>${generateTableWithBody(
    [{ text: "ITEM" }, { text: "AMOUNT", className: "amount" }],
    incomeExpenseHTML
  )}</div>
</div>
</div>
<script>${chartInitScript}</script>
</body></html>`;
};

const generatePayrollHTML = (data) => {
  const month = MONTHS[parseInt(data.month) - 1] || "";
  const kpi = data.kpi || {};

  const tax = data.taxBreakdown || {};
  const deduct = data.deductionsBreakdown || [];
  const salaryDept = data.salaryByDepartment || [];
  const totalPayroll = kpi.total_payroll?.total || "$0";
  const doctorSalary = kpi.doctor_salary?.total || "$0";

  const taxSIT = parseNumericString(tax.SIT, "0");
  const taxFTT = parseNumericString(tax.FTT, "0");
  const taxSOCSEC = parseNumericString(tax.SOCSEC, "0");
  const taxMEDICARE = parseNumericString(tax.MEDICARE, "0");
  const taxData = [taxSIT, taxFTT, taxSOCSEC, taxMEDICARE];

  const deductLabels = deduct
    .slice(0, 5)
    .map((deductionItem) => deductionItem.deduction_name);
  const deductData = deduct
    .slice(0, 5)
    .map((deductionItem) =>
      parseNumericString(deductionItem.deductions_amount, "0")
    );

  const salaryDeptLabels = salaryDept.map(
    (department) => department.department
  );
  const salaryDeptData = salaryDept.map((department) =>
    parseNumericString(department.total_salary, "0")
  );
  const salaryCompData = [
    parseNumericString(totalPayroll, "0"),
    parseNumericString(doctorSalary, "0"),
  ];

  const roleLabels = salaryDept.map((department) =>
    department.department.replace(" Salaries", "").replace(" Salary", "")
  );
  const headcountData = salaryDept.map(
    (department) => department.headcount || 0
  );
  const avgSalaryData = salaryDept.map((department) =>
    parseNumericString(department.average_salary, "0")
  );

  const taxTableHTML = generateTaxTableHTML(taxData);
  const deductTableHTML = generateChartTableHTML(deductLabels, deductData, [
    "Deduction",
    "Amount",
  ]);
  const salaryCompTableHTML = generateSalaryCompTableHTML(
    totalPayroll,
    doctorSalary
  );
  const salaryByStaffTableHTML = generateChartTableHTML(
    salaryDeptLabels,
    salaryDeptData,
    ["Department", "Amount"]
  );
  const headcountTableHTML = generateHeadcountTableHTML(
    roleLabels,
    headcountData
  );
  const avgSalaryTableHTML = generateChartTableHTML(roleLabels, avgSalaryData, [
    "Role",
    "Average Salary",
  ]);

  const taxChartConfig = generateChartConfig(
    "bar",
    ["SIT", "FTT", "SOCSEC", "MEDICARE"],
    taxData,
    ["#3b82f6", "#10b981", "#f59e0b", "#ef4444"]
  );
  const deductChartConfig = generateChartConfig(
    "doughnut",
    deductLabels,
    deductData,
    ["#3b82f6", "#f59e0b", "#8b5cf6", "#ec4899", "#10b981"],
    {
      plugins: {
        legend: {
          position: "bottom",
          labels: { font: { size: 12 }, boxWidth: 14 },
        },
        datalabels: {
          color: "#1e293b",
          font: { weight: "bold", size: 12 },
          formatter: (value) => "$" + value.toFixed(1) + "K",
        },
      },
    }
  );
  const salaryCompChartConfig = generateChartConfig(
    "bar",
    ["Total Payroll", "Doctor Salary"],
    salaryCompData,
    ["#3b82f6", "#f59e0b"]
  );
  const salaryByStaffChartConfig = generateChartConfig(
    "bar",
    salaryDeptLabels,
    salaryDeptData,
    "#3b82f6",
    {
      indexAxis: "y",
      plugins: {
        legend: { display: false },
        datalabels: {
          color: "#1e293b",
          font: { weight: "bold", size: 12 },
          formatter: (value) => "$" + value.toFixed(1) + "K",
        },
      },
      scales: {
        x: {
          beginAtZero: true,
          ticks: {
            font: { size: 12 },
            callback: (tickValue) => "$" + tickValue + "K",
          },
        },
        y: { ticks: { font: { size: 12 } } },
      },
    }
  );
  const headcountChartConfig = generateChartConfig(
    "bar",
    roleLabels,
    headcountData,
    ["#3b82f6", "#10b981", "#f59e0b", "#8b5cf6"],
    {
      plugins: {
        legend: { display: false },
        datalabels: {
          color: "#1e293b",
          font: { weight: "bold", size: 13 },
          formatter: (value) => value,
        },
      },
      scales: {
        y: { beginAtZero: true, ticks: { font: { size: 12 }, stepSize: 2 } },
        x: { ticks: { font: { size: 12 } } },
      },
    }
  );
  const avgSalaryChartConfig = generateChartConfig(
    "bar",
    roleLabels,
    avgSalaryData,
    ["#8b5cf6", "#3b82f6", "#10b981", "#f59e0b"],
    {
      plugins: {
        legend: { display: false },
        datalabels: {
          color: "#1e293b",
          font: { weight: "bold", size: 13 },
          formatter: (value) => "$" + value.toFixed(1) + "K",
        },
      },
      scales: {
        y: {
          beginAtZero: true,
          ticks: {
            font: { size: 12 },
            callback: (tickValue) => "$" + tickValue + "K",
          },
        },
        x: { ticks: { font: { size: 12 } } },
      },
    }
  );

  const taxChartCanvas = generateChartCanvas("taxChart", taxChartConfig);
  const deductChartCanvas = generateChartCanvas(
    "deductChart",
    deductChartConfig
  );
  const salaryCompCanvas = generateChartCanvas(
    "salaryComp",
    salaryCompChartConfig
  );
  const salaryByStaffCanvas = generateChartCanvas(
    "salaryByStaff",
    salaryByStaffChartConfig
  );
  const headcountChartCanvas = generateChartCanvas(
    "headcountChart",
    headcountChartConfig
  );
  const avgSalaryChartCanvas = generateChartCanvas(
    "avgSalaryChart",
    avgSalaryChartConfig
  );
  const payrollChartInitScript = generateChartInitScript();

  const kpiCard = (title, value, pm, ytd, change) => `
    <div class="kpi-card">
      <h3>${title}</h3>
      <div class="kpi-value">${value}</div>
      <div class="kpi-details">
        <div>PM: ${pm} ${change ? `(${change})` : ""}</div>
        <div>YTD: ${ytd}</div>
      </div>
    </div>`;

  return `<!DOCTYPE html>
<html><head><meta charset="UTF-8"><title>Payroll Report</title>
<script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.umd.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-datalabels@2.2.0/dist/chartjs-plugin-datalabels.min.js"></script>
<style>
*{margin:0;padding:0;box-sizing:border-box}
body{font-family:-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,Arial,sans-serif;padding:20px;background:#f5f7fa;-webkit-user-select:text;-moz-user-select:text;-ms-user-select:text;user-select:text}
.container{max-width:1000px;margin:0 auto;background:#fff;padding:30px;border-radius:8px}
.header{text-align:center;margin-bottom:25px;border-bottom:3px solid#2563eb;padding-bottom:15px}
.header h1{font-size:26px;color:#1e293b;margin-bottom:6px;font-weight:600}
.header h2{font-size:16px;color:#64748b;text-transform:uppercase;font-weight:500}
.kpi-grid{display:grid;grid-template-columns:repeat(5,1fr);gap:12px;margin-bottom:25px}
.kpi-card{background:linear-gradient(135deg,#667eea 0%,#764ba2 100%);padding:16px;border-radius:6px;color:#fff}
.kpi-card h3{font-size:13px;margin-bottom:8px;opacity:0.9;text-transform:uppercase;font-weight:600}
.kpi-value{font-size:24px;font-weight:bold;margin-bottom:6px}
.kpi-details{font-size:11px;opacity:0.9}
.kpi-details div{margin-bottom:3px}
.charts-grid{display:grid;grid-template-columns:repeat(2,1fr);gap:15px;margin-bottom:20px;max-width:900px;margin-left:auto;margin-right:auto}
.chart-box{background:#f8fafc;padding:15px;border-radius:6px;border:1px solid#e2e8f0;position:relative;max-width:100%}
.chart-box h4{font-size:15px;font-weight:600;margin-bottom:10px;color:#1e293b}
.chart-container{height:250px;margin-bottom:10px;max-width:100%}
.chart-data-table{width:95%;font-size:11px;border-collapse:collapse;margin-top:8px;background:#fff;margin-left:auto;margin-right:auto}
.chart-data-table th{text-align:left;padding:4px 6px;background:#e2e8f0;font-weight:600;font-size:10px}
.chart-data-table td{padding:3px 6px;border-bottom:1px solid#e2e8f0;color:#475569}
.chart-data-table .amount{text-align:right;font-weight:500}
.table-box{background:#f8fafc;padding:15px;border-radius:6px;border:1px solid#e2e8f0}
.table-box h4{font-size:15px;font-weight:600;margin-bottom:10px;color:#1e293b;border-bottom:2px solid#2563eb;padding-bottom:6px}
table{width:95%;font-size:12px;border-collapse:collapse;margin-left:auto;margin-right:auto}
th{text-align:left;padding:6px;background:#e2e8f0;font-weight:600}
td{padding:5px 6px;border-bottom:1px solid#e2e8f0;color:#475569}
tr:last-child td{border-bottom:none}
.amount{text-align:right;font-weight:500}
</style></head><body><div class="container">
<div class="header"><h2>Payroll ${
    data.organization || "Organization"
  }</h2><h1>Summary For ${month} - ${data.year}</h1></div>
<div class="kpi-grid">
  ${kpiCard(
    "Total Payroll",
    kpi.total_payroll?.total || "$0",
    kpi.total_payroll?.pm || "$0",
    kpi.total_payroll?.ytd || "$0",
    kpi.total_payroll?.pm_change || ""
  )}
  ${kpiCard(
    "Doctor Salary",
    kpi.doctor_salary?.total || "$0",
    kpi.doctor_salary?.pm || "$0",
    kpi.doctor_salary?.ytd || "$0",
    kpi.doctor_salary?.pm_change || ""
  )}
  ${kpiCard(
    "Total Deductions",
    kpi.total_deductions?.total || "$0",
    kpi.total_deductions?.pm || "$0",
    kpi.total_deductions?.ytd || "$0",
    kpi.total_deductions?.pm_change || ""
  )}
  ${kpiCard(
    "Total Taxes",
    kpi.total_taxes?.total || "$0",
    kpi.total_taxes?.pm || "$0",
    kpi.total_taxes?.ytd || "$0",
    kpi.total_taxes?.pm_change || ""
  )}
  ${kpiCard(
    "Doctors Contribution",
    kpi.doctors_contribution?.total || "0%",
    kpi.doctors_contribution?.pm || "0%",
    kpi.doctors_contribution?.ytd || "0%",
    kpi.doctors_contribution?.pm_change || ""
  )}
</div>
<div class="charts-grid">
  <div class="chart-box"><h4>Total vs Doctor Salary</h4><div class="chart-container">${salaryCompCanvas}</div><table class="chart-data-table">${salaryCompTableHTML}</table></div>
  <div class="chart-box"><h4>Salary By Staff Category</h4><div class="chart-container">${salaryByStaffCanvas}</div><table class="chart-data-table">${salaryByStaffTableHTML}</table></div>
</div>
<div class="charts-grid">
  <div class="chart-box"><h4>Deductions Breakdown</h4><div class="chart-container">${deductChartCanvas}</div><table class="chart-data-table">${deductTableHTML}</table></div>
  <div class="chart-box"><h4>Tax Breakdown</h4><div class="chart-container">${taxChartCanvas}</div><table class="chart-data-table">${taxTableHTML}</table></div>
</div>
<div class="charts-grid">
  <div class="chart-box"><h4>Headcount By Role</h4><div class="chart-container">${headcountChartCanvas}</div><table class="chart-data-table">${headcountTableHTML}</table></div>
  <div class="chart-box"><h4>Average Salary By Role</h4><div class="chart-container">${avgSalaryChartCanvas}</div><table class="chart-data-table">${avgSalaryTableHTML}</table></div>
</div>
</div>
<script>${payrollChartInitScript}</script>
</body></html>`;
};

const generateOperationsHTML = (data) => {
  const month = MONTHS[parseInt(data.month) - 1] || "";
  const overview = data.overview || [];

  const formatValue = (item) => {
    if (!item) return "0";
    if (item.format === "currency")
      return formatCurrency(item.value || 0);
    return (item.value || 0).toString();
  };

  const kpiCard = (item) => {
    if (!item)
      return '<div class="kpi-card"><h3>N/A</h3><div class="kpi-value">0</div></div>';
    const formattedValue = formatValue(item);
    const pm =
      item.format === "currency"
        ? formatCurrency(item.previousMonth || 0)
        : (item.previousMonth || 0).toString();
    const ytd =
      item.format === "currency"
        ? formatCurrency(item.yearToDate || 0)
        : (item.yearToDate || 0).toString();
    const change = item.previousMonthChange || "0%";
    return `
    <div class="kpi-card">
      <h3>${item.title || "N/A"}</h3>
      <div class="kpi-value">${formattedValue}</div>
      <div class="kpi-details">
        <div>PM: ${pm} (${change})</div>
        <div>YTD: ${ytd}</div>
      </div>
    </div>`;
  };

  const collectionsComp =
    data.summary?.collectionsComparison?.comparison?.[0] || {};
  const collectionsByDoctor = data.summary?.collectionsByDoctor?.doctors || [];
  const collectionsByPayer =
    data.summary?.collectionsByPayerType?.categories || [];
  const treatmentPlan = data.trends?.treatmentPlanStatus?.categories || [];
  const agingReceivables = data.trends?.agingAccountReceivables?.periods || [];

  const op1Labels = ["Patient", "Insurance"];
  const op1Data = [
    collectionsComp.patientCollection || 0,
    collectionsComp.insuranceCollection || 0,
  ];
  const op1TableHTML = generateOperationsTableHTML(op1Labels, op1Data, [
    "Type",
    "Amount",
  ]);

  const op2Labels = collectionsByDoctor
    .slice(0, 5)
    .map((doctor) => doctor.name || "N/A");
  const op2Data = collectionsByDoctor
    .slice(0, 5)
    .map((doctor) => doctor.value || 0);
  const op2TableHTML = generateOperationsTableHTML(op2Labels, op2Data, [
    "Doctor",
    "Amount",
  ]);

  const op3Labels = collectionsByPayer
    .slice(0, 5)
    .map((payer) => payer.name || "N/A");
  const op3Data = collectionsByPayer
    .slice(0, 5)
    .map((payer) => payer.value || 0);
  const op3TableHTML = generateOperationsTableHTML(op3Labels, op3Data, [
    "Payer",
    "Amount",
  ]);

  const op4Labels = treatmentPlan
    .slice(0, 6)
    .map((status) => status.name || "N/A");
  const op4Data = treatmentPlan.slice(0, 6).map((status) => status.value || 0);
  const op4TableHTML = generateOperationsTableHTML(
    op4Labels,
    op4Data,
    ["Status", "Amount"],
    (value) => "$" + value
  );

  const op5Labels = agingReceivables.map((period) => period.name || "N/A");
  const op5Data = agingReceivables.map((period) => period.value || 0);
  const op5TableHTML = generateOperationsTableHTML(op5Labels, op5Data, [
    "Period",
    "Amount",
  ]);

  const op1ChartConfig = generateChartConfig(
    "bar",
    ["Patient", "Insurance"],
    op1Data,
    ["#3b82f6", "#f59e0b"],
    {
      plugins: {
        legend: { display: false },
        datalabels: {
          color: "#1e293b",
          font: { weight: "bold", size: 13 },
          formatter: (value) => "$" + (value / 1000).toFixed(1) + "K",
        },
      },
      scales: {
        y: {
          beginAtZero: true,
          ticks: {
            font: { size: 12 },
            callback: (tickValue) => "$" + (tickValue / 1000).toFixed(0) + "K",
          },
        },
        x: { ticks: { font: { size: 12 } } },
      },
    }
  );
  const op2ChartConfig = generateChartConfig(
    "bar",
    op2Labels,
    op2Data,
    "#3b82f6",
    {
      plugins: {
        legend: { display: false },
        datalabels: {
          color: "#1e293b",
          font: { weight: "bold", size: 12 },
          formatter: (value) => "$" + (value / 1000).toFixed(1) + "K",
        },
      },
      scales: {
        y: {
          beginAtZero: true,
          ticks: {
            font: { size: 12 },
            callback: (tickValue) => "$" + (tickValue / 1000).toFixed(0) + "K",
          },
        },
        x: { ticks: { font: { size: 12 } } },
      },
    }
  );
  const op3ChartConfig = generateChartConfig(
    "doughnut",
    op3Labels,
    op3Data,
    ["#8b5cf6", "#10b981", "#f59e0b", "#ef4444", "#3b82f6"],
    {
      plugins: {
        legend: {
          position: "bottom",
          labels: { font: { size: 12 }, boxWidth: 14 },
        },
        datalabels: {
          color: "#1e293b",
          font: { weight: "bold", size: 12 },
          formatter: (value) => "$" + (value / 1000).toFixed(1) + "K",
        },
      },
    }
  );
  const op4ChartConfig = generateChartConfig(
    "doughnut",
    op4Labels,
    op4Data,
    ["#3b82f6", "#f59e0b", "#10b981", "#8b5cf6", "#ec4899", "#ef4444"],
    {
      plugins: {
        legend: {
          position: "bottom",
          labels: { font: { size: 12 }, boxWidth: 14 },
        },
        datalabels: {
          color: "#1e293b",
          font: { weight: "bold", size: 12 },
          formatter: (value) => "$" + value,
        },
      },
    }
  );
  const op5ChartConfig = generateChartConfig(
    "bar",
    op5Labels,
    op5Data,
    ["#10b981", "#3b82f6", "#f59e0b", "#ef4444"],
    {
      plugins: {
        legend: { display: false },
        datalabels: {
          color: "#1e293b",
          font: { weight: "bold", size: 13 },
          formatter: (value) => "$" + (value / 1000).toFixed(1) + "K",
        },
      },
      scales: {
        y: {
          beginAtZero: true,
          ticks: {
            font: { size: 12 },
            callback: (tickValue) => "$" + (tickValue / 1000).toFixed(0) + "K",
          },
        },
        x: { ticks: { font: { size: 12 } } },
      },
    }
  );

  const op1Canvas = generateChartCanvas("op1", op1ChartConfig);
  const op2Canvas = generateChartCanvas("op2", op2ChartConfig);
  const op3Canvas = generateChartCanvas("op3", op3ChartConfig);
  const op4Canvas = generateChartCanvas("op4", op4ChartConfig);
  const op5Canvas = generateChartCanvas("op5", op5ChartConfig);
  const operationsChartInitScript = generateChartInitScript();

  return `<!DOCTYPE html>
<html><head><meta charset="UTF-8"><title>Operations Report</title>
<script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.umd.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-datalabels@2.2.0/dist/chartjs-plugin-datalabels.min.js"></script>
<style>
*{margin:0;padding:0;box-sizing:border-box}
body{font-family:-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,Arial,sans-serif;padding:20px;background:#f5f7fa;-webkit-user-select:text;-moz-user-select:text;-ms-user-select:text;user-select:text}
.container{max-width:1000px;margin:0 auto;background:#fff;padding:30px;border-radius:8px}
.header{text-align:center;margin-bottom:25px;border-bottom:3px solid#2563eb;padding-bottom:15px}
.header h1{font-size:26px;color:#1e293b;margin-bottom:6px;font-weight:600}
.header h2{font-size:16px;color:#64748b;text-transform:uppercase;font-weight:500}
.kpi-grid{display:grid;grid-template-columns:repeat(5,1fr);gap:12px;margin-bottom:25px}
.kpi-card{background:linear-gradient(135deg,#667eea 0%,#764ba2 100%);padding:16px;border-radius:6px;color:#fff}
.kpi-card h3{font-size:13px;margin-bottom:8px;opacity:0.9;text-transform:uppercase;font-weight:600}
.kpi-value{font-size:24px;font-weight:bold;margin-bottom:6px}
.kpi-details{font-size:11px;opacity:0.9}
.kpi-details div{margin-bottom:3px}
.charts-grid{display:grid;grid-template-columns:repeat(2,1fr);gap:15px;margin-bottom:20px;max-width:900px;margin-left:auto;margin-right:auto}
.chart-box{background:#f8fafc;padding:15px;border-radius:6px;border:1px solid#e2e8f0;position:relative;max-width:100%}
.chart-box h4{font-size:15px;font-weight:600;margin-bottom:10px;color:#1e293b}
.chart-container{height:250px;margin-bottom:10px;max-width:100%}
.chart-data-table{width:95%;font-size:11px;border-collapse:collapse;margin-top:8px;background:#fff;margin-left:auto;margin-right:auto}
.chart-data-table th{text-align:left;padding:4px 6px;background:#e2e8f0;font-weight:600;font-size:10px}
.chart-data-table td{padding:3px 6px;border-bottom:1px solid#e2e8f0;color:#475569}
.chart-data-table .amount{text-align:right;font-weight:500}
</style></head><body><div class="container">
<div class="header"><h2>Operations ${
    data.organization || "Organization"
  }</h2><h1>Practice Management Dashboard For ${month} - ${data.year}</h1></div>
<div class="kpi-grid">
  ${kpiCard(overview[0])}
  ${kpiCard(overview[1])}
  ${kpiCard(overview[2])}
  ${kpiCard(overview[3])}
  ${kpiCard(overview[4])}
</div>
<div class="charts-grid">
  <div class="chart-box"><h4>Patient vs Insurance</h4><div class="chart-container">${op1Canvas}</div><table class="chart-data-table">${op1TableHTML}</table></div>
  <div class="chart-box"><h4>Collections by Doctor</h4><div class="chart-container">${op2Canvas}</div><table class="chart-data-table">${op2TableHTML}</table></div>
</div>
<div class="charts-grid">
  <div class="chart-box"><h4>Collections by Payer</h4><div class="chart-container">${op3Canvas}</div><table class="chart-data-table">${op3TableHTML}</table></div>
  <div class="chart-box"><h4>Treatment Plan Status</h4><div class="chart-container">${op4Canvas}</div><table class="chart-data-table">${op4TableHTML}</table></div>
</div>
<div class="charts-grid">
  <div class="chart-box"><h4>Aging Receivables</h4><div class="chart-container">${op5Canvas}</div><table class="chart-data-table">${op5TableHTML}</table></div>
</div>
</div>
<script>${operationsChartInitScript}</script>
</body></html>`;
};

const generatePDF = async (html) => {
  let browser;
  try {
    logger.info("Generating PDF with Playwright and Chart.js");
    browser = await chromium.launch({ headless: true, args: ["--no-sandbox"] });
    const page = await browser.newPage();
    await page.setContent(html, { waitUntil: "networkidle" });
    await page.waitForTimeout(2000);
    const pdf = await page.pdf({
      format: "A4",
      printBackground: true,
      preferCSSPageSize: true,
      margin: { top: "15px", right: "15px", bottom: "15px", left: "15px" },
    });
    logger.info("PDF generated successfully with charts");
    return pdf;
  } catch (error) {
    logger.error("PDF generation error:", error);
    throw error;
  } finally {
    if (browser) await browser.close();
  }
};

export {
  generateHTML,
  generatePayrollHTML,
  generateOperationsHTML,
  generatePDF,
};