"use client";

import { useRef, useState } from "react";
import { useSelector } from "react-redux";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "antd";
import { FaFileDownload } from "react-icons/fa";
import { ChartCard } from "../../ChartCard";
import { CollectionsComparisonChart } from "../../CollectionsComparisonChart";
import { CollectionsByDoctorChart } from "../../CollectionsByDoctorChart";
import { CollectionsByPayerChart } from "../../CollectionsByPayerChart";
import { TreatmentPlanStatusChart } from "../../TreatmentPlanStatusChart";
import { AgingReceivablesChart } from "../../AgingReceivablesChart";
import KPIBox from "../shared/KPIBox";
import { formatCardValue } from "@/utils/methods/formatters";
import { downloadReportFromBlob } from "@/utils/methods/pdfExport";
import {
  mapOperationsKpiData,
  mapOperationsCollectionsComparisonData,
  mapOperationsCollectionsByDoctorData,
  mapOperationsCollectionsByPayerData,
  mapOperationsTreatmentPlanStatusData,
  mapOperationsAgingReceivablesData,
} from "@/utils/methods/operationsReports";

const MONTHS = [
  "January",
  "February",
  "March",
  "April",
  "May",
  "June",
  "July",
  "August",
  "September",
  "October",
  "November",
  "December",
];

const MONTHS_SHORT = [
  "Jan",
  "Feb",
  "Mar",
  "Apr",
  "May",
  "Jun",
  "Jul",
  "Aug",
  "Sep",
  "Oct",
  "Nov",
  "Dec",
];

function ChartFallback({ message }) {
  return (
    <div className="flex h-full items-center justify-center text-sm text-slate-400">
      {message}
    </div>
  );
}

export default function OperationalDashboard({
  organizationId,
  month,
  year,
  organizationName,
}) {
  const dashboardRef = useRef(null);
  const { overviewData, summaryData, trendsData } = useSelector(
    (state) => state.operations
  );

  const parsedMonthIndex =
    month && Number(month) >= 1 && Number(month) <= 12
      ? Number(month) - 1
      : null;
  const monthFullName =
    parsedMonthIndex !== null ? MONTHS[parsedMonthIndex] : null;
  const monthShortName =
    parsedMonthIndex !== null ? MONTHS_SHORT[parsedMonthIndex] : null;
  const yearLabel = year || new Date().getFullYear();
  const headerPeriod =
    monthFullName && yearLabel ? `${monthFullName} - ${yearLabel}` : null;
  const chartPeriod =
    monthShortName && yearLabel
      ? `${monthShortName}-${String(yearLabel).slice(-2)}`
      : summaryData?.collectionsComparison?.comparison?.[0]?.period;

  const formatDisplayValue = (value, format) => {
    if (format === "currency") {
      return formatCardValue(value, format);
    }
    return typeof value === "number" ? value.toLocaleString() : value;
  };

  const cards = mapOperationsKpiData(overviewData);
  const collectionsComparisonData = mapOperationsCollectionsComparisonData(
    summaryData,
    chartPeriod
  );
  const collectionsByDoctorData =
    mapOperationsCollectionsByDoctorData(summaryData);
  const collectionsByPayerData =
    mapOperationsCollectionsByPayerData(summaryData);
  const treatmentPlanStatusData =
    mapOperationsTreatmentPlanStatusData(trendsData);
  const agingReceivablesData = mapOperationsAgingReceivablesData(trendsData);

  const [isExporting, setIsExporting] = useState(false);

  const handleExportPdf = () => {
    downloadReportFromBlob({
      organizationId,
      organizationName,
      month,
      year,
      serviceType: "operations",
      onLoadingChange: setIsExporting,
    });
  };

  return (
    <div className="flex flex-col gap-5 w-full py-3" ref={dashboardRef}>
      <section className="card-surface w-full p-5 lg:p-6">
        <div className="flex flex-col gap-3 lg:flex-row lg:items-start lg:justify-between">
          <div className="flex flex-col gap-1 text-center lg:flex-1">
            <p className="text-xs font-semibold uppercase tracking-wide text-slate-500">
              {organizationName ||
                overviewData?.organization ||
                "CHP SOLUTIONS"}
            </p>
            <h2 className="text-2xl sm:text-3xl lg:text-4xl font-semibold text-slate-900">
              Practice Management Dashboard For{" "}
              {headerPeriod || overviewData?.period || "January - 2025"}
            </h2>
          </div>
          <div className="flex justify-center lg:justify-end lg:flex-shrink-0">
            <Tooltip title="Export to PDF" placement="bottom">
              <Button
                icon={<FaFileDownload style={{ fontSize: "18px" }} />}
                onClick={handleExportPdf}
                size="large"
                loading={isExporting}
                shape="circle"
                style={{
                  backgroundColor: "#dc2626",
                  borderColor: "#dc2626",
                  color: "white",
                }}
              />
            </Tooltip>
          </div>
        </div>
      </section>
      {cards ? (
        <div className="w-full grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-5 gap-3 sm:gap-4 items-stretch">
          {cards.map((card) => (
            <div key={card.id} className="w-full h-full">
              <KPIBox
                title={card.title}
                amount={formatDisplayValue(card.value, card.format)}
                pmValue={formatDisplayValue(card.previousMonth, card.format)}
                ytdValue={formatDisplayValue(card.yearToDate, card.format)}
                change={card.change}
                isPositive={card.isPositive}
              />
            </div>
          ))}
        </div>
      ) : null}
      <div className="w-full grid grid-cols-1 md:grid-cols-2 gap-3 sm:gap-4">
        <ChartCard title="Patients vs Insurance">
          {collectionsComparisonData ? (
            <CollectionsComparisonChart data={collectionsComparisonData} />
          ) : (
            <ChartFallback message="Collections comparison data unavailable" />
          )}
        </ChartCard>
        <ChartCard title="Collections by Doctor">
          {collectionsByDoctorData ? (
            <CollectionsByDoctorChart data={collectionsByDoctorData} />
          ) : (
            <ChartFallback message="Collections by doctor data unavailable" />
          )}
        </ChartCard>
      </div>

      <div className="w-full grid grid-cols-1 md:grid-cols-3 gap-3 sm:gap-4">
        <ChartCard title="Collections by Payer Type">
          {collectionsByPayerData ? (
            <CollectionsByPayerChart data={collectionsByPayerData} />
          ) : (
            <ChartFallback message="Collections by payer data unavailable" />
          )}
        </ChartCard>
        <ChartCard title="Treatment Plan Status">
          {treatmentPlanStatusData ? (
            <TreatmentPlanStatusChart data={treatmentPlanStatusData} />
          ) : (
            <ChartFallback message="Treatment plan data unavailable" />
          )}
        </ChartCard>
        <ChartCard title="Aging Account Receivables">
          {agingReceivablesData ? (
            <AgingReceivablesChart data={agingReceivablesData} />
          ) : (
            <ChartFallback message="Aging receivables data unavailable" />
          )}
        </ChartCard>
      </div>
    </div>
  );
}
