import KpiRepository from "../repository/kpi.repository.js";
import { LOGGER_NAMES } from "../utils/constants/log.constants.js";
import { createLogger } from "../utils/logger.utils.js";
import {
  isValid<PERSON><PERSON>h,
  isValid<PERSON>ear,
  parseNumericValue,
} from "../utils/format.utils.js";
import income_expense_statementRepository from "../repository/income_expense_statement.repository.js";

const logger = createLogger(LOGGER_NAMES.REPORT_SERVICE);

/**
 * Calculate previous month and year
 * @param {number} month - Current month (1-12)
 * @param {number} year - Current year
 * @returns {Object} Previous month and year
 */
const getPreviousMonthYear = (month, year) => {
  if (month === 1) {
    return { month: 12, year: year - 1 };
  }
  return { month: month - 1, year };
};

/**
 * Calculate percentage change between current and previous values
 * @param {number} current - Current value
 * @param {number} previous - Previous value
 * @returns {string} Percentage change with + or - sign (e.g., "+25.50%" or "-15.30%")
 */
const calculatePercentageChange = (current, previous) => {
  // Handle division by zero
  if (previous === 0) {
    if (current === 0) return "0.00%";
    return "N/A";
  }

  const change = ((current - previous) / previous) * 100;
  const sign = change >= 0 ? "+" : "";
  return `${sign}${change.toFixed(2)}%`;
};

/**
 * Filter line items by keyword in path
 * @param {Array} lineItems - P&L line items
 * @param {string} keyword - Keyword to filter by (e.g., "income", "expense")
 * @returns {Array} Filtered line items
 */
const filterLineItemsByKeyword = (lineItems, keyword) => {
  return lineItems.filter((item) => {
    const path = item.path || "";
    return path.toLowerCase().includes(keyword.toLowerCase());
  });
};

/**
 * Calculate total amount from line items for a specific keyword
 * @param {Array} lineItems - P&L line items
 * @param {string} keyword - Keyword to filter by (e.g., "income", "expense")
 * @returns {number} Total amount as whole number
 */
const calculateTotalByKeyword = (lineItems, keyword) => {
  const filteredItems = filterLineItemsByKeyword(lineItems, keyword);
  const total = filteredItems.reduce((sum, item) => {
    return sum + parseNumericValue(item.amount);
  }, 0);
  return Math.round(total);
};

/**
 * Get total for a specific keyword for a specific month
 * @param {string} schemaName - Organization schema name
 * @param {number} month - Month (1-12)
 * @param {number} year - Year
 * @param {string} keyword - Keyword to filter by (e.g., "income", "expense")
 * @returns {Promise<number>} Total for the month
 */
const getMonthTotalByKeyword = async (schemaName, month, year, keyword) => {
  try {
    // Get report ID for the month
    const pnlReportRaw = await income_expense_statementRepository.getPNLReport(
      schemaName,
      month,
      year
    );

    const fields = {
      revenue: "total income",
      expense: "total expenses",
      income: "net income",
    };

    const fieldname = fields[keyword];
    console.log("fieldname", fieldname, keyword);
    const extractTotalIncomeFromQBReport = (pnlReportRaw) => {
      try {
        const rows = pnlReportRaw?.Rows?.Row || [];

        for (const row of rows) {
          const summary = row?.Summary;
          if (!summary) continue;

          const colData = summary.ColData || [];

          // Find summary row having Total Income
          if (colData[0]?.value?.toLowerCase() === fieldname) {
            const value = colData[1]?.value || colData[2]?.value;
            return Number(value);
          }
        }

        return null; // If not found
      } catch (err) {
        console.error("Error extracting Total Income:", err);
        return null;
      }
    };
    const total = extractTotalIncomeFromQBReport(pnlReportRaw);

    logger.info(`${keyword} for month ${month}, year ${year}: ${total}`);
    return total;
  } catch (error) {
    logger.error(`Error calculating month ${keyword}:`, error);
    throw error;
  }
};

/**
 * Get net cash flow for a specific month
 * @param {string} schemaName - Organization schema name
 * @param {number} month - Month (1-12)
 * @param {number} year - Year
 * @returns {Promise<number>} Net cash flow for the month
 */
const getMonthNetCashFlow = async (schemaName, month, year) => {
  try {
    // Get cash flow report ID for the month
    const reportId = await KpiRepository.getCashFlowReportId(
      schemaName,
      month,
      year
    );

    if (!reportId) {
      logger.info(
        `No Cash Flow report found for month: ${month}, year: ${year}`
      );
      return 0;
    }

    // Get net cash flow from totals table
    const netCashFlow = await KpiRepository.getNetCashFlow(
      schemaName,
      reportId
    );

    // Round to whole number
    const roundedValue = Math.round(netCashFlow);

    logger.info(
      `Net cash flow for month ${month}, year ${year}: ${roundedValue}`
    );
    return roundedValue;
  } catch (error) {
    logger.error(`Error calculating month net cash flow:`, error);
    throw error;
  }
};

/**
 * Get KPI data for organization
 * @param {Object} params - Query parameters
 * @param {string} params.organization_id - Organization ID
 * @param {number} params.month - Month (1-12)
 * @param {number} params.year - Year
 * @returns {Promise<Object>} KPI data with revenue and expense metrics
 */
const getKpiData = async ({ organization_id, month, year }) => {
  try {
    logger.info(
      `Fetching KPI data for org: ${organization_id}, month: ${month}, year: ${year}`
    );

    // Validate parameters
    if (!organization_id) {
      throw new Error("Organization ID is required");
    }

    if (!month || !isValidMonth(month)) {
      throw new Error("Valid month (1-12) is required");
    }

    if (!year || !isValidYear(year)) {
      throw new Error("Valid year is required");
    }

    // Convert to numbers
    const monthNum = parseInt(month, 10);
    const yearNum = parseInt(year, 10);

    // Get organization schema name
    const schemaName = await KpiRepository.getOrganizationSchemaName(
      organization_id
    );

    if (!schemaName) {
      logger.warn(`Schema not found for organization: ${organization_id}`);
      throw new Error("Organization not found or schema not configured");
    }

    // Calculate REVENUE KPIs
    logger.info("Calculating revenue KPIs...");

    // Current month revenue
    const currentMonthRevenue = await getMonthTotalByKeyword(
      schemaName,
      monthNum,
      yearNum,
      "revenue"
    );

    // Previous month revenue
    const { month: prevMonth, year: prevYear } = getPreviousMonthYear(
      monthNum,
      yearNum
    );
    const previousMonthRevenue = await getMonthTotalByKeyword(
      schemaName,
      prevMonth,
      prevYear,
      "revenue"
    );

    // YTD revenue
    let ytdRevenue = 0;
    for (let m = 1; m <= monthNum; m++) {
      const monthRevenue = await getMonthTotalByKeyword(
        schemaName,
        m,
        yearNum,
        "revenue"
      );
      ytdRevenue += monthRevenue;
    }

    logger.info(
      `Revenue KPI - Current: ${currentMonthRevenue}, PM: ${previousMonthRevenue}, YTD: ${ytdRevenue}`
    );

    // Calculate EXPENSE KPIs
    logger.info("Calculating expense KPIs...");

    // Current month expense
    const currentMonthExpense = await getMonthTotalByKeyword(
      schemaName,
      monthNum,
      yearNum,
      "expense"
    );

    // Previous month expense
    const previousMonthExpense = await getMonthTotalByKeyword(
      schemaName,
      prevMonth,
      prevYear,
      "expense"
    );

    // YTD expense
    let ytdExpense = 0;
    for (let m = 1; m <= monthNum; m++) {
      const monthExpense = await getMonthTotalByKeyword(
        schemaName,
        m,
        yearNum,
        "expense"
      );
      ytdExpense += monthExpense;
    }

    logger.info(
      `Expense KPI - Current: ${currentMonthExpense}, PM: ${previousMonthExpense}, YTD: ${ytdExpense}`
    );

    // Calculate EBITDA (Revenue - Expense)
    logger.info("Calculating EBITDA KPIs...");

    const currentMonthIncome = await getMonthTotalByKeyword(
      schemaName,
      monthNum,
      yearNum,
      "income"
    );
    const previousMonthIncome = await getMonthTotalByKeyword(
      schemaName,
      prevMonth,
      prevYear,
      "income"
    );
    const ytdIncome = await getMonthTotalByKeyword(
      schemaName,
      1,
      yearNum,
      "income"
    );

    logger.info(
      `Income KPI - Current: ${currentMonthIncome}, PM: ${previousMonthIncome}, YTD: ${ytdIncome}`
    );

    // Calculate CASH FLOW KPIs
    logger.info("Calculating Cash Flow KPIs...");

    // Current month cash flow
    const currentMonthCashFlow = await getMonthNetCashFlow(
      schemaName,
      monthNum,
      yearNum
    );

    // Previous month cash flow
    const previousMonthCashFlow = await getMonthNetCashFlow(
      schemaName,
      prevMonth,
      prevYear
    );

    // YTD cash flow
    let ytdCashFlow = 0;
    for (let m = 1; m <= monthNum; m++) {
      const monthCashFlow = await getMonthNetCashFlow(schemaName, m, yearNum);
      ytdCashFlow += monthCashFlow;
    }

    logger.info(
      `Cash Flow KPI - Current: ${currentMonthCashFlow}, PM: ${previousMonthCashFlow}, YTD: ${ytdCashFlow}`
    );

    // Calculate PROFIT MARGIN (Revenue - Expense) / Revenue * 100
    logger.info("Calculating Profit Margin KPIs...");

    // Current month profit margin
    const currentMonthProfitMargin =
      currentMonthRevenue !== 0
        ? parseFloat(
            (
              ((currentMonthRevenue - currentMonthExpense) /
                currentMonthRevenue) *
              100
            ).toFixed(2)
          )
        : 0;

    // Previous month profit margin
    const previousMonthProfitMargin =
      previousMonthRevenue !== 0
        ? parseFloat(
            (
              ((previousMonthRevenue - previousMonthExpense) /
                previousMonthRevenue) *
              100
            ).toFixed(2)
          )
        : 0;

    // YTD profit margin
    const ytdProfitMargin =
      ytdRevenue !== 0
        ? parseFloat(
            (((ytdRevenue - ytdExpense) / ytdRevenue) * 100).toFixed(2)
          )
        : 0;

    logger.info(
      `Profit Margin KPI - Current: ${currentMonthProfitMargin}%, PM: ${previousMonthProfitMargin}%, YTD: ${ytdProfitMargin}%`
    );

    // Return formatted response with all KPIs
    return {
      kpi: {
        revenue: {
          total: currentMonthRevenue,
          pm: previousMonthRevenue,
          ytd: ytdRevenue,
          pm_change: calculatePercentageChange(
            currentMonthRevenue,
            previousMonthRevenue
          ),
        },
        expense: {
          total: currentMonthExpense,
          pm: previousMonthExpense,
          ytd: ytdExpense,
          pm_change: calculatePercentageChange(
            currentMonthExpense,
            previousMonthExpense
          ),
        },
        income: {
          total: currentMonthIncome,
          pm: previousMonthIncome,
          ytd: ytdIncome,
          pm_change: calculatePercentageChange(
            currentMonthIncome,
            previousMonthIncome
          ),
        },
        cashflow: {
          net_cashflow: currentMonthCashFlow,
          pm: previousMonthCashFlow,
          ytd: ytdCashFlow,
          pm_change: calculatePercentageChange(
            currentMonthCashFlow,
            previousMonthCashFlow
          ),
        },
        profit_margin: {
          profit_margin: currentMonthProfitMargin,
          pm: previousMonthProfitMargin,
          ytd: ytdProfitMargin,
          pm_change: calculatePercentageChange(
            currentMonthProfitMargin,
            previousMonthProfitMargin
          ),
        },
      },
    };
  } catch (error) {
    logger.error("Error in KpiService.getKpiData:", error);
    throw error;
  }
};

export default {
  getKpiData,
};
