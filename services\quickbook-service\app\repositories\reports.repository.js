import {
  TrialBalanceReport,
  TrialBalanceColumn,
  TrialBalanceRow,
  PnLReport,
  PnLLine,
  PnLSummary,
  BalanceSheetReport,
  BalanceSheetLineItem,
  CashFlowReport,
  CashFlowLine,
  CashFlowTotal,
  sequelize,
} from "../models/index.js";
import {
  create,
  findById,
  findAll,
  createInSchema,
  createBulkInSchema,
  findOrCreateInSchema,
} from "../utils/database.utils.js";

// Helper functions
const createReport = (Model) => (reportData, options = {}, schemaName) =>
  schemaName
    ? createInSchema(Model, schemaName, reportData, options)
    : create(Model, reportData, options);

const createBulk = (Model) => (data, options = {}, schemaName) =>
  schemaName
    ? createBulkInSchema(Model, schemaName, data, options)
    : Model.bulkCreate(data, {
        validate: false,
        ignoreDuplicates: true,
        ...options,
      });

const findByRealmId = (Model) => (realmId, options = {}) =>
  findAll(Model, { where: { realm_id: realmId }, ...options });

const findOrCreateColumn = (Model) => async (whereConditions, defaults, options = {}, schemaName = null) =>
  schemaName
    ? findOrCreateInSchema(Model, schemaName, whereConditions, defaults, options)
    : Model.findOrCreate({
        where: whereConditions,
        defaults,
        ...options,
      });

export const reportsRepository = {
  // Trial Balance
  createTrialBalanceReport: createReport(TrialBalanceReport),
  createTrialBalanceColumns: createBulk(TrialBalanceColumn),
  createTrialBalanceRows: createBulk(TrialBalanceRow),
  findTrialBalanceReportById: (id, options = {}) => findById(TrialBalanceReport, id, options),
  findTrialBalanceReportsByRealmId: findByRealmId(TrialBalanceReport),
  findOrCreateTrialBalanceColumn: findOrCreateColumn(TrialBalanceColumn),

  // P&L
  createPnLReport: createReport(PnLReport),
  createPnLLines: createBulk(PnLLine),
  createPnLSummaries: createBulk(PnLSummary),
  findPnLReportById: (id, options = {}) => findById(PnLReport, id, options),
  findPnLReportsByRealmId: findByRealmId(PnLReport),
  findOrCreatePnLLine: (whereConditions, defaults, options = {}) =>
    PnLLine.findOrCreate({
      where: whereConditions,
      defaults,
      ...options,
    }),

  // Balance Sheet
  createBalanceSheetReport: createReport(BalanceSheetReport),
  createBalanceSheetLineItems: createBulk(BalanceSheetLineItem),
  findBalanceSheetReportById: (id, options = {}) => findById(BalanceSheetReport, id, options),
  findBalanceSheetReportsByRealmId: findByRealmId(BalanceSheetReport),
  findOrCreateBalanceSheetLineItem: (whereConditions, defaults, options = {}) =>
    BalanceSheetLineItem.findOrCreate({
      where: whereConditions,
      defaults,
      ...options,
    }),

  // Cash Flow
  createCashFlowReport: createReport(CashFlowReport),
  createCashFlowLines: createBulk(CashFlowLine),
  createCashFlowTotals: createBulk(CashFlowTotal),
  findCashFlowReportById: (id, options = {}) => findById(CashFlowReport, id, options),
  findCashFlowReportsByRealmId: findByRealmId(CashFlowReport),
  findOrCreateCashFlowLine: (whereConditions, defaults, options = {}) =>
    CashFlowLine.findOrCreate({
      where: whereConditions,
      defaults,
      ...options,
    }),

  // Transaction management
  createTransaction: () => sequelize.transaction(),
  commitTransaction: (transaction) => transaction.commit(),
  rollbackTransaction: (transaction) => transaction.rollback(),
};

export default reportsRepository;
