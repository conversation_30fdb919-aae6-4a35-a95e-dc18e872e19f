import { checkSchema } from "express-validator";
import { ONBOARDING_MESSAGES } from "../utils/constants/onboarding.constants.js";
import { validateService, validateMonth, validateYear } from "../utils/document.utils.js";

/**
 * Unified file upload validator
 * Validates parameters from request body
 * Supports:
 * 1. Logo: { orgId, type: "logo" }
 * 2. Report: { orgId, orgName, type: "report", service, year, month }
 */
export const uploadFileValidator = checkSchema(
  {
    // Body parameters - always required
    orgId: {
      in: ["body"],
      notEmpty: {
        errorMessage: ONBOARDING_MESSAGES.MISSING_REQUIRED_FIELDS,
      },
      isString: {
        errorMessage: "Organization ID must be a valid string",
      },
      trim: true,
    },
    // Type parameter - required
    type: {
      in: ["body"],
      notEmpty: {
        errorMessage: "Type is required (logo or report)",
      },
      custom: {
        options: (value) => {
          const normalizedValue = value?.toLowerCase?.();
          if (!["logo", "report"].includes(normalizedValue)) {
            throw new Error("Invalid type. Use 'logo' or 'report'");
          }
          return true;
        },
      },
    },
    // Optional parameters for report uploads
    orgName: {
      in: ["body"],
      optional: true,
      isString: {
        errorMessage: "Organization name must be a string",
      },
      trim: true,
    },
    service: {
      in: ["body"],
      optional: true,
      custom: {
        options: (value) => {
          if (value && !validateService(value)) {
            throw new Error("Invalid service type. Must be one of: Finance, Operations, Payroll");
          }
          return true;
        },
      },
    },
    year: {
      in: ["body"],
      optional: true,
      custom: {
        options: (value) => {
          if (value && !validateYear(value)) {
            throw new Error("Year must be between 2000 and 3000");
          }
          return true;
        },
      },
    },
    month: {
      in: ["body"],
      optional: true,
      custom: {
        options: (value) => {
          if (value && !validateMonth(value)) {
            throw new Error("Month must be between 1 and 12");
          }
          return true;
        },
      },
    },
    // File validation - always required
    file: {
      custom: {
        options: (_value, { req }) => {
          if (!req.file) {
            throw new Error(ONBOARDING_MESSAGES.MISSING_FILE);
          }
          return true;
        },
      },
    },
  },
  ["body"]
);
