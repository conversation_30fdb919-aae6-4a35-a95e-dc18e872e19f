// Listing Page Constants
export const LISTING_CONSTANTS = {
  // Page Information
  PAGE_TITLE: "Overview",
  PAGE_SUBTITLE: "Manage all your clients and their bookclosure data",
  PAGE_TITLE_USER: "Your Organization",
  PAGE_SUBTITLE_USER: "Manage your organization's bookclosure data",

  // Buttons
  ADD_BUTTON_TEXT: "Add New Client",
  ADD_BUTTON_PATH: "/masters/org/add",

  // Search and Filters
  SEARCH_PLACEHOLDER: "Search client...",
  STATUS_FILTER: {
    ALL: "All Status",
    ACTIVE: "Active",
    INACTIVE: "Inactive",
  },

  // Stats Cards - Colored backgrounds with matching icons
  STATS: {
    TOTAL_CLIENTS: {
      TITLE: "Total Clients",
      VALUE: "142",
      ICON: "users",
      BG_COLOR: "bg-blue-50",
      ICON_COLOR: "text-blue-600",
    },
    ACTIVE_CLIENTS: {
      TITLE: "Active Clients",
      VALUE: "128",
      ICON: "user-check",
      BG_COLOR: "bg-green-50",
      ICON_COLOR: "text-green-600",
    },
    PENDING_UPDATES: {
      TITLE: "Pending to sync",
      VALUE: "23",
      ICON: "clock",
      BG_COLOR: "bg-yellow-50",
      ICON_COLOR: "text-yellow-600",
    },
    THIS_MONTH: {
      TITLE: "New client onboarded TM",
      VALUE: "89",
      ICON: "trending-up",
      BG_COLOR: "bg-red-50",
      ICON_COLOR: "text-red-600",
    },
  },

  SEARCH: {
    ALL: "",
  },

  // Table Headers
  TABLE_HEADERS: {
    CLIENT_NAME: "Client Name",
    COMPANY_NAME: "Company Name",
    EMAIL: "Email",
    STATUS: "Status",
    LAST_BOOKING: "Last Closure Date",
    ACTIONS: "Actions",
  },

  // Action Labels
  ACTIONS: {
    VIEW_DETAILS: "Dashboard",
    SUBMIT_BOOK_CLOSURE: "Submit Book-closure",
    EDIT: "Edit",
    DELETE: "Delete",
  },

  // Messages
  MESSAGES: {
    LAST_UPDATED: "Updated 14 minutes ago",
    CLIENTS_COUNT: "clients",
    NO_DASHBOARDS_AVAILABLE: "No dashboards available for this organization.",
    FAILED_TO_LOAD_DASHBOARDS:
      "Failed to load dashboards. Please try again later.",
    FAILED_TO_LOAD_CLIENTS: "Failed to load clients.",
    LOADING_CLIENTS: "Loading clients...",
  },

  // Toast Configuration
  TOAST: {
    DURATION: {
      DEFAULT: 3000,
      INFO: 4000,
      ERROR: 4000,
      SUCCESS: 3000,
      WARNING: 4000,
    },
  },

  // Sync Configuration
  SYNC: {
    CRITICAL_THRESHOLD_DAYS: 30,
  },

  // Pagination
  PAGINATION: {
    ITEMS_PER_PAGE: 10,
    DATE_FORMAT: {
      month: "short",
      day: "numeric",
      year: "numeric",
    },
  },

  // Table Row Styling
  TABLE_STYLING: {
    ROW_HOVER:
      "bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 hover:shadow-sm",
    AVATAR_HOVER: "transform scale-110 shadow-lg",
    NAME_HOVER: "group-hover:text-blue-600",
    EMAIL_HOVER: "group-hover:text-gray-700",
    ACTION_HOVER: "transform translate-x-1",
  },
};
