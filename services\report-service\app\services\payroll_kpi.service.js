import PayrollKpiRepository from "../repository/payroll_kpi.repository.js";
import { LOGGER_NAMES } from "../utils/constants/log.constants.js";
import { createLogger } from "../utils/logger.utils.js";
import { isValid<PERSON>onth, isValidYear, parseNumericValue, formatToKWithDecimals } from "../utils/format.utils.js";
import { getMonthDateRange } from "../utils/repository.utils.js";

const logger = createLogger(LOGGER_NAMES.REPORT_SERVICE);

/**
 * Calculate previous month and year
 * @param {number} month - Current month (1-12)
 * @param {number} year - Current year
 * @returns {Object} Previous month and year
 */
const getPreviousMonthYear = (month, year) => {
  if (month === 1) {
    return { month: 12, year: year - 1 };
  }
  return { month: month - 1, year };
};

/**
 * Calculate percentage change between current and previous values
 * @param {number} current - Current value
 * @param {number} previous - Previous value
 * @returns {string} Percentage change with + or - sign (e.g., "+25.50%" or "-15.30%")
 */
const calculatePercentageChange = (current, previous) => {
  // Handle division by zero
  if (previous === 0) {
    if (current === 0) return "0.00%";
    return "N/A";
  }
  
  const change = ((current - previous) / previous) * 100;
  const sign = change >= 0 ? "+" : "";
  return `${sign}${change.toFixed(2)}%`;
};

/**
 * Get payroll data for a specific month
 * @param {string} schemaName - Organization schema name
 * @param {number} month - Month (1-12)
 * @param {number} year - Year
 * @returns {Promise<Object>} Payroll data with totals and doctor salary
 */
const getMonthPayrollData = async (schemaName, month, year) => {
  try {
    // Calculate date range from month and year
    const { startDate, endDate } = getMonthDateRange(month, year);
    
    // Fetch payroll data
    const payrollData = await PayrollKpiRepository.getPayrollData(
      schemaName,
      startDate,
      endDate
    );

    // Fetch doctor salary
    const doctorSalary = await PayrollKpiRepository.getDoctorSalary(
      schemaName,
      startDate,
      endDate
    );

    // Parse numeric values
    const totalPayroll = parseNumericValue(payrollData.total_payroll);
    const totalDeductions = parseNumericValue(payrollData.total_deductions);
    const totalTaxes = parseNumericValue(payrollData.total_taxes);
    const doctorSalaryValue = parseNumericValue(doctorSalary);

    // Calculate doctors contribution percentage
    let doctorsContribution = 0;
    if (totalPayroll > 0) {
      doctorsContribution = parseFloat(
        ((doctorSalaryValue / totalPayroll) * 100).toFixed(2)
      );
    }

    return {
      totalPayroll,
      totalDeductions,
      totalTaxes,
      doctorSalaryValue,
      doctorsContribution,
    };
  } catch (error) {
    logger.error(`Error fetching payroll data for month ${month}, year ${year}:`, error);
    throw error;
  }
};

/**
 * Get payroll KPI data for organization
 * @param {Object} params - Query parameters
 * @param {string} params.organization_id - Organization ID
 * @param {number} params.month - Month (1-12)
 * @param {number} params.year - Year
 * @returns {Promise<Object>} Payroll KPI data
 */
const getPayrollKpiData = async ({ organization_id, month, year }) => {
  try {
    logger.info(
      `Fetching payroll KPI data for org: ${organization_id}, month: ${month}, year: ${year}`
    );

    // Validate parameters
    if (!organization_id) {
      throw new Error("Organization ID is required");
    }

    if (!month || !isValidMonth(month)) {
      throw new Error("Valid month (1-12) is required");
    }

    if (!year || !isValidYear(year)) {
      throw new Error("Valid year is required");
    }

    // Convert to numbers
    const monthNum = parseInt(month, 10);
    const yearNum = parseInt(year, 10);

    // Get organization schema name
    const schemaName = await PayrollKpiRepository.getOrganizationSchemaName(
      organization_id
    );

    if (!schemaName) {
      logger.warn(`Schema not found for organization: ${organization_id}`);
      throw new Error("Organization not found or schema not configured");
    }

    // Calculate CURRENT MONTH payroll KPIs
    logger.info("Calculating current month payroll KPIs...");
    const currentMonthData = await getMonthPayrollData(schemaName, monthNum, yearNum);

    // Calculate PREVIOUS MONTH payroll KPIs
    logger.info("Calculating previous month payroll KPIs...");
    const { month: prevMonth, year: prevYear } = getPreviousMonthYear(monthNum, yearNum);
    const previousMonthData = await getMonthPayrollData(schemaName, prevMonth, prevYear);

    // Calculate YTD payroll KPIs
    logger.info("Calculating YTD payroll KPIs...");
    let ytdTotalPayroll = 0;
    let ytdTotalDeductions = 0;
    let ytdTotalTaxes = 0;
    let ytdDoctorSalary = 0;

    for (let m = 1; m <= monthNum; m++) {
      const monthData = await getMonthPayrollData(schemaName, m, yearNum);
      ytdTotalPayroll += monthData.totalPayroll;
      ytdTotalDeductions += monthData.totalDeductions;
      ytdTotalTaxes += monthData.totalTaxes;
      ytdDoctorSalary += monthData.doctorSalaryValue;
    }

    // Calculate YTD doctors contribution percentage
    let ytdDoctorsContribution = 0;
    if (ytdTotalPayroll > 0) {
      ytdDoctorsContribution = parseFloat(
        ((ytdDoctorSalary / ytdTotalPayroll) * 100).toFixed(2)
      );
    }

    logger.info(
      `Payroll KPI calculations completed - Current: ${currentMonthData.totalPayroll}, PM: ${previousMonthData.totalPayroll}, YTD: ${ytdTotalPayroll}`
    );

    // Return formatted response with total, pm, ytd, and pm_change
    return {
      kpi: {
        total_payroll: {
          total: formatToKWithDecimals(currentMonthData.totalPayroll),
          pm: formatToKWithDecimals(previousMonthData.totalPayroll),
          ytd: formatToKWithDecimals(ytdTotalPayroll),
          pm_change: calculatePercentageChange(currentMonthData.totalPayroll, previousMonthData.totalPayroll),
        },
        doctor_salary: {
          total: formatToKWithDecimals(currentMonthData.doctorSalaryValue),
          pm: formatToKWithDecimals(previousMonthData.doctorSalaryValue),
          ytd: formatToKWithDecimals(ytdDoctorSalary),
          pm_change: calculatePercentageChange(currentMonthData.doctorSalaryValue, previousMonthData.doctorSalaryValue),
        },
        total_deductions: {
          total: formatToKWithDecimals(currentMonthData.totalDeductions),
          pm: formatToKWithDecimals(previousMonthData.totalDeductions),
          ytd: formatToKWithDecimals(ytdTotalDeductions),
          pm_change: calculatePercentageChange(currentMonthData.totalDeductions, previousMonthData.totalDeductions),
        },
        total_taxes: {
          total: formatToKWithDecimals(currentMonthData.totalTaxes),
          pm: formatToKWithDecimals(previousMonthData.totalTaxes),
          ytd: formatToKWithDecimals(ytdTotalTaxes),
          pm_change: calculatePercentageChange(currentMonthData.totalTaxes, previousMonthData.totalTaxes),
        },
        doctors_contribution: {
          total: `${currentMonthData.doctorsContribution.toFixed(2)}%`,
          pm: `${previousMonthData.doctorsContribution.toFixed(2)}%`,
          ytd: `${ytdDoctorsContribution.toFixed(2)}%`,
          pm_change: calculatePercentageChange(currentMonthData.doctorsContribution, previousMonthData.doctorsContribution),
        },
      },
    };
  } catch (error) {
    logger.error("Error in PayrollKpiService.getPayrollKpiData:", error);
    throw error;
  }
};

export default {
  getPayrollKpiData,
};
