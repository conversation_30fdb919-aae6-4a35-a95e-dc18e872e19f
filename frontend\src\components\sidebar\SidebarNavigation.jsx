"use client";

import { usePathname } from "next/navigation";
import { Md<PERSON><PERSON><PERSON><PERSON> } from "react-icons/md";
import { FileText } from "lucide-react";
import { NAVIGATION_CONSTANTS } from "@/utils/constants/navigation";
import "./sidebar.css";

const SidebarNavigation = ({ onNavigate }) => {
  const pathname = usePathname();

  // Dashboard is active if we're on listing page or dashboard page
  const isDashboardActive =
    pathname === NAVIGATION_CONSTANTS.MAIN_MENU.DASHBOARD.PATH ||
    pathname?.startsWith("/dashboard");

  // Reports is active if we're on reports page
  const isReportsActive = pathname?.startsWith("/reports");

  return (
    <>
      <button
        onClick={() =>
          onNavigate(NAVIGATION_CONSTANTS.MAIN_MENU.DASHBOARD.PATH)
        }
        className={`sidebar-nav-item ${
          isDashboardActive
            ? "sidebar-nav-item--active"
            : "sidebar-nav-item--inactive"
        }`}
      >
        <MdPieChart size={22} className="mr-2" />
        {NAVIGATION_CONSTANTS.MAIN_MENU.DASHBOARD.LABEL}
      </button>

      <button
        onClick={() => onNavigate(NAVIGATION_CONSTANTS.MAIN_MENU.REPORTS.PATH)}
        className={`sidebar-nav-item ${
          isReportsActive
            ? "sidebar-nav-item--active"
            : "sidebar-nav-item--inactive"
        }`}
      >
        <FileText size={22} className="mr-2" />
        {NAVIGATION_CONSTANTS.MAIN_MENU.REPORTS.LABEL}
      </button>
    </>
  );
};

export default SidebarNavigation;
