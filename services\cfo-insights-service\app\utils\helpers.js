// app/utils/helpers.js

/**
 * OPTIMIZED: Essential utility functions only
 * HTML-related functions removed since we're using JSON format
 */

/** Clean text output for display (kept for potential text responses) */
export function sanitizeToPlain(text) {
  if (!text) return "";
  let s = String(text);
  s = s.replace(/```[\s\S]*?```/g, " ");
  s = s.replace(/`([^`]+)`/g, "$1");
  s = s.replace(/[*_]+/g, "");
  s = s.replace(/\r\n/g, "\n");
  s = s.replace(/\n{3,}/g, "\n\n");
  s = s.replace(/\s{2,}/g, " ").trim();
  return s;
}

/** Normalize loosely-typed boolean */
export function toBoolean(value, defaultValue = false) {
  if (value === true || value === 1 || value === "1" || value === "true")
    return true;
  if (value === false || value === 0 || value === "0" || value === "false")
    return false;
  return defaultValue;
}

/** Normalize and trim a session id */
export function normalizeSessionId(sessionId) {
  return String(sessionId || "").trim();
}
