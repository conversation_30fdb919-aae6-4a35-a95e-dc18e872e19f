import {
  SIKKA_API,
  LOG_ACTIONS,
  KPI_KEYS,
  K<PERSON>_MESSAGES,
  BATCH_PROCESSING,
  ERROR_MESSAGES,
} from "./constants.util.js";
import {
  AccountsReceivable,
  TreatmentPlanAnalysis,
  NoShowAppointments,
  AvgDailyProduction,
  NewPatients,
  TotalProductionPerDay,
  TotalProductionByDentist,
  TotalProductionByHygienist,
  HygieneReappointment,
  DirectRestorations,
  GrossCollections
} from "../models/index.model.js";
import { bulkCreate, bulkCreateInSchema } from "./database.util.js";
import { STATUS_CODE_BAD_REQUEST } from "./status_code.util.js";

/**
 * KPI Configuration Map
 * Contains all configuration needed for each KPI endpoint
 */
export const KPI_CONFIG = {
  [KPI_KEYS.ACCOUNT_RECEIVABLES]: {
    key: KPI_KEYS.ACCOUNT_RECEIVABLES,
    endpoint: SIKKA_API.ENDPOINTS.ACCOUNT_RECEIVABLES,
    model: AccountsReceivable,
    requiresDateRange: false,
    logAction: {
      fetching: LOG_ACTIONS.FETCHING_ACCOUNT_RECEIVABLES,
      success: LOG_ACTIONS.ACCOUNT_RECEIVABLES_SUCCESS,
      failed: LOG_ACTIONS.ACCOUNT_RECEIVABLES_FAILED,
    },
    message: {
      fetched: KPI_MESSAGES.ACCOUNT_RECEIVABLES_FETCHED,
    },
  },
  [KPI_KEYS.TREATMENT_ANALYSIS]: {
    key: KPI_KEYS.TREATMENT_ANALYSIS,
    endpoint: SIKKA_API.ENDPOINTS.TREATMENT_PLAN_ANALYSIS,
    model: TreatmentPlanAnalysis,
    requiresDateRange: true,
    logAction: {
      fetching: LOG_ACTIONS.FETCHING_TREATMENT_ANALYSIS,
      success: LOG_ACTIONS.TREATMENT_ANALYSIS_SUCCESS,
      failed: LOG_ACTIONS.TREATMENT_ANALYSIS_FAILED,
    },
    message: {
      fetched: KPI_MESSAGES.TREATMENT_ANALYSIS_FETCHED,
    },
  },
  [KPI_KEYS.HYGIENE_REAPPOINTMENT]: {
    key: KPI_KEYS.HYGIENE_REAPPOINTMENT,
    endpoint: SIKKA_API.ENDPOINTS.HYGIENE_REAPPOINTMENT,
    model: HygieneReappointment,
    requiresDateRange: true,
    logAction: {
      fetching: LOG_ACTIONS.FETCHING_HYGIENE_REAPPOINTMENT,
      success: LOG_ACTIONS.HYGIENE_REAPPOINTMENT_SUCCESS,
      failed: LOG_ACTIONS.HYGIENE_REAPPOINTMENT_FAILED,
    },
    message: {
      fetched: KPI_MESSAGES.HYGIENE_REAPPOINTMENT_FETCHED,
    },
  },
  [KPI_KEYS.AVG_DAILY_PRODUCTION]: {
    key: KPI_KEYS.AVG_DAILY_PRODUCTION,
    endpoint: SIKKA_API.ENDPOINTS.AVG_DAILY_PRODUCTION,
    model: AvgDailyProduction,
    requiresDateRange: true,
    logAction: {
      fetching: LOG_ACTIONS.FETCHING_AVG_DAILY_PRODUCTION,
      success: LOG_ACTIONS.AVG_DAILY_PRODUCTION_SUCCESS,
      failed: LOG_ACTIONS.AVG_DAILY_PRODUCTION_FAILED,
    },
    message: {
      fetched: KPI_MESSAGES.AVG_DAILY_PRODUCTION_FETCHED,
    },
  },
  [KPI_KEYS.NEW_PATIENTS]: {
    key: KPI_KEYS.NEW_PATIENTS,
    endpoint: SIKKA_API.ENDPOINTS.NEW_PATIENTS,
    model: NewPatients,
    requiresDateRange: true,
    logAction: {
      fetching: LOG_ACTIONS.FETCHING_NEW_PATIENTS,
      success: LOG_ACTIONS.NEW_PATIENTS_SUCCESS,
      failed: LOG_ACTIONS.NEW_PATIENTS_FAILED,
    },
    message: {
      fetched: KPI_MESSAGES.NEW_PATIENTS_FETCHED,
    },
  },
  [KPI_KEYS.NO_SHOW_APPOINTMENTS]: {
    key: KPI_KEYS.NO_SHOW_APPOINTMENTS,
    endpoint: SIKKA_API.ENDPOINTS.NO_SHOW_APPOINTMENTS,
    model: NoShowAppointments,
    requiresDateRange: true,
    logAction: {
      fetching: LOG_ACTIONS.FETCHING_NO_SHOW_APPOINTMENTS,
      success: LOG_ACTIONS.NO_SHOW_APPOINTMENTS_SUCCESS,
      failed: LOG_ACTIONS.NO_SHOW_APPOINTMENTS_FAILED,
    },
    message: {
      fetched: KPI_MESSAGES.NO_SHOW_APPOINTMENTS_FETCHED,
    },
  },
  [KPI_KEYS.DIRECT_RESTORATIONS]: {
    key: KPI_KEYS.DIRECT_RESTORATIONS,
    endpoint: SIKKA_API.ENDPOINTS.DIRECT_RESTORATIONS,
    model: DirectRestorations,
    requiresDateRange: true,
    logAction: {
      fetching: LOG_ACTIONS.FETCHING_DIRECT_RESTORATIONS,
      success: LOG_ACTIONS.DIRECT_RESTORATIONS_SUCCESS,
      failed: LOG_ACTIONS.DIRECT_RESTORATIONS_FAILED,
    },
    message: {
      fetched: KPI_MESSAGES.DIRECT_RESTORATIONS_FETCHED,
    },
  },
  [KPI_KEYS.TOTAL_PRODUCTION_PER_DAY]: {
    key: KPI_KEYS.TOTAL_PRODUCTION_PER_DAY,
    endpoint: SIKKA_API.ENDPOINTS.TOTAL_PRODUCTION_PER_DAY,
    model: TotalProductionPerDay,
    requiresDateRange: true,
    logAction: {
      fetching: LOG_ACTIONS.FETCHING_AVG_DAILY_PRODUCTION,
      success: LOG_ACTIONS.AVG_DAILY_PRODUCTION_SUCCESS,
      failed: LOG_ACTIONS.AVG_DAILY_PRODUCTION_FAILED,
    },
    message: {
      fetched: KPI_MESSAGES.TOTAL_PRODUCTION_PER_DAY_FETCHED,
    },
  },
  [KPI_KEYS.TOTAL_PRODUCTION_BY_DENTIST]: {
    key: KPI_KEYS.TOTAL_PRODUCTION_BY_DENTIST,
    endpoint: SIKKA_API.ENDPOINTS.TOTAL_PRODUCTION_BY_DENTIST,
    model: TotalProductionByDentist,
    requiresDateRange: true,
    logAction: {
      fetching: LOG_ACTIONS.FETCHING_AVG_DAILY_PRODUCTION,
      success: LOG_ACTIONS.AVG_DAILY_PRODUCTION_SUCCESS,
      failed: LOG_ACTIONS.AVG_DAILY_PRODUCTION_FAILED,
    },
    message: {
      fetched: KPI_MESSAGES.TOTAL_PRODUCTION_BY_DENTIST_FETCHED,
    },
  },
  [KPI_KEYS.TOTAL_PRODUCTION_BY_HYGIENIST]: {
    key: KPI_KEYS.TOTAL_PRODUCTION_BY_HYGIENIST,
    endpoint: SIKKA_API.ENDPOINTS.TOTAL_PRODUCTION_BY_HYGIENIST,
    model: TotalProductionByHygienist,
    requiresDateRange: true,
    logAction: {
      fetching: LOG_ACTIONS.FETCHING_AVG_DAILY_PRODUCTION,
      success: LOG_ACTIONS.AVG_DAILY_PRODUCTION_SUCCESS,
      failed: LOG_ACTIONS.AVG_DAILY_PRODUCTION_FAILED,
    },
    message: {
      fetched: KPI_MESSAGES.TOTAL_PRODUCTION_BY_HYGIENIST_FETCHED,
    },
  },
  [KPI_KEYS.GROSS_COLLECTION]: {
    key: KPI_KEYS.GROSS_COLLECTION,
    endpoint: SIKKA_API.ENDPOINTS.GROSS_COLLECTION,
    model: GrossCollections,
    requiresDateRange: true,
    logAction: {
      fetching: LOG_ACTIONS.FETCHING_AVG_DAILY_PRODUCTION,
      success: LOG_ACTIONS.AVG_DAILY_PRODUCTION_SUCCESS,
      failed: LOG_ACTIONS.AVG_DAILY_PRODUCTION_FAILED,
    },
    message: {
      fetched: KPI_MESSAGES.GROSS_COLLECTION_FETCHED,
    },
  },
};

/**
 * Generic repository function that handles both regular and schema-aware storage
 * @param {Object} model - Sequelize model
 * @param {Array} data - Data to store
 * @param {string|null} schema_name - Optional schema name
 * @returns {Promise<Array>} Created records
 */
export const storeKpiData = async (model, data, schema_name = null) => {
  if (schema_name) {
    return await bulkCreateInSchema(model, data, schema_name);
  }
  return await bulkCreate(model, data);
};

/**
 * Get KPI configuration by key
 * @param {string} kpi_key - KPI key
 * @returns {Object|null} KPI configuration or null
 */
export const getKpiConfig = (kpi_key) => {
  return KPI_CONFIG[kpi_key] || null;
};

/**
 * Validate KPI key exists
 * @param {string} kpi_key - KPI key
 * @returns {boolean} True if valid
 */
export const isValidKpiKey = (kpi_key) => {
  return !!KPI_CONFIG[kpi_key];
};

/**
 * Get all KPI keys
 * @returns {Array<string>} Array of KPI keys
 */
export const getAllKpiKeys = () => {
  return Object.keys(KPI_CONFIG);
};

/**
 * Process all KPI controllers and return their execution results
 * @param {Array} kpi_controllers - Array of KPI controller configurations with name and controller function
 * @param {Object} req - Express request object
 * @param {Object} logger - Logger instance for logging
 * @returns {Promise<Array>} Array of results with name, status, and optional error
 */
export const processAllKpiControllers = async (
  kpi_controllers,
  req,
  logger
) => {
  return await Promise.all(
    kpi_controllers.map(async (kpi) => {
      try {
        const state = { status: null, data: null };
        const mockRes = {
          status: (code) => {
            state.status = code;
            return {
              json: (data) => {
                state.data = data;
                return { statusCode: code, data };
              },
            };
          },
          json: (data) => {
            state.data = data;
            return { data };
          },
        };

        await kpi.controller(req, mockRes);

        logger.info(`${BATCH_PROCESSING.RESPONSE_FOR} ${kpi.name}`, {
          [BATCH_PROCESSING.FIELD_STATUS]: state.status,
          [BATCH_PROCESSING.FIELD_HAS_DATA]: !!state.data,
          [BATCH_PROCESSING.FIELD_IS_SUCCESS]: state.data?.success,
        });

        if (!state.data && !state.status) {
          logger.warn(
            `${BATCH_PROCESSING.NO_RESPONSE_DATA_OR_STATUS} ${kpi.name}`
          );
          return {
            [BATCH_PROCESSING.FIELD_NAME]: kpi.name,
            [BATCH_PROCESSING.FIELD_STATUS]: BATCH_PROCESSING.STATUS_FAIL,
            [BATCH_PROCESSING.FIELD_ERROR]: ERROR_MESSAGES.NO_RESPONSE_RECEIVED,
          };
        }

        if (
          state.status >= STATUS_CODE_BAD_REQUEST ||
          state.data?.success === false
        ) {
          logger.error(`${BATCH_PROCESSING.FAILED_TO_PROCESS} ${kpi.name}`, {
            [BATCH_PROCESSING.FIELD_STATUS]: state.status,
            [BATCH_PROCESSING.FIELD_MESSAGE]: state.data?.message,
          });
          return {
            [BATCH_PROCESSING.FIELD_NAME]: kpi.name,
            [BATCH_PROCESSING.FIELD_STATUS]: BATCH_PROCESSING.STATUS_FAIL,
            [BATCH_PROCESSING.FIELD_ERROR]:
              state.data?.message ||
              state.data?.[BATCH_PROCESSING.FIELD_ERRORS] ||
              `${ERROR_MESSAGES.HTTP_ERROR} ${
                state.status || ERROR_MESSAGES.UNKNOWN_ERROR
              }`,
          };
        }

        return {
          [BATCH_PROCESSING.FIELD_NAME]: kpi.name,
          [BATCH_PROCESSING.FIELD_STATUS]: BATCH_PROCESSING.STATUS_SUCCESS,
        };
      } catch (err) {
        logger.error(`${BATCH_PROCESSING.ERROR_IN} ${kpi.name}`, {
          error: err.message,
          stack: err.stack,
        });
        return {
          [BATCH_PROCESSING.FIELD_NAME]: kpi.name,
          [BATCH_PROCESSING.FIELD_STATUS]: BATCH_PROCESSING.STATUS_FAIL,
          [BATCH_PROCESSING.FIELD_ERROR]:
            err.message || ERROR_MESSAGES.UNKNOWN_ERROR,
        };
      }
    })
  );
};
