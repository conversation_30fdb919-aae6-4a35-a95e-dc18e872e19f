<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Welcome to Perfino</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
      background-color: transparent;
      padding: 0;
      margin: 0;
    }
    .email-container {
      max-width: 800px;
      width: 100%;
      margin: 0 auto;
      background-image: url('{{emailBgImage}}');
      background-size: cover;
      background-position: center center;
      background-repeat: no-repeat;
      background-color: #f0f7ff;
      position: relative;
      padding: 40px 20px;
      border-radius: 0;
    }
    .header {
      text-align: center;
      padding: 40px 20px 20px;
    }
    .logo {
      display: inline-block;
      max-width: 200px;
      height: auto;
    }
    .logo img {
      width: 100%;
      height: auto;
      display: block;
    }
    .card {
      background: #ffffff;
      border-radius: 12px;
      padding: 32px;
      margin-bottom: 24px;
    }
    .welcome-title {
      font-size: 28px;
      font-weight: bold;
      color: #1565c0;
      margin-bottom: 16px;
    }
    .greeting {
      font-size: 18px;
      font-weight: bold;
      color: #1565c0;
      margin-bottom: 16px;
    }
    .welcome-text {
      font-size: 16px;
      color: #333333;
      line-height: 1.6;
    }
    .login-details-card {
      background: #e3f2fd;
      border-radius: 12px;
      padding: 32px;
      margin-bottom: 24px;
      position: relative;
    }
    .login-details-card::before {
      content: '';
      position: absolute;
      left: 0;
      top: 0;
      bottom: 0;
      width: 4px;
      background: #4caf50;
      border-radius: 12px 0 0 12px;
    }
    .login-details-title {
      font-size: 28px;
      font-weight: bold;
      color: #1565c0;
      margin-bottom: 24px;
    }
    .detail-item {
      margin-bottom: 16px;
      font-size: 16px;
      color: #333333;
    }
    .detail-label {
      font-weight: 600;
      color: #333333;
      margin-bottom: 4px;
    }
    .detail-value {
      color: #333333;
      font-weight: 400;
    }
    .login-button {
      display: inline-block;
      background: #1565c0;
      color: #ffffff;
      text-decoration: none;
      padding: 14px 32px;
      border-radius: 8px;
      font-size: 16px;
      font-weight: 600;
      text-align: center;
      margin: 24px 0;
    }
    .button-container {
      text-align: center;
    }
    .security-title {
      font-size: 28px;
      font-weight: bold;
      color: #1565c0;
      margin-bottom: 16px;
    }
    .security-text {
      font-size: 16px;
      color: #333333;
      line-height: 1.6;
    }
    .footer {
      background: #1565c0;
      color: #ffffff;
      text-align: center;
      padding: 24px 20px;
      border-radius: 0 0 12px 12px;
      margin-top: 24px;
    }
    .footer-text {
      font-size: 14px;
      line-height: 1.6;
      color: #ffffff;
    }
  </style>
</head>
<body>
  <div class="email-container">
    <div class="header">
      <div class="logo">
        <img src="{{logoImage}}" alt="Perfino" />
      </div>
    </div>
    
    <div class="card">
      <h1 class="welcome-title">Welcome to Perfino</h1>
      <p class="greeting">Hi {{recipientName}},</p>
      <p class="welcome-text">
        We're excited to have you on board.<br>
        Perfino empowers you to transform client data into powerful business insights with AI-driven dashboards and analytics.
      </p>
    </div>
    
    <div class="login-details-card">
      <h2 class="login-details-title">Your Login Details</h2>
      <div class="detail-item">
        <div class="detail-label">Full Name</div>
        <div class="detail-value">{{recipientName}}</div>
      </div>
      <div class="detail-item">
        <div class="detail-label">Email</div>
        <div class="detail-value">{{recipientEmail}}</div>
      </div>
      <div class="detail-item">
        <div class="detail-label">Temporary Password</div>
        <div class="detail-value">{{plainPassword}}</div>
      </div>
    </div>
    
    <div class="button-container">
      <a href="{{loginUrl}}" class="login-button">Login to perfino</a>
    </div>
    
    <div class="card">
      <h2 class="security-title">Security Reminder</h2>
      <p class="security-text">
        Please change your password after your first login. For your security, Perfino recommends updating your password every 21 days.
      </p>
    </div>
    
    <div class="footer">
      <p class="footer-text">
        ©2025 Perfino. All rights reserved.<br>
        This is an automated email- please do not reply.
      </p>
    </div>
  </div>
</body>
</html>

