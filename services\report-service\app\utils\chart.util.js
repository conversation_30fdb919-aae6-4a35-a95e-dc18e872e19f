import {
  escapeHtml,
  HTML_ENTITY_ENCODE_REGEX,
  HTML_ENTITY_DECODE_REGEX,
  HTML_ENTITY_ENCODE_REPLACEMENTS,
  HTML_ENTITY_DECODE_REPLACEMENTS,
} from "./constants/pdf.constants.js";
import { CHART_ERROR_MESSAGES } from "./constants/error.constants.js";

export const generateChartConfig = (
  type,
  labels,
  data,
  backgroundColor,
  options = {}
) => {
  const defaultOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: { display: false },
      datalabels: {
        color: "#1e293b",
        font: { weight: "bold", size: 13 },
        formatter: (value) => "$" + value.toFixed(1) + "K",
      },
    },
    scales: {
      y: { beginAtZero: true, ticks: { font: { size: 12 } } },
      x: { ticks: { font: { size: 12 } } },
    },
  };

  return {
    type,
    data: {
      labels,
      datasets: [
        {
          data,
          backgroundColor: Array.isArray(backgroundColor)
            ? backgroundColor
            : [backgroundColor],
        },
      ],
    },
    options: { ...defaultOptions, ...options },
  };
};

export const generateChartCanvas = (id, config) => {
  const configWithFormatterStrings = JSON.parse(
    JSON.stringify(config, (key, value) => {
      if (typeof value === "function") {
        const funcStr = value.toString();
        return { __function__: true, __code__: funcStr };
      }
      return value;
    })
  );

  const configJson = JSON.stringify(configWithFormatterStrings);
  const escapedId = escapeHtml(id);

  // Use regex constants and replacement mappings from constants file
  let escapedConfig = configJson;
  escapedConfig = escapedConfig.replace(
    HTML_ENTITY_ENCODE_REGEX.AMPERSAND,
    HTML_ENTITY_ENCODE_REPLACEMENTS.AMPERSAND
  );
  escapedConfig = escapedConfig.replace(
    HTML_ENTITY_ENCODE_REGEX.DOUBLE_QUOTE,
    HTML_ENTITY_ENCODE_REPLACEMENTS.DOUBLE_QUOTE
  );
  escapedConfig = escapedConfig.replace(
    HTML_ENTITY_ENCODE_REGEX.SINGLE_QUOTE,
    HTML_ENTITY_ENCODE_REPLACEMENTS.SINGLE_QUOTE
  );
  escapedConfig = escapedConfig.replace(
    HTML_ENTITY_ENCODE_REGEX.LESS_THAN,
    HTML_ENTITY_ENCODE_REPLACEMENTS.LESS_THAN
  );
  escapedConfig = escapedConfig.replace(
    HTML_ENTITY_ENCODE_REGEX.GREATER_THAN,
    HTML_ENTITY_ENCODE_REPLACEMENTS.GREATER_THAN
  );

  return `<canvas id="${escapedId}" data-chart-config="${escapedConfig}"></canvas>`;
};

export const generateChartInitScript = () => {
  const decodePatterns = {
    doubleQuote: HTML_ENTITY_DECODE_REGEX.DOUBLE_QUOTE.source,
    singleQuote: HTML_ENTITY_DECODE_REGEX.SINGLE_QUOTE.source,
    lessThan: HTML_ENTITY_DECODE_REGEX.LESS_THAN.source,
    greaterThan: HTML_ENTITY_DECODE_REGEX.GREATER_THAN.source,
    ampersand: HTML_ENTITY_DECODE_REGEX.AMPERSAND.source,
  };
  const decodeReplacements = {
    doubleQuote: JSON.stringify(HTML_ENTITY_DECODE_REPLACEMENTS.DOUBLE_QUOTE),
    singleQuote: JSON.stringify(HTML_ENTITY_DECODE_REPLACEMENTS.SINGLE_QUOTE),
    lessThan: JSON.stringify(HTML_ENTITY_DECODE_REPLACEMENTS.LESS_THAN),
    greaterThan: JSON.stringify(HTML_ENTITY_DECODE_REPLACEMENTS.GREATER_THAN),
    ampersand: JSON.stringify(HTML_ENTITY_DECODE_REPLACEMENTS.AMPERSAND),
  };

  const initializationErrorMessage = JSON.stringify(
    CHART_ERROR_MESSAGES.INITIALIZATION_FAILED
  );
  const configParsingErrorMessage = JSON.stringify(
    CHART_ERROR_MESSAGES.CONFIG_PARSING_FAILED
  );

  return `(function() {
  if (!window.Chart || !window.ChartDataLabels) return;
  
  // Helper to restore functions from string representation
  const restoreFunctions = (obj) => {
    if (obj === null || typeof obj !== 'object') return obj;
    
    if (Array.isArray(obj)) {
      return obj.map(item => restoreFunctions(item));
    }
    
    const restored = {};
    for (const key in obj) {
      if (obj[key] && typeof obj[key] === 'object' && obj[key].__function__ === true) {
        // Restore function from string
        try {
          restored[key] = new Function('return ' + obj[key].__code__)();
        } catch (error) {
          restored[key] = null;
        }
      } else if (typeof obj[key] === 'object') {
        restored[key] = restoreFunctions(obj[key]);
      } else {
        restored[key] = obj[key];
      }
    }
    return restored;
  };
  
  const chartElements = document.querySelectorAll('[data-chart-config]');
  chartElements.forEach(element => {
    try {
      const configAttr = element.getAttribute('data-chart-config');
      if (!configAttr) return;
      
      // Decode HTML entities back to JSON using regex patterns and replacements from constants
      const configJson = configAttr
        .replace(new RegExp(${JSON.stringify(
          decodePatterns.doubleQuote
        )}, 'g'), ${decodeReplacements.doubleQuote})
        .replace(new RegExp(${JSON.stringify(
          decodePatterns.singleQuote
        )}, 'g'), ${decodeReplacements.singleQuote})
        .replace(new RegExp(${JSON.stringify(decodePatterns.lessThan)}, 'g'), ${
    decodeReplacements.lessThan
  })
        .replace(new RegExp(${JSON.stringify(
          decodePatterns.greaterThan
        )}, 'g'), ${decodeReplacements.greaterThan})
        .replace(new RegExp(${JSON.stringify(
          decodePatterns.ampersand
        )}, 'g'), ${decodeReplacements.ampersand});
      
      let chartConfig;
      try {
        chartConfig = JSON.parse(configJson);
      } catch (parseError) {
        console.error(${configParsingErrorMessage} + ': ' + parseError.message);
        return;
      }
      
      // Restore formatter functions
      chartConfig = restoreFunctions(chartConfig);
      chartConfig.plugins = [ChartDataLabels];
      new Chart(element, chartConfig);
    } catch (error) {
      console.error(${initializationErrorMessage} + ': ' + error.message);
    }
  });
})();`;
};

export default {
  generateChartConfig,
  generateChartCanvas,
  generateChartInitScript,
};