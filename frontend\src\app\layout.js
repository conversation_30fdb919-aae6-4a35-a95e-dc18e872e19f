import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mon<PERSON> } from "next/font/google";
import "./globals.css";
import ProvidersWrapper from "@/components/providers/ProvidersWrapper";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata = {
  title: {
    default: "Perfino",
    template: "%s | Perfino",
  },
  icons: {
    icon: "/favicon.ico",
  },
  description:
    "Perfino - Your comprehensive financial dashboard for professional accounting management, bookkeeping, client management, and financial reporting. Streamline your accounting practice with our advanced dashboard.",
  keywords: [
    "Perfino",
    "Financial Dashboard",
    "Accounting Management",
    "Bookkeeping",
    "Client Management",
    "Financial Reporting",
    "Professional Accounting",
    "Perfino Software",
    "Accounting Dashboard",
  ],
  authors: [{ name: "<PERSON>fin<PERSON>" }],
  creator: "<PERSON><PERSON><PERSON>",
  publisher: "<PERSON>fin<PERSON>",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL("http://perfino.getondataconsulting.in"),
  alternates: {
    canonical: "/",
  },
  openGraph: {
    title: "Perfino | Professional Financial Management",
    description:
      "Perfino - Your comprehensive financial dashboard for professional accounting management, bookkeeping, client management, and financial reporting.",
    url: "http://perfino.getondataconsulting.in",
    siteName: "Perfino",
    images: [
      {
        url: "/perfinologo.png",
        width: 1200,
        height: 630,
        alt: "Perfino - Professional Financial Management",
      },
    ],
    locale: "en_US",
    type: "website",
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
  category: "business",
};

export default function RootLayout({ children }) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <link rel="icon" href="/perfinologo.png" />
        <link rel="shortcut icon" href="/favicon.ico" />
        <link rel="apple-touch-icon" href="/logo-light.svg" />
        <meta name="theme-color" content="#4B3080" />
        <meta
          name="viewport"
          content="width=device-width, initial-scale=1, maximum-scale=5, viewport-fit=cover"
        />
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <ProvidersWrapper>{children}</ProvidersWrapper>
      </body>
    </html>
  );
}
