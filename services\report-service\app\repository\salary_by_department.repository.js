import { sequelize } from "../models/index.js";
import { LOGGER_NAMES } from "../utils/constants/log.constants.js";
import { createLogger } from "../utils/logger.utils.js";
import { getOrganizationSchemaName } from "../utils/repository.utils.js";

const logger = createLogger(LOGGER_NAMES.REPORT_REPOSITORY);

/**
 * Get salary data grouped by department for a specific date range
 * @param {string} schemaName - Organization schema name
 * @param {string} startDate - Start date (YYYY-MM-DD)
 * @param {string} endDate - End date (YYYY-MM-DD)
 * @returns {Promise<Array>} Salary data grouped by department
 */
const getSalaryByDepartment = async (schemaName, startDate, endDate) => {
  try {
    logger.info(
      `Fetching salary by department from schema: ${schemaName} for date range: ${startDate} to ${endDate}`
    );

    const query = `
      SELECT 
        e.department,
        COALESCE(SUM(pd.total_earnings), 0) as total_salary,
        COUNT(DISTINCT e.id) as headcount
      FROM "${schemaName}".adp_employee e
      INNER JOIN "${schemaName}".adp_payroll_details pd ON e.id = pd.employee_id
      WHERE pd.from_date >= :startDate 
        AND pd.to_date <= :endDate
      GROUP BY e.department
      ORDER BY total_salary DESC
    `;

    const results = await sequelize.query(query, {
      replacements: { startDate, endDate },
      type: sequelize.QueryTypes.SELECT,
    });

    logger.info(
      `Retrieved salary data for ${results.length} departments from schema: ${schemaName}`
    );

    return results;
  } catch (error) {
    logger.error(`Error in SalaryByDepartmentRepository.getSalaryByDepartment:`, error);
    throw error;
  }
};

export default {
  getOrganizationSchemaName,
  getSalaryByDepartment,
};
