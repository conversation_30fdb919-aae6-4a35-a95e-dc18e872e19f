import { sequelize } from "../models/index.js";
import { LOGGER_NAMES } from "../utils/constants/log.constants.js";
import { createLogger } from "../utils/logger.utils.js";
import { getOrganizationSchemaName } from "../utils/repository.utils.js";

const logger = createLogger(LOGGER_NAMES.REPORT_REPOSITORY);

/**
 * Get deductions breakdown data for a specific date range
 * @param {string} schemaName - Organization schema name
 * @param {string} startDate - Start date (YYYY-MM-DD)
 * @param {string} endDate - End date (YYYY-MM-DD)
 * @returns {Promise<Object>} Deductions breakdown data
 */
const getDeductionsBreakdown = async (schemaName, startDate, endDate) => {
  try {
    logger.info(
      `Fetching deductions breakdown from schema: ${schemaName} for date range: ${startDate} to ${endDate}`
    );

    // Define column mappings: { columnName: alias }
    const columnMappings = {
      "401k_retirement": "retirement_401k",
      retirement_401k_percentage: "retirement_401k_percentage",
      blue_cross_pre_tax: "blue_cross_pre_tax",
      deduction_total: "deduction_total",
    };

    // Check which columns exist in the table
    const columnNames = Object.keys(columnMappings);
    let existingColumnNames = [];

    try {
      const placeholders = columnNames
        .map((_, index) => `:col${index}`)
        .join(", ");
      const checkColumnsQuery = `
        SELECT column_name
        FROM information_schema.columns
        WHERE table_schema = :schemaName
          AND table_name = 'adp_payroll_details'
          AND column_name IN (${placeholders})
      `;

      const replacements = { schemaName };
      columnNames.forEach((col, index) => {
        replacements[`col${index}`] = col;
      });

      const existingColumns = await sequelize.query(checkColumnsQuery, {
        replacements,
        type: sequelize.QueryTypes.SELECT,
      });

      existingColumnNames = existingColumns.map((col) => col.column_name);
    } catch (checkError) {
      logger.warn(
        `Could not check column existence for schema: ${schemaName}, will attempt query with all columns`,
        checkError
      );
      // If column check fails, assume all columns exist and let the main query handle errors
      existingColumnNames = columnNames;
    }
    logger.info(
      `Found ${
        existingColumnNames.length
      } available columns: ${existingColumnNames.join(", ")}`
    );

    // Build SELECT clause dynamically based on available columns
    const selectClauses = [];
    for (const [columnName, alias] of Object.entries(columnMappings)) {
      if (existingColumnNames.includes(columnName)) {
        const quotedColumn = columnName.includes("401k")
          ? `"${columnName}"`
          : columnName;
        selectClauses.push(
          `COALESCE(SUM(CAST(REPLACE(REPLACE(CAST(${quotedColumn} AS TEXT), '$', ''), ',', '') AS NUMERIC)), 0) as ${alias}`
        );
      }
    }

    // If no columns exist, return default values
    if (selectClauses.length === 0) {
      logger.warn(
        `No matching columns found in table for schema: ${schemaName}`
      );
      return {
        retirement_401k: 0,
        retirement_401k_percentage: 0,
        blue_cross_pre_tax: 0,
        deduction_total: 0,
      };
    }

    const query = `
      SELECT 
        ${selectClauses.join(",\n        ")}
      FROM "${schemaName}".adp_payroll_details
      WHERE from_date >= :startDate
        AND to_date <= :endDate
    `;

    const results = await sequelize.query(query, {
      replacements: { startDate, endDate },
      type: sequelize.QueryTypes.SELECT,
    });

    // Build result object with defaults for missing columns
    const defaultResult = {
      retirement_401k: 0,
      retirement_401k_percentage: 0,
      blue_cross_pre_tax: 0,
      deduction_total: 0,
    };

    if (results.length === 0) {
      logger.info(
        `No deductions breakdown data found for schema: ${schemaName}, date range: ${startDate} to ${endDate}`
      );
      return defaultResult;
    }

    // Merge query results with defaults (query results override defaults)
    const result = { ...defaultResult, ...results[0] };

    logger.info(
      `Retrieved deductions breakdown data from schema: ${schemaName}`
    );

    return result;
  } catch (error) {
    logger.error(
      `Error in DeductionsBreakdownRepository.getDeductionsBreakdown:`,
      error
    );
    // Return default values instead of throwing error
    logger.warn(
      `Returning default values due to error for schema: ${schemaName}`
    );
    return {
      retirement_401k: 0,
      retirement_401k_percentage: 0,
      blue_cross_pre_tax: 0,
      deduction_total: 0,
    };
  }
};

export default {
  getOrganizationSchemaName,
  getDeductionsBreakdown,
};
