import express from "express";
import dotenv from "dotenv";
import cors from "cors"; // ✅ Add this
import { connectDB } from "./config/db.config.js";
import sequelize from "./config/db.config.js";
import auditLogRoutes from "./app/routes/index.js";

dotenv.config();

const app = express();

// ✅ Enable CORS before routes
app.use(
  cors({
    origin: "*", // Allow all origins for local dev
    methods: ["GET", "POST", "PUT", "DELETE"],
    allowedHeaders: ["Content-Type", "Authorization"],
  })
);

// Middleware
app.use(express.json());

// ✅ Connect DB
await connectDB();

// ✅ Sync models (creates tables if not exist)
await sequelize.sync({ alter: true });
console.log("🧩 Database synchronized successfully");

// ✅ Routes
app.use("/api", auditLogRoutes);

// ✅ Health check
app.get("/", (req, res) => {
  res.send("✅ Audit Logging System (PostgreSQL) is running");
});

// ✅ Start server
const PORT = process.env.PORT || 3000;
app.listen(PORT, () => console.log(`🚀 Server running on port ${PORT}`));
