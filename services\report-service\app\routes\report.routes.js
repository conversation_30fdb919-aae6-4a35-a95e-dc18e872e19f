import express from "express";
import FinanceController from "../controllers/finance.controller.js";
import FinanceReportController from "../controllers/finance_report.controller.js";
import PayrollReportController from "../controllers/payroll_report.controller.js";
import OperationsReportController from "../controllers/operations_report.controller.js";
import BalanceSheetController from "../controllers/balance_sheet.controller.js";
import KpiController from "../controllers/kpi.controller.js";
import IncomeExpenseStatementController from "../controllers/income_expense_statement.controller.js";
import CashFlowController from "../controllers/cash_flow.controller.js";
import RevenueExpenseController from "../controllers/revenue_expense.controller.js";
import ExpenseBreakdownController from "../controllers/expense_breakdown.controller.js";
import PayrollKpiController from "../controllers/payroll_kpi.controller.js";
import SalaryByDepartmentController from "../controllers/salary_by_department.controller.js";
import TaxBreakdownController from "../controllers/tax_breakdown.controller.js";
import DeductionsBreakdownController from "../controllers/deductions_breakdown.controller.js";
import PayrollController from "../controllers/payroll.controller.js";
import OperationsController from "../controllers/operations.controller.js";

const router = express.Router();

router.get("/", (req, res) => {
  res.status(200).json({ message: "Report API working" });
});

// Aggregated finance route - calls all finance-related services
router.get("/calculate/finance", FinanceController.calculateFinance);

// Finance report PDF
router.get("/finance/pdf", FinanceReportController.generateFinancePDF);

// Payroll report PDF
router.get("/payroll/pdf", PayrollReportController.generatePayrollPDF);

// Operations report PDF
router.get("/operations/pdf", OperationsReportController.generateOperationsPDF);

router.get("/finance/balance-sheet", BalanceSheetController.getBalanceSheet);

router.get("/finance/kpis", KpiController.getKpis);

router.get(
  "/finance/income-expense-statement",
  IncomeExpenseStatementController.getIncomeExpenseStatement
);

router.get("/finance/cash-flow", CashFlowController.getCashFlowByGroup);

router.get(
  "/finance/revenue-expense",
  RevenueExpenseController.getRevenueExpense
);

router.get(
  "/finance/expense-breakdown",
  ExpenseBreakdownController.getExpenseBreakdown
);

// Aggregated payroll route - calls all payroll-related services
router.get("/calculate/payroll", PayrollController.calculatePayroll);

router.get("/payroll/kpis", PayrollKpiController.getPayrollKpis);

router.get(
  "/payroll/salary-by-department",
  SalaryByDepartmentController.getSalaryByDepartment
);

router.get("/payroll/tax-breakdown", TaxBreakdownController.getTaxBreakdown);

router.get(
  "/payroll/deductions-breakdown",
  DeductionsBreakdownController.getDeductionsBreakdown
);

// Operations routes
router.get("/operations/overview", OperationsController.getOperationsOverview);

router.get("/operations/summary", OperationsController.getOperationsSummary);

router.get("/operations/trends", OperationsController.getOperationsTrends);

router.get("/calculate/operations", OperationsController.calculateKpis);

export default router;
