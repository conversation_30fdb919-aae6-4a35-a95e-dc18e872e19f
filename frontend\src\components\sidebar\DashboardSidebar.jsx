import React, { memo } from "react";
import { useRouter } from "next/navigation";
import { NAVIGATION_CONSTANTS } from "@/utils/constants/navigation";
import { useAuthContext } from "@/redux/Providers/AuthProvider";
import Sidebar<PERSON>ogo from "./SidebarLogo";
import SidebarNavigation from "./SidebarNavigation";
import SidebarMastersMenu from "./SidebarMastersMenu";
import SidebarFooter from "./SidebarFooter";
import "./sidebar.css";

const SidebarComponent = function DashboardSidebar() {
  const router = useRouter();
  const { isAdmin } = useAuthContext();

  const handleNavigation = (path) => {
    router.push(path);
  };

  return (
    <div
      className="sidebar-container sidebar-container--dashboard"
      style={{ backgroundColor: NAVIGATION_CONSTANTS.SIDEBAR.COLORS.PRIMARY }}
    >
      {/* Logo Section */}
      <SidebarLogo />

      {/* Navigation Menu */}
      <div className="sidebar-content">
        <nav className="sidebar-nav">
          {/* Dashboard */}
          <SidebarNavigation onNavigate={handleNavigation} />

          {/* Masters menu with submenu */}
          <SidebarMastersMenu isAdmin={isAdmin} onNavigate={handleNavigation} />
        </nav>
          </div>

      {/* Footer with Profile and Logout */}
          <SidebarFooter variant="dashboard" />
    </div>
  );
};

const DashboardSidebar = memo(SidebarComponent);
export default DashboardSidebar;
