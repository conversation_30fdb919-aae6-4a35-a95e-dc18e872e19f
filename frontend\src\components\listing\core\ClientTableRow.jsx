"use client";

import { memo } from "react";
import StatusBadge from "../../common/StatusBadge";
import TableActions from "../../common/TableActions";
import { LISTING_CONSTANTS } from "@/utils/constants/listing";
import ClientAvatar from "./ClientAvatar";
import SyncBadges from "./SyncBadges";
import "@/styles/listing.css";

const ClientTableRow = memo(function ClientTableRow({
  client,
  isLastRow,
  isHovered,
  onMouseEnter,
  onMouseLeave,
  actions,
}) {
  return (
    <tr
      className={`listing-table-row ${isLastRow ? "listing-table-row-last" : ""}`}
      onMouseEnter={onMouseEnter}
      onMouseLeave={onMouseLeave}
    >
      <td className="listing-table-cell">
        <div className="listing-client-info">
          <ClientAvatar name={client.name} isHovered={isHovered} />
          <div className="listing-client-details">
            <div className="listing-client-name">
              {client.name}
            </div>
            <div className="listing-client-email">
              {client.email}
            </div>
          </div>
        </div>
      </td>
      <td className="listing-table-cell-center">
        <div className="flex justify-center">
          <StatusBadge
            status={client.is_active ? "Active" : "Inactive"}
            variant="outline"
          />
        </div>
      </td>
      <td className="listing-table-cell-center">
        <div className="flex justify-center">
          <SyncBadges client={client} />
        </div>
      </td>
      <td className="listing-table-cell-right">
        <div
          className={`flex justify-end transition-all duration-300 ${
            isHovered ? LISTING_CONSTANTS.TABLE_STYLING.ACTION_HOVER : ""
          }`}
        >
          <TableActions item={client} actions={actions} variant="buttons" />
        </div>
      </td>
    </tr>
  );
});

export default ClientTableRow;
