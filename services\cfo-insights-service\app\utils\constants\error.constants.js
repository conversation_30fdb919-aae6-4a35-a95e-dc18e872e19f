export const ERROR_MESSAGES = {
  SERVER: {
    INTERNAL_SERVER_ERROR: "Internal server error",
    ROUTE_NOT_FOUND: "Route not found",
    ORIGIN_NOT_ALLOWED: "Origin not allowed by CORS policy",
    CORS_POLICY_VIOLATION: "CORS policy violation",
    DATABASE_CONNECTION_FAILED: "Database connection failed",
    SERVICE_UNAVAILABLE: "Service temporarily unavailable",
    INVALID_REQUEST: "Invalid request",
    UNAUTHORIZED: "Unauthorized access",
    FORBIDDEN: "Forbidden access",
    VALIDATION_ERROR: "Validation error",
    RATE_LIMIT_EXCEEDED: "Rate limit exceeded",
    FILE_TOO_LARGE: "File too large",
    UNSUPPORTED_FILE_TYPE: "Unsupported file type",
    PROCESSING_ERROR: "Error processing request",
    AZURE_CONNECTION_FAILED: "Azure connection failed",
    DOCUMENT_PROCESSING_FAILED: "Document processing failed",
    INSIGHTS_GENERATION_FAILED: "Insights generation failed",
  },
  CLIENT: {
    BAD_REQUEST: "Bad request",
    UNAUTHORIZED: "Unauthorized",
    FORBIDDEN: "Forbidden",
    NOT_FOUND: "Not found",
    CONFLICT: "Conflict",
    UNPROCESSABLE_ENTITY: "Unprocessable entity",
    TOO_MANY_REQUESTS: "Too many requests",
    PAYLOAD_TOO_LARGE: "Payload too large",
    UNSUPPORTED_MEDIA_TYPE: "Unsupported media type",
  },
  VALIDATION: {
    REQUIRED_FIELD: "This field is required",
    INVALID_EMAIL: "Invalid email format",
    INVALID_URL: "Invalid URL format",
    INVALID_UUID: "Invalid UUID format",
    INVALID_DATE: "Invalid date format",
    INVALID_NUMBER: "Invalid number format",
    INVALID_BOOLEAN: "Invalid boolean value",
    INVALID_ARRAY: "Invalid array format",
    INVALID_OBJECT: "Invalid object format",
    MIN_LENGTH: "Minimum length not met",
    MAX_LENGTH: "Maximum length exceeded",
    MIN_VALUE: "Minimum value not met",
    MAX_VALUE: "Maximum value exceeded",
    INVALID_FILE_TYPE: "Invalid file type",
    FILE_SIZE_EXCEEDED: "File size exceeded",
    INVALID_QUERY_PARAMS: "Invalid query parameters",
    MISSING_HEADERS: "Required headers missing",
    INVALID_CONTENT_TYPE: "Invalid content type",
    ORG_ID_REQUIRED: "orgId is required and cannot be empty",
    ORG_NAME_REQUIRED: "organizationName (or orgName) is required and cannot be empty",
    ORG_ID_EMPTY: "orgId cannot be empty",
    ORG_NAME_EMPTY: "orgName cannot be empty",
    FILENAME_EMPTY: "filename cannot be empty",
    MONTH_REQUIRED: "month is required and must be a valid number (1-12)",
    YEAR_REQUIRED: "year is required and must be a valid number",
    MONTH_INVALID: "Invalid month. Must be a number between 1 and 12",
    YEAR_INVALID: "Invalid year. Must be a number between 2000 and 2100",
    ORGANIZATION_ID_REQUIRED: "organizationId is required and cannot be empty",
    ORGANIZATION_NAME_REQUIRED: "organizationName is required and cannot be empty",
    FOLDER_REQUIRED: "folder (service) is required to resolve file",
    FOLDER_INVALID: "Expected folder to be one of: Finance, Operations, Payroll",
    MISSING_REQUIRED_FIELDS: "Missing required fields",
  },
  CHAT: {
    FILENAME_REQUIRED: "filename is required",
    SESSION_AND_MESSAGE_REQUIRED: "sessionId and message are required",
    SESSION_ID_REQUIRED: "sessionId is required",
    DOCUMENT_ID_REQUIRED: "documentId is required",
    INVALID_SESSION_ID: "Invalid sessionId",
    SESSION_NOT_FOUND: "Session not found or expired",
    SESSION_NOT_FOUND_OR_EXPIRED: "Session not found or expired",
    SESSION_ALREADY_EXPIRED: "Session not found or already expired",
    FAILED_START_CHAT: "Failed to start chat",
    FAILED_SEND_MESSAGE: "Failed to send message",
    FAILED_SEND_SUMMARY: "Failed to send summary",
    FAILED_END_SESSION: "Failed to end session",
    INVALID_JSON_PAYLOAD: "Invalid JSON payload",
    ORGANIZATION_CONTEXT_MISMATCH:
      "Organization context does not match the active session",
  },
  GENERAL: {
    GENERIC_ERROR: "An unexpected error occurred.",
    INTERNAL_ERROR: "Internal service error.",
    FILE_SERVICE_URL_MISSING: "File service base URL is not configured.",
    DOCUMENT_IDENTIFIER_REQUIRED: "Document identifier is required.",
    DOCUMENT_RESOLVE_FAILED: "Unable to resolve document record.",
    DOCUMENT_NOT_FOUND: "Document not found in storage.",
    DOCUMENT_UPDATE_FAILED: "Unable to update document record.",
    AZURE_CONNECTION_STRING_MISSING:
      "Azure blob connection string is not configured.",
    AZURE_CONTAINER_NAME_MISSING:
      "Azure blob container name is not configured.",
    DOWNLOAD_HTTP_FAILED: "Failed to download PDF from remote resource.",
    DOWNLOAD_FAILED: "Failed to download PDF content.",
    SUMMARY_QUEUE_DOCUMENT_MISSING:
      "Document identifier is required to queue summary generation.",
    SUMMARY_DOCUMENT_NOT_FOUND: "Document not found while preparing summary.",
    FILE_RESOLUTION_INCOMPLETE: "Resolved file is missing blob metadata.",
    FILE_RESOLUTION_FAILED: "Failed to resolve file",
    FILE_RESOLUTION_REQUIRED: "File resolution is mandatory.",
    AZURE_OPENAI_API_KEY_MISSING: "Azure OpenAI API key is not configured.",
    AZURE_OPENAI_CONFIGURATION_INCOMPLETE:
      "Azure OpenAI configuration is incomplete.",
    PDF_BUFFER_EXPECTED: "PDF buffer is required for text extraction.",
    OPENAI_EMPTY_RESPONSE: "No response generated.",
    FILENAME_REQUIRED_RESOLVE: "filename is required to resolve document",
    ORGANIZATION_ID_REQUIRED_RESOLVE: "organizationId is required to resolve file",
    ORGANIZATION_NAME_REQUIRED_RESOLVE: "organizationName is required to resolve file",
    MONTH_REQUIRED_RESOLVE: "month is required and must be a valid number (1-12) to resolve file",
    YEAR_REQUIRED_RESOLVE: "year is required and must be a valid number to resolve file",
    REQUEST_TIMEOUT: "Request timeout while resolving file",
    FILE_SERVICE_TIMEOUT: "The file service took too long to respond. Please try again.",
    FILE_NOT_FOUND_VERIFY_PATH: "File not found. Please verify the file path",
    FILE_MISSING_BLOB_METADATA: "File found but missing blob metadata",
    FILE_RESOLUTION_NO_DATA: "No resolution data returned",
    FILE_RESOLUTION_NO_FILES: "No files returned from file service",
    FILE_RESOLUTION_HTTP_404: "HTTP 404 from file service",
    FILE_RESOLUTION_BAD_REQUEST: "Bad request to file service",
    FILE_SERVICE_ERROR: "File service error while resolving",
    INVALID_JSON_FORMAT: "Invalid JSON format",
    MISSING_REQUIRED_SECTIONS: "Missing required sections",
    NO_DATA_EXTRACTED: "No data extracted from document",
    FAILED_EXTRACT_FINANCIAL_DATA: "Failed to extract financial data",
    NO_HTML_GENERATED: "No HTML generated",
    FAILED_GENERATE_HTML: "Failed to generate HTML",
    NO_JSON_GENERATED: "No JSON generated",
    FAILED_GENERATE_JSON: "Failed to generate JSON",
    MISSING_SECTIONS_ARRAY: "Missing 'sections' array in JSON output",
  },
};

export const ERROR_PATTERNS = {
  CHAT: {
    INVALID_SESSION_ID: /invalid sessionid/i,
  },
};
