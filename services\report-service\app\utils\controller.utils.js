/**
 * Validate required query parameters
 * @param {Object} params - Query parameters object
 * @param {Array<string>} requiredParams - Array of required parameter names
 * @returns {Object|null} Error object if validation fails, null if successful
 */
export const validateRequiredParams = (params, requiredParams) => {
  for (const param of requiredParams) {
    if (!params[param]) {
      return {
        success: false,
        message: `${getParamDisplayName(param)} is required`,
        error: `Missing required parameter: ${param}`,
      };
    }
  }
  return null;
};

/**
 * Get display name for parameter
 * @param {string} param - Parameter name
 * @returns {string} Display name
 */
const getParamDisplayName = (param) => {
  const displayNames = {
    organization_id: "Organization ID (organization_id)",
    org_id: "Organization ID (org_id)",
    month: "Month",
    year: "Year",
  };
  return displayNames[param] || param;
};

/**
 * Handle controller errors with appropriate status codes
 * @param {Error} error - Error object
 * @param {Object} res - Express response object
 * @param {string} defaultMessage - Default error message
 */
export const handleControllerError = (error, res, defaultMessage) => {
  // Handle validation errors with 400 status
  if (
    error.message.includes("required") ||
    error.message.includes("Valid") ||
    error.message.includes("Invalid")
  ) {
    return res.status(400).json({
      success: false,
      message: error.message,
      error: error.message,
    });
  }

  // Handle not found errors with 404 status
  if (error.message.includes("not found")) {
    return res.status(404).json({
      success: false,
      message: error.message,
      error: error.message,
    });
  }

  // Handle all other errors with 500 status
  return res.status(500).json({
    success: false,
    message: defaultMessage,
    error: error.message,
  });
};

/**
 * Send success response
 * @param {Object} res - Express response object
 * @param {string} message - Success message
 * @param {*} data - Response data
 * @param {number} statusCode - HTTP status code (default: 200)
 */
export const sendSuccessResponse = (res, message, data, statusCode = 200) => {
  return res.status(statusCode).json({
    success: true,
    message,
    data,
  });
};

export default {
  validateRequiredParams,
  handleControllerError,
  sendSuccessResponse,
};
