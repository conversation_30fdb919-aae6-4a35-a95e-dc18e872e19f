import React from "react";
import { Button } from "../ui/button";
import { Plus } from "lucide-react";
import { useRouter } from "next/navigation";

export default function PageTitle({
  title,
  subtitle,
  addButtonText,
  addButtonPath,
  onAddClick,
  showAddButton = true,
  addButtonIcon = <Plus />,
  className = "",
  children,
}) {
  const router = useRouter();

  const handleAddClick = () => {
    if (onAddClick) {
      onAddClick();
    } else if (addButtonPath) {
      router.push(addButtonPath);
    }
  };

  return (
    <div
      className={`flex flex-col md:flex-row md:items-center md:justify-between w-full mb-6 ${className}`}
    >
      {/* Left: Title and Subtitle */}
      <div className="mb-6 md:mb-0">
        <h1 className="text-2xl font-semibold text-text-heading">{title}</h1>
        {subtitle && (
          <p className="text-sm text-text-body mt-1 mb-0">{subtitle}</p>
        )}
      </div>

      {/* Center: Custom content */}
      {children && (
        <div className="flex-1 flex items-center justify-center gap-3 mb-4 md:mb-0">
          {children}
        </div>
      )}

      {/* Right: Add Button */}
      {showAddButton && addButtonText && (
        <div className="flex items-center mt-4 md:mt-0">
          <Button leftIcon={addButtonIcon} onClick={handleAddClick}>
            {addButtonText}
          </Button>
        </div>
      )}
    </div>
  );
}
