import {
  CashFlowReport,
  CashFlowLine,
  CashFlowTotal,
} from "../models/index.js";
import logger from "../../config/logger.config.js";
import { createInSchema, createBulkInSchema } from "./database.utils.js";
import { CASH_FLOW_LOGS } from "./constants/log.constants.js";

function parseAmount(v) {
  if (v == null) return null;
  const n = Number(String(v).replace(/,/g, "").trim());
  return Number.isFinite(n) ? n : null;
}

// Map QBO groups/sections to our normalized buckets
function normalizeGroup(rawGroup, headerLabel) {
  const g = String(rawGroup || "").toLowerCase();
  const h = String(headerLabel || "").toLowerCase();

  if (g.includes("operating") || h.includes("operating")) return "Operating";
  if (g.includes("investing") || h.includes("investing")) return "Investing";
  if (g.includes("financing") || h.includes("financing")) return "Financing";
  if (
    g.includes("cashincrease") ||
    h.includes("net cash increase") ||
    h.includes("net cash decrease") ||
    h.includes("net cash")
  )
    return "NetCash";
  if (g.includes("beginningcash") || h.includes("cash at beginning"))
    return "BeginningCash";
  if (g.includes("endingcash") || h.includes("cash at end")) return "EndingCash";
  return null;
}

/**
 * Calculate totals from cash flow lines
 * @param {Array} lines - Flattened cash flow lines
 * @returns {Object} Totals object
 */
function calculateTotals(lines) {
  const totals = {
    operating: 0,
    investing: 0,
    financing: 0,
    net_cash_flow: 0,
    beginning_cash: 0,
    ending_cash: 0,
  };

  for (const line of lines) {
    const group = line.group?.toLowerCase() || "";
    const amount = line.amount || 0;

    if (group === "operating") totals.operating += amount;
    else if (group === "investing") totals.investing += amount;
    else if (group === "financing") totals.financing += amount;
    else if (group === "netcash") totals.net_cash_flow = amount;
    else if (group === "beginningcash") totals.beginning_cash = amount;
    else if (group === "endingcash") totals.ending_cash = amount;
  }

  return totals;
}

/**
 * Flatten QuickBooks Cash Flow data into a structured format
 * @param {Object} cashFlowData - Raw QuickBooks Cash Flow data
 * @returns {Object} Flattened data with header and lines
 */
export function flattenCashFlow(cashFlowData) {
  const flat = [];
  const header = cashFlowData?.Header || {};
  const rowsNode = cashFlowData?.Rows || {};

  function walk(node, path = [], currentGroup = null) {
    const rows = node?.Row || [];
    for (const row of rows) {
      const rowType = row.type;
      let newPath = path.slice();
      let group = currentGroup;

      // Header section updates path + maybe group
      const headerLabel = row.Header?.ColData?.[0]?.value || "";
      if (headerLabel) {
        newPath.push(headerLabel);
        const normalized = normalizeGroup(row.group, headerLabel);
        if (normalized) group = normalized;
      }

      if (rowType === "Data") {
        const columnData = row.ColData || [];
        flat.push({
          path: newPath.join(" > "),
          label: columnData[0]?.value || "",
          group: normalizeGroup(row.group, columnData[0]?.value) || group,
          amount: parseAmount(columnData[1]?.value),
        });
      }

      const hasSummary = row.Summary?.ColData?.length >= 2;
      if (rowType === "Section" && hasSummary) {
        const summaryData = row.Summary.ColData;
        flat.push({
          path: newPath.join(" > "),
          label: summaryData[0]?.value || "",
          group: normalizeGroup(row.group, summaryData[0]?.value) || group,
          amount: parseAmount(summaryData[1]?.value),
        });
      }

      if (row.Rows) walk(row.Rows, newPath, group);
    }
  }

  walk(rowsNode, [], null);
  return { header, lines: flat };
}

/**
 * Transform flattened cash flow data into PowerBI KPI format
 * @param {Array} lines - Flattened cash flow lines
 * @returns {Object} PowerBI KPI JSON structure
 */
export function transformToPowerBIKPI(lines) {
  const kpi = {
    report_type: "CashFlow",
    generated_at: new Date().toISOString(),
    activities: { operating: {}, investing: {}, financing: {} },
    totals: {
      operating: 0,
      investing: 0,
      financing: 0,
      net_cash_flow: 0,
      beginning_cash: 0,
      ending_cash: 0,
    },
  };

  for (const line of lines) {
    const group = line.group?.toLowerCase() || "";
    const amount = line.amount || 0;

    if (group === "operating") {
      kpi.activities.operating[line.label] = amount;
      kpi.totals.operating += amount;
    } else if (group === "investing") {
      kpi.activities.investing[line.label] = amount;
      kpi.totals.investing += amount;
    } else if (group === "financing") {
      kpi.activities.financing[line.label] = amount;
      kpi.totals.financing += amount;
    } else if (group === "netcash") {
      kpi.totals.net_cash_flow = amount;
    } else if (group === "beginningcash") {
      kpi.totals.beginning_cash = amount;
    } else if (group === "endingcash") {
      kpi.totals.ending_cash = amount;
    }
  }

  return kpi;
}

/**
 * Insert or update cash flow report header
 * @param {Object} cashFlowData - Raw QuickBooks data
 * @param {Object} powerbiKpiJson - PowerBI KPI JSON
 * @param {string} realmId - Realm ID
 * @param {string} schemaName - Schema name (optional)
 * @param {number} existingReportId - Existing report ID for update (optional)
 * @returns {number} Report ID
 */
export async function insertReportHeader(
  cashFlowData,
  powerbiKpiJson,
  realmId,
  schemaName = null,
  existingReportId = null
) {
  try {
    const header = cashFlowData?.Header || {};
    const reportData = {
      report_name: header.ReportName || "CashFlow",
      report_basis: header.ReportBasis || "Cash",
      start_date: header.StartPeriod || null,
      end_date: header.EndPeriod || null,
      currency: header.Currency || "USD",
      generated_at: header.Time ? new Date(header.Time) : new Date(),
      raw: JSON.stringify(cashFlowData),
      powerbi_kpi_json: JSON.stringify(powerbiKpiJson),
      realm_id: realmId,
    };

    let report;
    if (existingReportId) {
      if (schemaName) {
        const { updateInSchema } = await import("./database.utils.js");
        await updateInSchema(CashFlowReport, schemaName, existingReportId, reportData);
      } else {
        await CashFlowReport.update(reportData, { where: { id: existingReportId } });
      }
        report = { id: existingReportId };
    } else {
      report = schemaName
        ? await createInSchema(CashFlowReport, schemaName, reportData)
        : await CashFlowReport.create(reportData);
    }
    return report.id;
  } catch (error) {
    logger.error(CASH_FLOW_LOGS.INSERT_HEADER_FAILED, {
      error: error.message,
      realmId,
      status: 500,
    });
    throw error;
  }
}

/**
 * Insert cash flow lines
 * @param {number} reportId - Report ID
 * @param {Array} lines - Flattened lines
 * @param {string} realmId - Realm ID
 * @param {string} schemaName - Schema name (optional)
 */
export async function insertLines(reportId, lines, realmId, schemaName = null) {
  if (!lines.length) return;

  try {
    const prepared = lines.map((line) => ({
      report_id: reportId,
      path: line.path,
      label: line.label,
      group: line.group,
      amount: line.amount,
      realm_id: realmId,
    }));

    const bulkInsert = schemaName
      ? (data) => createBulkInSchema(CashFlowLine, schemaName, data, { validate: false })
      : (data) =>
          CashFlowLine.bulkCreate(data, { validate: false, ignoreDuplicates: true });

    await bulkInsert(prepared);
  } catch (error) {
    logger.error(CASH_FLOW_LOGS.INSERT_LINES_FAILED, {
      error: error.message,
      reportId,
      realmId,
      status: 500,
    });
    throw error;
  }
}

/**
 * Insert cash flow totals
 * @param {number} reportId - Report ID
 * @param {Array} lines - Flattened lines
 * @param {string} realmId - Realm ID
 * @param {string} schemaName - Schema name (optional)
 */
export async function insertTotals(
  reportId,
  lines,
  realmId,
  schemaName = null
) {
  try {
    const totals = calculateTotals(lines);
    const totalData = { report_id: reportId, ...totals, realm_id: realmId };

    if (schemaName) {
      await createInSchema(CashFlowTotal, schemaName, totalData);
    } else {
      await CashFlowTotal.create(totalData);
    }
  } catch (error) {
    logger.error(CASH_FLOW_LOGS.INSERT_TOTALS_FAILED, {
      error: error.message,
      reportId,
      realmId,
      status: 500,
    });
    throw error;
  }
}

/**
 * Process cash flow data and save to database
 * @param {Object} cashFlowData - Raw QuickBooks data
 * @param {string} realmId - Realm ID
 * @param {string} schemaName - Schema name (optional)
 * @param {number} existingReportId - Existing report ID for update (optional)
 * @returns {Object} Processing result
 */
export async function processCashFlowData(
  cashFlowData,
  realmId,
  schemaName = null,
  existingReportId = null
) {
  try {
    const { header, lines } = flattenCashFlow(cashFlowData);
    const powerbiKpiJson = transformToPowerBIKPI(lines);

    const reportId = await insertReportHeader(
      cashFlowData,
      powerbiKpiJson,
      realmId,
      schemaName,
      existingReportId
    );

    await insertLines(reportId, lines, realmId, schemaName);
    await insertTotals(reportId, lines, realmId, schemaName);

    const totals = calculateTotals(lines);

    return { reportId, linesCount: lines.length, totals, powerbiKpiJson };
  } catch (error) {
    logger.error(CASH_FLOW_LOGS.PROCESS_DATA_FAILED, {
      error: error.message,
      realmId,
      status: 500,
    });
    throw error;
  }
}

/**
 * Get cash flow reports by realm ID
 * @param {string} realmId - Realm ID
 * @param {Object} options - Query options
 * @returns {Array} Cash flow reports
 */
export async function getCashFlowReportsByRealmId(realmId, options = {}) {
  try {
    const reports = await CashFlowReport.findAll({
      where: { realm_id: realmId },
      include: [
        { model: CashFlowLine, as: "lines" },
        { model: CashFlowTotal, as: "totals" },
      ],
      order: [["created_at", "DESC"]],
      ...options,
    });

    return reports;
  } catch (error) {
    logger.error(CASH_FLOW_LOGS.GET_REPORTS_FAILED, {
      error: error.message,
      realmId,
      status: 500,
    });
    throw error;
  }
}
