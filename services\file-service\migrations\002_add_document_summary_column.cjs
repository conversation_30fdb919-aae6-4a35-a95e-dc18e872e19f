"use strict";

const TABLE_REFERENCE = { tableName: "document", schema: "Authentication" };
const COLUMN_NAME = "summary";

async function columnExists(queryInterface) {
  const description = await queryInterface.describeTable(
    TABLE_REFERENCE.tableName,
    { schema: TABLE_REFERENCE.schema }
  );

  return Object.prototype.hasOwnProperty.call(description, COLUMN_NAME);
}

module.exports = {
  up: async function (queryInterface, Sequelize) {
    const exists = await columnExists(queryInterface);

    if (exists) {
      return;
    }

    await queryInterface.addColumn(
      TABLE_REFERENCE,
      COLUMN_NAME,
      {
        type: Sequelize.TEXT,
        allowNull: true,
        comment: "Document summary text",
      }
    );
  },

  down: async function (queryInterface, Sequelize) {
    const exists = await columnExists(queryInterface);

    if (!exists) {
      return;
    }

    await queryInterface.removeColumn(TABLE_REFERENCE, COLUMN_NAME);
  },
};

