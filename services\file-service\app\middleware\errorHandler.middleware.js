// app/middleware/errorHandler.middleware.js
import logger from "../../config/logger.config.js";
import {
  STATUS_CODE_BAD_REQUEST,
  STATUS_CODE_INTERNAL_SERVER_ERROR,
  STATUS_CODE_NOT_FOUND,
} from "../utils/status_code.utils.js";
import {
  ERROR_MESSAGES,
  ERROR_LOG_MESSAGES,
} from "../utils/constants/error.constants.js";
import { errorResponse } from "../utils/response.util.js";

/**
 * Global error handler middleware
 */
export const errorHandler = (err, req, res, next) => {
  // Log the error
  logger.error(`${ERROR_LOG_MESSAGES.ERROR_OCCURRED}: ${err.message}`, err);

  // Handle specific error types using early return pattern
  if (err.name === ERROR_MESSAGES.MULTER_ERROR) {
    return res.status(STATUS_CODE_BAD_REQUEST).json(
      errorResponse(ERROR_MESSAGES.FILE_UPLOAD_ERROR, err.message)
    );
  }

  // Handle JSON parsing errors (SyntaxError from express.json())
  // Express.json() throws SyntaxError with status 400 and body property
  if (err instanceof SyntaxError && err.status === 400) {
    const errorMessage = err.message || "Invalid JSON format";
    return res.status(STATUS_CODE_BAD_REQUEST).json(
      errorResponse(
        ERROR_MESSAGES.JSON_PARSE_ERROR,
        `The request body contains invalid JSON: ${errorMessage}. Please ensure your JSON is properly formatted with escaped quotes and valid syntax.`
      )
    );
  }

  // REFACTORED: No fallback operators - use error properties directly
  // Guard clause: validate error properties exist
  const statusCode = err.statusCode ? err.statusCode : STATUS_CODE_INTERNAL_SERVER_ERROR;
  const message = err.message ? err.message : ERROR_MESSAGES.INTERNAL_SERVER_ERROR;

  res.status(statusCode).json(
    errorResponse(message, ERROR_MESSAGES.REQUEST_PROCESSING_ERROR)
  );
};

/**
 * 404 Not Found handler
 */
export const notFoundHandler = (req, res) => {
  res.status(STATUS_CODE_NOT_FOUND).json(
    errorResponse(ERROR_MESSAGES.ROUTE_NOT_FOUND, `${ERROR_MESSAGES.ROUTE_NOT_FOUND_MESSAGE}: ${req.originalUrl}`)
  );
};

