import { BOOKCLOSURE_CONSTANTS } from "../constants/bookclosure";

/**
 * Generate dynamic array of completed months for the current year
 * Returns months from January up to (but not including) the current month
 * @returns {string[]} Array of month strings in format "MonthName YYYY" (e.g., "January 2025")
 */
export const getBookkeepingMonths = () => {
  const now = new Date();
  const currentYear = now.getFullYear();
  const currentMonth = now.getMonth(); // 0-indexed (0 = January, 10 = November)

  const monthNames = BOOKCLOSURE_CONSTANTS.MONTH_NAMES.FULL;

  // Generate months from January up to (but not including) the current month
  const months = monthNames
    .slice(0, currentMonth)
    .map((monthName) => `${monthName} ${currentYear}`);

  return months;
};

/**
 * Validate if the month in the uploaded file name matches the selected month
 *
 * @param {*} selectedMonth
 * @param {*} fileName
 * @returns
 */
export const isFileMonthMatching = (selectedMonth, fileName) => {
  try {
    if (!selectedMonth || !fileName) return false;

    const match = fileName.match(/\d{2}([A-Za-z]+)\d{4}_\d{2}([A-Za-z]+)\d{4}/);

    if (!match) return false;

    const monthFromFile = match[1];
    const yearFromFile = fileName.match(/\d{4}/g)?.[0];

    const [selectedMonthName, selectedYear] = selectedMonth.split(" ");

    return (
      monthFromFile.toLowerCase() === selectedMonthName.toLowerCase() &&
      yearFromFile === selectedYear
    );
  } catch (_error) {
    return false;
  }
};

/**
 * Parse month string into month, year, monthYear, and monthName
 * Supports formats: "January 2025", "Jan-2025", "Jan-25", Date objects
 * @param {string} monthStr - Month string to parse
 * @returns {Object} Object with month, year, monthYear, monthName
 */
export const parseMonthString = (monthStr) => {
  const monthNames = BOOKCLOSURE_CONSTANTS.MONTH_NAMES.ABBREVIATED;
  const fullMonthNames = BOOKCLOSURE_CONSTANTS.MONTH_NAMES.FULL;

  if (!monthStr) {
    const now = new Date();
    const month = now.getMonth() + 1;
    const year = now.getFullYear();
    const monthName = monthNames[month - 1];
    const fullMonthName = fullMonthNames[month - 1];
    const monthYear = `${fullMonthName}-${year}`;
    return { month, year, monthYear, monthName };
  }

  const monthNameMatch = monthStr.match(/^([A-Za-z]+)\s+(\d{4})$/);
  if (monthNameMatch) {
    const [, monthNameFull, yearStr] = monthNameMatch;
    const monthIndex = fullMonthNames.findIndex(
      (m) => m.toLowerCase() === monthNameFull.toLowerCase()
    );
    if (monthIndex !== -1) {
      const month = monthIndex + 1;
      const year = parseInt(yearStr, 10);
      const monthAbbr = monthNames[monthIndex];
      const fullMonthName = fullMonthNames[monthIndex];
      const monthYear = `${fullMonthName}-${year}`;
      return { month, year, monthYear, monthName: monthAbbr };
    }
  }

  const date = new Date(monthStr);
  if (!Number.isNaN(date.getTime())) {
    const month = date.getMonth() + 1;
    const year = date.getFullYear();
    const monthName = monthNames[month - 1];
    const fullMonthName = fullMonthNames[month - 1];
    const monthYear = `${fullMonthName}-${year}`;
    return { month, year, monthYear, monthName };
  }

  const dashMatch4Digit = monthStr.match(/^([A-Za-z]{3})-(\d{4})$/);
  if (dashMatch4Digit) {
    const [, monthAbbr, yearStr] = dashMatch4Digit;
    const monthIndex = monthNames.findIndex(
      (m) => m.toLowerCase() === monthAbbr.toLowerCase()
    );
    if (monthIndex !== -1) {
      const month = monthIndex + 1;
      const year = parseInt(yearStr, 10);
      const fullMonthName = fullMonthNames[monthIndex];
      const monthYear = `${fullMonthName}-${year}`;
      return { month, year, monthYear, monthName: monthAbbr };
    }
  }

  const dashMatch2Digit = monthStr.match(/^([A-Za-z]{3})-(\d{2})$/);
  if (dashMatch2Digit) {
    const [, monthAbbr, yearSuffix] = dashMatch2Digit;
    const monthIndex = monthNames.findIndex(
      (m) => m.toLowerCase() === monthAbbr.toLowerCase()
    );
    if (monthIndex !== -1) {
      const month = monthIndex + 1;
      const year = 2000 + parseInt(yearSuffix, 10);
      const fullMonthName = fullMonthNames[monthIndex];
      const monthYear = `${fullMonthName}-${year}`;
      return { month, year, monthYear, monthName: monthAbbr };
    }
  }

  const now = new Date();
  const month = now.getMonth() + 1;
  const year = now.getFullYear();
  const monthName = monthNames[month - 1];
  const fullMonthName = fullMonthNames[month - 1];
  const monthYear = `${fullMonthName}-${year}`;
  return { month, year, monthYear, monthName };
};

/**
 * Get short organization name from full name
 * For single word: returns first 3 characters uppercase
 * For multiple words: returns first letter of each word uppercase
 * @param {string} orgName - Organization name
 * @returns {string} Short organization name
 */
export const getShortOrgName = (orgName) => {
  if (!orgName) return "";

  const words = orgName.trim().split(/\s+/);
  if (words.length === 0) return "";

  if (words.length === 1) {
    return words[0].substring(0, 3).toUpperCase();
  }

  return words.map((word) => word.charAt(0).toUpperCase()).join("");
};

/**
 * Generate file name for Power BI dashboard
 * Format: "{ShortOrgName} {Service} Dashboard - {MonthName} {Year}.pdf"
 * @param {string} orgName - Organization name
 * @param {string} service - Service name (Finance, Operations, Payroll)
 * @param {string} monthName - Month abbreviation (Jan, Feb, etc.)
 * @param {number} year - Year (e.g., 2025)
 * @returns {string} Generated file name
 */
export const generateFileName = (orgName, service, monthName, year) => {
  const monthNames = BOOKCLOSURE_CONSTANTS.MONTH_NAMES.MAPPING;
  const monthNameFull = monthNames[monthName] ?? monthName ?? "August";
  const shortOrgName = getShortOrgName(orgName);
  const orgPrefix = shortOrgName ? `${shortOrgName} ` : "";
  return `${orgPrefix}${service} Dashboard - ${monthNameFull} ${year}.pdf`;
};

/**
 * Check if a selected month is exactly one month after a given last synced date
 * @param {string|Date} lastSyncedAt - The last sync timestamp (ISO string or Date)
 * @param {string} selectedMonth - Month string to check (e.g., "January 2025")
 * @returns {boolean} true if selectedMonth is exactly one month after lastSyncedAt
 */
export const isNextMonthAfter = (lastSyncedAt, selectedMonth) => {
  if (!lastSyncedAt) return true; // no previous sync -> allow

  const lastDate = new Date(lastSyncedAt);
  if (isNaN(lastDate.getTime())) return true; // unparsable date -> allow

  const lastMonth = lastDate.getMonth() + 1; // 1-12
  const lastYear = lastDate.getFullYear();

  const expectedMonth = lastMonth === 12 ? 1 : lastMonth + 1;
  const expectedYear = lastMonth === 12 ? lastYear + 1 : lastYear;

  try {
    const { month: selMonth, year: selYear } = parseMonthString(selectedMonth);
    return selMonth === expectedMonth && selYear === expectedYear;
  } catch (err) {
    // If parse fails, be permissive
    return true;
  }
};
