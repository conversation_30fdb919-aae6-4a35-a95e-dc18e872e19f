import { Request<PERSON><PERSON> } from "../models/index.model.js";
import {
  create,
  findOne,
  update,
  findById,
  exists,
  findOneInSchema,
  createInSchema,
  updateInSchema,
} from "../utils/database.util.js";

export const sikkaRepository = {
  /**
   * Create a new Sikka request key record
   * @param {Object} params - { request_key, start_time, end_time, expires_in, office_id }
   * @param {string|null} schema_name - Optional schema name
   * @returns {Promise<Object>} Created record
   */
  createRequestKey: async (params, schema_name = null) => {
    if (schema_name) {
      return await createInSchema(RequestKey, schema_name, params);
    }
    return await create(RequestKey, params);
  },

  /**
   * Find Sikka request key record by query
   * @param {Object} where - Where conditions
   * @param {string|null} schema_name - Optional schema name for organization-specific search
   * @returns {Promise<Object|null>} Found record or null
   */
  findRequestKey: async (where, schema_name = null) => {
    if (schema_name) {
      return await findOneInSchema(RequestK<PERSON>, schema_name, { where });
    }
    return await findOne(RequestKey, { where });
  },

  /**
   * Find Sikka request key record by ID
   * @param {number} id - Record ID
   * @returns {Promise<Object|null>} Found record or null
   */
  findRequestKeyById: async (id) => {
    return await findById(RequestKey, id);
  },

  /**
   * Update Sikka request key record
   * @param {string} id - Record ID
   * @param {Object} update_data - Data to update
   * @param {string|null} schema_name - Optional schema name
   * @returns {Promise<Object|null>} Updated record or null
   */
  updateRequestKey: async (id, update_data, schema_name = null) => {
    if (schema_name) {
      return await updateInSchema(RequestKey, schema_name, id, update_data);
    }
    return await update(RequestKey, id, update_data);
  },

  /**
   * Check if request key exists
   * @param {number} id - Record ID
   * @returns {Promise<boolean>} True if exists, false otherwise
   */
  requestKeyExists: async (id) => {
    return await exists(RequestKey, id);
  },
};
