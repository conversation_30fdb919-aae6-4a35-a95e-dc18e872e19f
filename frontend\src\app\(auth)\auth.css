/* Auth Pages Styles */
:root {
  --gradient-start: #6c39ff;
  --gradient-mid: #5b26f0;
  --gradient-end: #4316d9;
  --primary-color: #4F46E5;
  --primary-dark: #4338CA;
  --text-muted: #6B7280;
  --text-dark: #111827;
  --text-heading: #111827;
  --input-border: #E5E7EB;
  --input-border-focus: #4F46E5;
  --right-bg: #ffffff;
  --form-radius: 18px;
  --transition: 200ms ease-in-out;
  --font-family:
    "Inter", "Segoe UI", -apple-system, BlinkMacSystemFont, sans-serif;
}

html,
body {
  height: 100%;
  margin: 0;
  padding: 0;
  font-family: var(--font-family);
  background-color: #ffffff;
  color: var(--text-dark);
  overflow-x: hidden;
}

.auth-layout {
  min-height: 100vh;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
  width: 100%;
  position: relative;
  overflow-x: hidden;
  overflow-y: hidden;
  transition:
    opacity 0.3s ease-out,
    transform 0.3s ease-out;
}

.auth-layout.fade-out {
  opacity: 0;
  transform: translateY(-10px);
  pointer-events: none;
}

@media (min-width: 768px) {
  .auth-layout {
    flex-direction: row;
    height: 100vh;
    max-height: 100vh;
  }
}

.left-column {
  position: relative;
  display: flex;
  width: 100%;
  min-height: max(42vh, 320px);
  flex-direction: column;
  justify-content: space-between;
  align-items: flex-start;
  gap: clamp(24px, 4vh, 36px);
  padding: clamp(24px, 3vw, 40px) clamp(20px, 4vw, 40px);
  overflow: hidden;
  color: #ffffff;
  background: linear-gradient(
    135deg,
    var(--gradient-start) 0%,
    var(--gradient-mid) 100%
  );
  box-shadow: inset 0 0 0 1px rgba(255, 255, 255, 0.08);
  flex-shrink: 0;
}

.left-column::after {
  content: "";
  position: absolute;
  inset: 0;
  background-image: radial-gradient(
    rgba(255, 255, 255, 0.15) 1px,
    transparent 0
  );
  background-size: 32px 32px;
  opacity: 0.25;
  pointer-events: none;
}

.brand {
  position: relative;
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  margin-bottom: 0;
}

.brand img {
  max-width: 140px;
  width: auto;
  height: auto;
  object-fit: contain;
  display: block;
}

.illustration-wrapper {
  position: relative;
  z-index: 2;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: auto;
  margin-bottom: auto;
  max-width: 540px;
  width: 100%;
  min-height: 300px;
  flex-shrink: 1;
  flex-grow: 1;
}

.illustration-wrapper img {
  width: 100%;
  height: auto;
  max-height: 100%;
  object-fit: contain;
  object-position: center;
}

.left-column > span,
.left-column > div {
  position: relative;
  z-index: 2;
}

.background-graphic {
  position: absolute !important;
  inset: 0 !important;
  object-fit: cover !important;
  opacity: 0.12 !important;
  z-index: 0 !important;
  pointer-events: none !important;
  user-select: none !important;
}

@media (min-width: 768px) {
  .left-column {
    width: 50%;
    min-height: 100vh;
    height: 100vh;
    max-height: 100vh;
    padding: 48px 56px;
    gap: 0;
    overflow-y: auto;
    overflow-x: hidden;
    position: relative;
    justify-content: flex-start;
  }

  .brand {
    margin-bottom: 0;
    flex-shrink: 0;
  }

  .illustration-wrapper {
    margin-top: auto;
    margin-bottom: auto;
    min-height: 300px;
    max-height: 60vh;
    flex-grow: 1;
    flex-shrink: 1;
  }
}

.right-column {
  display: flex;
  width: 100%;
  min-height: 58vh;
  align-items: flex-start;
  justify-content: center;
  padding: clamp(24px, 4vh, 48px) clamp(18px, 3vw, 20px);
  background: var(--right-bg);
  flex-direction: column;
  gap: clamp(20px, 3vh, 24px);
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
}

@media (min-width: 768px) {
  .right-column {
    width: 50%;
    min-height: 100vh;
    height: 100vh;
    max-height: 100vh;
    padding: 4rem 56px;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    gap: 32px;
    position: relative;
  }
}

.page-header {
  width: 100%;
  max-width: 420px;
  text-align: left;
  margin-bottom: 16px;
}

.page-header h1 {
  font-size: clamp(2rem, 1.8vw + 1.35rem, 2.35rem);
  font-weight: 700;
  color: var(--text-heading);
  margin: 0;
}

.page-header p {
  margin: 14px 0 0;
  color: var(--text-muted);
  font-size: clamp(0.98rem, 0.35vw + 0.9rem, 1.1rem);
}

.form-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  max-width: 420px;
  padding: 0;
  gap: 28px;
  margin: 0 auto;
  flex-shrink: 0;
  position: relative;
  z-index: 1;
}

.login-form {
  display: flex;
  flex-direction: column;
  gap: 24px;
  width: 100%;
}

.field-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.field-group label {
  color: var(--text-muted);
  font-size: 0.875rem;
  font-weight: 500;
  margin-bottom: 4px;
}

.input-wrapper {
  position: relative;
  width: 100%;
}

.input-icon {
  position: absolute;
  inset: 50% auto auto 16px;
  transform: translateY(-50%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: rgba(118, 131, 150, 0.8);
  pointer-events: none;
}

.password-toggle {
  position: absolute;
  inset: 50% 14px auto auto;
  transform: translateY(-50%);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  border: none;
  background: transparent;
  color: rgba(118, 131, 150, 0.9);
  cursor: pointer;
  transition:
    background-color var(--transition),
    color var(--transition),
    opacity var(--transition);
}

.password-toggle:hover:not(:disabled) {
  background-color: rgba(79, 70, 229, 0.1);
  color: var(--primary-color);
}

.password-toggle:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

.auth-input {
  width: 100%;
  border-radius: 12px;
  border: 1px solid var(--input-border);
  background-color: #ffffff;
  padding: 12px 16px 12px 44px;
  font-size: 0.975rem;
  transition:
    border-color var(--transition),
    box-shadow var(--transition),
    background-color var(--transition),
    opacity var(--transition),
    cursor var(--transition);
}

.auth-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
  background-color: #ffffff;
}

.auth-input:disabled {
  background-color: #f9fafb;
  border-color: #e5e7eb;
  color: #9ca3af;
  cursor: not-allowed;
  opacity: 0.7;
}

.auth-input:disabled::placeholder {
  color: #d1d5db;
}

.input-wrapper:has(.auth-input:disabled) .input-icon {
  opacity: 0.5;
}

.auth-input::placeholder {
  color: rgba(118, 131, 150, 0.8);
  transition: color var(--transition);
}

.login-form button[type="submit"] {
  width: 100%;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  padding: 14px 20px;
  font-size: 1rem;
  font-weight: 600;
  letter-spacing: 0.01em;
  border-radius: 12px;
  border: none;
  color: #ffffff;
  background: var(--primary-color);
  box-shadow: 0 4px 6px -1px rgba(79, 70, 229, 0.2);
  cursor: pointer;
  transition:
    transform var(--transition),
    box-shadow var(--transition),
    background-color var(--transition);
}

.login-form button[type="submit"]:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 6px 12px -2px rgba(79, 70, 229, 0.3);
  background: var(--primary-dark);
}

.login-form button[type="submit"]:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  box-shadow: none;
  transform: none;
}

.login-form button[type="submit"]:focus-visible {
  outline: 3px solid rgba(79, 70, 229, 0.3);
  outline-offset: 3px;
}

.form-support {
  width: 100%;
  text-align: left;
  color: var(--text-muted);
  font-size: 0.92rem;
  line-height: 1.45;
}

.form-support a {
  color: var(--primary-color);
  font-weight: 600;
  text-decoration: none;
  transition: color var(--transition);
}

.form-support a:hover {
  color: var(--primary-dark);
}

.support-security {
  display: none;
}

.support-security svg {
  width: 20px;
  height: 20px;
}

.input-error {
  margin-top: 6px;
  font-size: 0.86rem;
  color: #dc2626;
}

.success-message {
  font-size: 0.9rem;
  color: #047857;
}

.frame-element {
  display: none;
}

/* Large Desktop (1200px and up) */
@media (min-width: 1200px) {
  .left-column {
    width: 50%;
    padding: 60px 72px;
    gap: 0;
    min-width: 380px;
    min-height: 100vh;
  }

  .brand img {
    max-width: 160px;
  }

  .right-column {
    padding: 60px 72px;
    min-height: 100vh;
  }

  .form-container {
    padding: 44px 48px 50px;
    max-width: 440px;
  }

  .illustration-wrapper {
    margin-top: auto;
    margin-bottom: auto;
    min-height: 350px;
    max-height: 65vh;
  }
}

/* Desktop (1024px to 1199px) */
@media (min-width: 1024px) and (max-width: 1199px) {
  .left-column {
    padding: 56px 60px;
    gap: 0;
    min-width: 360px;
    min-height: 100vh;
  }

  .brand img {
    max-width: 150px;
  }

  .right-column {
    padding: 56px 60px;
    gap: 32px;
    min-height: 100vh;
  }

  .form-container {
    padding: 40px 44px;
  }

  .illustration-wrapper {
    margin-top: auto;
    margin-bottom: auto;
    min-height: 320px;
    max-height: 62vh;
  }
}

/* Tablet (768px to 1023px) */
@media (min-width: 768px) and (max-width: 1023px) {
  .auth-layout {
    flex-direction: column;
    min-height: 100vh;
    height: auto;
  }

  .left-column {
    min-height: 300px;
    height: auto;
    padding: 36px 40px;
    border-bottom-left-radius: 40px;
    border-bottom-right-radius: 40px;
    gap: 32px;
    min-width: auto;
    justify-content: flex-start;
  }

  .brand {
    margin-bottom: 0;
  }

  .brand img {
    max-width: 130px;
  }

  .right-column {
    min-height: auto;
    height: auto;
    padding: 56px 40px 80px;
    justify-content: center;
    align-items: center;
    gap: 28px;
  }

  .form-container {
    max-width: 440px;
  }

  .illustration-wrapper {
    margin-top: auto;
    min-height: 250px;
    flex-grow: 0;
  }
}

/* Mobile Large (481px to 767px) */
@media (min-width: 481px) and (max-width: 767px) {
  .auth-layout {
    flex-direction: column;
    min-height: 100vh;
    height: auto;
  }

  .left-column {
    min-height: auto;
    height: auto;
    padding: 32px 24px 80px;
    border-bottom-left-radius: 36px;
    border-bottom-right-radius: 36px;
    gap: 28px;
    min-width: auto;
    justify-content: flex-start;
  }

  .brand {
    margin-bottom: 0;
  }

  .brand img {
    max-width: 120px;
  }

  .right-column {
    min-height: auto;
    padding: 46px 24px 72px;
    gap: 24px;
    align-items: center;
  }

  .form-container {
    padding: 36px 32px;
  }

  .illustration-wrapper {
    margin-top: auto;
    min-height: 200px;
    flex-grow: 0;
  }
}

/* Mobile Small (320px to 480px) */
@media (max-width: 480px) {
  .auth-layout {
    flex-direction: column;
    min-height: 100vh;
    height: auto;
  }

  .left-column {
    min-height: auto;
    height: auto;
    padding: 28px 18px 64px;
    border-bottom-left-radius: 32px;
    border-bottom-right-radius: 32px;
    gap: 24px;
    min-width: auto;
    justify-content: flex-start;
  }

  .brand {
    margin-bottom: 0;
  }

  .brand img {
    max-width: 110px;
  }

  .right-column {
    min-height: auto;
    padding: 38px 18px 60px;
    gap: 24px;
    align-items: center;
  }

  .form-container {
    padding: 32px 22px;
    margin: 0 auto;
  }

  .illustration-wrapper {
    margin-top: auto;
    min-height: 180px;
    flex-grow: 0;
  }
}

/* Extra Small Mobile (below 320px) */
@media (max-width: 319px) {
  .auth-layout {
    min-height: 100vh;
  }

  .left-column {
    min-height: auto;
    padding: 24px 14px 48px;
    justify-content: flex-start;
  }

  .brand img {
    max-width: 100px;
  }

  .right-column {
    min-height: auto;
    padding: 32px 14px 52px;
  }

  .form-container {
    padding: 28px 18px;
  }

  .illustration-wrapper {
    min-height: 150px;
    flex-grow: 0;
  }
}

/* Landscape orientation adjustments for mobile */
@media (max-width: 767px) and (orientation: landscape) {
  .auth-layout {
    flex-direction: row;
    min-height: 100vh;
  }

  .left-column {
    min-height: 100vh;
    height: auto;
    flex: 0.45;
    border-radius: 0;
    overflow-y: auto;
  }

  .right-column {
    flex: 0.55;
    min-height: 100vh;
    height: auto;
    padding: 48px 32px;
    overflow-y: auto;
  }

  .form-container {
    max-width: 380px;
  }

  .illustration-wrapper {
    min-height: 200px;
  }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .form-container {
    box-shadow: 0 20px 48px -26px rgba(46, 80, 200, 0.35);
  }
}

/* Support for dynamic viewport height (modern browsers) */
@supports (height: 100dvh) {
  .auth-layout {
    min-height: 100dvh;
    height: 100dvh;
  }

  .left-column {
    min-height: max(42dvh, 320px);
  }

  .right-column {
    min-height: 58dvh;
  }

  @media (min-width: 768px) {
    .auth-layout {
      height: 100dvh;
      max-height: 100dvh;
    }

    .left-column,
    .right-column {
      min-height: 100dvh;
      height: 100dvh;
      max-height: 100dvh;
    }

    .illustration-wrapper {
      max-height: 60dvh;
    }
  }

  @media (min-width: 1024px) {
    .left-column,
    .right-column {
      min-height: 100dvh;
      height: 100dvh;
    }
  }

  @media (max-width: 767px) {
    .auth-layout {
      min-height: 100dvh;
      height: auto;
    }

    @media (orientation: landscape) {
      .auth-layout {
        height: 100dvh;
      }

      .left-column,
      .right-column {
        min-height: 100dvh;
        height: 100dvh;
      }
    }
  }
}

/* Ensure proper scrolling on very small screens */
@media (max-height: 600px) {
  .auth-layout {
    min-height: auto;
  }

  .left-column,
  .right-column {
    min-height: auto;
    max-height: none;
  }

  .illustration-wrapper {
    min-height: 150px;
    max-height: 200px;
  }
}
