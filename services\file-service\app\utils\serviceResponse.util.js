// app/utils/serviceResponse.util.js
import {
  STATUS_CODE_OK,
  STATUS_CODE_CREATED,
  STATUS_CODE_BAD_REQUEST,
  STATUS_CODE_UNAUTHORIZED,
  STATUS_CODE_FORBIDDEN,
  STATUS_CODE_NOT_FOUND,
  STATUS_CODE_INTERNAL_SERVER_ERROR,
} from "./status_code.utils.js";

/**
 * Create a standardized service response
 * @param {boolean} success - Whether the operation was successful
 * @param {number} statusCode - HTTP status code
 * @param {string} message - Response message
 * @param {any} data - Response data (optional)
 * @param {any} error - Error details (optional)
 * @returns {Object} Service response object
 */
export const createServiceResponse = (
  success,
  statusCode,
  message,
  data = null,
  error = null
) => ({
  success,
  statusCode,
  message,
  timestamp: new Date().toISOString(),
  data,
  error,
});

/**
 * Create a success service response
 * @param {number} statusCode - HTTP status code
 * @param {string} message - Success message
 * @param {any} data - Response data
 * @returns {Object} Success service response
 */
export const createSuccessServiceResponse = (
  statusCode,
  message,
  data = null
) => createServiceResponse(true, statusCode, message, data, null);

/**
 * Create an error service response
 * @param {number} statusCode - HTTP status code
 * @param {string} message - Error message
 * @param {any} error - Error details
 * @returns {Object} Error service response
 */
export const createErrorResponse = (
  statusCode,
  message,
  error = null
) => createServiceResponse(false, statusCode, message, null, error);

/**
 * Create a bad request response
 * @param {string} message - Error message
 * @param {any} error - Error details
 * @returns {Object} Bad request response
 */
export const createBadRequestResponse = (message, error = null) =>
  createErrorResponse(STATUS_CODE_BAD_REQUEST, message, error);

/**
 * Create an unauthorized response
 * @param {string} message - Error message
 * @param {any} error - Error details
 * @returns {Object} Unauthorized response
 */
export const createUnauthorizedResponse = (message, error = null) =>
  createErrorResponse(STATUS_CODE_UNAUTHORIZED, message, error);

/**
 * Create a forbidden response
 * @param {string} message - Error message
 * @param {any} error - Error details
 * @returns {Object} Forbidden response
 */
export const createForbiddenResponse = (message, error = null) =>
  createErrorResponse(STATUS_CODE_FORBIDDEN, message, error);

/**
 * Create a not found response
 * @param {string} message - Error message
 * @param {any} error - Error details
 * @returns {Object} Not found response
 */
export const createNotFoundResponse = (message, error = null) =>
  createErrorResponse(STATUS_CODE_NOT_FOUND, message, error);

/**
 * Create an internal server error response
 * @param {string} message - Error message
 * @param {any} error - Error details
 * @returns {Object} Internal server error response
 */
export const createInternalServerErrorResponse = (message, error = null) =>
  createErrorResponse(STATUS_CODE_INTERNAL_SERVER_ERROR, message, error);

