// app/controllers/chat.controller.js
import { startChat, sendMessage } from "../services/chat.service.js";
import { getSession } from "../utils/memoryStore.js";
import {
  ERROR_MESSAGES,
  ERROR_PATTERNS,
} from "../utils/constants/error.constants.js";
import { CHAT_CONSTANTS } from "../utils/constants/chat.constants.js";
import { SUMMARY_CONSTANTS } from "../utils/constants/summary.constants.js";
import { normalizeSessionId } from "../utils/helpers.js";
import {
  HTTP_STATUS,
  buildSuccessResponse,
  buildErrorResponse,
} from "../utils/constants/http.constants.js";

// Utilities
function missingParams(res, message) {
  return res
    .status(HTTP_STATUS.BAD_REQUEST)
    .json(buildErrorResponse(message, HTTP_STATUS.BAD_REQUEST));
}

function mapServiceError(
  err,
  res,
  fallback = ERROR_MESSAGES.GENERAL.INTERNAL_ERROR
) {
  if (err && typeof err.message === "string") {
    if (ERROR_PATTERNS.CHAT.INVALID_SESSION_ID.test(err.message)) {
      return res
        .status(HTTP_STATUS.NOT_FOUND)
        .json(
          buildErrorResponse(
            ERROR_MESSAGES.CHAT.SESSION_NOT_FOUND_OR_EXPIRED,
            HTTP_STATUS.NOT_FOUND
          )
        );
    }
    if (err.message === ERROR_MESSAGES.CHAT.ORGANIZATION_CONTEXT_MISMATCH) {
      return res
        .status(HTTP_STATUS.BAD_REQUEST)
        .json(buildErrorResponse(err.message, HTTP_STATUS.BAD_REQUEST));
    }
  }
  const message = err?.message || fallback;
  return res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
    buildErrorResponse(message, HTTP_STATUS.INTERNAL_SERVER_ERROR, {
      reason:
        err?.message && err?.message !== message ? err.message : undefined,
    })
  );
}

/**
 * POST /api/chat/start
 * Starts a chat session for a selected document.
 */
export const handleStartChat = async (req, res) => {
  try {
    const {
      filename,
      organization,
      orgId,
      orgid,
      orgName,
      month,
      year,
      folder,
      months,
      service,
      blobStoragePath,
      blobBasePath,
    } = req.body;

    const session = await startChat(filename.trim(), {
      organizationId: (orgId || orgid || "").trim() || undefined,
      organizationName: (orgName || organization || "").trim() || undefined,
      month: month !== undefined && month !== null ? Number.parseInt(month, 10) : undefined,
      year: year !== undefined && year !== null ? Number.parseInt(year, 10) : undefined,
      folder: folder,
      months: months,
      service: service,
      blobStoragePath,
      blobBasePath,
    });

    return res.status(HTTP_STATUS.OK).json(
      buildSuccessResponse({
        sessionId: session.sessionId,
        filename: session.filename,
        organizationId: session.organizationId,
        organizationName: session.organizationName,
      })
    );
  } catch (err) {
    return mapServiceError(err, res, ERROR_MESSAGES.CHAT.FAILED_START_CHAT);
  }
};

/**
 * POST /api/chat/message
 * Sends user's message in chat mode (summary off by default). Organization is optional.
 * @param {string} sessionId - Chat session ID
 * @param {string} message - User's message/question
 * @param {string} [organization] - Organization name for context
 * @param {boolean} [summary] - If true, use summary prompt; if false, use conversational chat prompt
 */
export const handleChatMessage = async (req, res) => {
  try {
    const { message, organization, summary } = req.body;

    if (!message || typeof message !== "string" || !message.trim()) {
      return missingParams(res, "message is required and cannot be empty");
    }

    // OPTIMIZATION: Use session from middleware (already validated and cached)
    const session = req.chatSession;
    const normalizedSessionId = req.body.sessionId; // Already normalized by middleware
    const organizationName = session.organizationName;

    // summary flag is optional for /message; defaults to false (chat mode)
    const isSummaryMode =
      summary === true || summary === "true" || summary === 1 || summary === "1"
        ? true
        : false;

    // OPTIMIZATION: Use organization from session if not provided
    const effectiveOrganization = organization || organizationName;

    // organization and summary flag passed from frontend
    const result = await sendMessage(
      normalizedSessionId,
      message,
      effectiveOrganization,
      isSummaryMode
    );

    // Handle JSON response for regular chat (non-summary mode)
    if (result.jsonAnswer) {
      return res.status(HTTP_STATUS.OK).json(
        buildSuccessResponse({
          sessionId: normalizedSessionId,
          ...result.jsonAnswer, // Spread the JSON answer (responseType, narrative, comparisonData)
          filename: result.filename,
          organizationId:
            result.organizationId || session.organizationId || null,
          organizationName:
            result.organizationName || effectiveOrganization || null,
        })
      );
    }

    // Handle HTML/text response for summary mode or fallback
    return res.status(HTTP_STATUS.OK).json(
      buildSuccessResponse({
        sessionId: normalizedSessionId,
        plainAnswer: result.plainAnswer,
        filename: result.filename,
        organizationId: result.organizationId || session.organizationId || null,
        organizationName:
          result.organizationName || effectiveOrganization || null,
      })
    );
  } catch (err) {
    return mapServiceError(err, res, ERROR_MESSAGES.CHAT.FAILED_SEND_MESSAGE);
  }
};

/**
 * POST /api/chat/summary
 * Sends user's message using Summary prompt (always summary mode). Returns JSON format.
 * Body: { sessionId, message? }
 *
 * Required variables:
 * - sessionId: Session ID (required - all other data retrieved from session)
 *
 * Optional variables:
 * - message: Custom message/prompt (defaults to summary prompt if not provided)
 *
 * All other data (orgId, orgName, filename, year, month) is retrieved from the session.
 */
export const handleChatSummary = async (req, res) => {
  try {
    const { message } = req.body;

    // OPTIMIZATION: Use session from middleware (already validated and cached)
    const session = req.chatSession;
    const normalizedSessionId = req.body.sessionId; // Already normalized by middleware

    // Extract data from session
    const resolvedOrgId = session.organizationId;
    const resolvedOrgName = session.organizationName;
    const resolvedFilename = session.filename;
    const yearNum = session.year;
    const monthNum = session.month;

    // Validate session has all required data
    if (
      !resolvedOrgId ||
      !resolvedOrgName ||
      !resolvedFilename ||
      !yearNum ||
      !monthNum
    ) {
      return res
        .status(HTTP_STATUS.BAD_REQUEST)
        .json(
          buildErrorResponse(
            "Session is missing required data. Please start a new chat session.",
            HTTP_STATUS.BAD_REQUEST
          )
        );
    }

    // Validate year and month ranges
    if (
      yearNum < CHAT_CONSTANTS.YEAR_MIN ||
      yearNum > CHAT_CONSTANTS.YEAR_MAX
    ) {
      return missingParams(res, ERROR_MESSAGES.VALIDATION.YEAR_INVALID);
    }

    if (
      monthNum < CHAT_CONSTANTS.MONTH_MIN ||
      monthNum > CHAT_CONSTANTS.MONTH_MAX
    ) {
      return missingParams(res, ERROR_MESSAGES.VALIDATION.MONTH_INVALID);
    }

    const runSummary = async () => {
      const promptMessage =
        message && String(message).trim().length > 0
          ? String(message).trim()
          : SUMMARY_CONSTANTS.DEFAULT_SUMMARY_PROMPT;
      return sendMessage(
        normalizedSessionId,
        promptMessage,
        resolvedOrgName,
        true, // summaryMode = true
        false, // skipCompetitorSearch = false
        true // jsonFormat = true (return JSON instead of HTML)
      );
    };

    try {
      const result = await runSummary();

      // Parse JSON data from jsonAnswer or plainAnswer (if it's a JSON string)
      let summaryData = null;

      if (result.jsonAnswer) {
        // Already parsed JSON object
        summaryData = result.jsonAnswer;
      } else if (result.plainAnswer) {
        // Try to parse JSON string
        try {
          const { parseJsonSafely } = await import("../utils/jsonCleaner.util.js");
          summaryData = parseJsonSafely(result.plainAnswer);
        } catch (parseError) {
          // If parsing fails, return as plain text
          return res.status(HTTP_STATUS.OK).json(
            buildSuccessResponse({
              sessionId: normalizedSessionId,
              plainAnswer: result.plainAnswer,
              filename: result.filename,
              orgId: result.organizationId || resolvedOrgId,
              orgName: result.organizationName || resolvedOrgName,
              year: yearNum,
              month: monthNum,
            })
          );
        }
      }

      // Return structured JSON response with summaryData
      return res.status(HTTP_STATUS.OK).json(
        buildSuccessResponse({
          sessionId: normalizedSessionId,
          summaryData,
          filename: result.filename || resolvedFilename,
          orgId: result.organizationId || resolvedOrgId,
          orgName: result.organizationName || resolvedOrgName,
          year: yearNum,
          month: monthNum,
        })
      );
    } catch (error) {
      const isSessionError =
        error &&
        typeof error.message === "string" &&
        ERROR_PATTERNS.CHAT.INVALID_SESSION_ID.test(error.message);

      if (!isSessionError || !resolvedFilename) {
        return mapServiceError(
          error,
          res,
          ERROR_MESSAGES.CHAT.FAILED_SEND_SUMMARY
        );
      }

      // Try to create session again
      await startChat(resolvedFilename, {
        organizationId: resolvedOrgId,
        organizationName: resolvedOrgName,
        month: monthNum,
        year: yearNum,
      });

      const result = await runSummary();

      // Parse JSON data from jsonAnswer or plainAnswer (if it's a JSON string)
      let summaryData = null;

      if (result.jsonAnswer) {
        // Already parsed JSON object
        summaryData = result.jsonAnswer;
      } else if (result.plainAnswer) {
        // Try to parse JSON string
        try {
          const { parseJsonSafely } = await import("../utils/jsonCleaner.util.js");
          summaryData = parseJsonSafely(result.plainAnswer);
        } catch (parseError) {
          // If parsing fails, return as plain text
          return res.status(HTTP_STATUS.OK).json(
            buildSuccessResponse({
              sessionId: normalizedSessionId,
              plainAnswer: result.plainAnswer,
              filename: result.filename,
              orgId: result.organizationId || resolvedOrgId,
              orgName: result.organizationName || resolvedOrgName,
              year: yearNum,
              month: monthNum,
            })
          );
        }
      }

      // Return structured JSON response with summaryData
      return res.status(HTTP_STATUS.OK).json(
        buildSuccessResponse({
          sessionId: normalizedSessionId,
          summaryData,
          filename: result.filename || resolvedFilename,
          orgId: result.organizationId || resolvedOrgId,
          orgName: result.organizationName || resolvedOrgName,
          year: yearNum,
          month: monthNum,
        })
      );
    }
  } catch (err) {
    if (err?.message === ERROR_MESSAGES.CHAT.ORGANIZATION_CONTEXT_MISMATCH) {
      return res
        .status(HTTP_STATUS.BAD_REQUEST)
        .json(buildErrorResponse(err.message, HTTP_STATUS.BAD_REQUEST));
    }
    return mapServiceError(err, res, ERROR_MESSAGES.CHAT.FAILED_SEND_SUMMARY);
  }
};
