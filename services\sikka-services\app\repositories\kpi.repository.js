import { storeKpiData, getKpiConfig } from "../utils/kpis.util.js";
import { KPI_KEYS, ERROR_MESSAGES } from "../utils/constants.util.js";

/**
 * Generic method to store KPI data
 * @param {string} kpi_key - KPI key (e.g., 'account_receivables')
 * @param {Array} data - Data to store
 * @param {string|null} schema_name - Optional schema name for schema-aware storage
 * @returns {Promise<Array>} Created records
 */
const storeKpi = async (kpi_key, data, schema_name = null) => {
  const config = getKpiConfig(kpi_key);
  return await storeKpiData(config.model, data, schema_name);
};

/**
 * KPI Repository
 * @module kpiRepository
 * @description Generic repository for all KPIs
 */
export const kpiRepository = {
  /**
   * Generic method to store KPI data
   */
  storeKpi,

  // All methods accept optional schema_name parameter
  createAccountReceivable: async (data, schema_name = null) =>
    await storeKpi(KPI_KEYS.ACCOUNT_RECEIVABLES, data, schema_name),

  createTreatmentAnalysis: async (data, schema_name = null) =>
    await storeKpi(KPI_KEYS.TREATMENT_ANALYSIS, data, schema_name),

  directRestorations: async (data, schema_name = null) =>
    await storeKpi(KPI_KEYS.DIRECT_RESTORATIONS, data, schema_name),

  createAvgDailyProduction: async (data, schema_name = null) =>
    await storeKpi(KPI_KEYS.AVG_DAILY_PRODUCTION, data, schema_name),

  createNewPatients: async (data, schema_name = null) =>
    await storeKpi(KPI_KEYS.NEW_PATIENTS, data, schema_name),

  createNoShowAppointments: async (data, schema_name = null) =>
    await storeKpi(KPI_KEYS.NO_SHOW_APPOINTMENTS, data, schema_name),

  createTotalProductionPerDay: async (data, schema_name = null) =>
    await storeKpi(KPI_KEYS.TOTAL_PRODUCTION_PER_DAY, data, schema_name),

  createTotalProductionByDentist: async (data, schema_name = null) =>
    await storeKpi(KPI_KEYS.TOTAL_PRODUCTION_BY_DENTIST, data, schema_name),

  createTotalProductionByHygienist: async (data, schema_name = null) =>
    await storeKpi(KPI_KEYS.TOTAL_PRODUCTION_BY_HYGIENIST, data, schema_name),

  createHygieneReappointment: async (data, schema_name = null) =>
    await storeKpi(KPI_KEYS.HYGIENE_REAPPOINTMENT, data, schema_name),

  createGrossCollections: async (data, schema_name = null) =>
    await storeKpi(KPI_KEYS.GROSS_COLLECTION, data, schema_name),
};
