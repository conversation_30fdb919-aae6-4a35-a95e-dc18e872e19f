import express from "express";
import {
  login,
  logout,
  getProfile,
  updateProfile,
} from "../controllers/auth.controller.js";
import { verifyToken } from "../middleware/auth.middleware.js";
import { validate } from "../middleware/validation.middleware.js";
import { authValidations } from "../validators/auth.validator.js";

const router = express.Router();

router.post("/login", validate(authValidations.login), login);

router.post("/logout", logout);

router.get("/profile", verifyToken, getProfile);

router.put(
  "/profile/:id",
  verifyToken,
  validate(authValidations.updateUserProfile),
  updateProfile
);

export default router;
