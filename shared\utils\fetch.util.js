const DEFAULT_JSON_HEADERS = {
  "Content-Type": "application/json",
};

const sanitizePayload = (payload = {}) =>
  Object.fromEntries(
    Object.entries(payload).filter(([, value]) => value !== undefined)
  );

const buildJsonBody = (payload = {}) => JSON.stringify(sanitizePayload(payload));

/**
 * Build a RequestInit object for JSON-based fetch calls.
 * @param {string} method
 * @param {object|undefined} payload
 * @param {RequestInit} init
 * @returns {RequestInit}
 */
export const createJsonRequestInit = (method, payload, init = {}) => {
  const { headers, ...rest } = init;
  const requestInit = {
    method,
    headers: {
      ...DEFAULT_JSON_HEADERS,
      ...(headers || {}),
    },
    ...rest,
  };

  if (payload !== undefined) {
    requestInit.body = buildJsonBody(payload);
  }

  return requestInit;
};

/**
 * <PERSON>ric helper to perform JSON fetch requests.
 * @param {string} url
 * @param {object} options
 * @param {string} options.method
 * @param {object} [options.payload]
 * @param {RequestInit} [options.init]
 * @returns {Promise<Response>}
 */
export const jsonFetch = (url, { method = "GET", payload, init } = {}) => {
  return fetch(url, createJsonRequestInit(method, payload, init));
};

export const postJson = (url, payload, init) =>
  jsonFetch(url, { method: "POST", payload, init });

export const putJson = (url, payload, init) =>
  jsonFetch(url, { method: "PUT", payload, init });

export const patchJson = (url, payload, init) =>
  jsonFetch(url, { method: "PATCH", payload, init });

