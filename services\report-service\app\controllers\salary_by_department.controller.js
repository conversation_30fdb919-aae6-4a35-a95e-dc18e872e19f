import SalaryByDepartmentService from "../services/salary_by_department.service.js";
import { LOGGER_NAMES } from "../utils/constants/log.constants.js";
import { createLogger } from "../utils/logger.utils.js";
import { validateRequiredParams, handleControllerError, sendSuccessResponse } from "../utils/controller.utils.js";

const logger = createLogger(LOGGER_NAMES.REPORT_CONTROLLER);

/**
 * Get salary data grouped by department for organization by month and year
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getSalaryByDepartment = async (req, res) => {
  try {
    const { organization_id, month, year } = req.query;

    logger.info(
      `Fetching salary by department for org: ${organization_id}, month: ${month}, year: ${year}`
    );

    // Validate required parameters
    const validationError = validateRequiredParams(req.query, ['organization_id', 'month', 'year']);
    if (validationError) {
      return res.status(400).json(validationError);
    }

    // Fetch salary by department data
    const salaryData = await SalaryByDepartmentService.getSalaryByDepartmentData({
      organization_id,
      month,
      year,
    });

    // Return successful response
    sendSuccessResponse(res, "Salary by department fetched successfully", salaryData);
  } catch (error) {
    logger.error("Error fetching salary by department:", error);
    handleControllerError(error, res, "Error fetching salary by department data");
  }
};

export default {
  getSalaryByDepartment,
};
