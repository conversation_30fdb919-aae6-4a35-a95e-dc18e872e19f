import { LOGGER_NAMES } from "../utils/constants/log.constants.js";
import { createLogger } from "../utils/logger.utils.js";
import { getRecordsByDateRange } from "../utils/database.utils.js";
import {
  getStartDate,
  calculatePercentageDistribution,
  groupByKeyAndSum,
  parseCurrencyString,
} from "../utils/service.utils.js";
import { getOrganizationSchemaName } from "../utils/repository.utils.js";

const logger = createLogger(LOGGER_NAMES.REPORT_SERVICE);

const AGING_BENCHMARK_MAP = {
  less_than_30: "<30",
  between_30_60: "30-60",
  between_60_90: "60-90",
  greater_than_90: ">90",
};

/**
 * Get treatment plan status distribution
 * @param {string} schemaName - Schema name
 * @param {string} startDate - Start date
 * @returns {Promise<Object>} Treatment plan status data
 */
const getTreatmentPlanStatus = async (schemaName, startDate) => {
  const treatmentPlanData = await getRecordsByDateRange(
    schemaName,
    "sikka_treatment_plan_analysis",
    startDate
  );

  const grouped = groupByKeyAndSum(
    treatmentPlanData,
    "treatment_plan_status",
    "value"
  );

  const categories = Object.entries(grouped).map(([status, value]) => ({
    name: status,
    value,
  }));

  const categoriesWithPercentages = calculatePercentageDistribution(categories);

  return {
    title: "Treatment Plan Status",
    categories: categoriesWithPercentages,
  };
};

/**
 * Get aging account receivables
 * @param {string} schemaName - Schema name
 * @param {string} startDate - Start date
 * @returns {Promise<Object>} Aging account receivables data
 */
const getAgingAccountReceivables = async (schemaName, startDate) => {
  const receivablesData = await getRecordsByDateRange(
    schemaName,
    "sikka_accounts_receivable",
    startDate
  );

  const periods = Object.entries(AGING_BENCHMARK_MAP).map(
    ([benchmark, name]) => ({
      name,
      value: parseCurrencyString(
        receivablesData.find((p) => p.ar_benchmark === benchmark)?.amount
      ),
    })
  );

  return {
    title: "Aging Account Receivables",
    periods,
  };
};

/**
 * Get operations trends data for organization
 * @param {Object} params - Query parameters
 * @param {string} params.organization_id - Organization ID
 * @param {number} params.month - Month (1-12)
 * @param {number} params.year - Year
 * @returns {Promise<Object>} Operations trends data
 */
const getOperationsTrends = async ({ organization_id, month, year }) => {
  try {
    logger.info(
      `Fetching operations trends data for org: ${organization_id}, month: ${month}, year: ${year}`
    );
    const schemaName = await getOrganizationSchemaName(organization_id);
    const startDate = getStartDate(month, year);

    const [treatmentPlanStatus, agingAccountReceivables] = await Promise.all([
      getTreatmentPlanStatus(schemaName, startDate),
      getAgingAccountReceivables(schemaName, startDate),
    ]);

    const responseData = {
      treatmentPlanStatus,
      agingAccountReceivables,
    };

    logger.info("Operations trends data fetched successfully");
    return responseData;
  } catch (error) {
    logger.error("Error in OperationsService.getOperationsTrends:", error);
    throw error;
  }
};

export default {
  getOperationsTrends,
};

