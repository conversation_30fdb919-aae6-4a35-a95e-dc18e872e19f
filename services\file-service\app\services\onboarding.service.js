import { BlobServiceClient } from "@azure/storage-blob";
import { blobConfig } from "../../config/blob.config.js";
import logger from "../../config/logger.config.js";
import {
  ONBOARDING_CONTAINER_NAME,
  ONBOARDING_LOG_MESSAGES,
  ONBOARDING_MESSAGES,
} from "../utils/constants/onboarding.constants.js";
import {
  createInternalServerErrorResponse,
  createSuccessServiceResponse,
  createBadRequestResponse,
} from "../utils/serviceResponse.util.js";
import { STATUS_CODE_CREATED } from "../utils/status_code.utils.js";
import {
  buildOrganizationLogoBlobPath,
  buildOrganizationPdfBlobPath,
  buildPowerBiReportsBlobPath,
  buildReportsBlobPath,
  blobPathState,
} from "../utils/blobPath.util.js";
import {
  validateUploadFile,
  validateUploadJsonFile,
} from "../utils/validators.util.js";
import { getAuthServiceBaseUrl, getSystemApiKey } from "../utils/env.util.js";
import { httpGet } from "../../../../shared/utils/axios.util.js";

const initialize = () => {
  if (blobPathState.isConfigured) {
    return;
  }

  try {
    blobConfig.validate();
    const blobServiceClient = BlobServiceClient.fromConnectionString(
      blobConfig.connectionString
    );
    blobPathState.containerClient = blobServiceClient.getContainerClient(
      ONBOARDING_CONTAINER_NAME
    );
    blobPathState.isConfigured = true;
  } catch (error) {
    logger.error(
      `${ONBOARDING_LOG_MESSAGES.SERVICE_UPLOAD_FAILED}: ${error.message}`,
      { error }
    );
    blobPathState.isConfigured = false;
  }
};

const ensureContainerExists = async () => {
  if (!blobPathState.containerClient) {
    throw new Error(ONBOARDING_MESSAGES.STORAGE_NOT_CONFIGURED);
  }

  if (blobPathState.containerEnsured) {
    return;
  }

  logger.info(ONBOARDING_LOG_MESSAGES.SERVICE_CONTAINER_INIT);
  await blobPathState.containerClient.createIfNotExists();
  blobPathState.containerEnsured = true;
};

export const uploadOrganizationLogoService = async ({ orgId, file }) => {
  initialize();

  logger.info(ONBOARDING_LOG_MESSAGES.SERVICE_UPLOAD_START, {
    orgId,
  });

  if (!blobPathState.isConfigured) {
    return createInternalServerErrorResponse(
      ONBOARDING_MESSAGES.STORAGE_NOT_CONFIGURED
    );
  }

  if (!blobPathState.containerClient) {
    return createInternalServerErrorResponse(
      ONBOARDING_MESSAGES.STORAGE_NOT_CONFIGURED
    );
  }

  const validationMessage = validateUploadFile(file);
  if (validationMessage) {
    return createInternalServerErrorResponse(
      validationMessage,
      validationMessage
    );
  }

  try {
    await ensureContainerExists();

    const blobPath = buildOrganizationLogoBlobPath(orgId, file.originalname);

    const blockBlobClient =
      blobPathState.containerClient.getBlockBlobClient(blobPath);

    await blockBlobClient.uploadData(file.buffer, {
      blobHTTPHeaders: {
        blobContentType: file.mimetype,
      },
    });

    logger.info(ONBOARDING_LOG_MESSAGES.SERVICE_UPLOAD_SUCCESS, { blobPath });

    return createSuccessServiceResponse(
      STATUS_CODE_CREATED,
      ONBOARDING_MESSAGES.LOGO_UPLOAD_SUCCESS,
      {
        blobPath,
        blobUrl: blockBlobClient.url,
        size: file.size,
        contentType: file.mimetype,
      }
    );
  } catch (error) {
    logger.error(
      `${ONBOARDING_LOG_MESSAGES.SERVICE_UPLOAD_FAILED}: ${error.message}`,
      { error }
    );
    return createInternalServerErrorResponse(
      ONBOARDING_MESSAGES.LOGO_UPLOAD_FAILED,
      error.message
    );
  }
};

/**
 * Fetch organization name from auth service
 * @param {string} orgId - Organization ID
 * @returns {Promise<{success: boolean, orgName?: string, error?: string}>}
 */
const fetchOrganizationName = async (orgId) => {
  try {
    const baseUrl = getAuthServiceBaseUrl();
    const url = `${baseUrl}/organization/${orgId}`;

    const headers = {};
    try {
      const apiKey = getSystemApiKey();
      headers["x-api-key"] = apiKey;
    } catch (error) {
      logger.warn("System API key not configured for organization lookup");
    }

    logger.info(`[ONBOARDING] Fetching organization name for: ${orgId}`);
    const response = await httpGet(url, {
      headers,
      timeout: 5000,
    });

    const organizationName = response?.data?.data?.name ?? response?.data?.name ?? null;

    if (organizationName) {
      logger.info(`[ONBOARDING] Organization name fetched: ${organizationName}`);
      return { success: true, orgName: organizationName };
    }

    logger.warn(`[ONBOARDING] Organization name not found for: ${orgId}`);
    return { success: false, error: "Organization name not found" };
  } catch (error) {
    logger.error(`[ONBOARDING] Failed to fetch organization name: ${error.message}`, {
      orgId,
      error: error.response?.data || error.message,
    });
    return { success: false, error: error.message };
  }
};

export const uploadOrganizationPdfService = async ({ 
  orgId, 
  orgName, 
  service, 
  year, 
  month, 
  file 
}) => {
  initialize();

  logger.info(ONBOARDING_LOG_MESSAGES.SERVICE_PDF_UPLOAD_START, {
    orgId,
    orgName,
    service,
    year,
    month,
  });

  if (!blobPathState.isConfigured) {
    return createInternalServerErrorResponse(
      ONBOARDING_MESSAGES.STORAGE_NOT_CONFIGURED
    );
  }

  if (!blobPathState.containerClient) {
    return createInternalServerErrorResponse(
      ONBOARDING_MESSAGES.STORAGE_NOT_CONFIGURED
    );
  }

  const validationMessage = validateUploadJsonFile(file);
  if (validationMessage) {
    return createInternalServerErrorResponse(
      validationMessage,
      validationMessage
    );
  }

  try {
    await ensureContainerExists();

    // Resolve organization name - fetch if not provided
    let resolvedOrgName = orgName;
    if (!resolvedOrgName) {
      logger.info(`[ONBOARDING] Organization name not provided, fetching for: ${orgId}`);
      const orgNameResult = await fetchOrganizationName(orgId);
      if (!orgNameResult.success || !orgNameResult.orgName) {
        return createBadRequestResponse(
          "Organization name is required and could not be fetched",
          orgNameResult.error || "Organization name not found"
        );
      }
      resolvedOrgName = orgNameResult.orgName;
    }

    // Build blob path using the new Power BI-Reports structure
    const blobPath = buildPowerBiReportsBlobPath(
      orgId,
      resolvedOrgName,
      service,
      year,
      month,
      file.originalname
    );

    const blockBlobClient =
      blobPathState.containerClient.getBlockBlobClient(blobPath);

    await blockBlobClient.uploadData(file.buffer, {
      blobHTTPHeaders: {
        blobContentType: file.mimetype,
      },
    });

    logger.info(ONBOARDING_LOG_MESSAGES.SERVICE_PDF_UPLOAD_SUCCESS, {
      blobPath,
    });

    return createSuccessServiceResponse(
      STATUS_CODE_CREATED,
      ONBOARDING_MESSAGES.PDF_UPLOAD_SUCCESS,
      {
        blobPath,
        blobUrl: blockBlobClient.url,
        size: file.size,
        contentType: file.mimetype,
      }
    );
  } catch (error) {
    logger.error(
      `${ONBOARDING_LOG_MESSAGES.SERVICE_PDF_UPLOAD_FAILED}: ${error.message}`,
      { error }
    );
    return createInternalServerErrorResponse(
      ONBOARDING_MESSAGES.PDF_UPLOAD_FAILED,
      error.message
    );
  }
};

export const uploadOrganizationFileService = async ({ 
  orgId, 
  orgName, 
  service, 
  year, 
  month, 
  file 
}) => {
  initialize();

  // Determine upload type based on whether service is provided
  const isReportUpload = !!(service && year && month);
  const logMessage = isReportUpload 
    ? ONBOARDING_LOG_MESSAGES.SERVICE_JSON_UPLOAD_START
    : ONBOARDING_LOG_MESSAGES.SERVICE_UPLOAD_START;

  logger.info(logMessage, {
    orgId,
    orgName,
    service,
    year,
    month,
  });

  if (!blobPathState.isConfigured) {
    return createInternalServerErrorResponse(
      ONBOARDING_MESSAGES.STORAGE_NOT_CONFIGURED
    );
  }

  if (!blobPathState.containerClient) {
    return createInternalServerErrorResponse(
      ONBOARDING_MESSAGES.STORAGE_NOT_CONFIGURED
    );
  }

  // Validate file
  const validationMessage = isReportUpload 
    ? validateUploadJsonFile(file) 
    : validateUploadFile(file);
    
  if (validationMessage) {
    return createInternalServerErrorResponse(
      validationMessage,
      validationMessage
    );
  }

  try {
    await ensureContainerExists();

    let blobPath;

    if (isReportUpload) {
      // Resolve organization name - fetch if not provided
      let resolvedOrgName = orgName;
      if (!resolvedOrgName) {
        logger.info(`[ONBOARDING] Organization name not provided, fetching for: ${orgId}`);
        const orgNameResult = await fetchOrganizationName(orgId);
        if (!orgNameResult.success || !orgNameResult.orgName) {
          return createBadRequestResponse(
            "Organization name is required and could not be fetched",
            orgNameResult.error || "Organization name not found"
          );
        }
        resolvedOrgName = orgNameResult.orgName;
      }

      // Build blob path: {orgId}/{orgName}/Reports/{service}/{year}/{month}/{fileName}
      blobPath = buildReportsBlobPath(
        orgId,
        resolvedOrgName,
        service,
        year,
        month,
        file.originalname
      );
    } else {
      // Logo upload path: {orgId}/org-logos/{fileName}
      blobPath = buildOrganizationLogoBlobPath(orgId, file.originalname);
    }

    const blockBlobClient =
      blobPathState.containerClient.getBlockBlobClient(blobPath);

    await blockBlobClient.uploadData(file.buffer, {
      blobHTTPHeaders: {
        blobContentType: file.mimetype,
      },
    });

    const successLog = isReportUpload 
      ? ONBOARDING_LOG_MESSAGES.SERVICE_JSON_UPLOAD_SUCCESS
      : ONBOARDING_LOG_MESSAGES.SERVICE_UPLOAD_SUCCESS;
    const successMessage = isReportUpload 
      ? ONBOARDING_MESSAGES.JSON_UPLOAD_SUCCESS
      : ONBOARDING_MESSAGES.LOGO_UPLOAD_SUCCESS;

    logger.info(successLog, { blobPath });

    return createSuccessServiceResponse(
      STATUS_CODE_CREATED,
      successMessage,
      {
        blobPath,
        blobUrl: blockBlobClient.url,
        size: file.size,
        contentType: file.mimetype,
        uploadType: isReportUpload ? "report" : "logo",
      }
    );
  } catch (error) {
    const errorLog = isReportUpload 
      ? ONBOARDING_LOG_MESSAGES.SERVICE_JSON_UPLOAD_FAILED
      : ONBOARDING_LOG_MESSAGES.SERVICE_UPLOAD_FAILED;
    const errorMessage = isReportUpload 
      ? ONBOARDING_MESSAGES.JSON_UPLOAD_FAILED
      : ONBOARDING_MESSAGES.LOGO_UPLOAD_FAILED;

    logger.error(
      `${errorLog}: ${error.message}`,
      { error }
    );
    return createInternalServerErrorResponse(
      errorMessage,
      error.message
    );
  }
};
