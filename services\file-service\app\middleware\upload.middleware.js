import multer from "multer";
import {
  ONBOARDING_ALLOWED_FILE_SIZE_BYTES,
  ONBOARDING_ALLOWED_MIME_TYPES,
  ONBOARDING_MESSAGES,
  ONBOARDING_JSON_ALLOWED_FILE_SIZE_BYTES,
  ONBOARDING_JSON_ALLOWED_MIME_TYPES,
} from "../utils/constants/onboarding.constants.js";
import { errorResponse } from "../utils/response.util.js";
import { STATUS_CODE_BAD_REQUEST } from "../utils/status_code.utils.js";
import { createMemoryUploadMiddleware } from "../../../../shared/middleware/upload.middleware.js";

/**
 * Parse form-data to get the data field
 * This must run BEFORE type detection
 */
export const parseFormDataField = (req, res, next) => {
  // Use multer to parse multipart/form-data (without file validation yet)
  const upload = multer({ storage: multer.memoryStorage() }).any();
  
  upload(req, res, (err) => {
    if (err) {
      return res.status(STATUS_CODE_BAD_REQUEST).json(
        errorResponse("Failed to parse form data", err.message)
      );
    }

    // req.files contains all files
    // req.body contains all text fields
    
    // Parse the data field if it exists
    if (req.body?.data) {
      try {
        const parsedData = typeof req.body.data === "string" 
          ? JSON.parse(req.body.data) 
          : req.body.data;
        
        // Merge parsed data into req.body
        req.body = { ...req.body, ...parsedData };
      } catch (e) {
        return res.status(STATUS_CODE_BAD_REQUEST).json(
          errorResponse("Invalid JSON in data field", e.message)
        );
      }
    }

    next();
  });
};

/**
 * Unified upload middleware
 * Validates file based on type detected from body
 * Supports:
 * 1. Logo files (images): type: "logo"
 * 2. Report files (JSON): type: "report"
 */
export const handleUnifiedUpload = (req, res, next) => {
  // Determine upload type from JSON body (already parsed by parseFormDataField)
  const type = req.body?.type;
  const isLogoUpload = type?.toLowerCase() === "logo";

  // Get the file from req.files (set by parseFormDataField middleware)
  const file = req.files?.[0];
  
  if (isLogoUpload) {
    // Logo upload configuration
    const logoUpload = createMemoryUploadMiddleware({
      fieldName: "file",
      allowedMimeTypes: ONBOARDING_ALLOWED_MIME_TYPES,
      fileSizeLimitBytes: ONBOARDING_ALLOWED_FILE_SIZE_BYTES,
      invalidTypeMessage: ONBOARDING_MESSAGES.INVALID_FILE_TYPE,
      fileTooLargeMessage: ONBOARDING_MESSAGES.FILE_TOO_LARGE,
      statusCodeBadRequest: STATUS_CODE_BAD_REQUEST,
      errorResponseBuilder: (message, error) => errorResponse(message, error),
    });
    
    // Manually set the file to req.file for logo validation
    req.file = file;
    return next();
  } else {
    // Report upload configuration
    const reportUpload = createMemoryUploadMiddleware({
      fieldName: "file",
      allowedMimeTypes: ONBOARDING_JSON_ALLOWED_MIME_TYPES,
      fileSizeLimitBytes: ONBOARDING_JSON_ALLOWED_FILE_SIZE_BYTES,
      invalidTypeMessage: ONBOARDING_MESSAGES.INVALID_JSON_FILE_TYPE,
      fileTooLargeMessage: ONBOARDING_MESSAGES.JSON_FILE_TOO_LARGE,
      statusCodeBadRequest: STATUS_CODE_BAD_REQUEST,
      errorResponseBuilder: (message, error) => errorResponse(message, error),
    });
    
    // Manually set the file to req.file for report validation
    req.file = file;
    return next();
  }
};
