/* Scrollbar Styles */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: #a5b4fc #373fa5;
}
.custom-scrollbar::-webkit-scrollbar {
  width: 8px;
  background: transparent;
}
.custom-scrollbar::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #6c63ff 40%, #a5b4fc 100%);
  border-radius: 12px;
  min-height: 40px;
  box-shadow: 0 2px 8px 0 rgba(108, 99, 255, 0.1);
  border: 2px solid #4953b8;
  transition: all 0.3s ease;
}
.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #5f6df7 40%, #818cf8 100%);
  box-shadow: 0 4px 12px 0 rgba(108, 99, 255, 0.2);
}
.custom-scrollbar::-webkit-scrollbar-track {
  background: #373fa5;
  border-radius: 12px;
}

/* Accordion Section Animations */
.accordion-section {
  animation: slideInUp 0.4s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Button Pulse Animation */
@keyframes buttonPulse {
  0% {
    box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.7);
  }
  70% {
    box-shadow: 0 0 0 8px rgba(255, 255, 255, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(255, 255, 255, 0);
  }
}

/* Smooth Expand Animation */
@keyframes expandHeight {
  from {
    max-height: 0;
    opacity: 0;
  }
  to {
    max-height: 500px;
    opacity: 1;
  }
}

/* Smooth Collapse Animation */
@keyframes collapseHeight {
  from {
    max-height: 500px;
    opacity: 1;
  }
  to {
    max-height: 0;
    opacity: 0;
  }
}

/* Stagger Animation for List Items */
@keyframes staggerFadeIn {
  from {
    opacity: 0;
    transform: translateX(-8px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Glow Effect for Selected Items */
@keyframes glowEffect {
  0% {
    box-shadow: 0 0 5px rgba(255, 255, 255, 0.3);
  }
  50% {
    box-shadow: 0 0 15px rgba(255, 255, 255, 0.5);
  }
  100% {
    box-shadow: 0 0 5px rgba(255, 255, 255, 0.3);
  }
}

/* Ripple Effect on Click */
@keyframes ripple {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(4);
    opacity: 0;
  }
}

/* Smooth Chevron Rotation */
@keyframes chevronRotate {
  from {
    transform: rotate(0deg) scale(1);
  }
  to {
    transform: rotate(180deg) scale(1.1);
  }
}

/* Icon Spin Animation */
@keyframes iconSpin {
  from {
    transform: rotate(0deg) scale(1);
  }
  to {
    transform: rotate(360deg) scale(1);
  }
}
