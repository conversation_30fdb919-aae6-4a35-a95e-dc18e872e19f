import { createAsyncThunk } from "@reduxjs/toolkit";
import api from "@/redux/ApiService/ApiService";
import { SERVICE_PORTS } from "@/utils/constants/api";

const axios = api(SERVICE_PORTS.AUTH);
const sikkaAxios = api(SERVICE_PORTS.SIKKA);
// Fetch all organizations
export const fetchOrganizations = createAsyncThunk(
  "organizations/fetchAll",
  async (params, { rejectWithValue }) => {
    try {
      return await axios
        .get("/organization", { params })
        .then((res) => res.data.data);
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

// Create organization
export const createOrganization = createAsyncThunk(
  "organizations/create",
  async (data, { rejectWithValue }) => {
    try {
      // POST to the specified endpoint
      const response = await axios.post("/organization", data);
      return response.data; // Return only serializable data, not the full axios response
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

// Update organization
export const updateOrganization = createAsyncThunk(
  "organizations/update",
  async ({ id, data }, { rejectWithValue }) => {
    try {
      const response = await axios.put(`/organization/${id}`, data);
      return response.data; // Return only serializable data, not the full axios response
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

// Delete organization
export const deleteOrganization = createAsyncThunk(
  "organizations/delete",
  async (id, { rejectWithValue }) => {
    try {
      const response = await axios.delete(`/auth/organizations/${id}`);
      return response.data; // Return only serializable data, not the full axios response
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

// Get organization by ID
export const getOrganizationById = createAsyncThunk(
  "organizations/getById",
  async (id, { rejectWithValue }) => {
    try {
      const response = await axios.get(`/organization/${id}`);
      return response.data.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

// Check Sikka connection
export const checkSikkaConnection = createAsyncThunk(
  "organizations/checkSikkaConnection",
  async (office_id, { rejectWithValue }) => {
    try {
      const response = await sikkaAxios.post("/sikka/check-connection", {
        office_id,
      });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

// Fetch services for a specific organization
export const fetchOrganizationServices = createAsyncThunk(
  "organizations/fetchServices",
  async (organizationId, { rejectWithValue }) => {
    try {
      if (!organizationId) {
        throw new Error("Organization ID is required");
      }
      const response = await axios.get(
        `/organization/services/${organizationId}`
      );
      // Extract services array from the response
      const responseData = response.data.data;
      return {
        organizationId,
        services: responseData.services || [],
      };
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);
