// app/services/blobStorage.service.js
import { BlobServiceClient } from "@azure/storage-blob";
import { blobConfig, uploadOptions } from "../../config/blob.config.js";
import logger from "../../config/logger.config.js";
import {
  BLOB_STORAGE_MESSAGES,
  BLOB_STORAGE_LOG_MESSAGES,
  BLOB_STORAGE_DEFAULTS,
} from "../utils/constants/blobStorage.constants.js";

/**
 * Azure Blob Storage Service
 * Handles all interactions with Azure Blob Storage
 */
class BlobStorageService {
  constructor() {
    // Try to validate configuration, but don't throw if missing
    try {
      blobConfig.validate();
      
      // Initialize blob service client
      this.blobServiceClient = BlobServiceClient.fromConnectionString(
        blobConfig.connectionString
      );
      
      // Get container client
      this.containerClient = this.blobServiceClient.getContainerClient(
        blobConfig.containerName
      );
      
      this.isConfigured = true;
    } catch (error) {
      logger.warn(`${BLOB_STORAGE_LOG_MESSAGES.CONFIGURATION_WARNING}: ${error.message}`);
      this.isConfigured = false;
    }
  }

  /**
   * Ensure the container exists, create if it doesn't
   */
  async ensureContainer() {
    // Guard clause: check configuration first
    if (!this.isConfigured) {
      throw new Error(BLOB_STORAGE_MESSAGES.NOT_CONFIGURED);
    }
    
    try {
      const exists = await this.containerClient.exists();
      
      // Early return pattern: if container exists, no need to create
      if (exists) {
        logger.info(`${BLOB_STORAGE_LOG_MESSAGES.CONTAINER_EXISTS}: ${blobConfig.containerName}`);
        return;
      }

      // Create container if it doesn't exist
      logger.info(`${BLOB_STORAGE_LOG_MESSAGES.CONTAINER_CREATING}: ${blobConfig.containerName}`);
      await this.containerClient.create();
      logger.info(`${BLOB_STORAGE_LOG_MESSAGES.CONTAINER_CREATED}: ${blobConfig.containerName}`);
    } catch (error) {
      logger.error(`${BLOB_STORAGE_LOG_MESSAGES.ERROR_ENSURE_CONTAINER}: ${error.message}`, {
        containerName: blobConfig.containerName,
        error,
      });
      throw new Error(`${BLOB_STORAGE_MESSAGES.CONTAINER_ERROR}: ${error.message}`);
    }
  }

  /**
   * Upload a file to Azure Blob Storage
   * @param {Buffer} fileBuffer - File buffer
   * @param {string} fileName - Name of the file
   * @param {string} contentType - MIME type of the file
   * @returns {Promise<Object>} Upload result with blob URL and metadata
   */
  async uploadFile(fileBuffer, fileName, contentType = BLOB_STORAGE_DEFAULTS.CONTENT_TYPE) {
    // Guard clause: check configuration first
    if (!this.isConfigured) {
      throw new Error(BLOB_STORAGE_MESSAGES.NOT_CONFIGURED);
    }

    try {
      const blobClient = this.containerClient.getBlockBlobClient(fileName);
      
      const uploadOptionsConfig = {
        blobHTTPHeaders: {
          blobContentType: contentType,
          blobCacheControl: BLOB_STORAGE_DEFAULTS.CACHE_CONTROL,
        },
      };

      await blobClient.upload(fileBuffer, fileBuffer.length, uploadOptionsConfig);
      
      logger.info(`${BLOB_STORAGE_LOG_MESSAGES.FILE_UPLOADED}: ${fileName}`);
      
      return {
        success: true,
        blobUrl: blobClient.url,
        fileName,
        size: fileBuffer.length,
        contentType,
      };
    } catch (error) {
      logger.error(`${BLOB_STORAGE_LOG_MESSAGES.ERROR_UPLOADING} ${fileName}: ${error.message}`, {
        fileName,
        error,
      });
      throw new Error(`${BLOB_STORAGE_MESSAGES.UPLOAD_FAILED}: ${error.message}`);
    }
  }

  /**
   * Download a file from Azure Blob Storage
   * @param {string} fileName - Name of the file to download
   * @returns {Promise<ReadableStream>} File download stream
   */
  async downloadFile(fileName) {
    // Guard clause: check configuration first
    if (!this.isConfigured) {
      throw new Error(BLOB_STORAGE_MESSAGES.NOT_CONFIGURED);
    }

    try {
      const blobClient = this.containerClient.getBlockBlobClient(fileName);
      
      // Early return pattern: check if file exists first
      const exists = await blobClient.exists();
      if (!exists) {
        throw new Error(`${BLOB_STORAGE_MESSAGES.FILE_NOT_FOUND}: ${fileName}`);
      }

      // Parallel execution for better performance
      const [properties, downloadResponse] = await Promise.all([
        blobClient.getProperties(),
        blobClient.download(),
      ]);
      
      logger.info(`${BLOB_STORAGE_LOG_MESSAGES.FILE_DOWNLOADED}: ${fileName}`);
      
      return {
        stream: downloadResponse.readableStreamBody,
        properties,
      };
    } catch (error) {
      logger.error(`${BLOB_STORAGE_LOG_MESSAGES.ERROR_DOWNLOADING} ${fileName}: ${error.message}`, {
        fileName,
        error,
      });
      throw new Error(`${BLOB_STORAGE_MESSAGES.DOWNLOAD_FAILED}: ${error.message}`);
    }
  }

  /**
   * List all files in the container
   * @returns {Promise<Array>} Array of file names and metadata
   */
  async listFiles() {
    // Guard clause: check configuration first
    if (!this.isConfigured) {
      throw new Error(BLOB_STORAGE_MESSAGES.NOT_CONFIGURED);
    }

    try {
      const files = [];
      
      // Use for-await loop for async iteration
      for await (const blob of this.containerClient.listBlobsFlat()) {
        files.push({
          name: blob.name,
          size: blob.properties.contentLength,
          contentType: blob.properties.contentType,
          lastModified: blob.properties.lastModified,
          etag: blob.properties.etag,
        });
      }
      
      logger.info(`${BLOB_STORAGE_LOG_MESSAGES.FILES_LISTED}: ${files.length} files`);
      
      return {
        success: true,
        files,
        count: files.length,
      };
    } catch (error) {
      logger.error(`${BLOB_STORAGE_LOG_MESSAGES.ERROR_LISTING}: ${error.message}`, { error });
      throw new Error(`${BLOB_STORAGE_MESSAGES.LIST_FAILED}: ${error.message}`);
    }
  }

  /**
   * Delete a file from Azure Blob Storage
   * @param {string} fileName - Name of the file to delete
   * @returns {Promise<Object>} Deletion result
   */
  async deleteFile(fileName) {
    // Guard clause: check configuration first
    if (!this.isConfigured) {
      throw new Error(BLOB_STORAGE_MESSAGES.NOT_CONFIGURED);
    }

    try {
      const blobClient = this.containerClient.getBlockBlobClient(fileName);
      
      // Early return pattern: check if file exists first
      const exists = await blobClient.exists();
      if (!exists) {
        throw new Error(`${BLOB_STORAGE_MESSAGES.FILE_NOT_FOUND}: ${fileName}`);
      }

      await blobClient.delete();
      
      logger.info(`${BLOB_STORAGE_LOG_MESSAGES.FILE_DELETED}: ${fileName}`);
      
      return {
        success: true,
        message: `${BLOB_STORAGE_MESSAGES.DELETE_SUCCESS}: ${fileName}`,
      };
    } catch (error) {
      logger.error(`${BLOB_STORAGE_LOG_MESSAGES.ERROR_DELETING} ${fileName}: ${error.message}`, {
        fileName,
        error,
      });
      throw new Error(`${BLOB_STORAGE_MESSAGES.DELETE_FAILED}: ${error.message}`);
    }
  }

  /**
   * Get blob URL for a file
   * @param {string} fileName - Name of the file
   * @returns {string} Blob URL
   */
  getBlobUrl(fileName) {
    try {
      // Guard clause: check configuration first
      if (!this.isConfigured) {
        throw new Error(BLOB_STORAGE_MESSAGES.NOT_CONFIGURED);
      }

      const blobClient = this.containerClient.getBlockBlobClient(fileName);
      return blobClient.url;
    } catch (error) {
      logger.error(`Error getting blob URL for ${fileName}: ${error.message}`, {
        fileName,
        error,
      });
      throw error;
    }
  }

  /**
   * Check if a file exists
   * @param {string} fileName - Name of the file
   * @returns {Promise<boolean>} True if file exists
   */
  async fileExists(fileName) {
    // Guard clause: check configuration first
    if (!this.isConfigured) {
      throw new Error(BLOB_STORAGE_MESSAGES.NOT_CONFIGURED);
    }

    try {
      const blobClient = this.containerClient.getBlockBlobClient(fileName);
      return await blobClient.exists();
    } catch (error) {
      logger.error(`${BLOB_STORAGE_LOG_MESSAGES.ERROR_CHECKING_EXISTENCE} ${fileName}: ${error.message}`, {
        fileName,
        error,
      });
      // Return false instead of throwing to allow graceful handling
      return false;
    }
  }

  /**
   * List blobs by prefix
   * @param {string} prefix - Prefix to filter blobs
   * @returns {Promise<Array>} Array of blob items with name and metadata
   * OPTIMIZED: Uses Array.from for better performance with async iterators
   */
  async listByPrefix(prefix) {
    // Guard clause: check configuration first
    if (!this.isConfigured) {
      throw new Error(BLOB_STORAGE_MESSAGES.NOT_CONFIGURED);
    }

    try {
      // PERFORMANCE: Use Array.from with async iterator (more efficient than for-await + push)
      const blobIterator = this.containerClient.listBlobsFlat({ prefix });
      
      const blobs = [];
      for await (const blob of blobIterator) {
        // Extract properties once (avoid redundant property access)
        const properties = blob.properties;
        
        blobs.push({
          name: blob.name,
          size: properties.contentLength,
          contentType: properties.contentType,
          lastModified: properties.lastModified,
          etag: properties.etag,
        });
      }
      
      logger.info(`${BLOB_STORAGE_LOG_MESSAGES.BLOBS_LISTED}: ${blobs.length} blobs, prefix: ${prefix}`);
      
      return blobs;
    } catch (error) {
      logger.error(`${BLOB_STORAGE_LOG_MESSAGES.ERROR_LISTING_BLOBS} ${prefix}: ${error.message}`, {
        prefix,
        error,
      });
      throw new Error(`${BLOB_STORAGE_MESSAGES.LIST_FAILED}: ${error.message}`);
    }
  }

  /**
   * Get a SAS (Shared Access Signature) URL for a blob
   * @param {string} fileName - Name of the file
   * @param {number} expiryHours - Hours until expiry (default: 1)
   * @returns {Promise<string>} SAS URL
   * OPTIMIZED: Caches dynamic imports and credential parsing
   */
  async getBlobSasUrl(fileName, expiryHours = BLOB_STORAGE_DEFAULTS.DEFAULT_EXPIRY_HOURS) {
    // Guard clause: check configuration first
    if (!this.isConfigured) {
      throw new Error(BLOB_STORAGE_MESSAGES.NOT_CONFIGURED);
    }

    try {
      // PERFORMANCE: Cache dynamic imports (only import once)
      if (!this._sasUtils) {
        const {
          generateBlobSASQueryParameters,
          BlobSASPermissions,
          StorageSharedKeyCredential,
        } = await import("@azure/storage-blob");
        
        this._sasUtils = {
          generateBlobSASQueryParameters,
          BlobSASPermissions,
          StorageSharedKeyCredential,
        };
      }

      // PERFORMANCE: Cache credential parsing (connection string parsing is expensive)
      if (!this._credential) {
        const { blobConfig } = await import("../../config/blob.config.js");
        const connectionString = blobConfig.connectionString;
        
        // Parse connection string once and cache
        const accountNameMatch = connectionString.match(/AccountName=([^;]+)/);
        const accountKeyMatch = connectionString.match(/AccountKey=([^;]+)/);
        
        if (!accountNameMatch || !accountKeyMatch) {
          throw new Error(BLOB_STORAGE_MESSAGES.INVALID_CONNECTION_STRING);
        }

        const accountName = accountNameMatch[1];
        const accountKey = accountKeyMatch[1];
        this._credential = new this._sasUtils.StorageSharedKeyCredential(accountName, accountKey);
        this._blobConfig = blobConfig; // Cache config too
      }

      const {
        generateBlobSASQueryParameters,
        BlobSASPermissions,
      } = this._sasUtils;
      
      const blobClient = this.containerClient.getBlockBlobClient(fileName);
      
      // Early return pattern: check if blob exists first
      const exists = await blobClient.exists();
      if (!exists) {
        throw new Error(`${BLOB_STORAGE_MESSAGES.BLOB_NOT_FOUND}: ${fileName}`);
      }

      // Calculate expiry time (optimized: single date operation)
      const expiryTime = new Date();
      expiryTime.setHours(expiryTime.getHours() + expiryHours);

      // Generate SAS token with read permission (using cached credential)
      const sasToken = generateBlobSASQueryParameters(
        {
          containerName: this._blobConfig.containerName,
          blobName: fileName,
          permissions: BlobSASPermissions.parse(BLOB_STORAGE_DEFAULTS.SAS_PERMISSIONS),
          startsOn: new Date(),
          expiresOn: expiryTime,
        },
        this._credential
      );

      // Construct full SAS URL
      const sasUrl = `${blobClient.url}?${sasToken}`;
      
      logger.info(`${BLOB_STORAGE_LOG_MESSAGES.SAS_URL_GENERATED}: ${fileName}`);
      
      return sasUrl;
    } catch (error) {
      logger.error(`${BLOB_STORAGE_LOG_MESSAGES.ERROR_GENERATING_SAS} ${fileName}: ${error.message}`, {
        fileName,
        expiryHours,
        error,
      });
      throw new Error(`${BLOB_STORAGE_MESSAGES.SAS_URL_GENERATION_FAILED}: ${error.message}`);
    }
  }
}

// Export singleton instance
export default new BlobStorageService();

