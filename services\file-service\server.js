// server.js
import express from "express";
import cors from "cors";
import morgan from "morgan";
import dotenv from "dotenv";
import fileRoutes from "./app/routes/file.routes.js";
import documentRoutes from "./app/routes/document.routes.js";
import powerBiWorkflowRoutes from "./app/routes/powerBiWorkflow.routes.js";
import onboardingRoutes from "./app/routes/onboarding.routes.js";
import {
  errorHandler,
  notFoundHandler,
} from "./app/middleware/errorHandler.middleware.js";
import { corsOptions } from "./config/cors.config.js";
import logger from "./config/logger.config.js";
import blobStorageService from "./app/services/blobStorage.service.js";
import { sequelize } from "./app/models/index.js";
import { startCacheCleanup } from "./app/utils/cacheCleanup.util.js";

// Load environment variables
dotenv.config();

const app = express();
// REFACTORED: No fallback operators - assumes env vars are validated externally
const PORT = process.env.PORT;
const HOST = process.env.HOST;

// Middleware setup
app.use(cors(corsOptions));
// Configure JSON parser with error handling and increased limit for file metadata
app.use(express.json({ 
  limit: '10mb',
  strict: true,
  type: 'application/json'
}));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Request logging with Morgan
app.use(
  morgan("combined", {
  stream: {
    write: (message) => logger.info(message.trim()),
  },
  })
);

// Health check endpoint
app.get("/health", (_req, res) => {
  res.status(200).json({
    success: true,
    service: "File Storage Service",
    status: "healthy",
    timestamp: new Date().toISOString(),
  });
});

// Cache endpoint URLs (only baseUrl changes per request)
const ENDPOINT_PATHS = {
  health: "/health",
  reportFolders: "/api/files/report-folders",
  getReport: "/api/files/get-report",
  resolve: "/api/files/resolve",
  storeDocument: "/api/document",
  getDocuments: "/api/document",
  uploadLogo: "/api/onboarding/logo",
};

// Root endpoint - optimized with cached endpoint paths
app.get("/", (req, res) => {
  try {
    // Build base URL with early return pattern
    // REFACTORED: No fallback operators - use request values directly
    const protocol = req.protocol;
    const host = req.get("host");
    
    // Guard clause: validate required values
    if (!protocol || !host) {
      throw new Error("Unable to determine request protocol or host");
    }
    
    const baseUrl = `${protocol}://${host}`;
    
    // Build endpoints object using mapping pattern
    const endpoints = Object.fromEntries(
      Object.entries(ENDPOINT_PATHS).map(([key, path]) => [
        key,
        `${baseUrl}${path}`,
      ])
    );
    
    res.status(200).json({
      success: true,
      service: "File Storage Service",
      message: "Welcome to the File Storage API",
      version: "1.0.0",
      baseUrl,
      endpoints,
    });
  } catch (error) {
    logger.error(`Error in root endpoint: ${error.message}`, { error });
    res.status(500).json({
      success: false,
      message: "Error generating endpoint information",
    });
  }
});

// API routes
app.use("/api/files", fileRoutes);
app.use("/api/document", documentRoutes);
app.use("/api/powerbi", powerBiWorkflowRoutes);
app.use("/api/onboarding", onboardingRoutes);

// Error handling middleware (must be last)
app.use(notFoundHandler);
app.use(errorHandler);

// Initialize blob storage service (optional)
try {
  // This will validate configuration and ensure container exists
  await blobStorageService.ensureContainer();
  logger.info("Blob storage service initialized successfully");
} catch (error) {
  logger.warn("Failed to initialize blob storage service:", error.message);
  logger.warn(
    "File upload/download features will not be available, but document metadata API will work"
  );
}

// Initialize database connection
try {
  await sequelize.authenticate();
  logger.info("Database connection established successfully");
} catch (error) {
  logger.error("Failed to connect to database:", error);
  logger.error(
    "Document metadata features will not function properly without database connection"
  );
}

// Start server
app.listen(PORT, HOST, () => {
  logger.info(`🚀 File Storage Service running on port ${PORT}`);
  logger.info(`📂 Environment: ${process.env.NODE_ENV}`);
  logger.info(`🌐 CORS Origin: ${corsOptions.origin}`);
  logger.info(`🌍 Server listening on ${HOST}:${PORT}`);
  
  // PERFORMANCE: Start cache cleanup scheduler
  try {
    startCacheCleanup();
  } catch (error) {
    logger.warn(`Failed to start cache cleanup: ${error.message}`);
  }
});

// Graceful shutdown
process.on("SIGTERM", () => {
  logger.info("SIGTERM signal received: closing HTTP server");
  process.exit(0);
});

process.on("SIGINT", () => {
  logger.info("SIGINT signal received: closing HTTP server");
  process.exit(0);
});

export default app;
