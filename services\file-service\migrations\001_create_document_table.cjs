"use strict";

const TABLE_REFERENCE = { tableName: "document", schema: "Authentication" };
const BASE_INDEXES = [
  {
    name: "idx_document_organization_id",
    fields: ["organization_id"],
  },
  {
    name: "idx_document_service",
    fields: ["service"],
  },
  {
    name: "idx_document_month_year",
    fields: ["month", "year"],
  },
  {
    name: "idx_document_organization_service_month_year",
    fields: ["organization_id", "service", "month", "year"],
  },
  {
    name: "idx_document_is_deleted",
    fields: ["is_deleted"],
  },
  {
    name: "idx_document_created_at",
    fields: ["created_at"],
  },
];
const UNIQUE_INDEX_DEFINITION = {
  name: "uq_document_org_service_period_file",
  unique: true,
  fields: ["organization_id", "service", "month", "year", "file_name"],
  where: {
    is_deleted: false,
  },
};

module.exports = {
  up: async function (queryInterface, Sequelize) {
    await queryInterface.sequelize.query(
      `CREATE SCHEMA IF NOT EXISTS "${TABLE_REFERENCE.schema}"`
    );

    await queryInterface.createTable(
      TABLE_REFERENCE.tableName,
      {
        id: {
          type: Sequelize.UUID,
          defaultValue: Sequelize.UUIDV4,
          primaryKey: true,
        },
        organization_id: {
          type: Sequelize.UUID,
          allowNull: false,
        },
        blob_storage_path: {
          type: Sequelize.TEXT,
          allowNull: false,
          comment: "Azure blob storage file path",
        },
        service: {
          type: Sequelize.TEXT,
          allowNull: true,
          comment: "Service type (financial, operational, pms)",
        },
        month: {
          type: Sequelize.INTEGER,
          allowNull: true,
          validate: {
            min: 1,
            max: 12,
          },
          comment: "Month (1-12)",
        },
        year: {
          type: Sequelize.INTEGER,
          allowNull: true,
          validate: {
            min: 2000,
            max: 3000,
          },
          comment: "Year (e.g., 2025)",
        },
        file_name: {
          type: Sequelize.TEXT,
          allowNull: false,
          comment: "Original filename",
        },
        file_size: {
          type: Sequelize.BIGINT,
          allowNull: true,
          comment: "File size in bytes",
        },
        mime_type: {
          type: Sequelize.TEXT,
          allowNull: true,
          comment: "File MIME type",
        },
        summary: {
          type: Sequelize.TEXT,
          allowNull: true,
          comment: "Document summary text",
        },
        is_active: {
          type: Sequelize.BOOLEAN,
          defaultValue: true,
        },
        is_deleted: {
          type: Sequelize.BOOLEAN,
          defaultValue: false,
        },
        metadata: {
          type: Sequelize.JSONB,
          allowNull: true,
          defaultValue: {},
          comment: "Additional metadata",
        },
        created_at: {
          type: Sequelize.DATE,
          defaultValue: Sequelize.NOW,
        },
        created_by: {
          type: Sequelize.UUID,
          allowNull: true,
        },
        updated_at: {
          type: Sequelize.DATE,
        },
        updated_by: {
          type: Sequelize.UUID,
          allowNull: true,
        },
      },
      {
        schema: TABLE_REFERENCE.schema,
        timestamps: true,
        createdAt: "created_at",
        updatedAt: "updated_at",
      }
    );

    for (const indexDefinition of BASE_INDEXES) {
      await queryInterface.addIndex(TABLE_REFERENCE, indexDefinition);
    }

    await queryInterface.addIndex(TABLE_REFERENCE, UNIQUE_INDEX_DEFINITION);
  },

  down: async function (queryInterface, Sequelize) {
    await queryInterface.dropTable(TABLE_REFERENCE.tableName, {
      schema: TABLE_REFERENCE.schema,
    });
  },
};
