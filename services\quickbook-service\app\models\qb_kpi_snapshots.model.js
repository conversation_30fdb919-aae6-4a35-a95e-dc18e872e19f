import { DataTypes } from "sequelize";

const QbKpiSnapshots = (sequelize) => {
  const KpiSnapshots = sequelize.define(
    "KpiSnapshots",
    {
      id: { type: DataTypes.BIGINT, primaryKey: true, autoIncrement: true },

      pnl_report_id: { type: DataTypes.BIGINT, allowNull: true },
      cashflow_report_id: { type: DataTypes.BIGINT, allowNull: true },

      total_expense_kpi: { type: DataTypes.JSONB, allowNull: true },
      total_revenue_kpi: { type: DataTypes.JSONB, allowNull: true },
      ebitda_kpi: { type: DataTypes.JSONB, allowNull: true },
      cashflow_kpi: { type: DataTypes.JSONB, allowNull: true },

      created_at: { type: DataTypes.DATE, defaultValue: DataTypes.NOW },
    },
    {
      tableName: "qb_kpi_snapshots",
      timestamps: false,
    }
  );

  return KpiSnapshots;
};

export default QbKpiSnapshots;

