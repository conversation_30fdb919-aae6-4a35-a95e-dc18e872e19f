const DEFAULT_CURRENCY = "USD";

export const parseKValueToNumber = (value) => {
  if (value === null || value === undefined) return 0;
  if (typeof value === "number") return value;
  if (typeof value === "string") {
    const cleaned = value
      .replace(/[\$,%]/g, "")
      .trim()
      .toUpperCase();
    const isK = cleaned.endsWith("K");
    const numeric = parseFloat(isK ? cleaned.slice(0, -1) : cleaned);
    if (Number.isNaN(numeric)) return 0;
    return isK ? numeric * 1000 : numeric;
  }
  return 0;
};

const parsePercentageChange = (changeString) => {
  if (!changeString || typeof changeString !== "string") return null;
  const trimmed = changeString.trim();
  if (!trimmed) return null;
  
  const isNegative = trimmed.startsWith("-");
  const cleaned = trimmed.replace(/[+%-]/g, "");
  const numeric = parseFloat(cleaned);
  
  if (Number.isNaN(numeric)) return null;
  
  const sign = isNegative ? "-" : "+";
  const formatted = `${sign}${Math.abs(numeric).toFixed(2)}%`;
  
  return {
    value: isNegative ? -Math.abs(numeric) : Math.abs(numeric),
    formatted,
    isPositive: !isNegative,
  };
};

export const mapPayrollKpiData = (payload = {}) => {
  const kpiPayload = payload?.data?.kpi || payload?.kpi || {};

  const totalPayrollKpi = kpiPayload.total_payroll || {};
  const doctorSalaryKpi = kpiPayload.doctor_salary || {};
  const totalDeductionsKpi = kpiPayload.total_deductions || {};
  const totalTaxesKpi = kpiPayload.total_taxes || {};
  const doctorsContributionKpi = kpiPayload.doctors_contribution || {};

  const totalPayrollValue = parseKValueToNumber(totalPayrollKpi.total);
  const totalPayrollPm = parseKValueToNumber(totalPayrollKpi.pm);
  const totalPayrollYtd = parseKValueToNumber(totalPayrollKpi.ytd);
  const totalPayrollChange = parsePercentageChange(totalPayrollKpi.pm_change);

  const doctorSalaryValue = parseKValueToNumber(doctorSalaryKpi.total);
  const doctorSalaryPm = parseKValueToNumber(doctorSalaryKpi.pm);
  const doctorSalaryYtd = parseKValueToNumber(doctorSalaryKpi.ytd);
  const doctorSalaryChange = parsePercentageChange(doctorSalaryKpi.pm_change);

  const totalDeductionsValue = parseKValueToNumber(totalDeductionsKpi.total);
  const totalDeductionsPm = parseKValueToNumber(totalDeductionsKpi.pm);
  const totalDeductionsYtd = parseKValueToNumber(totalDeductionsKpi.ytd);
  const totalDeductionsChange = parsePercentageChange(totalDeductionsKpi.pm_change);

  const totalTaxesValue = parseKValueToNumber(totalTaxesKpi.total);
  const totalTaxesPm = parseKValueToNumber(totalTaxesKpi.pm);
  const totalTaxesYtd = parseKValueToNumber(totalTaxesKpi.ytd);
  const totalTaxesChange = parsePercentageChange(totalTaxesKpi.pm_change);

  const parsePercentageValue = (value) => {
    if (!value || typeof value !== "string") return 0;
    const cleaned = value.replace(/%/g, "").trim();
    return parseFloat(cleaned) || 0;
  };

  const doctorContributionValue = parsePercentageValue(doctorsContributionKpi.total);
  const doctorContributionPm = parsePercentageValue(doctorsContributionKpi.pm);
  const doctorContributionYtd = parsePercentageValue(doctorsContributionKpi.ytd);
  const doctorContributionChange = parsePercentageChange(doctorsContributionKpi.pm_change);

  return [
    {
      id: "totalPayroll",
      title: "Total Payroll",
      format: "currency",
      value: totalPayrollValue,
      previousMonth: totalPayrollPm,
      previousMonthChange: totalPayrollChange,
      yearToDate: totalPayrollYtd,
    },
    {
      id: "doctorSalary",
      title: "Doctor Salary",
      format: "currency",
      value: doctorSalaryValue,
      previousMonth: doctorSalaryPm,
      previousMonthChange: doctorSalaryChange,
      yearToDate: doctorSalaryYtd,
    },
    {
      id: "totalDeductions",
      title: "Total Deductions",
      format: "currency",
      value: totalDeductionsValue,
      previousMonth: totalDeductionsPm,
      previousMonthChange: totalDeductionsChange,
      yearToDate: totalDeductionsYtd,
    },
    {
      id: "totalTaxes",
      title: "Total Taxes",
      format: "currency",
      value: totalTaxesValue,
      previousMonth: totalTaxesPm,
      previousMonthChange: totalTaxesChange,
      yearToDate: totalTaxesYtd,
    },
    {
      id: "doctorContribution",
      title: "Doctor's Contribution",
      format: "percent",
      value: doctorContributionValue,
      previousMonth: doctorContributionPm,
      previousMonthChange: doctorContributionChange,
      yearToDate: doctorContributionYtd,
    },
  ];
};

export const mapPayrollTaxBreakdownData = (payload = {}) => {
  const taxBreakdown =
    payload?.data?.tax_breakdown || payload?.tax_breakdown || {};

  const taxCategories = [
    { key: "FTT", label: "FTT" },
    { key: "SOCSEC", label: "SOCSEC" },
    { key: "SIT", label: "SIT" },
    { key: "MEDICARE", label: "MEDICARE" },
  ];

  return {
    title: "Tax Breakdown",
    categories: taxCategories.map((item) => ({
      name: item.label,
      value: parseKValueToNumber(taxBreakdown[item.key]),
    })),
  };
};

export const mapPayrollDeductionsBreakdownData = (payload = {}) => {
  const deductionsBreakdown =
    payload?.data?.deductions_breakdown || payload?.deductions_breakdown || [];

  if (!deductionsBreakdown || deductionsBreakdown.length === 0) {
    return null;
  }

  const categories = deductionsBreakdown.map((item) => ({
    name: item.deduction_name,
    value: parseKValueToNumber(item.deductions_amount),
    percentage: parseFloat(item.deductions_percentage) || 0,
  }));

  const hasValidData = categories.some((item) => item.value > 0);

  if (!hasValidData) {
    return null;
  }

  return {
    title: "Deductions Breakdown",
    categories,
  };
};

export const mapPayrollSalaryByDepartmentData = (payload = {}) => {
  const departments = payload?.data?.departments || payload?.departments || [];

  return {
    title: "Salary By Staff Category",
    categories: departments.map((item) => ({
      name: item.department,
      value: parseKValueToNumber(item.total_salary),
    })),
  };
};

export const mapPayrollDoctorSalaryComparisonData = (
  payload = {},
  chartPeriod = null
) => {
  const kpiPayload = payload?.data?.kpi || payload?.kpi || {};

  const totalPayrollKpi = kpiPayload.total_payroll || {};
  const doctorSalaryKpi = kpiPayload.doctor_salary || {};

  const totalPayrollValue = parseKValueToNumber(totalPayrollKpi.total);
  const doctorSalaryValue = parseKValueToNumber(doctorSalaryKpi.total);

  return {
    title: "Total VS Doctor Salary Comparison",
    comparison: [
      {
        period: chartPeriod,
        totalPayroll: totalPayrollValue,
        doctorSalary: doctorSalaryValue,
      },
    ],
  };
};

export const mapPayrollAverageSalaryByRoleData = (payload = {}) => {
  const departments = payload?.data?.departments || payload?.departments || [];

  const normalizeRoleName = (departmentName) => {
    return departmentName
      .replace(/\s*Salaries?$/i, "")
      .replace(/\s*Salary$/i, "")
      .trim();
  };

  return {
    title: "Average Salary By Role",
    roles: departments.map((item) => ({
      role: normalizeRoleName(item.department),
      value: parseKValueToNumber(item.average_salary),
    })),
  };
};

export const mapPayrollHeadcountByRoleData = (payload = {}) => {
  const departments = payload?.data?.departments || payload?.departments || [];

  const normalizeRoleName = (departmentName) => {
    return departmentName
      .replace(/\s*Salaries?$/i, "")
      .replace(/\s*Salary$/i, "")
      .trim();
  };

  return {
    title: "Headcount By Role",
    roles: departments.map((item) => ({
      role: normalizeRoleName(item.department),
      value: item.headcount || 0,
    })),
  };
};
