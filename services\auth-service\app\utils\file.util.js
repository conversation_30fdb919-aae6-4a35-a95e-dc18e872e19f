import path from "path";
import fs from "fs";

export const createUploadValue = (logo) => {
  const defaultOptions = {
    filename: `organization-logo-${Date.now()}.png`,
    contentType: "image/png",
  };

  if (!logo) {
    return null;
  }

  if (Buffer.isBuffer(logo)) {
    return { data: logo, options: defaultOptions };
  }

  if (typeof logo === "object") {
    if (logo.buffer) {
      const buffer = Buffer.isBuffer(logo.buffer)
        ? logo.buffer
        : Buffer.from(logo.buffer, "base64");
      return {
        data: buffer,
        options: {
          filename: logo.originalname || defaultOptions.filename,
          contentType: logo.mimetype || defaultOptions.contentType,
        },
      };
    }

    if (logo.path && fs.existsSync(logo.path)) {
      return {
        data: fs.createReadStream(logo.path),
        options: {
          filename: path.basename(logo.path),
          contentType: logo.mimetype || defaultOptions.contentType,
        },
      };
    }
  }

  if (typeof logo === "string") {
    const normalized = logo.trim();

    if (fs.existsSync(normalized)) {
      return {
        data: fs.createReadStream(normalized),
        options: {
          filename: path.basename(normalized),
          contentType: defaultOptions.contentType,
        },
      };
    }

    const base64 = normalized.startsWith("data:")
      ? normalized.split(",")[1]
      : normalized;

    try {
      const buffer = Buffer.from(base64, "base64");
      if (buffer.length > 0) {
        return { data: buffer, options: defaultOptions };
      }
    } catch (error) {
      return null;
    }
  }

  return null;
};
