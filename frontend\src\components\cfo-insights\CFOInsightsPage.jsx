﻿"use client";

import {
  useState,
  useRef,
  useEffect,
  memo,
  useCallback,
  useMemo,
} from "react";
import { useRouter } from "next/navigation";
import { useDispatch, useSelector } from "react-redux";
import { selectChatData } from "@/redux/Selectors/chat";
import { motion } from "framer-motion";
import { Button } from "../ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "../ui/card";
import { ScrollArea } from "../ui/scroll-area";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "../ui/accordion";
import {
  Bot,
  HelpCircle,
  ChevronLeft,
  MessageSquare,
  Settings,
  Lightbulb,
  Sparkles,
  X,
} from "lucide-react";
import { addMessage, setHasWelcomed, resetChat } from "@/redux/Slice/chat";
import {
  startChatSession,
  sendChatMessage,
} from "@/redux/Thunks/chat";
import {
  generatePdfFilename,
  formatWelcomeMessage,
  createMessage,
} from "@/utils/chat";
import { MESSAGE_TYPES, CHAT_ERRORS } from "@/utils/constants/chat";
import { CFOMessageList } from "./CFOMessageList";
import { CFOInputArea } from "./CFOInputArea";
import "@/styles/cfo-insights.css";

const CFOInsightsPage = memo(function CFOInsightsPage({
  onBack,
  selectedMonth,
  selectedMonthKey,
  selectedYear,
  selectedPage,
  isFinancialSelected,
  isOperationsSelected,
  isPayrollSelected,
  selectedDashboard,
  fileName,
  organizationName,
  organizationId,
  onDownload,
}) {
  const router = useRouter();
  const dispatch = useDispatch();
  const { session, messages, isLoading, hasWelcomed } =
    useSelector(selectChatData);

  // Local state
  const [question, setQuestion] = useState("");
  const [showSidebox, setShowSidebox] = useState(true);
  const scrollAreaRef = useRef(null);
  const scrollViewportRef = useRef(null);
  const welcomeAddedRef = useRef(false);

  // Sidebar accordion state - using Shadcn Accordion component
  const [accordionValue, setAccordionValue] = useState("gettingStarted");

  const welcomeContent = useMemo(() => {
    return formatWelcomeMessage(
      selectedMonth,
      organizationName,
      selectedYear,
      isFinancialSelected,
      isOperationsSelected,
      isPayrollSelected
    );
  }, [
    selectedMonth,
    organizationName,
    selectedYear,
    isFinancialSelected,
    isOperationsSelected,
    isPayrollSelected,
  ]);

  const effectiveMonth = selectedMonthKey || selectedMonth;

  const filename = useMemo(() => {
    if (
      fileName &&
      typeof fileName === "string" &&
      fileName.trim().length > 0
    ) {
      return fileName.trim();
    }
    return generatePdfFilename(
      effectiveMonth,
      isFinancialSelected,
      isOperationsSelected,
      isPayrollSelected,
      selectedDashboard
    );
  }, [
    fileName,
    effectiveMonth,
    isFinancialSelected,
    isOperationsSelected,
    isPayrollSelected,
    selectedDashboard,
  ]);

  useEffect(() => {
    if (!hasWelcomed && !welcomeAddedRef.current && messages.length === 0) {
      const welcomeMessage = createMessage(welcomeContent, MESSAGE_TYPES.AI);
      dispatch(addMessage(welcomeMessage));
      dispatch(setHasWelcomed(true));
      welcomeAddedRef.current = true;
    }
  }, [hasWelcomed, messages.length, dispatch, welcomeContent]);

  useEffect(() => {
    dispatch(resetChat());
    setQuestion("");
    welcomeAddedRef.current = false;
    dispatch(setHasWelcomed(false));

    const welcomeMessage = createMessage(welcomeContent, MESSAGE_TYPES.AI);
    dispatch(addMessage(welcomeMessage));
    dispatch(setHasWelcomed(true));
    welcomeAddedRef.current = true;
  }, [
    effectiveMonth,
    organizationName,
    selectedPage,
    isFinancialSelected,
    isOperationsSelected,
    isPayrollSelected,
    selectedDashboard,
    dispatch,
    welcomeContent,
  ]);

  const handleSubmit = useCallback(
    async (e) => {
      e.preventDefault();
      const trimmedQuestion = question.trim();
      if (!trimmedQuestion) return;

      // Input validation
      if (trimmedQuestion.length > 2000) {
        const errorMessage = createMessage(
          CHAT_ERRORS.VALIDATION_ERRORS.MESSAGE_TOO_LONG,
          MESSAGE_TYPES.AI
        );
        dispatch(addMessage(errorMessage));
        return;
      }

      // Add user message to chat
      const userMessage = createMessage(trimmedQuestion, MESSAGE_TYPES.USER);
      dispatch(addMessage(userMessage));
      setQuestion("");

      const extractErrorMessage = (err) => {
        if (!err) return "";
        if (typeof err === "string") return err;
        if (err.message) return err.message;
        return "";
      };

      const sendMessageWithSession = async (sessionIdToUse) => {
        await dispatch(
          sendChatMessage({
            sessionId: sessionIdToUse,
            message: userMessage.content,
            filename,
            service: isFinancialSelected ? "Finance" : isOperationsSelected ? "Operations" : isPayrollSelected ? "Payroll" : "Finance",
            isFinancialSelected,
            isOperationsSelected,
            isPayrollSelected,
            selectedMonth: effectiveMonth,
          })
        ).unwrap();
      };

      try {
        // If we don't have a chat session, start one first
        let currentSessionId = session.id;
        if (!currentSessionId) {
          if (!filename) {
            throw new Error(CHAT_ERRORS.VALIDATION_ERRORS.FILENAME_INVALID);
          }

          const startResult = await dispatch(
            startChatSession({
              filename,
              orgId: organizationId,
              orgName: organizationName,
              selectedMonth: effectiveMonth,
              isFinancialSelected,
              isOperationsSelected,
              isPayrollSelected,
            })
          ).unwrap();

          if (!startResult || !startResult.sessionId) {
            throw new Error(CHAT_ERRORS.START_SESSION_FAILED);
          }

          currentSessionId = startResult.sessionId;
        }

        await sendMessageWithSession(currentSessionId);
      } catch (error) {
        const fallbackMessage =
          extractErrorMessage(error) || CHAT_ERRORS.GENERIC_ERROR;
        const normalizedMessage = fallbackMessage.toLowerCase();
        const isSessionExpiredError =
          normalizedMessage.includes("session not found or expired") ||
          normalizedMessage.includes("chat session not found");

        if (isSessionExpiredError && filename) {
          try {
            const restartResult = await dispatch(
              startChatSession({
                filename,
                orgId: organizationId,
                orgName: organizationName,
                selectedMonth: effectiveMonth,
                isFinancialSelected,
                isOperationsSelected,
                isPayrollSelected,
              })
            ).unwrap();

            const newSessionId = restartResult?.sessionId;
            if (!newSessionId) {
              throw new Error(CHAT_ERRORS.START_SESSION_FAILED);
            }

            await sendMessageWithSession(newSessionId);
            return;
          } catch (retryError) {
            const retryFallback =
              extractErrorMessage(retryError) || fallbackMessage;
            const errorMessage = createMessage(retryFallback, MESSAGE_TYPES.AI);
            dispatch(addMessage(errorMessage));
            return;
          }
        }

        const errorMessage = createMessage(fallbackMessage, MESSAGE_TYPES.AI);
        dispatch(addMessage(errorMessage));
      }
    },
    [question, dispatch, session.id, organizationId, organizationName, filename, effectiveMonth, isFinancialSelected, isOperationsSelected, isPayrollSelected]
  );

  const handleEndChat = useCallback(async () => {
    // Clear chat history and reset state
    dispatch(resetChat());
    setQuestion("");
    welcomeAddedRef.current = false; // Reset the welcome ref

    if (onBack) {
      onBack();
    } else {
      router.push("/dashboard");
    }
  }, [dispatch, onBack, router]);

  const toggleSidebox = useCallback(() => {
    setShowSidebox((prev) => !prev);
  }, []);

  // Memoized scroll function for better performance
  const scrollToBottomWithDelay = useCallback(() => {
    const el = scrollViewportRef.current;
    if (el) {
      requestAnimationFrame(() => {
        el.scrollTop = el.scrollHeight;
      });
    }
  }, []);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    scrollToBottomWithDelay();
  }, [messages, scrollToBottomWithDelay]);

  // Scroll to bottom when loading state changes
  useEffect(() => {
    if (!isLoading) {
      scrollToBottomWithDelay();
    }
  }, [isLoading, scrollToBottomWithDelay]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      // Reset welcome ref on unmount
      welcomeAddedRef.current = false;
    };
  }, []);

  // Cache the scroll viewport element once after mount
  useEffect(() => {
    if (!scrollViewportRef.current && scrollAreaRef.current) {
      const el = scrollAreaRef.current.querySelector(
        "[data-radix-scroll-area-viewport]"
      );
      if (el) scrollViewportRef.current = el;
    }
  }, []);

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.95 }}
      transition={{ duration: 0.3, ease: "easeOut" }}
      className="cfo-insights-container"
    >
      <div className="cfo-insights-main-content">
        {/* Chat Panel - Left Column (70%) */}
        <div className="cfo-insights-chat-panel">
        <Card className="cfo-insights-chat-card">
          <CardHeader className="cfo-insights-chat-header">
              <div className="cfo-insights-chat-header-content">
            <CardTitle className="cfo-insights-chat-title">
                  <div className="cfo-insights-chat-title-row">
              <div className="cfo-insights-chat-icon">
                      <Sparkles className="w-4 h-4 sm:w-5 sm:h-5 text-white" />
                    </div>
                    <span className="cfo-insights-chat-title-text">
                      CFO Insights Assistant
                    </span>
                  </div>
                  <span className="cfo-insights-chat-subtitle">
                    AI-powered analysis
                  </span>
                </CardTitle>
                <button
                  onClick={handleEndChat}
                  className="cfo-insights-header-end-chat-link"
                  aria-label="End Chat"
                  title="End Chat"
                >
                  <X className="w-5 h-5 sm:w-6 sm:h-6" />
                </button>
              </div>
          </CardHeader>

          <CardContent className="cfo-insights-messages-container">
            <ScrollArea ref={scrollAreaRef} className="h-full">
              <div className="cfo-insights-messages-content">
                <CFOMessageList
                  messages={messages}
                  isLoading={isLoading}
                  effectiveMonth={effectiveMonth}
                  selectedPage={selectedPage}
                  isOperationsSelected={isOperationsSelected}
                  isFinancialSelected={isFinancialSelected}
                />
              </div>
            </ScrollArea>
          </CardContent>

            {/* Input Bar - Fixed at bottom of chat panel */}
        <CFOInputArea
          question={question}
          isLoading={isLoading}
          onChange={(e) => setQuestion(e.target.value)}
          onSubmit={handleSubmit}
        />
          </Card>
      </div>

        {/* Hub Panel - Right Column (30%) */}
        <div className="cfo-insights-sidebar open">
        <div className="h-full flex flex-col">
          <div className="cfo-insights-sidebar-header">
            <div className="cfo-insights-sidebar-header-bg" />
            <div className="cfo-insights-sidebar-header-content">
              <motion.div
                animate={{ rotate: [0, 5, -5, 0], scale: [1, 1.05, 1] }}
                transition={{
                  duration: 3,
                  repeat: Infinity,
                  ease: "easeInOut",
                }}
                className="cfo-insights-sidebar-icon"
              >
                <HelpCircle className="w-4 h-4 sm:w-5 sm:h-5 text-indigo-600" strokeWidth={1.5} />
              </motion.div>
              <h3 className="cfo-insights-sidebar-title">
                CFO Intelligence
                <br />
                Hub
              </h3>
            </div>
          </div>

          <div className="cfo-insights-sidebar-content">
            <aside className="cfo-insights-sidebar-section">
              <Accordion
                type="single"
                collapsible
                defaultValue="gettingStarted"
                value={accordionValue}
                onValueChange={setAccordionValue}
                className="w-full space-y-3 !bg-transparent !border-0 !shadow-none !p-0"
              >
                <AccordionItem
                  value="gettingStarted"
                  className="border-0 border-b border-gray-100 rounded-none data-[state=open]:bg-transparent transition-all duration-500 ease-in-out mb-0"
                >
                  <AccordionTrigger className="flex items-center gap-2 sm:gap-2.5 text-sm sm:text-base font-semibold text-gray-900 rounded-lg !px-3 sm:!px-4 !py-2.5 sm:!py-3 hover:bg-gray-50 transition-all duration-300">
                    <Sparkles className="text-indigo-600 w-4 h-4 sm:w-5 sm:h-5 flex-shrink-0" strokeWidth={1.5} />
                    <span>Getting Started</span>
                  </AccordionTrigger>
                  <AccordionContent className="px-4 pb-4 pt-2 text-sm leading-relaxed">
                    <ul className="text-left">
                      <li>
                        Engage with your financial data through natural language
                        queries.
                      </li>
                      <li>
                        Leverage advanced AI algorithms for comprehensive
                        financial analysis.
                      </li>
                      <li>
                        Access real-time insights from your dashboard documents.
                      </li>
                    </ul>
                  </AccordionContent>
                </AccordionItem>

                <AccordionItem
                  value="exampleQuestions"
                  className="border-0 border-b border-gray-100 rounded-none data-[state=open]:bg-transparent transition-all duration-500 ease-in-out mb-0"
                >
                  <AccordionTrigger className="flex items-center gap-2 sm:gap-2.5 text-sm sm:text-base font-semibold text-gray-900 rounded-lg !px-3 sm:!px-4 !py-2.5 sm:!py-3 hover:bg-gray-50 transition-all duration-300">
                    <MessageSquare className="text-indigo-600 w-4 h-4 sm:w-5 sm:h-5 flex-shrink-0" strokeWidth={1.5} />
                    <span>Example Questions</span>
                  </AccordionTrigger>
                  <AccordionContent className="px-4 pb-4 pt-2 text-sm leading-relaxed transition-all duration-300">
                    <ul className="text-left">
                      <li
                        className="cursor-pointer hover:text-[#4F46E5] transition-all duration-300"
                        onClick={() => setQuestion("Compare revenue between August and September.")}
                      >
                        Compare revenue between August and September.
                      </li>
                      <li
                        className="cursor-pointer hover:text-[#4F46E5] transition-all duration-300"
                        onClick={() => setQuestion("What are this month's top-performing departments?")}
                      >
                        What are this month&apos;s top-performing departments?
                      </li>
                      <li
                        className="cursor-pointer hover:text-[#4F46E5] transition-all duration-300"
                        onClick={() => setQuestion("Analyze profit margin fluctuations and identify key drivers.")}
                      >
                        Analyze profit margin fluctuations and identify key
                        drivers.
                      </li>
                      <li
                        className="cursor-pointer hover:text-[#4F46E5] transition-all duration-300"
                        onClick={() => setQuestion("Generate comprehensive cash flow analysis with forecasting.")}
                      >
                        Generate comprehensive cash flow analysis with
                        forecasting.
                      </li>
                      <li
                        className="cursor-pointer hover:text-[#4F46E5] transition-all duration-300"
                        onClick={() => setQuestion("Compare financial performance across business units.")}
                      >
                        Compare financial performance across business units.
                      </li>
                    </ul>
                  </AccordionContent>
                </AccordionItem>

                <AccordionItem
                  value="keyFeatures"
                  className="border-0 border-b border-gray-100 rounded-none data-[state=open]:bg-transparent transition-all duration-500 ease-in-out mb-0"
                >
                  <AccordionTrigger className="flex items-center gap-2 sm:gap-2.5 text-sm sm:text-base font-semibold text-gray-900 rounded-lg !px-3 sm:!px-4 !py-2.5 sm:!py-3 hover:bg-gray-50 transition-all duration-300">
                    <Settings className="text-indigo-600 w-4 h-4 sm:w-5 sm:h-5 flex-shrink-0" strokeWidth={1.5} />
                    <span>Key Features</span>
                  </AccordionTrigger>
                  <AccordionContent className="px-4 pb-4 pt-2 text-sm leading-relaxed">
                    <ul className="text-left">
                      <li>
                        Interactive AI financial summaries
                      </li>
                      <li>Drill-down data exploration</li>
                      <li>
                        Predictive forecasting tools
                      </li>
                      <li>
                        Advanced natural language processing
                      </li>
                      <li>
                        Real-time financial analysis
                      </li>
                    </ul>
                  </AccordionContent>
                </AccordionItem>

                <AccordionItem
                  value="proTips"
                  className="border-0 border-b border-gray-100 rounded-none data-[state=open]:bg-transparent transition-all duration-500 ease-in-out mb-0"
                >
                  <AccordionTrigger className="flex items-center gap-2 sm:gap-2.5 text-sm sm:text-base font-semibold text-gray-900 rounded-lg !px-3 sm:!px-4 !py-2.5 sm:!py-3 hover:bg-gray-50 transition-all duration-300">
                    <Lightbulb className="text-indigo-600 w-4 h-4 sm:w-5 sm:h-5 flex-shrink-0" strokeWidth={1.5} />
                    <span>Pro Tips</span>
                  </AccordionTrigger>
                  <AccordionContent className="px-4 pb-4 pt-2 text-sm leading-relaxed">
                    <ul className="text-left">
                      <li>
                        Be specific with your queries.
                      </li>
                      <li>
                        Ask for comparative or trend-based insights.
                      </li>
                      <li>
                        Request visualizations when analyzing KPIs.
                      </li>
                      <li>
                        Use follow-up questions for deeper understanding.
                      </li>
                      <li>
                        Leverage precise terminology for optimal responses.
                      </li>
                    </ul>
                  </AccordionContent>
                </AccordionItem>
              </Accordion>
            </aside>
          </div>

        </div>
      </div>
      </div>

      {/* Toggle button removed - sidebar is always visible in new layout */}
    </motion.div>
  );
});

export default CFOInsightsPage;
