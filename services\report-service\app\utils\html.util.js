import { escapeHtml } from "./constants/pdf.constants.js";

export const generateTableHTML = (headers, rows, options = {}) => {
  if (!headers || headers.length === 0) {
    return "";
  }

  const {
    emptyMessage = "No data",
    columnClasses = [],
    rowFormatter = null,
    cellFormatter = null,
  } = options;

  const headerCells = headers
    .map((header) => {
      const headerText =
        typeof header === "string" ? header : header.text || "";
      const headerClass =
        typeof header === "object" && header.className
          ? ` class="${escapeHtml(header.className)}"`
          : "";
      return `<th${headerClass}>${escapeHtml(headerText)}</th>`;
    })
    .join("");

  const headerRow = `<thead><tr>${headerCells}</tr></thead>`;

  if (!rows || rows.length === 0) {
    const colspan = headers.length;
    return (
      headerRow +
      `<tbody><tr><td colspan="${colspan}">${escapeHtml(
        emptyMessage
      )}</td></tr></tbody>`
    );
  }

  const bodyRows = rows
    .map((row, rowIndex) => {
      const processedRow = rowFormatter ? rowFormatter(row, rowIndex) : row;

      const rowArray =
        Array.isArray(processedRow) || typeof processedRow !== "object"
          ? processedRow
          : headers.map((header) => {
              const key =
                typeof header === "object" && header.key
                  ? header.key
                  : typeof header === "string"
                  ? header
                  : header.text;
              return processedRow[key] !== undefined ? processedRow[key] : "";
            });

      const cells = rowArray
        .map((cell, colIndex) => {
          const processedCell = cellFormatter
            ? cellFormatter(cell, rowIndex, colIndex)
            : cell;

          const columnClass =
            columnClasses[colIndex] || headers[colIndex]?.cellClassName || "";

          const rowClass =
            typeof processedRow === "object" &&
            !Array.isArray(processedRow) &&
            processedRow.className
              ? processedRow.className
              : "";

          const cellClass = [columnClass, rowClass]
            .filter(Boolean)
            .join(" ")
            .trim();

          const classAttr = cellClass
            ? ` class="${escapeHtml(cellClass)}"`
            : "";

          const cellValue =
            processedCell !== null && processedCell !== undefined
              ? String(processedCell)
              : "";

          return `<td${classAttr}>${escapeHtml(cellValue)}</td>`;
        })
        .join("");

      const rowClassAttr =
        options.rowClasses && options.rowClasses[rowIndex]
          ? ` class="${escapeHtml(options.rowClasses[rowIndex])}"`
          : "";

      return `<tr${rowClassAttr}>${cells}</tr>`;
    })
    .join("");

  return headerRow + `<tbody>${bodyRows}</tbody>`;
};

export const generateChartTableHTML = (
  labels,
  data,
  headers = ["Category", "Amount"]
) => {
  const tableHeaders = [
    { text: headers[0] || "Category" },
    { text: headers[1] || "Amount", className: "amount" },
  ];

  if (!labels || !data || labels.length === 0) {
    return generateTableHTML(tableHeaders, [], {
      columnClasses: ["", "amount"],
    });
  }

  const rows = labels.map((label, index) => {
    const value = data[index] !== undefined ? data[index].toFixed(1) : "0.0";
    return [label, `$${value}K`];
  });

  return generateTableHTML(tableHeaders, rows, {
    columnClasses: ["", "amount"],
  });
};

export const generateTaxTableHTML = (taxData) => {
  const labels = ["SIT", "FTT", "SOCSEC", "MEDICARE"];
  const tableHeaders = [
    { text: "Tax Type" },
    { text: "Amount", className: "amount" },
  ];

  const rows = labels.map((label, index) => {
    const value =
      taxData[index] !== undefined ? taxData[index].toFixed(1) : "0.0";
    return [label, `$${value}K`];
  });

  return generateTableHTML(tableHeaders, rows, {
    columnClasses: ["", "amount"],
  });
};

export const generateSalaryCompTableHTML = (totalPayroll, doctorSalary) => {
  const tableHeaders = [
    { text: "Type" },
    { text: "Amount", className: "amount" },
  ];

  const rows = [
    ["Total Payroll", totalPayroll],
    ["Doctor Salary", doctorSalary],
  ];

  return generateTableHTML(tableHeaders, rows, {
    columnClasses: ["", "amount"],
  });
};

export const generateHeadcountTableHTML = (labels, data) => {
  const tableHeaders = [
    { text: "Role" },
    { text: "Count", className: "amount" },
  ];

  if (!labels || !data || labels.length === 0) {
    return generateTableHTML(tableHeaders, [], {
      columnClasses: ["", "amount"],
    });
  }

  const rows = labels.map((label, index) => {
    const value = data[index] !== undefined ? data[index] : 0;
    return [label, value];
  });

  return generateTableHTML(tableHeaders, rows, {
    columnClasses: ["", "amount"],
  });
};

export const generateOperationsTableHTML = (
  labels,
  data,
  headers,
  valueFormatter = (value) => "$" + (value / 1000).toFixed(1) + "K"
) => {
  const tableHeaders = [
    { text: headers[0] || "Item" },
    { text: headers[1] || "Amount", className: "amount" },
  ];

  if (!labels || !data || labels.length === 0) {
    return generateTableHTML(tableHeaders, [], {
      columnClasses: ["", "amount"],
    });
  }

  const rows = labels.map((label, index) => {
    const value = data[index] !== undefined ? data[index] : 0;
    const formattedValue = valueFormatter(value);
    return [label, formattedValue];
  });

  return generateTableHTML(tableHeaders, rows, {
    columnClasses: ["", "amount"],
  });
};

export const generateTableWithBody = (headers, bodyContent) => {
  const tableHeaders = Array.isArray(headers)
    ? headers.map((header) =>
        typeof header === "string" ? { text: header } : header
      )
    : [];

  const headerCells = tableHeaders
    .map((header) => {
      const headerText =
        typeof header === "string" ? header : header.text || "";
      const headerClass =
        typeof header === "object" && header.className
          ? ` class="${escapeHtml(header.className)}"`
          : "";
      return `<th${headerClass}>${escapeHtml(headerText)}</th>`;
    })
    .join("");

  return `<table><thead><tr>${headerCells}</tr></thead><tbody>${bodyContent}</tbody></table>`;
};

const renderSubsection = (subsection, indentLevel, formatCurrency) => {
  const getIndentStyle = (level) => level > 0 ? ` style="padding-left: ${level * 12}px;"` : "";
  const indentClass = indentLevel === 0 ? "subcategory" : "item-indent";
  
  let rows = `<tr><td class="${indentClass}"${getIndentStyle(indentLevel)}>${escapeHtml(subsection.name || "N/A")}</td><td class="amount">${escapeHtml(subsection.total_formatted || formatCurrency(subsection.total || 0))}</td></tr>`;

  subsection.nestedSubsections?.forEach((nested) => {
    rows += renderSubsection(nested, indentLevel + 1, formatCurrency);
  });

  subsection.accounts?.forEach((account) => {
    const negativeClass = (account.amount || 0) < 0 ? " negative" : "";
    rows += `<tr><td class="item-indent"${getIndentStyle(indentLevel + 1)}>${escapeHtml(account.name || "N/A")}</td><td class="amount${negativeClass}">${escapeHtml(account.amount_formatted || formatCurrency(account.amount || 0))}</td></tr>`;
  });

  if (subsection.summary) {
    rows += `<tr><td class="subcategory"${getIndentStyle(indentLevel + 1)}><strong>${escapeHtml(subsection.summary.name || "Total")}</strong></td><td class="amount"><strong>${escapeHtml(subsection.summary.amount_formatted || formatCurrency(subsection.summary.amount || 0))}</strong></td></tr>`;
  }

  return rows;
};

export const generateBalanceSheetHTML = (balanceSheet, formatCurrency) => {
  if (!balanceSheet || balanceSheet.length === 0) {
    return '<tr><td colspan="2">No data</td></tr>';
  }

  return balanceSheet
    .map((section) => {
      const sectionName = escapeHtml(section.section || "N/A");
      let rows = `<tr class="category-header"><td colspan="2">${sectionName}</td></tr>`;

      (section.subsections || []).forEach((subsection) => {
        rows += renderSubsection(subsection, 0, formatCurrency);
      });

      if (section.summary) {
        const summaryName = escapeHtml(section.summary.name || "Total");
        const summaryAmount = escapeHtml(
          section.summary.amount_formatted || formatCurrency(section.summary.amount || 0)
        );
        rows += `<tr class="category-header"><td><strong>${summaryName}</strong></td><td class="amount"><strong>${summaryAmount}</strong></td></tr>`;
      }

      return rows;
    })
    .join("");
};

export const generateIncomeExpenseHTML = (incomeExpense, formatCurrency) => {
  if (!incomeExpense || incomeExpense.length === 0) {
    return '<tr><td colspan="2">No data</td></tr>';
  }

  return incomeExpense
    .map((category) => {
      const categoryName = escapeHtml(category.category || "N/A");
      const categoryAmount = escapeHtml(formatCurrency(category.amount || 0));
      let rows = `<tr class="category-header"><td>${categoryName}</td><td class="amount">${categoryAmount}</td></tr>`;

      (category.items || []).forEach((item) => {
        const itemName = escapeHtml(item.account_name || item.name || "N/A");
        const itemAmount = escapeHtml(formatCurrency(item.amount || 0));
        const negativeClass = (item.amount || 0) < 0 ? " negative" : "";
        rows += `<tr><td class="item-indent">${itemName}</td><td class="amount${negativeClass}">${itemAmount}</td></tr>`;
      });

      return rows;
    })
    .join("");
};

export default {
  generateTableHTML,
  generateTableWithBody,
  generateChartTableHTML,
  generateTaxTableHTML,
  generateSalaryCompTableHTML,
  generateHeadcountTableHTML,
  generateOperationsTableHTML,
  generateBalanceSheetHTML,
  generateIncomeExpenseHTML,
};