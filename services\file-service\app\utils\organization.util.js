import logger from "../../config/logger.config.js";
import {
  DOCUMENT_MESSAGES,
  DOCUMENT_LOG_MESSAGES,
} from "./constants/document.constants.js";
import { ORGANIZATION_LOG_MESSAGES } from "./constants/logMessages.constants.js";
import { getAuthServiceBaseUrl, getSystemApiKey } from "./env.util.js";
import { httpGet } from "../../../../shared/utils/axios.util.js";

export const ensureOrganizationExists = async (organizationId) => {
  try {
    const baseUrl = getAuthServiceBaseUrl();
    const url = `${baseUrl}/organization/${organizationId}`;

    const headers = {};
    try {
      const apiKey = getSystemApiKey();
      headers["x-api-key"] = apiKey;
    } catch (error) {
      logger.warn(ORGANIZATION_LOG_MESSAGES.SYSTEM_API_KEY_MISSING);
    }

    // Log the curl-equivalent request for debugging
    const curlCommand = `curl -X GET "${url}" ${Object.entries(headers).map(([k, v]) => `-H "${k}: ${v}"`).join(" ")}`;
    logger.info(`[ORGANIZATION_CHECK] Checking organization existence: ${curlCommand}`, {
      organizationId,
      url,
      headers: Object.keys(headers),
    });

    const response = await httpGet(url, {
      headers,
      timeout: 5000,
    });

    logger.info(`[ORGANIZATION_CHECK] Organization found: ${organizationId}`, {
      status: response?.status,
      responseData: response?.data,
    });

    return { success: true };
  } catch (error) {
    const status = error.response?.status;
    const responseData = error.response?.data;
    const responseMessage =
      responseData?.message ||
      responseData?.error ||
      error.message ||
      DOCUMENT_MESSAGES.ORGANIZATION_VALIDATION_FAILED;

    // Log detailed error information
    logger.error(
      `[ORGANIZATION_CHECK] Organization validation failed: ${responseMessage}`,
      {
        organizationId,
        status,
        responseData,
        errorMessage: error.message,
        url: error.config?.url,
        method: error.config?.method,
        headers: error.config?.headers,
        stack: error.stack,
      }
    );

    if (status === 404) {
      return {
        success: false,
        code: "NOT_FOUND",
        message: DOCUMENT_MESSAGES.ORGANIZATION_NOT_FOUND,
      };
    }

    return {
      success: false,
      code: status && status >= 400 && status < 500 ? "CLIENT_ERROR" : "FAILED",
      message: responseMessage,
      status,
    };
  }
};

