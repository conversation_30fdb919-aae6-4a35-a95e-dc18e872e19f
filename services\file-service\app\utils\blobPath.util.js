import path from "path";
import { ONBOARDING_DEFAULT_SEGMENTS } from "./constants/onboarding.constants.js";
import { INVALID_SEGMENT_REGEX } from "./constants/blobStorage.constants.js";
import { getFullMonthName, normalizeServiceName } from "./document.utils.js";
import { REPORT_FOLDER_NAMES } from "./constants/report.constants.js";

export const blobPathState = {
  isConfigured: false,
  containerEnsured: false,
  containerClient: null,
};

export const sanitizeBlobSegment = (segment, fallback) => {
  if (segment === undefined || segment === null) {
    return fallback || "general";
  }

  const value = String(segment);
  const normalized = value.trim();
  const slug = normalized
    ? normalized
        .replaceAll(/\s+/g, "-")
        .replaceAll(INVALID_SEGMENT_REGEX, "-")
        .replaceAll(/-+/g, "-")
        .replaceAll(/^-|-$/g, "")
        .toLowerCase()
    : "";

  if (slug) {
    return slug;
  }

  if (fallback) {
    return fallback;
  }

  return "general";
};

export const buildOrganizationLogoBlobPath = (orgId, fileName) => {
  const orgSegment = sanitizeBlobSegment(
    orgId,
    ONBOARDING_DEFAULT_SEGMENTS.ORG_ID
  );
  const extension = (
    path.extname(fileName || "").toLowerCase() || ".png"
  ).replace(/^\./, "");
  const timestamp = Date.now();

  return `${orgSegment}/org-logos/logo_${timestamp}.${extension}`;
};

export const buildOrganizationPdfBlobPath = (orgId, fileName) => {
  const orgSegment = sanitizeBlobSegment(
    orgId,
    ONBOARDING_DEFAULT_SEGMENTS.ORG_ID
  );
  const safeFileName = fileName || `pdf_${Date.now()}.pdf`;

  // Keep the original file name in storage; only the folder path is prefixed.
  return `${orgSegment}/org-pdfs/${safeFileName}`;
};

/**
 * Build blob path for Power BI-Reports structure
 * Format: {orgId}/{orgName}/Power BI-Reports/{service}/{year}/{month}/{fileName}
 * @param {string} orgId - Organization ID
 * @param {string} orgName - Organization name
 * @param {string} service - Service name (e.g., "Finance", "Operations", "Payroll")
 * @param {number} year - Year (e.g., 2025)
 * @param {number} month - Month number (1-12)
 * @param {string} fileName - File name
 * @returns {string} Blob storage path
 */
export const buildPowerBiReportsBlobPath = (
  orgId,
  orgName,
  service,
  year,
  month,
  fileName
) => {
  // Validate required parameters
  if (!orgId || !orgName || !service || !year || !month || !fileName) {
    throw new Error("All parameters are required for Power BI-Reports path");
  }

  // Sanitize orgId - keep it as UUID, no transformation
  const sanitizedOrgId = String(orgId).trim();
  
  // Sanitize orgName - preserve spaces, apostrophes, and common characters
  // Only remove truly invalid path characters (like /, \, :, etc.)
  const sanitizedOrgName = String(orgName)
    .trim()
    .replace(/[/\\:*?"<>|]/g, ""); // Remove invalid path characters
  
  // Normalize and format service name (Finance, Operations, Payroll)
  const normalizedService = normalizeServiceName(service);
  const serviceMap = {
    financial: REPORT_FOLDER_NAMES.FINANCE,
    operational: REPORT_FOLDER_NAMES.OPERATIONS,
    payroll: REPORT_FOLDER_NAMES.PAYROLL,
  };
  const sanitizedService = serviceMap[normalizedService] || 
    String(service).trim().charAt(0).toUpperCase() + String(service).trim().slice(1).toLowerCase();
  
  // Validate year format (should be 4 digits)
  const yearStr = String(year).trim();
  if (!/^\d{4}$/.test(yearStr)) {
    throw new Error(`Invalid year format: ${year}. Expected 4-digit year.`);
  }
  
  // Convert month number to full month name (e.g., 4 -> "April")
  const monthName = getFullMonthName(Number(month));
  if (!monthName) {
    throw new Error(`Invalid month: ${month}. Expected number between 1-12.`);
  }
  
  // Ensure fileName has .pdf extension
  const safeFileName = fileName || `pdf_${Date.now()}.pdf`;
  
  // Build path: orgId/orgName/Power BI-Reports/service/year/month/filename
  return `${sanitizedOrgId}/${sanitizedOrgName}/Power BI-Reports/${sanitizedService}/${yearStr}/${monthName}/${safeFileName}`;
};

/**
 * Build blob path for Reports structure (JSON files)
 * Format: {orgId}/{orgName}/Reports/{service}/{year}/{month}/{fileName}
 * @param {string} orgId - Organization ID
 * @param {string} orgName - Organization name
 * @param {string} service - Service name (e.g., "Finance", "Operations", "Payroll")
 * @param {number} year - Year (e.g., 2025)
 * @param {number} month - Month number (1-12)
 * @param {string} fileName - File name
 * @returns {string} Blob storage path
 */
export const buildReportsBlobPath = (
  orgId,
  orgName,
  service,
  year,
  month,
  fileName
) => {
  // Validate required parameters
  if (!orgId || !orgName || !service || !year || !month || !fileName) {
    throw new Error("All parameters are required for Reports path");
  }

  // Sanitize orgId - keep it as UUID, no transformation
  const sanitizedOrgId = String(orgId).trim();
  
  // Sanitize orgName - preserve spaces, apostrophes, and common characters
  // Only remove truly invalid path characters (like /, \, :, etc.)
  const sanitizedOrgName = String(orgName)
    .trim()
    .replace(/[/\\:*?"<>|]/g, ""); // Remove invalid path characters
  
  // Normalize and format service name (Finance, Operations, Payroll)
  const normalizedService = normalizeServiceName(service);
  const serviceMap = {
    financial: REPORT_FOLDER_NAMES.FINANCE,
    operational: REPORT_FOLDER_NAMES.OPERATIONS,
    payroll: REPORT_FOLDER_NAMES.PAYROLL,
  };
  const sanitizedService = serviceMap[normalizedService] || 
    String(service).trim().charAt(0).toUpperCase() + String(service).trim().slice(1).toLowerCase();
  
  // Validate year format (should be 4 digits)
  const yearStr = String(year).trim();
  if (!/^\d{4}$/.test(yearStr)) {
    throw new Error(`Invalid year format: ${year}. Expected 4-digit year.`);
  }
  
  // Convert month number to full month name (e.g., 4 -> "April")
  const monthName = getFullMonthName(Number(month));
  if (!monthName) {
    throw new Error(`Invalid month: ${month}. Expected number between 1-12.`);
  }
  
  // Ensure fileName has .json extension
  const safeFileName = fileName || `file_${Date.now()}.json`;
  
  // Build path: orgId/orgName/Reports/service/year/month/filename
  return `${sanitizedOrgId}/${sanitizedOrgName}/Reports/${sanitizedService}/${yearStr}/${monthName}/${safeFileName}`;
};