export const permissionsData = [
  {
    id: 1,
    name: "user.create",
    description: "Create new users",
    module: "User Management",
    type: "Create",
    roles: ["Super Admin", "Admin"],
    status: "Active",
    createdAt: "2024-01-15",
    resource: "users",
  },
  {
    id: 2,
    name: "user.read",
    description: "View user information",
    module: "User Management",
    type: "Read",
    roles: ["Super Admin", "Admin", "Manager"],
    status: "Active",
    createdAt: "2024-01-15",
    resource: "users",
  },
  {
    id: 3,
    name: "user.update",
    description: "Update user information",
    module: "User Management",
    type: "Update",
    roles: ["Super Admin", "Admin"],
    status: "Active",
    createdAt: "2024-01-15",
    resource: "users",
  },
  {
    id: 4,
    name: "user.delete",
    description: "Delete users",
    module: "User Management",
    type: "Delete",
    roles: ["Super Admin"],
    status: "Active",
    createdAt: "2024-01-15",
    resource: "users",
  },
  {
    id: 5,
    name: "tenant.manage",
    description: "Manage tenant settings",
    module: "Tenant Management",
    type: "Manage",
    roles: ["Super Admin"],
    status: "Active",
    createdAt: "2024-01-16",
    resource: "tenants",
  },
  {
    id: 6,
    name: "role.create",
    description: "Create new roles",
    module: "Role Management",
    type: "Create",
    roles: ["Super Admin"],
    status: "Active",
    createdAt: "2024-01-17",
    resource: "roles",
  },
  {
    id: 7,
    name: "role.read",
    description: "View role information",
    module: "Role Management",
    type: "Read",
    roles: ["Super Admin", "Admin"],
    status: "Active",
    createdAt: "2024-01-17",
    resource: "roles",
  },
  {
    id: 8,
    name: "org.manage",
    description: "Manage organization structure",
    module: "Organization Management",
    type: "Manage",
    roles: ["Super Admin", "Admin"],
    status: "Active",
    createdAt: "2024-01-18",
    resource: "organizations",
  },
];

export const permissionFields = [
  {
    name: "name",
    label: "Permission Name",
    type: "text",
    placeholder: "e.g., user.create",
    validation: { required: "Permission name is required" },
  },
  {
    name: "description",
    label: "Description",
    type: "textarea",
    placeholder: "Enter permission description",
    validation: { required: "Description is required" },
    colSpan: 2,
  },
  {
    name: "module",
    label: "Module",
    type: "select",
    options: [
      { label: "User Management", value: "User Management" },
      { label: "Role Management", value: "Role Management" },
      { label: "Tenant Management", value: "Tenant Management" },
      { label: "Organization Management", value: "Organization Management" },
      { label: "Permission Management", value: "Permission Management" },
    ],
    validation: { required: "Module is required" },
  },
  {
    name: "type",
    label: "Permission Type",
    type: "select",
    options: [
      { label: "Create", value: "Create" },
      { label: "Read", value: "Read" },
      { label: "Update", value: "Update" },
      { label: "Delete", value: "Delete" },
      { label: "Manage", value: "Manage" },
      { label: "Execute", value: "Execute" },
    ],
    validation: { required: "Permission type is required" },
  },
  {
    name: "resource",
    label: "Resource",
    type: "text",
    placeholder: "e.g., users, roles, tenants",
    validation: { required: "Resource is required" },
  },
  {
    name: "status",
    label: "Status",
    type: "select",
    options: [
      { label: "Active", value: "Active" },
      { label: "Inactive", value: "Inactive" },
    ],
    validation: { required: "Status is required" },
  },
];

export const initialValues = {
  name: "",
  description: "",
  module: "User Management",
  type: "Create",
  resource: "",
  status: "Active",
};
