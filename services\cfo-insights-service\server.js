
import dotenv from "dotenv";
dotenv.config();

import express from "express";
import bodyParser from "body-parser";
import cors from "cors";
import logger from "./app/config/logger.config.js";
import { ERROR_MESSAGES } from "./app/utils/constants/error.constants.js";
import { cleanupExpiredSessions } from "./app/utils/memoryStore.js";

import mainRouter from "./app/routes/index.js";

logger.info("📝 Starting CFO Insights Service...");

const app = express();

// ✅ Middleware setup

// ✅ Middleware setup
app.use(cors());
app.use(bodyParser.json({ limit: "10mb" }));

// ✅ Graceful handling for invalid JSON payloads
app.use((err, _req, res, next) => {
  if (err instanceof SyntaxError && "body" in err) {
    logger.error("Invalid JSON payload", { error: err.message });
    return res.status(400).json({
      success: false,
      message: ERROR_MESSAGES.CHAT.INVALID_JSON_PAYLOAD,
      error: err.message,
    });
  }
  next(err);
});

// ✅ API routes
app.use("/api", mainRouter);

// ✅ Default route
app.get("/", (_req, res) => {
  res.send("CFO Insights Chat Service is running 🚀");
});
const PORT = process.env.CFO_INSIGHTS_SERVICE_PORT || 3007;
app.listen(PORT, () => {
  logger.info(`🚀 CFO Insights Service running on port ${PORT}`);

  // Set up periodic cleanup of expired sessions (every hour)
  setInterval(cleanupExpiredSessions, 60 * 60 * 1000); // 1 hour
});
