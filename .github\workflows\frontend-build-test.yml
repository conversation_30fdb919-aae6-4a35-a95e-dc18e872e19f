name: Frontend Production Build Check

on:
  push:
    branches:
      - "**"
    paths:
      - "frontend/**"
      - ".github/workflows/frontend-build-test.yml"

jobs:
  build-test:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js 20.x
        uses: actions/setup-node@v4
        with:
          node-version: "20.x"

      - name: Install dependencies
        working-directory: frontend
        run: npm install

      - name: Run linter
        working-directory: frontend
        run: npm run lint

      - name: Build production
        working-directory: frontend
        run: npm run build

      - name: Verify build output
        working-directory: frontend
        run: |
          if [ -d ".next" ]; then
            echo "✓ Build successful - .next directory created"
            ls -la .next
          else
            echo "✗ Build failed - .next directory not found"
            exit 1
          fi
