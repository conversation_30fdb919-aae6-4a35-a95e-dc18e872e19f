import SalaryByDepartmentRepository from "../repository/salary_by_department.repository.js";
import { LOGGER_NAMES } from "../utils/constants/log.constants.js";
import { createLogger } from "../utils/logger.utils.js";
import { isValidMonth, isValidYear, parseNumericValue, formatToKWithDecimals } from "../utils/format.utils.js";
import { getMonthDateRange } from "../utils/repository.utils.js";

const logger = createLogger(LOGGER_NAMES.REPORT_SERVICE);

/**
 * Get salary data grouped by department for organization
 * @param {Object} params - Query parameters
 * @param {string} params.organization_id - Organization ID
 * @param {number} params.month - Month (1-12)
 * @param {number} params.year - Year
 * @returns {Promise<Object>} Salary data by department
 */
const getSalaryByDepartmentData = async ({ organization_id, month, year }) => {
  try {
    logger.info(
      `Fetching salary by department for org: ${organization_id}, month: ${month}, year: ${year}`
    );

    // Validate parameters
    if (!organization_id) {
      throw new Error("Organization ID is required");
    }

    if (!month || !isValidMonth(month)) {
      throw new Error("Valid month (1-12) is required");
    }

    if (!year || !isValidYear(year)) {
      throw new Error("Valid year is required");
    }

    // Convert to numbers
    const monthNum = parseInt(month, 10);
    const yearNum = parseInt(year, 10);

    // Get organization schema name
    const schemaName = await SalaryByDepartmentRepository.getOrganizationSchemaName(
      organization_id
    );

    if (!schemaName) {
      logger.warn(`Schema not found for organization: ${organization_id}`);
      throw new Error("Organization not found or schema not configured");
    }

    // Calculate date range from month and year
    const { startDate, endDate } = getMonthDateRange(monthNum, yearNum);
    
    logger.info(
      `Calculated date range for month ${monthNum}, year ${yearNum}: ${startDate} to ${endDate}`
    );

    // Fetch salary by department
    const departmentData = await SalaryByDepartmentRepository.getSalaryByDepartment(
      schemaName,
      startDate,
      endDate
    );

    // Format the response
    const formattedData = departmentData.map((item) => {
      const totalSalary = parseNumericValue(item.total_salary);
      const headcount = parseInt(item.headcount, 10) || 0;
      const averageSalary = headcount > 0 ? totalSalary / headcount : 0;
      
      return {
        department: item.department || "Unknown",
        total_salary: formatToKWithDecimals(totalSalary),
        headcount: headcount,
        average_salary: formatToKWithDecimals(averageSalary),
      };
    });

    logger.info(
      `Salary by department data formatted successfully - Total departments: ${formattedData.length}`
    );

    // Return formatted response
    return {
      departments: formattedData,
    };
  } catch (error) {
    logger.error("Error in SalaryByDepartmentService.getSalaryByDepartmentData:", error);
    throw error;
  }
};

export default {
  getSalaryByDepartmentData,
};
