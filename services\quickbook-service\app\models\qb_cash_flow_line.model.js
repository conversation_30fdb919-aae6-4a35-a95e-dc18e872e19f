import { DataTypes } from "sequelize";

const QbCashFlowLineModel = (sequelize) => {
  const CashFlowLine = sequelize.define(
    "CashFlowLine",
    {
      id: {
        type: DataTypes.BIGINT,
        primaryKey: true,
        autoIncrement: true,
      },
      report_id: {
        type: DataTypes.BIGINT,
        allowNull: false,
        references: {
          model: "qb_cash_flow_reports",
          key: "id",
        },
        onUpdate: "CASCADE",
        onDelete: "CASCADE",
      },
      path: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      label: {
        type: DataTypes.TEXT,
        allowNull: false,
      },
      group: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      amount: {
        type: DataTypes.DECIMAL(18, 2),
        allowNull: true,
        defaultValue: 0,
      },
      realm_id: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      created_at: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
      },
      updated_at: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
      },
    },
    {
      tableName: "qb_cash_flow_lines",
      timestamps: true,
      createdAt: "created_at",
      updatedAt: "updated_at",
    }
  );
  return CashFlowLine;
};

export default QbCashFlowLineModel;

