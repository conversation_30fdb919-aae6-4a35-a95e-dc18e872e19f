import React from "react";
import { Timer } from "lucide-react";

export default function TableHeader({
  title,
  count,
  lastUpdated,
  showCount = true,
  showLastUpdated = true,
  className = "",
  children,
}) {
  return (
    <div className={`px-4 py-3 border-b border-gray-100 ${className}`}>
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          {/* Count indicator - Elegant layout */}
          {showCount && (
            <div className="flex items-center space-x-2">
              <span className="text-gray-900 font-semibold text-lg">
                <span className="font-bold">{count}</span>{" "}
                <span className="capitalize">
                  {count === 1 ? title.replace(/s$/, "") : title}
                </span>
              </span>
              {showLastUpdated && lastUpdated && (
                <>
                  <span className="text-gray-400">•</span>
                  <span className="text-sm text-gray-500 italic">
                    Updated {lastUpdated}
                  </span>
                </>
              )}
            </div>
          )}

          {/* Fallback: Last updated without count */}
          {!showCount && showLastUpdated && lastUpdated && (
            <div className="flex items-center space-x-2 text-sm text-gray-500">
              <Timer className="w-4 h-4" />
              <span>Updated {lastUpdated}</span>
            </div>
          )}
        </div>

        {/* Custom content */}
        {children && (
          <div className="flex items-center space-x-2">{children}</div>
        )}
      </div>
    </div>
  );
}
