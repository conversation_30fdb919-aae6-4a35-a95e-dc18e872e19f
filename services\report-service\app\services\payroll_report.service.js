import PayrollKpiService from "./payroll_kpi.service.js";
import TaxBreakdownService from "./tax_breakdown.service.js";
import DeductionsBreakdownService from "./deductions_breakdown.service.js";
import SalaryByDepartmentService from "./salary_by_department.service.js";
import { generatePayrollHTML, generatePDF } from "../utils/pdf.utils.js";
import { createLogger } from "../utils/logger.utils.js";
import { LOGGER_NAMES } from "../utils/constants/log.constants.js";

const logger = createLogger(LOGGER_NAMES.REPORT_SERVICE);

const generatePayrollReportPDF = async ({
  organization_id,
  organization_name,
  month,
  year,
}) => {
  try {
    logger.info(
      `Fetching payroll data: org=${organization_id}, month=${month}, year=${year}`
    );

    const [kpi, taxBreak, deductBreak, salaryDept] = await Promise.allSettled([
      PayrollKpiService.getPayrollKpiData({ organization_id, month, year }),
      TaxBreakdownService.getTaxBreakdownData({ organization_id, month, year }),
      DeductionsBreakdownService.getDeductionsBreakdownData({
        organization_id,
        month,
        year,
      }),
      SalaryByDepartmentService.getSalaryByDepartmentData({
        organization_id,
        month,
        year,
      }),
    ]);

    const getData = (res, path) =>
      res.status === "fulfilled" ? res.value?.[path] : null;

    const reportData = {
      organization: organization_name || "Organization",
      month,
      year,
      kpi: getData(kpi, "kpi"),
      taxBreakdown: getData(taxBreak, "tax_breakdown"),
      deductionsBreakdown: getData(deductBreak, "deductions_breakdown") || [],
      salaryByDepartment: getData(salaryDept, "departments") || [],
    };

    logger.info("Generating PDF from aggregated payroll data");
    return await generatePDF(generatePayrollHTML(reportData));
  } catch (error) {
    logger.error("Error generating payroll PDF:", error);
    throw error;
  }
};

export default { generatePayrollReportPDF };
