import React from "react";
import { DropdownMenu } from "../ui/dropdown-menu";

export default function FilterDropdown({
  value,
  onChange,
  options = [],
  placeholder = "Select...",
  className = "",
  disabled = false,
  label,
}) {
  return (
    <div className={`flex items-center space-x-2 ${className}`}>
      {label && (
        <span className="text-sm font-medium text-gray-700 whitespace-nowrap">
          {label}:
        </span>
      )}
      <DropdownMenu
        selected={value}
        onSelect={onChange}
        options={options}
        disabled={disabled}
        placeholder={placeholder}
        buttonClassName="px-4 py-2.5 rounded-lg border border-slate-200 shadow-[0_1px_3px_rgba(0,0,0,0.08)] bg-white text-[#374151] focus:outline-none focus:ring-2 focus:ring-[#6C63FF]/20 flex items-center justify-between min-w-[140px] transition-all duration-150 hover:shadow-[0_2px_6px_rgba(0,0,0,0.12)] disabled:opacity-60 disabled:cursor-not-allowed"
        menuClassName="min-w-[140px]"
      />
    </div>
  );
}
