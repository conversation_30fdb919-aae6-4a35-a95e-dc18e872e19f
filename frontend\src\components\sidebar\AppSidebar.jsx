"use client";

import { useRouter } from "next/navigation";
import { NAVIGATION_CONSTANTS } from "@/utils/constants/navigation";
import { useAuthContext } from "@/redux/Providers/AuthProvider";
import SidebarLogo from "./SidebarLogo";
import SidebarNavigation from "./SidebarNavigation";
import SidebarMastersMenu from "./SidebarMastersMenu";
import SidebarFooter from "./SidebarFooter";
import "./sidebar.css";

const AppSidebar = () => {
  const router = useRouter();
  const { isAdmin } = useAuthContext();

  const handleNavigation = (path) => {
    router.push(path);
  };

  return (
    <div
      className="sidebar-container sidebar-container--app"
      style={{ backgroundColor: NAVIGATION_CONSTANTS.SIDEBAR.COLORS.PRIMARY }}
    >
      {/* Logo Section */}
      <SidebarLogo />

      {/* Navigation Menu */}
      <div className="sidebar-content">
        <nav className="sidebar-nav">
          {/* Dashboard */}
          <SidebarNavigation onNavigate={handleNavigation} />

          {/* Masters menu with submenu */}
          <SidebarMastersMenu isAdmin={isAdmin} onNavigate={handleNavigation} />
        </nav>
      </div>

      {/* Footer with Profile and Logout */}
      <SidebarFooter />
    </div>
  );
};

export default AppSidebar;
