"use client";

import { User } from "lucide-react";
import { useRouter } from "next/navigation";
import { useAuthContext } from "@/redux/Providers/AuthProvider";
import PropTypes from "prop-types";

/**
 * ProfileButton - A reusable component for displaying user profile information
 * @param {Object} props
 * @param {Function} props.onClick - Click handler (defaults to navigating to /profile)
 * @param {Object} props.user - Optional user data (if not provided, uses AuthContext)
 * @param {string} props.className - Optional additional CSS classes
 * @param {string} props.variant - Optional variant for styling (default: 'sidebar')
 */
export default function ProfileButton({
  onClick,
  user: userProp,
  className = "",
  variant = "sidebar",
}) {
  const router = useRouter();
  const { user: authUser } = useAuthContext();
  const user = userProp || authUser;

  // Default click handler navigates to profile
  const handleClick =
    onClick ||
    (() => {
      router.push("/profile");
    });

  // Variant-specific styles
  const variantStyles = {
    sidebar: "bg-white/10 hover:bg-white/20 text-white",
    default: "bg-gray-100 hover:bg-gray-200 text-gray-900",
    card: "bg-white hover:bg-gray-50 text-gray-900 shadow-sm",
  };

  const baseStyles =
    "rounded-lg p-3 w-full text-left transition-all duration-300 ease-out focus:outline-none focus:ring-2 focus:ring-white/40 hover:scale-105 hover:shadow-md active:scale-95";

  const iconStyles = {
    sidebar: "bg-white/20 text-white",
    default: "bg-gray-200 text-gray-600",
    card: "bg-gray-100 text-gray-600",
  };

  const textStyles = {
    sidebar: {
      name: "text-white text-xs font-semibold truncate",
      email: "text-white/70 text-xs truncate",
      role: "text-white/60 text-xs",
    },
    default: {
      name: "text-gray-900 text-xs font-semibold truncate",
      email: "text-gray-600 text-xs truncate",
      role: "text-gray-500 text-xs",
    },
    card: {
      name: "text-gray-900 text-xs font-semibold truncate",
      email: "text-gray-600 text-xs truncate",
      role: "text-gray-500 text-xs",
    },
  };

  return (
    <button
      onClick={handleClick}
      className={`${baseStyles} ${variantStyles[variant]} ${className}`}
      type="button"
    >
      <div className="flex items-center space-x-2">
        <div
          className={`w-8 h-8 ${iconStyles[variant]} rounded-full flex items-center justify-center`}
        >
          <User className="w-4 h-4" />
        </div>
        <div className="flex-1 min-w-0">
          <div className={textStyles[variant].name}>
            {user?.full_name || "User"}
          </div>
          <div className={textStyles[variant].email}>
            {user?.email || "<EMAIL>"}
          </div>
          <div className={textStyles[variant].role}>{user?.role || "Role"}</div>
        </div>
      </div>
    </button>
  );
}

ProfileButton.propTypes = {
  onClick: PropTypes.func,
  user: PropTypes.object,
  className: PropTypes.string,
  variant: PropTypes.oneOf(["sidebar", "default", "card"]),
};
