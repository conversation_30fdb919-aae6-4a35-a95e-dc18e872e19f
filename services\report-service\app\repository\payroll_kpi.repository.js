import { sequelize } from "../models/index.js";
import { LOGGER_NAMES } from "../utils/constants/log.constants.js";
import { createLogger } from "../utils/logger.utils.js";
import { getOrganizationSchemaName, executeSchemaQuery } from "../utils/repository.utils.js";

const logger = createLogger(LOGGER_NAMES.REPORT_REPOSITORY);

/**
 * Get payroll data aggregated for a specific date range
 * @param {string} schemaName - Organization schema name
 * @param {string} startDate - Start date (YYYY-MM-DD)
 * @param {string} endDate - End date (YYYY-MM-DD)
 * @returns {Promise<Object>} Aggregated payroll data
 */
const getPayrollData = async (schemaName, startDate, endDate) => {
  try {
    logger.info(
      `Fetching payroll data from schema: ${schemaName} for date range: ${startDate} to ${endDate}`
    );

    const query = `
      SELECT 
        COALESCE(SUM(total_earnings), 0) as total_payroll,
        COALESCE(SUM(deduction_total), 0) as total_deductions,
        COALESCE(SUM(total_taxes), 0) as total_taxes
      FROM "${schemaName}".adp_payroll_details
      WHERE from_date >= :startDate AND to_date <= :endDate
    `;

    const results = await sequelize.query(query, {
      replacements: { startDate, endDate },
      type: sequelize.QueryTypes.SELECT,
    });

    if (results.length === 0) {
      logger.info(
        `No payroll data found for schema: ${schemaName}, date range: ${startDate} to ${endDate}`
      );
      return {
        total_payroll: 0,
        total_deductions: 0,
        total_taxes: 0,
      };
    }

    logger.info(
      `Retrieved payroll data from schema: ${schemaName} - Total Payroll: ${results[0].total_payroll}`
    );

    return results[0];
  } catch (error) {
    logger.error(`Error in PayrollKpiRepository.getPayrollData:`, error);
    throw error;
  }
};

/**
 * Get doctor salary aggregated for a specific date range
 * @param {string} schemaName - Organization schema name
 * @param {string} startDate - Start date (YYYY-MM-DD)
 * @param {string} endDate - End date (YYYY-MM-DD)
 * @returns {Promise<number>} Total doctor salary
 */
const getDoctorSalary = async (schemaName, startDate, endDate) => {
  try {
    logger.info(
      `Fetching doctor salary from schema: ${schemaName} for date range: ${startDate} to ${endDate}`
    );

    const query = `
      SELECT 
        COALESCE(SUM(pd.total_earnings), 0) as doctor_salary
      FROM "${schemaName}".adp_employee e
      INNER JOIN "${schemaName}".adp_payroll_details pd ON e.id = pd.employee_id
      WHERE e.department = 'Doctor Salary'
        AND pd.from_date >= :startDate 
        AND pd.to_date <= :endDate
    `;

    const results = await sequelize.query(query, {
      replacements: { startDate, endDate },
      type: sequelize.QueryTypes.SELECT,
    });

    if (results.length === 0) {
      logger.info(
        `No doctor salary data found for schema: ${schemaName}, date range: ${startDate} to ${endDate}`
      );
      return 0;
    }

    const doctorSalary = parseFloat(results[0].doctor_salary) || 0;
    
    logger.info(
      `Retrieved doctor salary from schema: ${schemaName} - Amount: ${doctorSalary}`
    );

    return doctorSalary;
  } catch (error) {
    logger.error(`Error in PayrollKpiRepository.getDoctorSalary:`, error);
    throw error;
  }
};

export default {
  getOrganizationSchemaName,
  getPayrollData,
  getDoctorSalary,
};
