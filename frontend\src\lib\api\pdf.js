import { apiService } from "@/lib/axiosConfig";
import { CFO_ENDPOINT } from "@/utils/constants/chat";
import { postJson } from "@shared/utils/fetch.util.js";
import { startChatSessionRequest } from "@shared/utils/chatSession.util.js";

// PDF API endpoints
const PDF_ENDPOINTS = {
  UPLOAD: "/api/pdf/upload",
  DOWNLOAD: "/api/pdf/download",
  DELETE: "/api/pdf/delete",
  LIST: "/api/pdf/list",
};

// CFO Insights Service endpoints
const CFO_INSIGHTS_ENDPOINTS = {
  CHAT_START: `${CFO_ENDPOINT}/api/chat/start`,
  CHAT_MESSAGE: `${CFO_ENDPOINT}/api/chat/message`,
};

/**
 * Upload PDF file
 */
export const uploadPdf = async (file, metadata = {}) => {
  try {
    const formData = new FormData();
    formData.append("file", file);
    formData.append("metadata", JSON.stringify(metadata));

    const response = await apiService.post(PDF_ENDPOINTS.UPLOAD, formData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });

    return {
      success: true,
      data: response.data,
    };
  } catch (error) {
    return {
      success: false,
      message: error.response?.data?.message || "Failed to upload PDF",
    };
  }
};

/**
 * Download PDF file
 */
export const downloadPdf = async (fileId) => {
  try {
    const response = await apiService.get(
      `${PDF_ENDPOINTS.DOWNLOAD}/${fileId}`,
      {
        responseType: "blob",
      }
    );

    return {
      success: true,
      data: response.data,
    };
  } catch (error) {
    return {
      success: false,
      message: error.response?.data?.message || "Failed to download PDF",
    };
  }
};

/**
 * Delete PDF file
 */
export const deletePdf = async (fileId) => {
  try {
    const response = await apiService.delete(
      `${PDF_ENDPOINTS.DELETE}/${fileId}`
    );
    return {
      success: true,
      data: response.data,
    };
  } catch (error) {
    return {
      success: false,
      message: error.response?.data?.message || "Failed to delete PDF",
    };
  }
};

/**
 * List PDF files
 */
export const listPdfs = async (params = {}) => {
  try {
    const response = await apiService.get(PDF_ENDPOINTS.LIST, { params });
    return {
      success: true,
      data: response.data,
    };
  } catch (error) {
    return {
      success: false,
      message: error.response?.data?.message || "Failed to list PDFs",
    };
  }
};

/**
 * Get PDF metadata
 */
export const getPdfMetadata = async (fileId) => {
  try {
    const response = await apiService.get(`${PDF_ENDPOINTS.LIST}/${fileId}`);
    return {
      success: true,
      data: response.data,
    };
  } catch (error) {
    return {
      success: false,
      message: error.response?.data?.message || "Failed to get PDF metadata",
    };
  }
};

/**
 * Map selected month and dashboard type to PDF filename
 */
const getPdfFilename = (
  selectedMonth,
  isFinancialSelected,
  isOperationsSelected,
  isPayrollSelected,
  selectedDashboard
) => {
  // Default filename mapping
  const monthToFilename = {
    May: "CHP Finance Dashboard - May 2025.pdf",
    June: "CHP Finance Dashboard - June 2025.pdf",
    July: "CHP Finance Dashboard - July 2025.pdf",
  };

  // If no specific dashboard is selected, use default CHP Finance
  if (!selectedDashboard || selectedDashboard === "chp") {
    if (isFinancialSelected) {
      return (
        monthToFilename[selectedMonth] ||
        "CHP Finance Dashboard - June 2025.pdf"
      );
    } else if (isOperationsSelected) {
      return `CHP Operations Dashboard - ${selectedMonth} 2025.pdf`;
    } else if (isPayrollSelected) {
      return `CHP Payroll Dashboard - ${selectedMonth} 2025.pdf`;
    }
  } else if (selectedDashboard === "dental") {
    if (isFinancialSelected) {
      return `Dental Finance Dashboard - ${selectedMonth} 2025.pdf`;
    } else if (isOperationsSelected) {
      return `Dental Operations Dashboard - ${selectedMonth} 2025.pdf`;
    } else if (isPayrollSelected) {
      return `Dental Payroll Dashboard - ${selectedMonth} 2025.pdf`;
    }
  }

  // Default fallback
  return (
    monthToFilename[selectedMonth] || "CHP Finance Dashboard - June 2025.pdf"
  );
};

/**
 * Extract year and month from selectedMonth
 */
const parseYearAndMonth = (selectedMonth) => {
  const currentYear = new Date().getFullYear();
  const monthNameMap = {
    January: 1, February: 2, March: 3, April: 4, May: 5, June: 6,
    July: 7, August: 8, September: 9, October: 10, November: 11, December: 12
  };
  
  if (monthNameMap[selectedMonth]) {
    return { year: currentYear, month: monthNameMap[selectedMonth] };
  }
  
  const numericMonth = parseInt(selectedMonth, 10);
  if (!isNaN(numericMonth) && numericMonth >= 1 && numericMonth <= 12) {
    return { year: currentYear, month: numericMonth };
  }
  
  return { year: currentYear, month: new Date().getMonth() + 1 };
};

/**
 * Start a chat session for a specific document
 */
export const startChatSession = async (selectedMonth, options = {}) => {
  try {
    const {
      isFinancialSelected = true,
      isOperationsSelected = false,
      isPayrollSelected = false,
      selectedDashboard = "chp",
      orgId,
      orgName,
      organizationId,
      organizationName,
    } = options;

    const filename = getPdfFilename(
      selectedMonth,
      isFinancialSelected,
      isOperationsSelected,
      isPayrollSelected,
      selectedDashboard
    );

    const { year, month } = parseYearAndMonth(selectedMonth);
    const resolvedOrgId = orgId || organizationId;
    const resolvedOrgName = orgName || organizationName;

    const { data, message } = await startChatSessionRequest({
      url: CFO_INSIGHTS_ENDPOINTS.CHAT_START,
      filename,
      orgId: resolvedOrgId,
      orgName: resolvedOrgName,
      year,
      month,
    });

    const sessionId = data?.sessionId;
    if (!sessionId) {
      throw new Error(message || "Failed to start chat session");
    }

    return {
      success: true,
      data,
      sessionId,
      filename: data?.filename || filename,
      organizationId: data?.organizationId || resolvedOrgId,
      organizationName: data?.organizationName || resolvedOrgName,
      year,
      month,
    };
  } catch (error) {
    return {
      success: false,
      message: error.message || "Failed to start chat session",
      error: error.message,
    };
  }
};

/**
 * Send a message in an existing chat session
 */
export const sendChatMessage = async (
  sessionId,
  message,
  organization,
  options = {}
) => {
  try {
    const response = await postJson(CFO_INSIGHTS_ENDPOINTS.CHAT_MESSAGE, {
      sessionId,
      message,
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();

    return {
      success: true,
      data: data.data,
      answer: data.data?.plainAnswer || "No answer received",
      output: data.data?.plainAnswer || "No answer received",
      filename: data.data?.filename,
    };
  } catch (error) {
    return {
      success: false,
      message: error.message || "Failed to send message",
      error: error.message,
    };
  }
};

/**
 * Ask question about PDF document using CFO Insights Service (Chat API)
 * This function now uses the chat session approach
 */
export const askQuestion = async (question, selectedMonth, options = {}) => {
  try {
    // Start a chat session first
    const chatSession = await startChatSession(selectedMonth, options);

    if (!chatSession.success) {
      return chatSession;
    }

    // Send the question as a message
    const response = await sendChatMessage(chatSession.sessionId, question);

    return response;
  } catch (error) {
    return {
      success: false,
      message: error.message || "Failed to ask question",
      error: error.message,
    };
  }
};

const pdfApi = {
  uploadPdf,
  downloadPdf,
  deletePdf,
  listPdfs,
  getPdfMetadata,
  askQuestion,
  startChatSession,
  sendChatMessage,
};

export default pdfApi;
