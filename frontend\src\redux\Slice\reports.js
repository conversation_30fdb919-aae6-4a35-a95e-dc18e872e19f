import { createSlice } from "@reduxjs/toolkit";
import {
  fetchBalanceSheet,
  fetchIncomeExpenseData,
  fetchKpisData,
  fetchCashFlowData,
  fetchExpenseBreakdownData,
  fetchRevenueExpenseData,
  fetchPayrollKpis,
  fetchPayrollTaxBreakdown,
  fetchPayrollDeductionsBreakdown,
  fetchPayrollSalaryByDepartment,
} from "@/redux/Thunks/reports";
import {
  buildFinanceApiResponsePayload,
  getFinanceDefaults,
} from "@/utils/methods/financeReports";

const initialState = {
  balanceSheetData: [],
  balanceSheetError: null,
  balanceSheetLoading: false,
  incomeExpenseData: [],
  incomeExpenseLoading: false,
  incomeExpenseError: null,
  kpisData: [],
  kpisLoading: false,
  kpisError: null,
  cashFlowData: [],
  cashFlowLoading: false,
  cashFlowError: null,
  expenseBreakdownData: [],
  expenseBreakdownLoading: false,
  expenseBreakdownError: null,
  revenueExpenseData: [],
  revenueExpenseLoading: false,
  revenueExpenseError: null,
  payrollKpisData: null,
  payrollKpisLoading: false,
  payrollKpisError: null,
  payrollTaxBreakdownData: null,
  payrollTaxBreakdownLoading: false,
  payrollTaxBreakdownError: null,
  payrollDeductionsBreakdownData: null,
  payrollDeductionsBreakdownLoading: false,
  payrollDeductionsBreakdownError: null,
  payrollSalaryByDepartmentData: null,
  payrollSalaryByDepartmentLoading: false,
  payrollSalaryByDepartmentError: null,
  lastFetchedParams: null,
};

const balanceSheetSlice = createSlice({
  name: "reports",
  initialState,
  reducers: {
    resetBalanceSheet: () => initialState,
    clearBalanceSheetError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchBalanceSheet.pending, (state, action) => {
        state.balanceSheetLoading = true;
        state.balanceSheetError = null;
        state.lastFetchedParams = action.meta?.arg || null;
      })
      .addCase(fetchBalanceSheet.fulfilled, (state, action) => {
        state.balanceSheetLoading = false;
        state.balanceSheetError = null;
        state.balanceSheetData = action.payload;
        state.lastFetchedParams = action.meta?.arg || null;
      })
      .addCase(fetchBalanceSheet.rejected, (state, action) => {
        state.balanceSheetLoading = false;
        state.balanceSheetError =
          action.payload || "Failed to fetch balance sheet";
        state.lastFetchedParams = action.meta?.arg || null;
      })
      .addCase(fetchIncomeExpenseData.pending, (state, action) => {
        state.incomeExpenseLoading = true;
        state.incomeExpenseError = null;
        state.lastFetchedParams = action.meta?.arg || null;
      })
      .addCase(fetchIncomeExpenseData.fulfilled, (state, action) => {
        state.incomeExpenseLoading = false;
        state.incomeExpenseError = null;
        state.incomeExpenseData = action.payload;
        state.lastFetchedParams = action.meta?.arg || null;
      })
      .addCase(fetchIncomeExpenseData.rejected, (state, action) => {
        state.incomeExpenseLoading = false;
        state.incomeExpenseError =
          action.payload || "Failed to fetch balance sheet";
        state.lastFetchedParams = action.meta?.arg || null;
      })
      .addCase(fetchKpisData.pending, (state, action) => {
        state.kpisLoading = true;
        state.kpisError = null;
        state.lastFetchedParams = action.meta?.arg || null;
      })
      .addCase(fetchKpisData.fulfilled, (state, action) => {
        state.kpisLoading = false;
        state.kpisError = null;
        state.kpisData = action.payload;
        state.lastFetchedParams = action.meta?.arg || null;
      })
      .addCase(fetchKpisData.rejected, (state, action) => {
        state.kpisLoading = false;
        state.kpisError = action.payload || "Failed to fetch balance sheet";
        state.lastFetchedParams = action.meta?.arg || null;
      })
      .addCase(fetchCashFlowData.pending, (state, action) => {
        state.cashFlowLoading = true;
        state.cashFlowError = null;
        state.lastFetchedParams = action.meta?.arg || null;
      })
      .addCase(fetchCashFlowData.fulfilled, (state, action) => {
        state.cashFlowLoading = false;
        state.cashFlowError = null;
        state.cashFlowData = action.payload;
        state.lastFetchedParams = action.meta?.arg || null;
      })
      .addCase(fetchCashFlowData.rejected, (state, action) => {
        state.cashFlowLoading = false;
        state.cashFlowError = action.payload || "Failed to fetch balance sheet";
        state.lastFetchedParams = action.meta?.arg || null;
      })
      .addCase(fetchRevenueExpenseData.pending, (state, action) => {
        state.revenueExpenseLoading = true;
        state.revenueExpenseError = null;
        state.lastFetchedParams = action.meta?.arg || null;
      })
      .addCase(fetchRevenueExpenseData.fulfilled, (state, action) => {
        state.revenueExpenseLoading = false;
        state.revenueExpenseError = null;
        state.revenueExpenseData = action.payload;
        state.lastFetchedParams = action.meta?.arg || null;
      })
      .addCase(fetchRevenueExpenseData.rejected, (state, action) => {
        state.revenueExpenseLoading = false;
        state.revenueExpenseError =
          action.payload || "Failed to fetch balance sheet";
        state.lastFetchedParams = action.meta?.arg || null;
      })
      .addCase(fetchExpenseBreakdownData.pending, (state, action) => {
        state.expenseBreakdownLoading = true;
        state.expenseBreakdownError = null;
        state.lastFetchedParams = action.meta?.arg || null;
      })
      .addCase(fetchExpenseBreakdownData.fulfilled, (state, action) => {
        state.expenseBreakdownLoading = false;
        state.expenseBreakdownError = null;
        state.expenseBreakdownData = action.payload;
        state.lastFetchedParams = action.meta?.arg || null;
      })
      .addCase(fetchExpenseBreakdownData.rejected, (state, action) => {
        state.expenseBreakdownLoading = false;
        state.expenseBreakdownError =
          action.payload || "Failed to fetch balance sheet";
        state.lastFetchedParams = action.meta?.arg || null;
      })
      .addCase(fetchPayrollKpis.pending, (state, action) => {
        state.payrollKpisLoading = true;
        state.payrollKpisError = null;
        state.lastFetchedParams = action.meta?.arg || null;
      })
      .addCase(fetchPayrollKpis.fulfilled, (state, action) => {
        state.payrollKpisLoading = false;
        state.payrollKpisError = null;
        state.payrollKpisData = action.payload;
        state.lastFetchedParams = action.meta?.arg || null;
      })
      .addCase(fetchPayrollKpis.rejected, (state, action) => {
        state.payrollKpisLoading = false;
        state.payrollKpisError = action.payload || "Failed to fetch payroll KPIs";
        state.lastFetchedParams = action.meta?.arg || null;
      })
      .addCase(fetchPayrollTaxBreakdown.pending, (state, action) => {
        state.payrollTaxBreakdownLoading = true;
        state.payrollTaxBreakdownError = null;
        state.lastFetchedParams = action.meta?.arg || null;
      })
      .addCase(fetchPayrollTaxBreakdown.fulfilled, (state, action) => {
        state.payrollTaxBreakdownLoading = false;
        state.payrollTaxBreakdownError = null;
        state.payrollTaxBreakdownData = action.payload;
        state.lastFetchedParams = action.meta?.arg || null;
      })
      .addCase(fetchPayrollTaxBreakdown.rejected, (state, action) => {
        state.payrollTaxBreakdownLoading = false;
        state.payrollTaxBreakdownError =
          action.payload || "Failed to fetch tax breakdown";
        state.lastFetchedParams = action.meta?.arg || null;
      })
      .addCase(fetchPayrollDeductionsBreakdown.pending, (state, action) => {
        state.payrollDeductionsBreakdownLoading = true;
        state.payrollDeductionsBreakdownError = null;
        state.lastFetchedParams = action.meta?.arg || null;
      })
      .addCase(fetchPayrollDeductionsBreakdown.fulfilled, (state, action) => {
        state.payrollDeductionsBreakdownLoading = false;
        state.payrollDeductionsBreakdownError = null;
        state.payrollDeductionsBreakdownData = action.payload;
        state.lastFetchedParams = action.meta?.arg || null;
      })
      .addCase(fetchPayrollDeductionsBreakdown.rejected, (state, action) => {
        state.payrollDeductionsBreakdownLoading = false;
        state.payrollDeductionsBreakdownError =
          action.payload || "Failed to fetch deductions breakdown";
        state.lastFetchedParams = action.meta?.arg || null;
      })
      .addCase(fetchPayrollSalaryByDepartment.pending, (state, action) => {
        state.payrollSalaryByDepartmentLoading = true;
        state.payrollSalaryByDepartmentError = null;
        state.lastFetchedParams = action.meta?.arg || null;
      })
      .addCase(fetchPayrollSalaryByDepartment.fulfilled, (state, action) => {
        state.payrollSalaryByDepartmentLoading = false;
        state.payrollSalaryByDepartmentError = null;
        state.payrollSalaryByDepartmentData = action.payload;
        state.lastFetchedParams = action.meta?.arg || null;
      })
      .addCase(fetchPayrollSalaryByDepartment.rejected, (state, action) => {
        state.payrollSalaryByDepartmentLoading = false;
        state.payrollSalaryByDepartmentError =
          action.payload || "Failed to fetch salary by department";
        state.lastFetchedParams = action.meta?.arg || null;
      });
  },
});

export const { resetBalanceSheet, clearBalanceSheetError } =
  balanceSheetSlice.actions;
export default balanceSheetSlice.reducer;
