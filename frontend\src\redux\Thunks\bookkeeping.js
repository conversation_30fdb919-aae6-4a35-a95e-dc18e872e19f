import { createAsyncThunk } from "@reduxjs/toolkit";
import api from "@/redux/ApiService/ApiService";

import { SERVICE_PORTS } from "@/utils/constants/api";
import { getMonthDateRange } from "@/utils/methods/formatters";

// Sync Financial data
export const syncFinancialData = createAsyncThunk(
  "bookkeeping/syncFinancial",
  async (
    { clientId, email, month, files, realmId, schemaName },
    { rejectWithValue }
  ) => {
    try {
      const { startDate, endDate } = getMonthDateRange(month);
      // Use QUICKBOOK service port (3005)
      const axios = api(SERVICE_PORTS.QUICKBOOK);
      const response = await axios.post("/quickbooks/sync-all-reports", {
        email: email,
        startDate: startDate,
        endDate: endDate,
        realmId: realmId,
        organization_id: clientId,
        schemaName: schemaName,
      });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

// Sync Operational data
export const syncOperationalData = createAsyncThunk(
  "bookkeeping/syncOperational",
  async ({ office_id, month, schema_name, organization_id }, { rejectWithValue }) => {
    try {
      const axios = api(SERVICE_PORTS.SIKKA);
      const { startDate, endDate } = getMonthDateRange(month);
      const response = await axios.post("/sikka/sync-all-reports", {
        office_id: office_id,
        start_date: startDate,
        end_date: endDate,
        schema_name: schema_name,
        organization_id: organization_id,
      });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);
