import axios from "axios";
import fs from "fs";
import path from "path";
import {
  LOG_ACTIONS,
  SIKKA_API,
  VALIDATION_RULES,
  LOGGER_NAMES,
  SIKKA_MESSAGES,
  BUSINESS_CONSTANTS,
  HTTP_HEADERS,
  ERROR_MESSAGES,
  MODEL_FIELDS,
  NUMERIC_CONSTANTS,
  HTTP_HEADER_CONSTANTS,
  METHOD_TYPES,
  VALIDATION_MESSAGES,
  API_URLS,
  LOG_MESSAGES,
  VALIDATION_FIELD_MESSAGES,
  FILE_SYSTEM,
} from "./constants.util.js";

import { createLogger } from "../utils/logger.util.js";
import {
  getRequestKey,
} from "../services/sikka.service.js";

const logger = createLogger(LOGGER_NAMES.SIKKA_SERVICE);


/**
 * Common method to make a Sikka API call with logging and error handling
 * @param {string} method - HTTP method ('get', 'post', etc.)
 * @param {string} endpoint - API endpoint path
 * @param {Object} options - Optional parameters
 * @param {Object} [options.headers] - Additional headers
 * @param {Object} [options.data] - Request payload for POST/PUT
 * @param {Object} [options.log_context] - Additional context for logging
 * @param {string} [options.success_message] - Message for successful operation
 * @param {string} [options.error_message] - Error message if API call fails
 * @param {Function} [options.validate_response] - Custom response validation function
 * @returns {Object} API response data
 */
export async function sikkaApiCall(
  method,
  endpoint,
  {
    headers = null,
    data = null,
    logContext = {},
    successMessage,
    errorMessage,
  } = {}
) {
  const log_context = logContext;
  const success_message = successMessage;
  const error_message = errorMessage;

  logger.info(
    `${LOG_ACTIONS.CALLING_API} ${method.toUpperCase()} ${endpoint}`,
    log_context
  );

  const api_url = formatSikkaApiUrl(endpoint);
  const config = getConfig(method, api_url, headers, data);
  const response = await axiosRequestWithLogging(config, logger);

  if (response.status !== BUSINESS_CONSTANTS.HTTP_SUCCESS_STATUS) {
    throw new Error(
      error_message || `${SIKKA_MESSAGES.API_CALL_FAILED}: ${response.status}`
    );
  }

  logger.info(
    success_message ||
      `${method.toUpperCase()} ${endpoint} ${LOG_ACTIONS.API_SUCCESSFUL}`,
    {
      ...log_context,
      status: response.status,
    }
  );
  return response.data;
}

/**
 * Format response data for request key
 * @param {Object} request_key_data - Response from request key API
 * @returns {Object} Formatted response data
 */
export const formatRequestKeyResponse = (request_key_data) => {
  const { request_key, issued_to, start_time, end_time, expires_in } =
    request_key_data;
  return {
    [MODEL_FIELDS.REQUEST_KEY]: request_key,
    [MODEL_FIELDS.OFFICE_ID]: issued_to,
    [MODEL_FIELDS.START_TIME]: start_time,
    [MODEL_FIELDS.END_TIME]: end_time,
    [MODEL_FIELDS.EXPIRES_IN]: parseInt(
      expires_in,
      NUMERIC_CONSTANTS.RADIX_DECIMAL
    ),
  };
};

/**
 * Generic function to make an axios request with error handling
 * @param {Object} config - Axios request config
 * @param {Object} logger - Logger instance
 * @param {string} [office_id] - Optional office_id for logging
 * @returns {Object} Axios response
 */
export async function axiosRequestWithLogging(config, logger) {
  try {
    return await axios(config);
  } catch (error) {
    logger.error(LOG_ACTIONS.REQUEST_KEY_API_FAILED, {
      error: error.message,
    });
    if (error.response) {
      throw new Error(
        `${SIKKA_MESSAGES.SIKKA_API_ERROR}: ${error.response.status} - ${
          error.response.data?.message || error.response.statusText
        }`
      );
    } else if (error.request) {
      throw new Error(SIKKA_MESSAGES.NETWORK_ERROR_OCCURRED);
    } else {
      throw error;
    }
  }
}

export const getConfig = (method, url, headers = null, data = null) => {
  const defaultHeaders = {
    [HTTP_HEADERS.CONTENT_TYPE]: HTTP_HEADERS.APPLICATION_JSON,
  };
  return {
    method,
    url,
    headers: headers || defaultHeaders,
    data: data || {},
  };
};

/**
 * Validate Sikka credentials
 * @param {string} app_id - App ID to validate
 * @param {string} app_key - App Key to validate
 * @returns {Object} Validation result
 */
export const validateSikkaCredentials = (app_id, app_key) => {
  const errors = [];

  // Validate app_id
  if (!app_id || typeof app_id !== BUSINESS_CONSTANTS.STRING_TYPE) {
    errors.push(ERROR_MESSAGES.APP_ID_REQUIRED_STRING);
  } else if (
    app_id.length < VALIDATION_RULES.APP_ID_MIN_LENGTH ||
    app_id.length > VALIDATION_RULES.APP_ID_MAX_LENGTH
  ) {
    errors.push(
      `${ERROR_MESSAGES.APP_ID_LENGTH_ERROR} ${VALIDATION_RULES.APP_ID_MIN_LENGTH} ${ERROR_MESSAGES.AND_CHARACTERS} ${VALIDATION_RULES.APP_ID_MAX_LENGTH} ${ERROR_MESSAGES.AND_CHARACTERS}`
    );
  }

  // Validate app_key
  if (!app_key || typeof app_key !== BUSINESS_CONSTANTS.STRING_TYPE) {
    errors.push(ERROR_MESSAGES.APP_KEY_REQUIRED_STRING);
  } else if (
    app_key.length < VALIDATION_RULES.APP_KEY_MIN_LENGTH ||
    app_key.length > VALIDATION_RULES.APP_KEY_MAX_LENGTH
  ) {
    errors.push(
      `${ERROR_MESSAGES.APP_KEY_LENGTH_ERROR} ${VALIDATION_RULES.APP_KEY_MIN_LENGTH} ${ERROR_MESSAGES.AND_CHARACTERS} ${VALIDATION_RULES.APP_KEY_MAX_LENGTH} ${ERROR_MESSAGES.AND_CHARACTERS}`
    );
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
};

/**
 * Format Sikka API URL
 * @param {string} endpoint - API endpoint
 * @returns {string} Complete API URL
 */
export const formatSikkaApiUrl = (endpoint) => {
  return `${SIKKA_API.BASE_URL}/${SIKKA_API.VERSION}${endpoint}`;
};

/**
 * Find practice by email from practices data
 * @param {Array} practices_items - Array of practice items
 * @param {string} office_id - Office ID to search for
 * @returns {Object|undefined} Found practice or undefined
 */
export const findPracticeByOfficeId = (practices_items, office_id) => {
  return practices_items.find(
    (item) => item[MODEL_FIELDS.OFFICE_ID] === office_id
  );
};

/**
 * Validate practice credentials
 * @param {Object} practice - Practice object
 * @returns {Object} Validation result with secret_key
 */
export const validateAndGetPracticeCredentials = (practice) => {
  const secret_key = practice?.[MODEL_FIELDS.SECRET_KEY];

  if (!secret_key) {
    throw new Error(SIKKA_MESSAGES.MISSING_PRACTICE_CREDENTIALS);
  }

  return secret_key;
};

/**
 * Create request key payload
 * @param {Object} params - Parameters for payload
 * @param {string} params.office_id - Office ID
 * @param {string} params.secret_key - Secret key
 * @param {string} params.app_id - App ID
 * @param {string} params.app_key - App key
 * @returns {Object} Request key payload
 */
export const createRequestKeyPayload = ({
  office_id,
  secret_key,
  app_id,
  app_key,
}) => {
  return {
    [MODEL_FIELDS.GRANT_TYPE]: SIKKA_API.GRANT_TYPES.REQUEST_KEY,
    [MODEL_FIELDS.OFFICE_ID]: office_id,
    [MODEL_FIELDS.SECRET_KEY]: secret_key,
    [MODEL_FIELDS.APP_ID]: app_id,
    [MODEL_FIELDS.APP_KEY]: app_key,
  };
};

/**
 * Create headers for Sikka API calls
 * @param {string} app_id - App ID
 * @param {string} app_key - App key
 * @returns {Object} Headers object
 */
export const createSikkaHeaders = (app_id, app_key) => {
  return {
    [HTTP_HEADERS.APP_ID]: app_id,
    [HTTP_HEADERS.APP_KEY]: app_key,
  };
};

/**
 * Fetch authorized practices from Sikka API
 * @param {string} app_id - App ID
 * @param {string} app_key - App key
 * @returns {Promise<Object>} Practices data with message
 */
export const fetchAuthorizedPractices = async (app_id, app_key) => {
  const headers = createSikkaHeaders(app_id, app_key);

  const practices_data = await sikkaApiCall(
    METHOD_TYPES.GET,
    SIKKA_API.ENDPOINTS.AUTHORIZED_PRACTICES,
    {
      headers,
      logContext: { [MODEL_FIELDS.APP_ID]: app_id },
      successMessage: LOG_ACTIONS.PRACTICES_FETCHED,
      errorMessage: ERROR_MESSAGES.API_CALL_FAILED_FOR,
    }
  ).then((data) => ({
    message: SIKKA_MESSAGES.AUTHORIZED_PRACTICES_SUCCESS,
    data,
  }));

  // Validate practices data exists and has items
  if (!practices_data.data || practices_data.data.items.length === 0) {
    throw new Error(SIKKA_MESSAGES.NO_AUTHORIZED_PRACTICES);
  }

  return practices_data;
};

/**
 * Validate credentials and throw error if invalid
 * @param {string} app_id - App ID
 * @param {string} app_key - App key
 * @throws {Error} If credentials are invalid
 */
export const validateAndThrowIfInvalid = (app_id, app_key) => {
  const validation = validateSikkaCredentials(app_id, app_key);
  if (!validation.isValid) {
    throw new Error(
      `${SIKKA_MESSAGES.INVALID_CREDENTIALS}: ${validation.errors.join(
        BUSINESS_CONSTANTS.CREDENTIAL_SEPARATOR
      )}`
    );
  }
};

export const getKpiUrl = (kpi, practice_id, start_date, end_date) => {
  const params = new URLSearchParams({
    [API_URLS.PRACTICE_ID_PARAM]: practice_id,
  });

  if (start_date && end_date) {
    params.append(API_URLS.STARTDATE_PARAM, start_date);
    params.append(API_URLS.ENDDATE_PARAM, end_date);
  }

  return `${API_URLS.KPI_BASE}${kpi}?${params.toString()}`;
};

/**
 * Unified KPI fetch and store method that handles request key management and data storage
 * @param {string} office_id - Office ID
 * @param {string} start_date - Start date (optional for some KPIs)
 * @param {string} end_date - End date (optional for some KPIs)
 * @param {string} endpoint - Sikka API endpoint
 * @param {Function} repository_function - Repository function to store data
 * @param {string} success_log_action - Success log action
 * @param {string} fail_log_action - Fail log action
 * @param {Object} logger - Logger instance
 * @param {string|null} schema_name - Optional schema name for schema-aware storage
 * @returns {Promise<Array>} Array of stored records
 */
export const fetchAndStoreKpi = async (
  office_id,
  start_date,
  end_date,
  endpoint,
  repositoryFunction,
  successLogAction,
  failLogAction,
  logger,
  schemaName = null
) => {
  const repository_function = repositoryFunction;
  const success_log_action = successLogAction;
  const fail_log_action = failLogAction;
  const schema_name = schemaName;

  try {
    logger.info(success_log_action);
    // Get current request key
    const { request_key } = await getRequestKey(office_id, schema_name);

    // Create headers for API call
    const headers = {
      [HTTP_HEADER_CONSTANTS.REQUEST_KEY_HEADER]: request_key,
    };

    // Fetch data from Sikka API
    const response = await sikkaApiCall(
      METHOD_TYPES.GET,
      getKpiUrl(
        endpoint,
        NUMERIC_CONSTANTS.PRACTICE_ID_DEFAULT,
        start_date,
        end_date
      ),
      {
        headers,
        successMessage: success_log_action,
        errorMessage: fail_log_action,
      }
    );

    if (
      response[MODEL_FIELDS.ITEMS] &&
      response[MODEL_FIELDS.ITEMS].length > 0
    ) {
       const response_items = response[MODEL_FIELDS.ITEMS].map(item => ({
        ...item,
        start_date,
        end_date
      }));
      // Store all data in bulk using the provided repository function
      // Use schema_name from organization API call if available
      const final_schema_name = schema_name;
      const stored_data = final_schema_name
        ? await repository_function(
            response_items,
            final_schema_name
          )
        : await repository_function(response_items);
      logger.info(
        `${LOG_MESSAGES.SUCCESSFULLY_STORED_RECORDS} ${stored_data.length} ${
          LOG_MESSAGES.RECORDS
        }${
          final_schema_name
            ? ` ${LOG_MESSAGES.IN_SCHEMA_FOR_ORGANIZATION} ${final_schema_name}`
            : ""
        }`
      );
      return stored_data;
    } else {
      logger.info(SIKKA_MESSAGES.NO_DATA_RECEIVED);
      return [];
    }
  } catch (error) {
    logger.error(fail_log_action, { error: error.message });
    throw error;
  }
};

/**
 * Validate data for bulkCreate
 * Filters out invalid entries like null, undefined, non-objects
 * Optionally checks for required fields
 *
 * @param {Array} data - Array of data objects
 * @param {Array<string>} requiredFields - Fields that must be present in each object
 * @returns {Object} - { validData: Array, invalidData: Array }
 */
export const validateBulkCreateData = (data, requiredFields = []) => {
  const required_fields = requiredFields;

  if (!Array.isArray(data)) {
    throw new Error(VALIDATION_MESSAGES.DATA_MUST_BE_ARRAY);
  }

  const valid_data = [];
  const invalid_data = [];

  for (const item of data) {
    if (!item || typeof item !== "object" || Array.isArray(item)) {
      invalid_data.push({ item, reason: VALIDATION_MESSAGES.INVALID_OBJECT });
      continue;
    }

    let missing_fields = required_fields.filter((field) => !(field in item));
    if (missing_fields.length > 0) {
      invalid_data.push({
        item,
        reason: `${
          VALIDATION_FIELD_MESSAGES.MISSING_REQUIRED_FIELDS
        } ${missing_fields.join(", ")}`,
      });
      continue;
    }

    valid_data.push(item);
  }

  return { validData: valid_data, invalidData: invalid_data };
};

/**
 * Get the models directory path
 * @param {string} baseDir - Base directory path (usually __dirname from calling file)
 * @returns {string} Path to models directory
 */
export const getModelsDirectory = (baseDir) => {
  return path.join(baseDir, FILE_SYSTEM.TABLES_DIRECTORY);
};

/**
 * Get list of model file names from the models directory
 * @param {string} models_dir - Path to models directory
 * @returns {Array<string>} Array of model file names (without extension)
 */
export const getModelFiles = (modelsDir) => {
  const models_dir = modelsDir;

  if (!fs.existsSync(models_dir)) {
    throw new Error(SIKKA_MESSAGES.MODELS_DIRECTORY_NOT_FOUND);
  }

  return fs
    .readdirSync(models_dir)
    .filter(
      (file) =>
        file.endsWith(FILE_SYSTEM.MODEL_JS_EXTENSION) ||
        file.endsWith(FILE_SYSTEM.JS_EXTENSION)
    )
    .map((file) => file.replace(/\.(model\.)?js$/, ""));
};

/**
 * Find the actual path to a model file (handles different naming conventions)
 * @param {string} models_dir - Path to models directory
 * @param {string} model_file_name - Model file name without extension
 * @returns {string|null} Actual path to model file or null if not found
 */
export const findModelFilePath = (modelsDir, modelFileName) => {
  const models_dir = modelsDir;
  const model_file_name = modelFileName;

  const model_path = path.join(
    models_dir,
    `${model_file_name}.${
      model_file_name.includes(".model")
        ? FILE_SYSTEM.JS_EXTENSION.replace(".", "")
        : FILE_SYSTEM.MODEL_JS_EXTENSION.replace(".", "")
    }`
  );

  if (fs.existsSync(model_path)) {
    return model_path;
  }

  const alt_path = path.join(
    models_dir,
    `${model_file_name}${FILE_SYSTEM.JS_EXTENSION}`
  );
  if (fs.existsSync(alt_path)) {
    return alt_path;
  }

  return null;
};

/**
 * Extract column information from a Sequelize model
 * @param {Object} model - Sequelize model instance
 * @returns {Object} Object with column information
 */
export const extractModelColumns = (model) => {
  const columns = {};
  Object.keys(model.rawAttributes).forEach((columnName) => {
    const attribute = model.rawAttributes[columnName];
    columns[columnName] = {
      type:
        attribute.type?.constructor?.name ||
        attribute.type?.toString() ||
        "UNKNOWN",
      allowNull: attribute.allowNull !== false,
      defaultValue: attribute.defaultValue,
      primaryKey: attribute.primaryKey || false,
      autoIncrement: attribute.autoIncrement || false,
      unique: attribute.unique || false,
      comment: attribute.comment || null,
    };
  });
  return columns;
};

/**
 * Extract association information from a Sequelize model
 * @param {Object} model - Sequelize model instance
 * @returns {Object} Object with association information
 */
export const extractModelAssociations = (model) => {
  const associations = {};
  if (model.associations) {
    Object.keys(model.associations).forEach((associationName) => {
      const association = model.associations[associationName];
      associations[associationName] = {
        type: association.associationType,
        target: association.target?.name,
        foreignKey: association.foreignKey,
        sourceKey: association.sourceKey,
        as: association.as,
      };
    });
  }
  return associations;
};

/**
 * Extract complete model information from a Sequelize model instance
 * @param {Object} model - Sequelize model instance
 * @returns {Object} Complete model information including columns, associations, and metadata
 */
export const extractModelInfo = (model) => {
  return {
    [MODEL_FIELDS.TABLE_NAME]: model.tableName || model.name,
    [MODEL_FIELDS.MODEL_NAME]: model.name,
    [MODEL_FIELDS.COLUMNS]: extractModelColumns(model),
    [MODEL_FIELDS.ASSOCIATIONS]: extractModelAssociations(model),
    indexes: model.options?.indexes || [],
    timestamps: model.options?.timestamps || false,
    createdAt: model.options?.createdAt || "createdAt",
    updatedAt: model.options?.updatedAt || "updatedAt",
  };
};

/**
 * Process a single model file and extract its information
 * @param {string} models_dir - Path to models directory
 * @param {string} model_file_name - Model file name without extension
 * @param {Object} sequelize - Sequelize instance
 * @param {Object} logger - Logger instance
 * @returns {Object|null} Model information object or null if processing failed
 */
export const processModelFile = async (
  modelsDir,
  modelFileName,
  sequelize,
  logger
) => {
  const models_dir = modelsDir;
  const model_file_name = modelFileName;

  try {
    const actual_path = findModelFilePath(models_dir, model_file_name);
    if (!actual_path) {
      logger.warn(`Model file not found: ${model_file_name}`);
      return {
        error: `${SIKKA_MESSAGES.FAILED_TO_LOAD_MODEL} File not found`,
        [MODEL_FIELDS.TABLE_NAME]: null,
        [MODEL_FIELDS.COLUMNS]: {},
        [MODEL_FIELDS.ASSOCIATIONS]: {},
      };
    }

    const model_module = await import(`file://${actual_path}`);
    const model_function = model_module.default;

    if (typeof model_function !== "function") {
      logger.warn(`Model file does not export a function: ${model_file_name}`);
      return {
        error: `${SIKKA_MESSAGES.FAILED_TO_LOAD_MODEL} Invalid model export`,
        [MODEL_FIELDS.TABLE_NAME]: null,
        [MODEL_FIELDS.COLUMNS]: {},
        [MODEL_FIELDS.ASSOCIATIONS]: {},
      };
    }

    const model = model_function(sequelize);
    return extractModelInfo(model);
  } catch (error) {
    logger.error(
      `${SIKKA_MESSAGES.ERROR_PROCESSING_MODEL} ${model_file_name}:`,
      error.message
    );
    return {
      error: `${SIKKA_MESSAGES.FAILED_TO_LOAD_MODEL} ${error.message}`,
      [MODEL_FIELDS.TABLE_NAME]: null,
      [MODEL_FIELDS.COLUMNS]: {},
      [MODEL_FIELDS.ASSOCIATIONS]: {},
    };
  }
};
