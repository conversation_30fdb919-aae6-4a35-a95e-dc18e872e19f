import {
  <PERSON><PERSON>C<PERSON>BOOKS_SERVICE_LOGS,
  QUICKBOOKS_COMMON_LOGS,
  QUICKBOOKS_ERROR_LOGS,
} from "../utils/constants/log.constants.js";
import {
  QUICKBOOKS_DATABASE,
  QUICKBOOKS_DEFAULTS,
  QUICKBOOKS_FIELD_NAMES,
  QUICKBOOKS_HTTP_HEADERS,
} from "../utils/constants/config.constants.js";
import { QUICKBOOKS_MESSAGES } from "../utils/constants/error.constants.js";
import { HARDCODED_STRINGS } from "../utils/constants/strings.constants.js";
import logger from "../../config/logger.config.js";
import axios from "axios";
import {
  updateOrganizationRealmId,
  checkRealmIdAssociation,
  ORG_API_BASE_URL,
} from "../utils/organization-api.util.js";
import { decrypt, encrypt } from "../utils/encryption.utils.js";
import quickbooksRepository from "../repositories/quickbooks.repository.js";
import knex from "knex";
import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";
import sequelize from "../../config/postgres.config.js";

const QB_ENV = {
  clientID: process.env.QUICKBOOKS_CLIENT_ID,
  clientSecret: process.env.QUICKBOOKS_CLIENT_SECRET,
  tokenUrl: process.env.QUICKBOOKS_TOKEN_URL,
  baseUrl: process.env.SANDBOX_URL,
  dbServer: process.env.DB_SERVER,
  dbUser: process.env.DB_USER,
  dbPassword: process.env.DB_PASSWORD,
};

const validateEnvironment = () => {
  const required = HARDCODED_STRINGS.REQUIRED_ENV_VARS;
  const missing = required.filter((key) => !QB_ENV[key]);
  if (missing.length > 0) {
    throw new Error(
      `${HARDCODED_STRINGS.MISSING_REQUIRED_ENV_VARS}: ${missing.join(", ")}`
    );
  }
};

try {
  validateEnvironment();
} catch (error) {
  logger.error(QUICKBOOKS_SERVICE_LOGS.DATABASE_CONNECTION_ERROR, {
    error: error.message,
  });
}

const dbConnectionCache = new Map();

const createDBConfig = (databaseName) => {
  const isLocalDatabase = process.env.USE_LOCAL_DB === "true";
  return {
    client: HARDCODED_STRINGS.DB_CONFIG.CLIENT,
    connection: {
      host: process.env.LOCAL_DB_HOST,
      user: process.env.LOCAL_DB_USER,
      password: process.env.LOCAL_DB_PASS,
      database: databaseName || process.env.LOCAL_DB_NAME,
      port: parseInt(process.env.LOCAL_DB_PORT),
      ssl: isLocalDatabase ? false : { rejectUnauthorized: false },
    },
    pool: {
      min: QUICKBOOKS_DEFAULTS.POOL_MIN,
      max: QUICKBOOKS_DEFAULTS.POOL_MAX,
      acquireTimeoutMillis: QUICKBOOKS_DEFAULTS.POOL_ACQUIRE_TIMEOUT,
      idleTimeoutMillis: QUICKBOOKS_DEFAULTS.POOL_IDLE_TIMEOUT,
      createTimeoutMillis: QUICKBOOKS_DEFAULTS.POOL_CREATE_TIMEOUT,
      destroyTimeoutMillis: QUICKBOOKS_DEFAULTS.POOL_DESTROY_TIMEOUT,
      reapIntervalMillis: QUICKBOOKS_DEFAULTS.POOL_REAP_INTERVAL,
      createRetryIntervalMillis: QUICKBOOKS_DEFAULTS.POOL_RETRY_INTERVAL,
    },
    acquireConnectionTimeout: QUICKBOOKS_DEFAULTS.POOL_ACQUIRE_TIMEOUT,
    debug: process.env.NODE_ENV === HARDCODED_STRINGS.NODE_ENV_DEVELOPMENT,
  };
};

export const createKnexInstance = async (databaseName) => {
  if (dbConnectionCache.has(databaseName)) {
    const cachedInstance = dbConnectionCache.get(databaseName);
    try {
      await cachedInstance.raw(HARDCODED_STRINGS.SELECT_1);
      return cachedInstance;
    } catch {
      dbConnectionCache.delete(databaseName);
      await cachedInstance
        .destroy()
        .catch((error) =>
          logger.warn(
            QUICKBOOKS_COMMON_LOGS.ERROR_DESTROYING_CONNECTION,
            error.message
          )
        );
    }
  }

  const config = createDBConfig(databaseName);
  const dbInstance = knex(config);

  dbInstance.on(HARDCODED_STRINGS.QUERY_ERROR_EVENT, (error) => {
    const connectionErrors = [
      QUICKBOOKS_DATABASE.QB_DB_CONNECTION_CLOSED,
      QUICKBOOKS_DATABASE.QB_DB_CONNECTION_TIMEOUT,
      QUICKBOOKS_DATABASE.ERROR_TYPES.ETIMEOUT,
      QUICKBOOKS_DATABASE.ERROR_TYPES.ECONNRESET,
    ];

    const isConnectionError = connectionErrors.some((errorType) =>
      error.message.includes(errorType)
    );

    if (isConnectionError) {
      logger.error(
        QUICKBOOKS_SERVICE_LOGS.DATABASE_CONNECTION_ERROR + error.message
      );
      logger.info(QUICKBOOKS_SERVICE_LOGS.ATTEMPTING_RECONNECT);
      dbConnectionCache.delete(databaseName);
      dbInstance
        .destroy()
        .catch(() =>
          logger.warn(QUICKBOOKS_COMMON_LOGS.ERROR_DESTROYING_OLD_CONNECTION)
        );
    }
  });

  dbConnectionCache.set(databaseName, dbInstance);
  return dbInstance;
};

export const retryQuery = async (
  queryFn,
  maxRetries = QUICKBOOKS_DEFAULTS.MAX_RETRIES,
  delay = QUICKBOOKS_DEFAULTS.RETRY_DELAY_MS
) => {
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await queryFn();
    } catch (error) {
      const retryableErrors = [
        QUICKBOOKS_DATABASE.ERROR_TYPES.TIMEOUT,
        QUICKBOOKS_DATABASE.ERROR_TYPES.ETIMEOUT,
        QUICKBOOKS_DATABASE.ERROR_TYPES.ECONNRESET,
      ];

      const isRetryable = retryableErrors.some((errorType) =>
        error.message.includes(errorType)
      );

      if (isRetryable && attempt < maxRetries) {
        logger.warn(
          QUICKBOOKS_SERVICE_LOGS.RETRYING_QUERY(attempt, maxRetries)
        );
        await new Promise((resolve) => setTimeout(resolve, delay * attempt));
      } else {
        logger.error(
          QUICKBOOKS_SERVICE_LOGS.QUERY_FAILED(attempt, error.message)
        );
        throw error;
      }
    }
  }
};

export const exchangeAuthCodeForTokens = async (code) => {
  try {
    const authHeader = Buffer.from(
      `${QB_ENV.clientID}:${QB_ENV.clientSecret}`
    ).toString(HARDCODED_STRINGS.STRING_OPS.BASE64);

    const response = await axios.post(
      QB_ENV.tokenUrl,
      new URLSearchParams({
        grant_type: QUICKBOOKS_DEFAULTS.GRANT_TYPE_AUTH_CODE,
        code,
        redirect_uri: process.env.QUICKBOOKS_REDIRECT_URI,
      }).toString(),
      {
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
          [QUICKBOOKS_HTTP_HEADERS.AUTHORIZATION]: `${QUICKBOOKS_DEFAULTS.AUTH_BASIC_PREFIX}${authHeader}`,
        },
        timeout: QUICKBOOKS_DEFAULTS.REQUEST_TIMEOUT_MS,
      }
    );
    logger.info(`QB Tokens`, {
      tokens: response.data,
    });
    return response.data;
  } catch (error) {
    logger.error(QUICKBOOKS_SERVICE_LOGS.TOKEN_EXCHANGE_FAILED, error.message);
    throw error;
  }
};

const decryptRefreshToken = async (rawRefreshToken) => {
  if (rawRefreshToken.includes(":")) {
    try {
      return await decrypt(rawRefreshToken);
    } catch {
      return rawRefreshToken;
    }
  }
  return rawRefreshToken;
};

export const refreshTokens = async (quickbookAccount) => {
  try {
    const rawRefreshToken =
      quickbookAccount?.refresh_token || quickbookAccount?.refreshToken;
    if (!rawRefreshToken) {
      throw new Error(QUICKBOOKS_MESSAGES.ACCOUNT_NOT_FOUND);
    }

    const refreshToken = await decryptRefreshToken(rawRefreshToken);
    if (!refreshToken) {
      throw new Error(HARDCODED_STRINGS.FAILED_TO_DECRYPT_REFRESH_TOKEN);
    }

    const authString = Buffer.from(
      `${QB_ENV.clientID}:${QB_ENV.clientSecret}`
    ).toString(HARDCODED_STRINGS.STRING_OPS.BASE64);

    const tokenResponse = await axios.post(
      QB_ENV.tokenUrl,
      new URLSearchParams({
        grant_type: QUICKBOOKS_DEFAULTS.GRANT_TYPE_REFRESH,
        refresh_token: refreshToken,
      }),
      {
        headers: {
          [QUICKBOOKS_HTTP_HEADERS.AUTHORIZATION]: `${QUICKBOOKS_DEFAULTS.AUTH_BASIC_PREFIX}${authString}`,
          "Content-Type": QUICKBOOKS_DEFAULTS.CONTENT_TYPE_FORM,
        },
        timeout: QUICKBOOKS_DEFAULTS.REQUEST_TIMEOUT_MS,
      }
    );

    if (
      !tokenResponse.data?.access_token ||
      !tokenResponse.data?.refresh_token
    ) {
      throw new Error(
        QUICKBOOKS_MESSAGES.INVALID_TOKEN_RESPONSE_FROM_QUICKBOOKS
      );
    }

    const [encryptedRefreshToken, encryptedAccessToken] = await Promise.all([
      encrypt(tokenResponse.data.refresh_token),
      encrypt(tokenResponse.data.access_token),
    ]);

    const hasId = !!quickbookAccount?.realm_id;
    
    // Use expires_in from QuickBooks response (in seconds), fallback to default if not provided
    const expiresInMs = tokenResponse.data.expires_in
      ? tokenResponse.data.expires_in * 1000
      : QUICKBOOKS_DEFAULTS.TOKEN_EXPIRY_MS;
    
    const tokenExpiry = new Date(Date.now() + expiresInMs);

    if (hasId) {
      const updateData = {
        [QUICKBOOKS_FIELD_NAMES.REFRESH_TOKEN]: encryptedRefreshToken,
        [QUICKBOOKS_FIELD_NAMES.ACCESS_TOKEN]: encryptedAccessToken,
        [QUICKBOOKS_FIELD_NAMES.TOKEN_EXPIRY_TIME]: tokenExpiry,
        [QUICKBOOKS_FIELD_NAMES.UPDATED_AT]: new Date(),
      };
      console.log(updateData);
      const accountRecord = await quickbooksRepository.updateByRealmId(
        quickbookAccount.realm_id,
        updateData,
        {},
        quickbookAccount.schemaName || null
      );

      if (!accountRecord) {
        throw new Error(QUICKBOOKS_MESSAGES.ACCOUNT_NOT_FOUND);
      }

      return accountRecord;
    }

    return {
      ...quickbookAccount,
      access_token: encryptedAccessToken,
      refresh_token: encryptedRefreshToken,
      token_expiry_time: tokenExpiry,
      updated_at: new Date(),
    };
  } catch (error) {
    logger.error(QUICKBOOKS_ERROR_LOGS.ERROR_ADDING_TOKEN, error.message);
    if (error.response) {
      logger.error(QUICKBOOKS_SERVICE_LOGS.QUICKBOOKS_API_ERROR, {
        status: error.response.status,
        data: error.response.data,
      });
    }
    return null;
  }
};

export const fetchAllModelsInfo = async () => {
  try {
    logger.info("Getting models information");

    const __filename = fileURLToPath(import.meta.url);
    const __dirname = path.dirname(__filename);
    const modelsDir = path.join(__dirname, "../models");

    if (!fs.existsSync(modelsDir)) {
      throw new Error("Models directory not found");
    }

    const modelFiles = fs
      .readdirSync(modelsDir)
      .filter((file) => file.endsWith(".model.js"))
      .map((file) => file.replace(".model.js", ""));

    const modelsInfo = {};

    for (const modelFileName of modelFiles) {
      try {
        const modelPath = path.join(modelsDir, `${modelFileName}.model.js`);
        const modelModule = await import(`file://${modelPath}`);
        const ModelFunction = modelModule.default;

        if (typeof ModelFunction === "function") {
          const model = ModelFunction(sequelize);
          const modelInfo = {
            tableName: model.tableName || model.name,
            modelName: model.name,
            columns: {},
            associations: {},
            indexes: model.options?.indexes || [],
            timestamps: model.options?.timestamps || false,
            createdAt: model.options?.createdAt || "createdAt",
            updatedAt: model.options?.updatedAt || "updatedAt",
          };

          Object.keys(model.rawAttributes).forEach((columnName) => {
            const attribute = model.rawAttributes[columnName];
            modelInfo.columns[columnName] = {
              type:
                attribute.type?.constructor?.name ||
                attribute.type?.toString() ||
                "UNKNOWN",
              allowNull: attribute.allowNull !== false,
              defaultValue: attribute.defaultValue,
              primaryKey: attribute.primaryKey || false,
              autoIncrement: attribute.autoIncrement || false,
              unique: attribute.unique || false,
              comment: attribute.comment || null,
            };
          });

          if (model.associations) {
            Object.keys(model.associations).forEach((associationName) => {
              const association = model.associations[associationName];
              modelInfo.associations[associationName] = {
                type: association.associationType,
                target: association.target?.name,
                foreignKey: association.foreignKey,
                sourceKey: association.sourceKey,
                as: association.as,
              };
            });
          }

          modelsInfo[modelFileName] = modelInfo;
        }
      } catch (error) {
        logger.error(`Error processing model ${modelFileName}:`, error.message);
        modelsInfo[modelFileName] = {
          error: `Failed to load model: ${error.message}`,
          tableName: null,
          columns: {},
          associations: {},
        };
      }
    }

    logger.info(
      `Successfully processed ${Object.keys(modelsInfo).length} models`
    );
    return {
      totalModels: Object.keys(modelsInfo).length,
      models: modelsInfo,
      timestamp: new Date().toISOString(),
    };
  } catch (error) {
    logger.error("Error getting models information", {
      error: error.message,
      stack: error.stack,
    });
    throw error;
  }
};

export const getTokensDirectly = refreshTokens;

export const handleOrganizationRealmId = async (realmId, organizationId) => {
  if (!realmId || !organizationId) {
    logger.warn("Missing realmId or organizationId for realm_id operation");
    return {
      success: false,
      error: "Realm ID and Organization ID are required",
    };
  }

  try {
    const checkResponse = await checkRealmIdAssociation(realmId);

    if (!checkResponse.success) {
      logger.warn(
        `Failed to check realm_id association: ${checkResponse.error}`
      );
      return {
        success: false,
        error: `Failed to check realm_id association: ${checkResponse.error}`,
      };
    }

    const { is_associated, org_name } = checkResponse.data?.data || {};

    if (is_associated && org_name) {
      logger.info(
        `Realm ID ${realmId} is already associated with organization: ${org_name}`
      );
      return {
        success: false,
        warning: true,
        message: `This QuickBooks account is already connected to organization: ${org_name}`,
        error: "Realm ID already associated with another organization",
      };
    }

    return {
      success: true,
      message: HARDCODED_STRINGS.REALM_ID.REALM_ID_AVAILABLE,
    };
  } catch (error) {
    logger.error(`Error in handleOrganizationRealmId: ${error.message}`);
    return { success: false, error: error.message };
  }
};

export const updateOrganizationQbSyncTimestamp = async (
  organizationId,
  lastSyncedAt
) => {
  if (!organizationId) {
    logger.warn("Organization ID is required for sync timestamp update");
    return { success: false, error: "Organization ID is required" };
  }

  try {
    const syncApiUrl = `${ORG_API_BASE_URL}/sync/${organizationId}`;
    logger.info(
      QUICKBOOKS_SERVICE_LOGS.UPDATING_SYNC_TIMESTAMP(organizationId)
    );

    const response = await axios.put(syncApiUrl, { type: "qb", lastSyncedAt });

    const isSuccess = response.status >= 200 && response.status < 300;

    if (isSuccess) {
      logger.info(
        QUICKBOOKS_SERVICE_LOGS.SYNC_TIMESTAMP_UPDATED_SUCCESS(organizationId)
      );
      return { success: true, data: response.data, status: response.status };
    }

    logger.warn(
      QUICKBOOKS_SERVICE_LOGS.SYNC_TIMESTAMP_UPDATE_FAILED(
        organizationId,
        `Status: ${response.status}`
      )
    );
    return {
      success: false,
      error: `Request failed with status ${response.status}`,
      status: response.status,
    };
  } catch (error) {
    logger.warn(
      QUICKBOOKS_SERVICE_LOGS.SYNC_TIMESTAMP_UPDATE_FAILED(
        organizationId,
        error.message
      ),
      {
        error: error.message,
        response: error.response?.data || null,
      }
    );
    return {
      success: false,
      error: error.response?.data?.message || error.message || "Unknown error",
      status: error.response?.status || 500,
    };
  }
};

export const updateRealmIdForOrganization = async (organizationId, realmId) => {
  try {
    const updateResponse = await updateOrganizationRealmId(
      organizationId,
      realmId
    );

    if (!updateResponse.success) {
      logger.warn(
        HARDCODED_STRINGS.REALM_ID.UPDATE_FAILED_WARNING(
          organizationId,
          updateResponse.error
        )
      );
      return {
        success: false,
        error: HARDCODED_STRINGS.REALM_ID.UPDATE_FAILED_ERROR(
          updateResponse.error
        ),
      };
    }

    logger.info(HARDCODED_STRINGS.REALM_ID.UPDATE_SUCCESS(organizationId));
    return updateResponse;
  } catch (error) {
    logger.warn(
      HARDCODED_STRINGS.REALM_ID.UPDATE_ERROR_WARNING(
        organizationId,
        error.message
      )
    );
    return { success: false, error: error.message };
  }
};

export default {
  createKnexInstance,
  retryQuery,
  exchangeAuthCodeForTokens,
  refreshTokens,
  getTokensDirectly,
  handleOrganizationRealmId,
  updateRealmIdForOrganization,
  fetchAllModelsInfo,
  updateOrganizationQbSyncTimestamp,
};
