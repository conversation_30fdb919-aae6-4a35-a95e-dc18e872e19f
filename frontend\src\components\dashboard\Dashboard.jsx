import React, {
  lazy,
  memo,
  Suspense,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import SummaryPopup from "./summary/SummaryPopup";
import {
  DashboardLayout,
  EmptyState,
  LoadingState,
} from "./layout/DashboardLayout";
import DashboardHeader from "./layout/DashboardHeader";
import CategoryTabs from "./layout/CategoryTabs";
import { DashboardSelector } from "./core/DashboardSelector";
import { DASHBOARD_CONSTANTS } from "@/utils/constants/dashboard";
import "@/styles/dashboard.css";
import { useDispatch, useSelector } from "react-redux";
import { downloadFile } from "@/redux/Thunks/fileOperations";
import {
  fetchReportFolders,
  fetchReportFiles,
  fetchReportSummary,
} from "@/redux/Thunks/reportFolders";
import { setDashboardSummary, setLoading } from "@/redux/Slice/chat";
import { selectReportFolders } from "@/redux/Selectors/reportFolders";
import { selectChatData } from "@/redux/Selectors/chat";
import { useSidebarContext } from "@/contexts/SidebarContext";
import { createFolderMonthsMap } from "@/utils/sidebarUtils";
import tokenStorage from "@/lib/tokenStorage";
import { useSearchParams } from "next/navigation";

const LazyPdfViewer = lazy(
  () => import("@/components/dashboard/core/PDFViewer")
);
const MemoizedPdfViewer = memo(function MemoizedPdfViewer(props) {
  return (
    <Suspense fallback={<LoadingState tip="" />}>
      <LazyPdfViewer {...props} />
    </Suspense>
  );
});

const PAGE_TO_VIEW = 1;
const VALID_DASHBOARDS = ["chp", "dental"];
const CATEGORY_NAMES = {
  FINANCE: "Finance",
  OPERATIONS: "Operations",
  PAYROLL: "Payroll",
};

export default function Dashboard() {
  const [userRole, setUserRole] = useState("");
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [selectedDashboard, setSelectedDashboard] = useState("");
  const [organizationName, setOrganizationName] = useState(null);
  const [showSummaryPopup, setShowSummaryPopup] = useState(false);

  const searchParams = useSearchParams();
  const dispatch = useDispatch();
  const { dashboardSummary, isLoading } = useSelector(selectChatData);
  const {
    folders: reportFolders,
    monthsByFolder,
    filesByFolder,
    filesLoading,
    filesError,
    summaries,
    summaryLoading,
    loadingFolders,
    foldersError,
    organizationId: reduxOrganizationId,
    organizationName: reduxOrganizationName,
  } = useSelector(selectReportFolders);

  const {
    isFinancialSelected,
    isOperationsSelected,
    isPayrollSelected,
    setFinancialSelected,
    setOperationsSelected,
    setPayrollSelected,
    updateReportFolders,
    updateLoadingFolders,
    updateFoldersError,
    setMonths,
    getCurrentMonths,
    subscribeToMonthChanges,
  } = useSidebarContext();

  const [months, setLocalMonths] = useState(getCurrentMonths);

  const resolvedOrganizationName = organizationName || reduxOrganizationName || null;

  const categoryKeys = reportFolders.map((folder) => folder.key);

  const folderMonthsMap = useMemo(
    () => createFolderMonthsMap(reportFolders),
    [reportFolders]
  );

  const categoriesWithMonths = useMemo(
    () =>
      Object.entries(monthsByFolder || {})
        .filter(
          ([, monthList]) => Array.isArray(monthList) && monthList.length > 0
        )
        .map(([key]) => key),
    [monthsByFolder]
  );

  const getFolderMonths = useCallback((category) => (category && Array.isArray(monthsByFolder?.[category]) ? monthsByFolder[category] : []), [monthsByFolder]);

  useEffect(() => {
    const unsubscribe = subscribeToMonthChanges((newMonths) => {
      setLocalMonths(newMonths);
    });
    return unsubscribe;
  }, [subscribeToMonthChanges]);

  useEffect(() => {
    const userData = tokenStorage.getUserData();
    setUserRole(userData?.role?.name || "");
  }, []);

  const lastFetchedOrgIdRef = useRef(null);

  useEffect(() => {
    const dashboardParam = searchParams.get("dashboard");
    const organizationParam = searchParams.get("orgname");
    const organizationIdParam = searchParams.get("orgId");

    if (organizationParam)
      setOrganizationName(decodeURIComponent(organizationParam));

    if (organizationIdParam) {
      const orgIdChanged = lastFetchedOrgIdRef.current !== organizationIdParam;
      if (orgIdChanged) {
        lastFetchedOrgIdRef.current = organizationIdParam;
        dispatch(
          fetchReportFolders({
            organizationId: organizationIdParam,
            organizationName: organizationParam || null,
            forceRefresh: true,
          })
        );
      }
    }

    if (dashboardParam && VALID_DASHBOARDS.includes(dashboardParam)) {
      setSelectedDashboard(dashboardParam);
      setFinancialSelected(true);
      setOperationsSelected(false);
      setPayrollSelected(false);
    }
  }, [
    searchParams,
    dispatch,
    setFinancialSelected,
    setOperationsSelected,
    setPayrollSelected,
  ]);

  const pdfLoadedRef = useRef(false);
  const lastRefetchTimeRef = useRef(0);
  const REFETCH_COOLDOWN = 5000;
  const fetchingFilesRef = useRef(new Set());

  useEffect(() => {
    const refetchFreshData = () => {
      const organizationIdParam = searchParams.get("orgId") || reduxOrganizationId;
      const organizationParam = searchParams.get("orgname") || reduxOrganizationName;
      
      if (!organizationIdParam) return;

      const now = Date.now();
      const timeSinceLastRefetch = now - lastRefetchTimeRef.current;
      
      if (pdfLoadedRef.current && timeSinceLastRefetch < REFETCH_COOLDOWN) {
        return;
      }

      lastRefetchTimeRef.current = now;
      
      dispatch(
        fetchReportFolders({
          organizationId: organizationIdParam,
          organizationName: organizationParam || null,
          forceRefresh: true,
        })
      );
    };

    const handleVisibilityChange = () => {
      if (document.visibilityState === "visible") {
        refetchFreshData();
      }
    };

    const handleFocus = () => {
      refetchFreshData();
    };

    document.addEventListener("visibilitychange", handleVisibilityChange);
    window.addEventListener("focus", handleFocus);
    
    return () => {
      document.removeEventListener("visibilitychange", handleVisibilityChange);
      window.removeEventListener("focus", handleFocus);
    };
  }, [searchParams, reduxOrganizationId, reduxOrganizationName, dispatch]);

  const prioritizedCategories = categoriesWithMonths.length > 0 ? categoriesWithMonths : categoryKeys;

  const getSelectedCategory = useCallback(() => {
    if (!prioritizedCategories.length) return null;

    const categoryMap = {
      [CATEGORY_NAMES.OPERATIONS]: isOperationsSelected,
      [CATEGORY_NAMES.PAYROLL]: isPayrollSelected,
      [CATEGORY_NAMES.FINANCE]: isFinancialSelected,
    };

    for (const category of prioritizedCategories) {
      if (categoryMap[category]) return category;
    }

    return prioritizedCategories.includes(CATEGORY_NAMES.FINANCE) ? CATEGORY_NAMES.FINANCE : prioritizedCategories[0] || null;
  }, [prioritizedCategories, isOperationsSelected, isPayrollSelected, isFinancialSelected]);

  useEffect(() => {
    updateReportFolders(reportFolders);
    updateLoadingFolders(loadingFolders);
    updateFoldersError(foldersError);
  }, [reportFolders, loadingFolders, foldersError, updateReportFolders, updateLoadingFolders, updateFoldersError]);

  const prevReportFoldersRef = useRef(null);

  useEffect(() => {
    const foldersChanged = JSON.stringify(prevReportFoldersRef.current) !== JSON.stringify(reportFolders);
    if (!foldersChanged && prevReportFoldersRef.current !== null) return;

    prevReportFoldersRef.current = reportFolders;
    if (!prioritizedCategories.length) return;

    const defaultCategory = prioritizedCategories.includes(CATEGORY_NAMES.FINANCE) ? CATEGORY_NAMES.FINANCE : prioritizedCategories[0];
    const categoryMonths = getFolderMonths(defaultCategory);

    if (categoryMonths?.length && (!months || !categoryMonths.includes(months))) {
      setMonths(categoryMonths[categoryMonths.length - 1]);
    }
  }, [reportFolders, prioritizedCategories, months, setMonths, getFolderMonths]);

  const organizationIdParam = searchParams.get("orgId") || reduxOrganizationId;
  const selectedCategory = getSelectedCategory();

  useEffect(() => {
    if (!selectedCategory || !months || !organizationIdParam) {
      return;
    }

    const folderData = filesByFolder?.[selectedCategory];
    const monthLoaded = folderData?.byMonth?.[months];
    const isLoading = filesLoading?.[selectedCategory];

    const fetchKey = `${organizationIdParam}-${selectedCategory}-${months}`;

    if (monthLoaded || isLoading || fetchingFilesRef.current.has(fetchKey)) {
      return;
    }

    fetchingFilesRef.current.add(fetchKey);

    const fetchPromise = dispatch(
      fetchReportFiles({
        organizationId: organizationIdParam,
        organizationName: resolvedOrganizationName,
        folder: selectedCategory,
        months,
      })
    );

    const currentFetchingSet = fetchingFilesRef.current;
    fetchPromise.finally(() => {
      setTimeout(() => {
        currentFetchingSet.delete(fetchKey);
      }, 500);
    });

    return () => {
      currentFetchingSet.delete(fetchKey);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    dispatch,
    selectedCategory,
    months,
    organizationIdParam,
    resolvedOrganizationName,
  ]);

  const currentReportEntry = useMemo(() => {
    if (!selectedCategory || !months) return null;
    const folderData = filesByFolder?.[selectedCategory];
    if (!folderData) return null;
    const byMonth = folderData.byMonth || {};
    return byMonth[months] || null;
  }, [selectedCategory, months, filesByFolder]);

  const currentReportError = selectedCategory ? filesError?.[selectedCategory] || null : null;

  const pdfPathRef = useRef(null);
  const pdfPath = useMemo(() => {
    if (!currentReportEntry || currentReportEntry.error) {
      pdfPathRef.current = null;
      pdfLoadedRef.current = false;
      return null;
    }
    const newPath = currentReportEntry.url || null;
    if (newPath && newPath !== pdfPathRef.current) {
      pdfPathRef.current = newPath;
      pdfLoadedRef.current = false;
    }
    return pdfPathRef.current;
  }, [currentReportEntry]);

  const page = isFinancialSelected || isOperationsSelected || isPayrollSelected ? PAGE_TO_VIEW : 1;

  const handleDownload = useCallback(async () => {
    if (!currentReportEntry?.url) return;

    try {
      const filename =
        currentReportEntry.name ||
        currentReportEntry.url.split("/").pop() ||
        DASHBOARD_CONSTANTS.DEFAULT_DOWNLOAD_FILENAME;
      await dispatch(downloadFile({ url: currentReportEntry.url, filename }));
    } catch {
      updateFoldersError(DASHBOARD_CONSTANTS.DOWNLOAD_ERROR);
    }
  }, [currentReportEntry, dispatch, updateFoldersError]);

  const currentSummaryKey = selectedCategory && currentReportEntry?.name ? `${selectedCategory}::${currentReportEntry.name}` : null;
  const existingSummary = currentSummaryKey ? summaries?.[selectedCategory]?.[currentReportEntry.name]?.summary || null : null;
  const isSummaryLoadingActive = currentSummaryKey ? summaryLoading?.[currentSummaryKey] || false : false;

  const handleViewSummary = useCallback(() => {
    if (!currentReportEntry || !selectedCategory || !organizationIdParam) return;

    dispatch(setDashboardSummary(null));
    setShowSummaryPopup(true);

    if (existingSummary) {
      dispatch(setDashboardSummary(existingSummary));
      dispatch(setLoading(false));
      return;
    }

    dispatch(setLoading(true));
    dispatch(
      fetchReportSummary({
        organizationId: organizationIdParam,
        organizationName: resolvedOrganizationName,
        folder: selectedCategory,
        fileName: currentReportEntry.name,
      })
    )
      .unwrap()
      .then((result) => {
        dispatch(setDashboardSummary(result?.summary || DASHBOARD_CONSTANTS.SUMMARY_NOT_AVAILABLE));
      })
      .catch(() => {
        dispatch(setDashboardSummary(DASHBOARD_CONSTANTS.SUMMARY_UNABLE_TO_DISPLAY));
      })
      .finally(() => {
        dispatch(setLoading(false));
      });
  }, [currentReportEntry, dispatch, existingSummary, organizationIdParam, resolvedOrganizationName, selectedCategory]);

  const handleMobileMenuToggle = useCallback(() => setIsMobileMenuOpen((prev) => !prev), []);

  const handleMonthChange = useCallback((month) => {
    if (month) {
      setMonths(month);
    }
  }, [setMonths]);

  const handleCategoryChange = useCallback((categoryKey) => {
    if (!categoryKey) return;
    
    const categoryMap = {
      [CATEGORY_NAMES.FINANCE]: () => {
        setFinancialSelected(true);
        setOperationsSelected(false);
        setPayrollSelected(false);
      },
      [CATEGORY_NAMES.OPERATIONS]: () => {
        setFinancialSelected(false);
        setOperationsSelected(true);
        setPayrollSelected(false);
      },
      [CATEGORY_NAMES.PAYROLL]: () => {
        setFinancialSelected(false);
        setOperationsSelected(false);
        setPayrollSelected(true);
      },
    };

    const handler = categoryMap[categoryKey];
    if (handler) {
      handler();
      const monthsForCategory = getFolderMonths(categoryKey);
      if (monthsForCategory?.length) {
        setMonths(monthsForCategory[monthsForCategory.length - 1]);
      }
    }
  }, [setFinancialSelected, setOperationsSelected, setPayrollSelected, setMonths, getFolderMonths]);

  const availableCategories = useMemo(() => {
    return reportFolders
      .filter((folder) => {
        const monthsForFolder = folderMonthsMap[folder.key] || [];
        return monthsForFolder.length > 0;
      })
      .map((folder) => ({
        key: folder.key,
        label: folder.label || folder.key,
      }));
  }, [reportFolders, folderMonthsMap]);

  const handleDashboardSelect = useCallback(
    (dashboard) => {
      setSelectedDashboard(dashboard);
      setFinancialSelected(true);
      setOperationsSelected(false);
      setPayrollSelected(false);
    },
    [setFinancialSelected, setOperationsSelected, setPayrollSelected]
  );

  const dashboardHeader = (
    <DashboardHeader
      onDownload={handleDownload}
      onViewSummary={handleViewSummary}
      organizationName={resolvedOrganizationName}
      reportFolders={reportFolders}
      selectedMonth={months}
      onMonthChange={handleMonthChange}
      selectedCategory={selectedCategory}
    />
  );

  if (!pdfPath) {
    const hasError = foldersError || currentReportError;
    const hasEmptyFolders = !prioritizedCategories.length && !loadingFolders;

    if (hasError || hasEmptyFolders) {
      return (
        <DashboardLayout
          header={dashboardHeader}
          isMobileMenuOpen={isMobileMenuOpen}
          onMobileMenuToggle={handleMobileMenuToggle}
        >
          <EmptyState
            message={
              currentReportError || "No dashboards available for your account."
            }
          />
        </DashboardLayout>
      );
    }
  }

  if (userRole === "admin" && !selectedDashboard) {
    return <DashboardSelector onSelect={handleDashboardSelect} />;
  }

  return (
    <DashboardLayout
      header={dashboardHeader}
      isMobileMenuOpen={isMobileMenuOpen}
      onMobileMenuToggle={handleMobileMenuToggle}
    >
      <div className="dashboard-container">
        <div className="dashboard-content">
          {availableCategories.length > 0 && (
            <CategoryTabs
              categories={availableCategories}
              selectedCategory={selectedCategory}
              onCategoryChange={handleCategoryChange}
            />
          )}
          <div className="dashboard-pdf-container">
            {pdfPath && (
              <MemoizedPdfViewer
                key={pdfPath}
                url={pdfPath}
                pageToView={page}
                onLoadSuccess={() => {
                  pdfLoadedRef.current = true;
                }}
              />
            )}
          </div>
        </div>
      </div>
      <SummaryPopup
        isOpen={showSummaryPopup}
        onClose={() => setShowSummaryPopup(false)}
        dashboardSummary={dashboardSummary}
        selectedMonth={currentReportEntry?.label || months}
        isFinancialSelected={isFinancialSelected}
        isOperationsSelected={isOperationsSelected}
        isPayrollSelected={isPayrollSelected}
        isLoading={isLoading}
        isSummaryLoading={isSummaryLoadingActive}
        organizationName={resolvedOrganizationName}
      />
    </DashboardLayout>
  );
}
