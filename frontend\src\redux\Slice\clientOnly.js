import { createSlice } from "@reduxjs/toolkit";

const initialState = {
  isClient: false,
  isHydrated: false,
  mounted: false,
  error: null,
};

const clientOnlySlice = createSlice({
  name: "clientOnly",
  initialState,
  reducers: {
    // Set client state
    setClient: (state) => {
      state.isClient = true;
    },

    // Set hydration state
    setHydrated: (state) => {
      state.isHydrated = true;
    },

    // Set mounted state
    setMounted: (state, action) => {
      state.mounted = action.payload;
    },

    // Set error
    setError: (state, action) => {
      state.error = action.payload;
    },

    // Clear error
    clearError: (state) => {
      state.error = null;
    },

    // Reset client state
    resetClientState: (state) => {
      state.isClient = false;
      state.isHydrated = false;
      state.mounted = false;
      state.error = null;
    },
  },
});

export const {
  setClient,
  setHydrated,
  setMounted,
  setError,
  clearError,
  resetClientState,
} = clientOnlySlice.actions;

export default clientOnlySlice.reducer;
