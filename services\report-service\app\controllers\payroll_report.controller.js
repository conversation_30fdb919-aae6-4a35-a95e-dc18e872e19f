import PayrollReportService from "../services/payroll_report.service.js";
import { createLogger } from "../utils/logger.utils.js";
import { LOGGER_NAMES } from "../utils/constants/log.constants.js";
import {
  validateRequiredParams,
  handleControllerError,
} from "../utils/controller.utils.js";
import { storePdfToFileService } from "../utils/pdf-storage.utils.js";

const logger = createLogger(LOGGER_NAMES.REPORT_CONTROLLER);
const MONTHS = [
  "January",
  "February",
  "March",
  "April",
  "May",
  "June",
  "July",
  "August",
  "September",
  "October",
  "November",
  "December",
];

const generatePayrollPDF = async (req, res) => {
  try {
    const { organization_id, organization_name, month, year, store_to_blob } =
      req.query;

    const validationError = validateRequiredParams(req.query, [
      "organization_id",
      "month",
      "year",
    ]);
    if (validationError) return res.status(400).json(validationError);

    logger.info(
      `Generating payroll PDF: org=${organization_id}, month=${month}, year=${year}`
    );

    const pdf = await PayrollReportService.generatePayrollReportPDF({
      organization_id,
      organization_name,
      month,
      year,
    });

    const filename = `Payroll_Report_${
      MONTHS[parseInt(month) - 1]
    }_${year}.pdf`;

    // Store PDF to blob storage if requested
    if (store_to_blob === "true") {
      logger.info(
        `Storing payroll PDF to blob storage: org=${organization_id}`
      );
      const storageResult = await storePdfToFileService({
        pdfBuffer: pdf,
        organization_id,
        organization_name,
        service: "payroll",
        month: parseInt(month),
        year: parseInt(year),
        fileName: filename,
        refreshToken: req.cookies?.refresh_token,
      });

      if (storageResult.success) {
        logger.info(
          `Payroll PDF stored successfully: ${storageResult.data?.blobPath}`
        );
      } else {
        logger.warn(
          `Failed to store payroll PDF to blob: ${storageResult.error}`
        );
      }
    }

    res.setHeader("Content-Type", "application/pdf");
    res.setHeader("Content-Disposition", `attachment; filename="${filename}"`);
    res.send(pdf);
  } catch (error) {
    logger.error("Error generating payroll PDF:", error);
    handleControllerError(error, res, "Error generating payroll report PDF");
  }
};

export default { generatePayrollPDF };
