/* Sidebar Container */
.sidebar-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow-y: auto;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.sidebar-container--app {
  height: 100vh;
  width: 16rem; /* w-64 */
}

.sidebar-container--dashboard {
  gap: 0.5rem;
  width: 100%;
  padding: 0.75rem;
  height: 100%;
}

@media (min-width: 1024px) {
  .sidebar-container--dashboard {
    width: 14rem; /* lg:w-56 */
    padding: 1rem;
  }
}

@media (min-width: 1280px) {
  .sidebar-container--dashboard {
    width: 16rem; /* xl:w-64 */
    padding: 1.25rem;
  }
}

/* Sidebar Content */
.sidebar-content {
  flex: 1;
  min-height: 0;
  overflow-y: auto;
  max-height: calc(100vh - 180px);
}

/* Sidebar Navigation */
.sidebar-nav {
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  min-height: 0;
}

/* Navigation Item */
.sidebar-nav-item {
  width: 100%;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1rem;
  border-radius: 0.75rem;
  transition: all 0.2s;
  font-weight: 500;
  font-size: 1.125rem;
  margin-bottom: 0.5rem;
  border: none;
  outline: none;
  cursor: pointer;
}

.sidebar-nav-item:focus {
  outline: 2px solid rgba(255, 255, 255, 0.4);
  outline-offset: 2px;
}

.sidebar-nav-item--active {
  background-color: #6C63FF;
  color: white;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  font-weight: 700;
}

.sidebar-nav-item--inactive {
  color: rgba(255, 255, 255, 0.85);
  background-color: transparent;
}

.sidebar-nav-item--inactive:hover {
  background-color: rgba(108, 99, 255, 0.6);
  color: white;
}

/* Masters Menu */
.sidebar-masters-button {
  width: 100%;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1rem;
  border-radius: 0.75rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-weight: 500;
  font-size: 1rem;
  color: white;
  border: none;
  outline: none;
  cursor: pointer;
  position: relative;
}

.sidebar-masters-button:focus {
  outline: 2px solid rgba(255, 255, 255, 0.4);
  outline-offset: 2px;
}

.sidebar-masters-button--open {
  background-color: #4953B8;
  color: white;
  font-weight: 700;
  box-shadow: 0 4px 12px rgba(108, 99, 255, 0.3);
}

.sidebar-masters-button:not(.sidebar-masters-button--open):hover {
  background-color: rgba(108, 99, 255, 0.8);
  transform: translateX(2px);
}

/* Chevron icon animation */
.sidebar-masters-chevron {
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: center;
  display: inline-block;
}

.sidebar-masters-button--open .sidebar-masters-chevron {
  transform: rotate(180deg);
}

/* Submenu container with smooth expand/collapse */
.sidebar-masters-submenu {
  margin-left: 0.5rem;
  margin-top: 0.5rem;
  background-color: #4953B8;
  border-radius: 0.75rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  padding: 0;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  border: 1px solid #6C63FF;
  overflow: hidden;
  max-height: 0;
  opacity: 0;
  transform: translateY(-10px);
  transition: max-height 0.4s cubic-bezier(0.4, 0, 0.2, 1),
              opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1),
              transform 0.3s cubic-bezier(0.4, 0, 0.2, 1),
              padding 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  pointer-events: none;
}

.sidebar-masters-submenu--open {
  max-height: 500px;
  opacity: 1;
  transform: translateY(0);
  padding: 0.75rem;
  pointer-events: auto;
}

.sidebar-masters-submenu-item {
  color: white;
  font-size: 1rem;
  text-align: left;
  padding: 0.5rem 0.75rem;
  border-radius: 0.375rem;
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  font-weight: 500;
  border: none;
  outline: none;
  cursor: pointer;
  background-color: transparent;
  opacity: 0;
  transform: translateX(-10px);
  animation: fadeInSlide 0.3s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.sidebar-masters-submenu--open .sidebar-masters-submenu-item {
  opacity: 1;
  transform: translateX(0);
}

.sidebar-masters-submenu--open .sidebar-masters-submenu-item:nth-child(1) {
  animation-delay: 0.05s;
}

.sidebar-masters-submenu--open .sidebar-masters-submenu-item:nth-child(2) {
  animation-delay: 0.1s;
}

.sidebar-masters-submenu--open .sidebar-masters-submenu-item:nth-child(3) {
  animation-delay: 0.15s;
}

.sidebar-masters-submenu--open .sidebar-masters-submenu-item:nth-child(4) {
  animation-delay: 0.2s;
}

.sidebar-masters-submenu--open .sidebar-masters-submenu-item:nth-child(5) {
  animation-delay: 0.25s;
}

.sidebar-masters-submenu-item:hover {
  background-color: #5F6DF7;
  color: white;
  transform: translateX(4px);
  box-shadow: 0 2px 8px rgba(108, 99, 255, 0.3);
}

.sidebar-masters-submenu-item:focus {
  outline: 2px solid rgba(255, 255, 255, 0.4);
  outline-offset: 2px;
}

.sidebar-masters-submenu-item--active {
  background-color: #5F6DF7;
  font-weight: 700;
  box-shadow: 0 2px 8px rgba(108, 99, 255, 0.4);
  transform: translateX(4px);
}

@keyframes fadeInSlide {
  from {
    opacity: 0;
    transform: translateX(-10px);
}
  to {
  opacity: 1;
  transform: translateX(0);
}
}


/* Sidebar Footer */
.sidebar-footer {
  padding: 0.5rem;
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  width: 100%;
}

@media (min-width: 640px) {
  .sidebar-footer {
    padding: 0.75rem;
  }
}

/* Loading and Error States */
.sidebar-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem 0;
}

.sidebar-error {
  color: #fca5a5;
  font-size: 0.75rem;
  text-align: center;
  padding: 0.5rem;
}

/* Scrollbar Styles (from existing sidebar.css) */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: #a5b4fc #373fa5;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 8px;
  background: transparent;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #6c63ff 40%, #a5b4fc 100%);
  border-radius: 12px;
  min-height: 40px;
  box-shadow: 0 2px 8px 0 rgba(108, 99, 255, 0.1);
  border: 2px solid #4953b8;
  transition: all 0.3s ease;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #5f6df7 40%, #818cf8 100%);
  box-shadow: 0 4px 12px 0 rgba(108, 99, 255, 0.2);
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #373fa5;
  border-radius: 12px;
}


