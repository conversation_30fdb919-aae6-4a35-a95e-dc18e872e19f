import { createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";
import tokenStorage from "@/lib/tokenStorage";

const FILE_SERVICE_URL = process.env.NEXT_PUBLIC_FILE_SERVICE_URL;
const SYSTEM_API_KEY = process.env.NEXT_PUBLIC_SYSTEM_API_KEY;

/**
 * Get file service client for document API
 */
const getFileServiceClient = () => {
  if (!FILE_SERVICE_URL) {
    throw new Error(
      "NEXT_PUBLIC_FILE_SERVICE_URL environment variable is not configured"
    );
  }

  // Normalize base URL - remove trailing slashes
  const baseURL = FILE_SERVICE_URL.replace(/\/+$/, "");

  const instance = axios.create({
    baseURL: baseURL,
    headers: {
      "Content-Type": "application/json",
    },
    withCredentials: true,
    timeout: 30000,
  });

  instance.interceptors.request.use(
    (config) => {
      if (SYSTEM_API_KEY) {
        config.headers["x-api-key"] = SYSTEM_API_KEY;
      }
      const accessToken = tokenStorage.getAccessToken();
      if (accessToken && !tokenStorage.isTokenExpired(accessToken)) {
        config.headers.Authorization = `Bearer ${accessToken}`;
      }
      return config;
    },
    (error) => Promise.reject(error)
  );

  return instance;
};

/**
 * Get document endpoint path based on base URL
 */
const getDocumentEndpoint = () => {
  const FILE_SERVICE_URL = process.env.NEXT_PUBLIC_FILE_SERVICE_URL || "";
  const baseURL = FILE_SERVICE_URL.replace(/\/+$/, "");
  const endpointPrefix = baseURL.endsWith("/api") ? "" : "/api";
  return `${endpointPrefix}/document`;
};

export const fetchDocumentByFilters = createAsyncThunk(
  "document/fetchByFilters",
  async ({ organization_id, service, month, year }, { rejectWithValue }) => {
    try {
      const fileServiceClient = getFileServiceClient();
      const endpoint = getDocumentEndpoint();

      const response = await fileServiceClient.get(endpoint, {
        params: {
          organization_id,
          service,
          month: Number(month),
          year: Number(year),
        },
      });

      // Always update localStorage - replace with new values or clear if not found
      if (response.data?.success && response.data?.data?.length > 0) {
        const document = response.data.data[0];
        localStorage.setItem("blob_storage_path", document.blob_storage_path || "");
        localStorage.setItem("document_summary", document.summary ? JSON.stringify(document.summary) : "");
        
        return {
          success: true,
          document,
          blob_storage_path: document.blob_storage_path,
          summary: document.summary,
        };
      } else {
        // Clear localStorage if document not found
        localStorage.setItem("blob_storage_path", "");
        localStorage.setItem("document_summary", "");
        
        return {
          success: false,
          message: "Document not found",
        };
      }
    } catch (error) {
      // Clear localStorage on error
      localStorage.setItem("blob_storage_path", "");
      localStorage.setItem("document_summary", "");
      
      const errorMessage =
        error.response?.data?.message ||
        error.response?.data?.error ||
        error.message ||
        "Failed to fetch document";
      
      return rejectWithValue(errorMessage);
    }
  }
);

