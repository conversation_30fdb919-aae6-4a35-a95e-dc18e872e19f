// Work around pdf-parse index.js debug block by importing the library file directly
import pdf from "pdf-parse/lib/pdf-parse.js";
import { ERROR_MESSAGES } from "./constants/error.constants.js";

export async function extractTextFromPdfBuffer(buffer) {
  if (!Buffer.isBuffer(buffer)) {
    throw new Error(
      `${ERROR_MESSAGES.GENERAL.PDF_BUFFER_EXPECTED}: ${typeof buffer}`
    );
  }

  const data = await pdf(buffer);
  return data.text.replace(/\r/g, "").trim();
}
