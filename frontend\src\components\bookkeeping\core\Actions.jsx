"use client";

import { memo } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Save, X, Send } from "lucide-react";
import { BOOKCLOSURE_CONSTANTS } from "@/utils/constants/bookclosure";

const ActionButtons = memo(function ActionButtons({
  isSubmitting = false,
  isDrafting = false,
  onSaveAsDraft,
  onCancel,
  onSubmit,
}) {
  return (
    <div className="bookkeeping-actions">
      <Button
        onClick={onCancel}
        variant="outline"
        className="bookkeeping-actions-cancel"
        disabled={isSubmitting || isDrafting}
      >
        <X className="w-4 h-4 mr-2" />
        {BOOKCLOSURE_CONSTANTS.ACTION_BUTTONS.CANCEL}
      </Button>
      <div className="bookkeeping-actions-group">
        <Button
          onClick={onSaveAsDraft}
          variant="outline"
          className="bookkeeping-actions-draft"
          disabled={isSubmitting || isDrafting}
        >
          <Save className="w-4 h-4 mr-2" />
          {isDrafting
            ? BOOKCLOSURE_CONSTANTS.ACTION_BUTTONS.SAVING
            : BOOKCLOSURE_CONSTANTS.ACTION_BUTTONS.SAVE_AS_DRAFT}
        </Button>
        <Button
          onClick={onSubmit}
          className="bookkeeping-actions-submit"
          disabled={isSubmitting || isDrafting}
        >
          <Send className="w-4 h-4 mr-2" />
          {isSubmitting
            ? BOOKCLOSURE_CONSTANTS.ACTION_BUTTONS.SUBMITTING
            : BOOKCLOSURE_CONSTANTS.ACTION_BUTTONS.SUBMIT}
        </Button>
      </div>
    </div>
  );
});

ActionButtons.displayName = "ActionButtons";

export default ActionButtons;
