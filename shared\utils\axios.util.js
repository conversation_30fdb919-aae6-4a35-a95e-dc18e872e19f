import axios from "axios";

const DEFAULT_TIMEOUT_MS = 30000;
const DEFAULT_HEADERS = {
  "Content-Type": "application/json",
};

const defaultClient = axios.create({
  timeout: DEFAULT_TIMEOUT_MS,
  headers: DEFAULT_HEADERS,
});

defaultClient.interceptors.request.use(
  (config) => config,
  (error) => Promise.reject(error)
);

defaultClient.interceptors.response.use(
  (response) => response,
  (error) => Promise.reject(error)
);

export const createAxiosClient = (options = {}) => {
  const { timeout = DEFAULT_TIMEOUT_MS, headers = {}, ...rest } = options;

  return axios.create({
    timeout,
    headers: { ...DEFAULT_HEADERS, ...headers },
    ...rest,
  });
};

const mergeHeaders = (headers = {}) => {
  const finalHeaders = { ...DEFAULT_HEADERS, ...headers };

  if (!("Accept" in finalHeaders)) {
    finalHeaders.Accept = "application/json";
  }

  return finalHeaders;
};

const withRefreshTokenCookie = (refreshToken, headers = {}) => {
  const mergedHeaders = mergeHeaders(headers);

  if (refreshToken) {
    mergedHeaders.Cookie = `refresh_token=${refreshToken}`;
  }

  return mergedHeaders;
};

const isPayloadlessMethod = (method) =>
  ["get", "delete", "head", "options"].includes(method);

export const apiRequest = async (
  method,
  url,
  data = {},
  headers = {},
  config = {},
  client = defaultClient
) => {
  const httpMethod = method.toLowerCase();

  const requestConfig = {
    method: httpMethod,
    url,
    headers: mergeHeaders(headers),
    timeout: config.timeout ?? client.defaults.timeout ?? DEFAULT_TIMEOUT_MS,
    ...config,
  };

  if (isPayloadlessMethod(httpMethod)) {
    requestConfig.params = data;
  } else {
    requestConfig.data = data;
  }

  const response = await client(requestConfig);
  return response.data;
};

export const httpGet = (url, config = {}, client = defaultClient) =>
  client.get(url, config);

export const httpPost = (url, data, config = {}, client = defaultClient) =>
  client.post(url, data, config);

export const httpPostWithRefreshToken = (
  url,
  data,
  refreshToken,
  config = {},
  client = defaultClient
) => {
  const finalConfig = {
    ...config,
    headers: withRefreshTokenCookie(refreshToken, config.headers),
  };

  return client.post(url, data, finalConfig);
};

export const httpPut = (url, data, config = {}, client = defaultClient) =>
  client.put(url, data, config);

export const httpPatch = (url, data, config = {}, client = defaultClient) =>
  client.patch(url, data, config);

export const httpDelete = (url, config = {}, client = defaultClient) =>
  client.delete(url, config);

export const axiosClient = defaultClient;

export default {
  axiosClient,
  createAxiosClient,
  apiRequest,
  get: httpGet,
  post: httpPost,
  postWithRefreshToken: httpPostWithRefreshToken,
  put: httpPut,
  patch: httpPatch,
  delete: httpDelete,
};
