"use client";

import React, { useCallback, useMemo } from "react";
import { useRouter } from "next/navigation";
import { <PERSON><PERSON>ef<PERSON>, FileDown } from "lucide-react";
import { Button } from "../../ui/button";
import AnimatedSelect from "./AnimatedSelect";
import {
  formatMonthLabel,
  createFolderMonthsMap,
  normalizeMonths,
  parseMonthValue,
} from "@/utils/sidebarUtils";
import { DASHBOARD_CONSTANTS } from "@/utils/constants/dashboard";
import "@/styles/dashboard.css";

export default function DashboardHeader({
  onDownload,
  onViewSummary,
  organizationName,
  reportFolders = [],
  selectedMonth,
  onMonthChange,
  selectedCategory,
  className = "",
}) {
  const router = useRouter();

  const handleBack = useCallback(() => {
    router.push("/listing");
  }, [router]);

  const folderMonthsMap = useMemo(
    () => createFolderMonthsMap(reportFolders),
    [reportFolders]
  );

  const availableMonths = useMemo(() => {
    if (!selectedCategory || !folderMonthsMap[selectedCategory]) {
      return [];
    }
    const rawMonths = folderMonthsMap[selectedCategory] || [];
    const normalized = normalizeMonths(rawMonths, formatMonthLabel);
    
    // Sort months in descending order (December to January)
    return normalized.sort((a, b) => {
      const firstMonthDate = parseMonthValue(a.value, DASHBOARD_CONSTANTS.MONTH_ABBREVIATION_TO_INDEX);
      const secondMonthDate = parseMonthValue(b.value, DASHBOARD_CONSTANTS.MONTH_ABBREVIATION_TO_INDEX);
      
      // Sort in descending order (newest first)
      return secondMonthDate.getTime() - firstMonthDate.getTime();
    });
  }, [selectedCategory, folderMonthsMap]);

  const pageTitle = organizationName || "Financial Summary";

  return (
    <header className={`dashboard-header ${className}`}>
      <div className="dashboard-header-content">
        {/* Left Section: Back Icon, Title, Date Filter */}
        <div className="dashboard-header-left">
          <button
            onClick={handleBack}
            className="dashboard-header-back-icon"
            aria-label="Back to Listing"
            title="Back to Listing"
          >
            <ArrowLeft className="w-5 h-5" />
          </button>

          <div className="dashboard-header-title-section">
            <h1 className="dashboard-header-title">{pageTitle}</h1>
          </div>

          {availableMonths.length > 0 && (
            <div className="dashboard-header-date-filter">
              <AnimatedSelect
                options={availableMonths}
                value={selectedMonth}
                onChange={onMonthChange}
                placeholder="Select month..."
              />
            </div>
          )}
        </div>

        {/* Right Section: Action Buttons */}
        <div className="dashboard-header-right">
          <Button
            variant="default"
            onClick={onViewSummary}
            className="dashboard-header-view-insights-btn"
          >
            <span>View Insights</span>
          </Button>

          <Button
            variant="outline"
            onClick={onDownload}
            className="dashboard-header-export-btn"
          >
            <FileDown className="w-4 h-4" />
            <span className="hidden sm:inline">Export Report</span>
            <span className="sm:hidden">Export</span>
          </Button>
        </div>
      </div>
    </header>
  );
}

