import { COMMON_CONSTANTS } from "../constants";
import { MESSAGES } from "../constants";

export const readFileAsDataUrl = (file) =>
  new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => {
      if (typeof reader.result === "string") {
        resolve(reader.result);
      } else {
        reject(new Error(MESSAGES.FILE.UNABLE_TO_READ_FILE));
      }
    };
    reader.onerror = () =>
      reject(reader.error || new Error(MESSAGES.FILE.FILE_READ_ERROR));
    reader.readAsDataURL(file);
  });

export const compressPng = (file) =>
  new Promise((resolve, reject) => {
    const objectUrl = URL.createObjectURL(file);
    const img = new Image();
    img.onload = () => {
      try {
        const MAX_DIMENSION = 512;
        const largestSide = Math.max(img.width, img.height);
        const scale =
          largestSide > MAX_DIMENSION ? MAX_DIMENSION / largestSide : 1;

        const canvas = document.createElement("canvas");
        canvas.width = Math.max(1, Math.round(img.width * scale));
        canvas.height = Math.max(1, Math.round(img.height * scale));

        const context = canvas.getContext("2d");
        context.drawImage(img, 0, 0, canvas.width, canvas.height);
        canvas.toBlob(
          (blob) => {
            URL.revokeObjectURL(objectUrl);
            if (!blob) {
              reject(new Error(MESSAGES.FILE.FAILED_TO_COMPRESS_LOGO));
              return;
            }
            const compressedFile = new File([blob], file.name, {
              type: COMMON_CONSTANTS.FILES.PNG_MIME_TYPE,
            });
            resolve(readFileAsDataUrl(compressedFile));
          },
          COMMON_CONSTANTS.FILES.PNG_MIME_TYPE,
          0.92
        );
      } catch (error) {
        URL.revokeObjectURL(objectUrl);
        reject(error);
      }
    };
    img.onerror = () => {
      URL.revokeObjectURL(objectUrl);
      reject(new Error(MESSAGES.FILE.FAILED_TO_LOAD_LOGO_FOR_COMPRESSION));
    };
    img.src = objectUrl;
  });

export const prepareLogoPayload = async (file) => {
  if (!(file instanceof File)) {
    return null;
  }

  if (file.type === COMMON_CONSTANTS.FILES.SVG_MIME_TYPE) {
    return readFileAsDataUrl(file);
  }

  if (file.size <= COMMON_CONSTANTS.FILES.MAX_INLINE_BYTES) {
    return readFileAsDataUrl(file);
  }

  return compressPng(file);
};

export const getDefaultLogoAsBase64 = async () => {
  try {
    const response = await fetch("/logo-dark.png");
    const blob = await response.blob();
    const file = new File([blob], "logo-dark.png", { type: blob.type });
    return await prepareLogoPayload(file);
  } catch (error) {
    console.error("Failed to load default logo:", error);
    return null;
  }
};
