"use strict";

const SCHEMA_NAME = "Authentication";
const TABLE_NAME = "app_user";
const TABLE_REFERENCE = { tableName: TABLE_NAME, schema: SCHEMA_NAME };

const ensureSchemaExists = async (queryInterface) => {
  await queryInterface.sequelize.query(
    `CREATE SCHEMA IF NOT EXISTS "${SCHEMA_NAME}"`
  );
};

export const up = async (queryInterface, Sequelize) => {
  await ensureSchemaExists(queryInterface);

  await queryInterface.createTable(
    TABLE_REFERENCE,
    {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true,
      },
      tenant_id: {
        type: Sequelize.UUID,
        allowNull: false,
      },
      email: {
        type: Sequelize.TEXT,
        allowNull: false,
      },
      password_hash: {
        type: Sequelize.TEXT,
        allowNull: false,
      },
      full_name: {
        type: Sequelize.TEXT,
      },
      phone_number: {
        type: Sequelize.TEXT,
      },
      roles: {
        type: Sequelize.ARRAY(Sequelize.TEXT),
      },
      email_verified: {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
      },
      email_verified_at: {
        type: Sequelize.DATE,
      },
      last_login: {
        type: Sequelize.DATE,
      },
      mfa_enabled: {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
      },
      mfa_secret: {
        type: Sequelize.TEXT,
      },
      mfa_backup_codes: {
        type: Sequelize.TEXT,
      },
      invited_by: {
        type: Sequelize.UUID,
      },
      invited_at: {
        type: Sequelize.DATE,
      },
      is_active: {
        type: Sequelize.BOOLEAN,
        defaultValue: true,
      },
      created_at: {
        type: Sequelize.DATE,
        defaultValue: Sequelize.NOW,
      },
      created_by: {
        type: Sequelize.UUID,
      },
      updated_at: {
        type: Sequelize.DATE,
      },
      updated_by: {
        type: Sequelize.UUID,
      },
    },
    {
      schema: SCHEMA_NAME,
      timestamps: true,
      createdAt: "created_at",
      updatedAt: "updated_at",
    }
  );

  const indexDefinitions = [
    {
      name: "idx_user_tenant",
      fields: ["tenant_id"],
    },
    {
      name: "unique_email_per_tenant",
      unique: true,
      fields: ["tenant_id", "email"],
    },
  ];

  await Promise.all(
    indexDefinitions.map((definition) =>
      queryInterface.addIndex(TABLE_REFERENCE, definition)
    )
  );
};

export const down = async (queryInterface) => {
  await queryInterface.dropTable(TABLE_REFERENCE);
};
