// app/routes/powerBiWorkflow.routes.js
import express from "express";
import { triggerWorkflow } from "../controllers/powerBiWorkflow.controller.js";
import { authMiddleware } from "../middleware/auth.middleware.js";
import { bodySanitizer } from "../middleware/bodySanitizer.middleware.js";

const router = express.Router();

router.post("/trigger", authMiddleware, bodySanitizer, triggerWorkflow);

export default router;
