// REFACTORED: Memoized selectors for chat state to prevent unnecessary re-renders
import { createSelector } from "@reduxjs/toolkit";

// Base selector for chat state
const selectChatState = (state) => state.chat;

// Memoized selector for chat data
export const selectChatData = createSelector(
  [selectChatState],
  (chatState) => ({
    dashboardSummary: chatState?.dashboardSummary || null,
    isLoading: chatState?.ui?.isLoading || false,
    session: chatState?.session || {
      id: null,
      filename: null,
      isActive: false,
    },
    messages: chatState?.messages || [],
    hasWelcomed: chatState?.ui?.hasWelcomed || false,
  })
);

// Individual memoized selectors
export const selectDashboardSummary = createSelector(
  [selectChatState],
  (state) => state?.dashboardSummary || null
);

export const selectIsLoading = createSelector(
  [selectChatState],
  (state) => state?.ui?.isLoading || false
);

export const selectSession = createSelector(
  [selectChatState],
  (state) => state?.session || { id: null, filename: null, isActive: false }
);

export const selectMessages = createSelector(
  [selectChatState],
  (state) => state?.messages || []
);

export const selectHasWelcomed = createSelector(
  [selectChatState],
  (state) => state?.ui?.hasWelcomed || false
);
