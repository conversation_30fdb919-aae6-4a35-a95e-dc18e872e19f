// app/middleware/auth.middleware.js
/**
 * API Key authentication middleware
 * REFACTORED: Uses cached env vars, supports header and query param
 */
import logger from "../../config/logger.config.js";
import {
  STATUS_CODE_UNAUTHORIZED,
  STATUS_CODE_INTERNAL_SERVER_ERROR,
} from "../utils/status_code.utils.js";
import {
  AUTH_MESSAGES,
  AUTH_LOG_MESSAGES,
} from "../utils/constants/auth.constants.js";
import { errorResponse } from "../utils/response.util.js";
import { getSystemApiKey } from "../utils/env.util.js";

/**
 * API Key authentication middleware
 * Validates x-api-key header or apiKey query parameter
 * OPTIMIZED: Uses cached environment variables
 */
export const authMiddleware = async (req, res, next) => {
  try {
    // Get API key from header or query parameter
    // REFACTORED: Early return pattern - check header first, then query
    const apiKey = req.headers["x-api-key"] || req.query.apiKey;

    // Guard clause: validate API key is provided
    if (!apiKey) {
      logger.warn(AUTH_LOG_MESSAGES.API_KEY_MISSING);
      return res.status(STATUS_CODE_UNAUTHORIZED).json(
        errorResponse(AUTH_MESSAGES.UNAUTHORIZED, AUTH_MESSAGES.API_KEY_REQUIRED)
      );
    }

    // PERFORMANCE: Get system API key from cache (no redundant process.env reads)
    let systemApiKey;
    try {
      systemApiKey = getSystemApiKey();
    } catch (error) {
      logger.error(AUTH_LOG_MESSAGES.API_KEY_NOT_CONFIGURED);
      return res.status(STATUS_CODE_INTERNAL_SERVER_ERROR).json(
        errorResponse(
          AUTH_MESSAGES.SERVER_CONFIGURATION_ERROR,
          AUTH_MESSAGES.API_KEY_SERVICE_NOT_CONFIGURED
        )
      );
    }

    // Guard clause: verify the API key matches
    if (apiKey !== systemApiKey) {
      logger.warn(AUTH_LOG_MESSAGES.INVALID_API_KEY);
      return res.status(STATUS_CODE_UNAUTHORIZED).json(
        errorResponse(AUTH_MESSAGES.UNAUTHORIZED, AUTH_MESSAGES.INVALID_API_KEY)
      );
    }

    logger.info(AUTH_LOG_MESSAGES.API_KEY_VERIFIED);
    next();
  } catch (error) {
    logger.error(`${AUTH_LOG_MESSAGES.AUTH_MIDDLEWARE_ERROR}: ${error.message}`);
    return res.status(STATUS_CODE_INTERNAL_SERVER_ERROR).json(
      errorResponse(
        AUTH_MESSAGES.API_KEY_VERIFICATION_FAILED,
        error.message
      )
    );
  }
};

