import { createLogger } from "./logger.utils.js";
import { DATABASE_REPOSITORY_LOGS } from "./constants/messages.constants.js";

const logger = createLogger("DATABASE_UTILS");

/**
 * Create a new record
 * @param {Object} Model - Sequelize model
 * @param {Object} data - Data to create
 * @param {Object} options - Additional options
 * @returns {Promise<Object>} Created record
 */
export const create = async (Model, data, options = {}) => {
  try {
    const record = await Model.create(data, options);
    logger.info(DATABASE_REPOSITORY_LOGS.RECORD_CREATED(Model.name, record.id));
    return record;
  } catch (error) {
    logger.error(
      `database.utils.js:create - ${DATABASE_REPOSITORY_LOGS.CREATE_ERROR(
        Model.name,
        error.message
      )}`
    );
    throw error;
  }
};

/**
 * Find record by ID
 * @param {Object} Model - Sequelize model
 * @param {number|string} id - Record ID
 * @param {Object} options - Additional options
 * @returns {Promise<Object|null>} Found record or null
 */
export const findById = async (Model, id, options = {}) => {
  try {
    const record = await Model.findByPk(id, options);
    if (record) {
      logger.debug(DATABASE_REPOSITORY_LOGS.RECORD_FOUND(Model.name, id));
    } else {
      logger.debug(DATABASE_REPOSITORY_LOGS.RECORD_NOT_FOUND(Model.name, id));
    }
    return record;
  } catch (error) {
    logger.error(
      `database.utils.js:findById - ${DATABASE_REPOSITORY_LOGS.FIND_ERROR(
        Model.name,
        id,
        error.message
      )}`
    );
    throw error;
  }
};

/**
 * Find one record with conditions
 * @param {Object} Model - Sequelize model
 * @param {Object} where - Where conditions
 * @param {Object} options - Additional options
 * @returns {Promise<Object|null>} Found record or null
 */
export const findOne = async (Model, where, options = {}) => {
  try {
    const record = await Model.findOne({ where, ...options });
    if (record) {
      logger.debug(
        DATABASE_REPOSITORY_LOGS.RECORD_FOUND(Model.name, "by conditions")
      );
    } else {
      logger.debug(
        DATABASE_REPOSITORY_LOGS.RECORD_NOT_FOUND(Model.name, "by conditions")
      );
    }
    return record;
  } catch (error) {
    logger.error(
      `database.utils.js:findOne - ${DATABASE_REPOSITORY_LOGS.FIND_ERROR(
        Model.name,
        "by conditions",
        error.message
      )}`
    );
    throw error;
  }
};

/**
 * Update record by ID
 * @param {Object} Model - Sequelize model
 * @param {number|string} id - Record ID
 * @param {Object} data - Data to update
 * @param {Object} options - Additional options
 * @returns {Promise<Object|null>} Updated record or null
 */
export const update = async (Model, id, data, options = {}) => {
  try {
    const [affectedRows] = await Model.update(data, {
      where: { id },
      ...options,
    });
    if (affectedRows > 0) {
      const updatedRecord = await Model.findByPk(id, options);
      logger.info(DATABASE_REPOSITORY_LOGS.RECORD_UPDATED(Model.name, id));
      return updatedRecord;
    } else {
      logger.warn(DATABASE_REPOSITORY_LOGS.NO_RECORD_TO_UPDATE(Model.name, id));
      return null;
    }
  } catch (error) {
    logger.error(
      `database.utils.js:update - ${DATABASE_REPOSITORY_LOGS.UPDATE_ERROR(
        Model.name,
        id,
        error.message
      )}`
    );
    throw error;
  }
};

/**
 * Soft delete record by ID
 * @param {Object} Model - Sequelize model
 * @param {number|string} id - Record ID
 * @param {Object} options - Additional options
 * @returns {Promise<boolean>} Success status
 */
export const softDelete = async (Model, id, options = {}) => {
  try {
    const [affectedRows] = await Model.update(
      { is_deleted: true, deleted_at: new Date() },
      { where: { id }, ...options }
    );
    if (affectedRows > 0) {
      logger.info(DATABASE_REPOSITORY_LOGS.RECORD_SOFT_DELETED(Model.name, id));
      return true;
    } else {
      logger.warn(DATABASE_REPOSITORY_LOGS.NO_RECORD_TO_DELETE(Model.name, id));
      return false;
    }
  } catch (error) {
    logger.error(
      `database.utils.js:softDelete - ${DATABASE_REPOSITORY_LOGS.DELETE_ERROR(
        Model.name,
        id,
        error.message
      )}`
    );
    throw error;
  }
};

/**
 * Find all records
 * @param {Object} Model - Sequelize model
 * @param {Object} options - Query options
 * @returns {Promise<Array>} Found records
 */
export const findAll = async (Model, options = {}) => {
  try {
    const records = await Model.findAll(options);
    logger.debug(
      DATABASE_REPOSITORY_LOGS.RECORDS_FOUND(Model.name, records.length)
    );
    return records;
  } catch (error) {
    logger.error(
      `database.utils.js:findAll - ${DATABASE_REPOSITORY_LOGS.FIND_ALL_ERROR(
        Model.name,
        error.message
      )}`
    );
    throw error;
  }
};

/**
 * Paginate records
 * @param {Object} Model - Sequelize model
 * @param {Object} options - Pagination options
 * @returns {Promise<Object>} Paginated result
 */
export const paginate = async (Model, options = {}) => {
  try {
    const {
      page = 1,
      limit = 10,
      offset = (page - 1) * limit,
      ...queryOptions
    } = options;

    const { count, rows } = await Model.findAndCountAll({
      ...queryOptions,
      limit: parseInt(limit),
      offset: parseInt(offset),
    });

    const totalPages = Math.ceil(count / limit);
    const hasNext = page < totalPages;
    const hasPrev = page > 1;

    logger.debug(
      DATABASE_REPOSITORY_LOGS.RECORDS_PAGINATED(Model.name, page, limit, count)
    );

    return {
      data: rows,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: count,
        totalPages,
        hasNext,
        hasPrev,
      },
    };
  } catch (error) {
    logger.error(
      `database.utils.js:paginate - ${DATABASE_REPOSITORY_LOGS.PAGINATION_ERROR(
        Model.name,
        error.message
      )}`
    );
    throw error;
  }
};

/**
 * Check if record exists
 * @param {Object} Model - Sequelize model
 * @param {number|string} id - Record ID
 * @param {Object} options - Additional options
 * @returns {Promise<boolean>} Exists status
 */
export const exists = async (Model, id, options = {}) => {
  try {
    const count = await Model.count({ where: { id }, ...options });
    const exists = count > 0;
    logger.debug(
      DATABASE_REPOSITORY_LOGS.RECORD_EXISTS_CHECK(Model.name, id, exists)
    );
    return exists;
  } catch (error) {
    logger.error(
      `database.utils.js:exists - ${DATABASE_REPOSITORY_LOGS.EXISTS_CHECK_ERROR(
        Model.name,
        id,
        error.message
      )}`
    );
    throw error;
  }
};

/**
 * Count records
 * @param {Object} Model - Sequelize model
 * @param {Object} whereConditions - Where conditions
 * @param {Object} options - Additional options
 * @returns {Promise<number>} Count of records
 */
export const count = async (Model, whereConditions = {}, options = {}) => {
  try {
    const count = await Model.count({
      where: whereConditions,
      ...options,
    });
    logger.debug(DATABASE_REPOSITORY_LOGS.RECORDS_COUNTED(Model.name, count));
    return count;
  } catch (error) {
    logger.error(
      `database.utils.js:count - ${DATABASE_REPOSITORY_LOGS.COUNT_ERROR(
        Model.name,
        error.message
      )}`
    );
    throw error;
  }
};

/**
 * Create one record in specific organization schema
 * @param {Object} Model - Sequelize model
 * @param {string} schemaName - Schema name
 * @param {Object} data - Data to create
 * @param {Object} options - Additional options
 * @returns {Promise<Object>} Created record
 */
export const createInSchema = async (Model, schemaName, data, options = {}) => {
  try {
    // Add timestamp columns if they don't exist and the model has them
    const enhancedData = { ...data };
    const now = new Date();

    // Check if model has created_at and updated_at columns
    if (Model.rawAttributes.created_at && !enhancedData.created_at) {
      enhancedData.created_at = now;
    }
    if (Model.rawAttributes.updated_at && !enhancedData.updated_at) {
      enhancedData.updated_at = now;
    }

    // Build INSERT query for the specific schema
    const columns = Object.keys(enhancedData);
    const values = columns.map((col) => `:${col}`).join(", ");
    const columnNames = columns.map((col) => `"${col}"`).join(", ");

    const query = `
      INSERT INTO "${schemaName}"."${Model.tableName}" (${columnNames})
      VALUES (${values})
      RETURNING *
    `;
    // Execute the query
    const [results] = await Model.sequelize.query(query, {
      replacements: enhancedData,
      type: Model.sequelize.QueryTypes.INSERT,
    });

    logger.info(`Created ${Model.name} record in schema: ${schemaName}`);
    return results[0];
  } catch (error) {
    logger.error(
      `Error creating ${Model.name} record in schema: ${error.message}`
    );
    throw error;
  }
};

/**
 * Update one record in specific organization schema
 * @param {Object} Model - Sequelize model
 * @param {string} schemaName - Schema name
 * @param {string|number} id - Record ID
 * @param {Object} data - Data to update
 * @param {Object} options - Additional options
 * @returns {Promise<Object|null>} Updated record or null
 */
export const updateInSchema = async (
  Model,
  schemaName,
  id,
  data,
  options = {}
) => {
  try {
    // Add updated_at timestamp if the model has it and it's not provided
    const enhancedData = { ...data };
    if (Model.rawAttributes.updated_at && !enhancedData.updated_at) {
      enhancedData.updated_at = new Date();
    }

    // Build SET clause from update data
    const setClause = Object.keys(enhancedData)
      .map((key) => `"${key}" = :${key}`)
      .join(", ");

    const query = `
      UPDATE "${schemaName}"."${Model.tableName}"
      SET ${setClause}
      WHERE "id" = :id
      RETURNING *
    `;
    // Execute the query
    const [results] = await Model.sequelize.query(query, {
      replacements: { ...enhancedData, id },
      type: Model.sequelize.QueryTypes.UPDATE,
    });

    if (results && results.length > 0) {
      logger.info(
        `Updated ${Model.name} record with ID ${id} in schema: ${schemaName}`
      );
      return results[0];
    } else {
      logger.info(`Record with ID ${id} not found in schema: ${schemaName}`);
      return null;
    }
  } catch (error) {
    logger.error(
      `Error updating ${Model.name} record in schema: ${error.message}`
    );
    throw error;
  }
};

/**
 * Find one record in specific organization schema
 * @param {Object} Model - Sequelize model
 * @param {string} schemaName - Schema name
 * @param {Object} where - Where conditions
 * @param {Object} options - Additional options
 * @returns {Promise<Object|null>} Found record or null
 */
export const findOneInSchema = async (
  Model,
  schemaName,
  where,
  options = {}
) => {
  try {
    // Build WHERE clause from where conditions
    const whereClause = Object.keys(where)
      .map((key) => `"${key}" = :${key}`)
      .join(" AND ");

    // Build SELECT clause from model attributes
    const attributes = Object.keys(Model.rawAttributes);
    const selectClause = attributes.map((attr) => `"${attr}"`).join(", ");

    // Build the complete query
    const query = `
      SELECT ${selectClause}
      FROM "${schemaName}"."${Model.tableName}"
      WHERE ${whereClause}
      LIMIT 1
    `;

    // Execute the query
    const [results] = await Model.sequelize.query(query, {
      replacements: where,
      type: Model.sequelize.QueryTypes.SELECT,
    });

    if (results) {
      logger.debug(`Found ${Model.name} record in schema: ${schemaName}`);
      return results;
    } else {
      logger.debug(`No ${Model.name} record found in schema: ${schemaName}`);
      return null;
    }
  } catch (error) {
    logger.error(
      `Error finding ${Model.name} record in schema: ${error.message}`
    );
    throw error;
  }
};

/**
 * Create multiple records in specific organization schema
 * @param {Object} Model - Sequelize model
 * @param {string} schemaName - Schema name
 * @param {Array} data - Array of data to create
 * @param {Object} options - Additional options
 * @returns {Promise<Array>} Created records
 */
export const createBulkInSchema = async (
  Model,
  schemaName,
  data,
  options = {}
) => {
  try {
    if (!data || data.length === 0) {
      return [];
    }

    // Add timestamp columns if they don't exist and the model has them
    const now = new Date();
    const enhancedData = data.map((record) => {
      const enhanced = { ...record };

      // Check if model has created_at and updated_at columns
      if (Model.rawAttributes.created_at && !enhanced.created_at) {
        enhanced.created_at = now;
      }
      if (Model.rawAttributes.updated_at && !enhanced.updated_at) {
        enhanced.updated_at = now;
      }

      return enhanced;
    });

    // Get all unique columns from all records
    const allColumns = new Set();
    enhancedData.forEach((record) => {
      Object.keys(record).forEach((key) => allColumns.add(key));
    });

    const columns = Array.from(allColumns);
    const columnNames = columns.map((col) => `"${col}"`).join(", ");

    // Build VALUES clause for multiple records
    const valuesClauses = enhancedData
      .map((record, index) => {
        const values = columns
          .map((col) => {
            const value = record[col];
            if (value === null || value === undefined) {
              return "NULL";
            } else if (typeof value === "string") {
              return `'${value.replace(/'/g, "''")}'`;
            } else if (value instanceof Date) {
              return `'${value.toISOString()}'`;
            } else {
              return value;
            }
          })
          .join(", ");
        return `(${values})`;
      })
      .join(", ");

    const query = `
      INSERT INTO "${schemaName}"."${Model.tableName}" (${columnNames})
      VALUES ${valuesClauses}
      RETURNING *
    `;

    // Execute the query
    const [results] = await Model.sequelize.query(query, {
      type: Model.sequelize.QueryTypes.INSERT,
    });

    logger.info(
      `Created ${enhancedData.length} ${Model.name} records in schema: ${schemaName}`
    );
    return results;
  } catch (error) {
    logger.error(
      `Error creating bulk ${Model.name} records in schema: ${error.message}`
    );
    throw error;
  }
};

/**
 * Find or create a record in specific organization schema
 * @param {Object} Model - Sequelize model
 * @param {string} schemaName - Schema name
 * @param {Object} where - Where conditions
 * @param {Object} defaults - Default values for creation
 * @param {Object} options - Additional options
 * @returns {Promise<Array>} [record, created] tuple
 */
export const findOrCreateInSchema = async (
  Model,
  schemaName,
  where,
  defaults,
  options = {}
) => {
  try {
    // First try to find the record
    const whereClause = Object.keys(where)
      .map((key) => `"${key}" = :${key}`)
      .join(" AND ");

    // Build SELECT clause from model attributes
    const attributes = Object.keys(Model.rawAttributes);
    const selectClause = attributes.map((attr) => `"${attr}"`).join(", ");

    const findQuery = `
      SELECT ${selectClause}
      FROM "${schemaName}"."${Model.tableName}"
      WHERE ${whereClause}
      LIMIT 1
    `;
    const [findResults] = await Model.sequelize.query(findQuery, {
      replacements: where,
      type: Model.sequelize.QueryTypes.SELECT,
    });

    if (findResults && findResults.length > 0) {
      return [findResults[0], false];
    }

    // Record not found, create it
    const createData = { ...where, ...defaults };

    // Add timestamp columns if they don't exist and the model has them
    const now = new Date();
    if (Model.rawAttributes.created_at && !createData.created_at) {
      createData.created_at = now;
    }
    if (Model.rawAttributes.updated_at && !createData.updated_at) {
      createData.updated_at = now;
    }

    // Build INSERT query
    const columns = Object.keys(createData);
    const values = columns.map((col) => `:${col}`).join(", ");
    const columnNames = columns.map((col) => `"${col}"`).join(", ");

    const createQuery = `
      INSERT INTO "${schemaName}"."${Model.tableName}" (${columnNames})
      VALUES (${values})
      RETURNING *
    `;
    const [createResults] = await Model.sequelize.query(createQuery, {
      replacements: createData,
      type: Model.sequelize.QueryTypes.INSERT,
    });

    return [createResults[0], true];
  } catch (error) {
    logger.error(
      `Error finding or creating ${Model.name} record in schema: ${error.message}`
    );
    throw error;
  }
};

export default {
  create,
  findById,
  findOne,
  update,
  softDelete,
  findAll,
  paginate,
  exists,
  count,
  createInSchema,
  updateInSchema,
  findOneInSchema,
  createBulkInSchema,
  findOrCreateInSchema,
};
