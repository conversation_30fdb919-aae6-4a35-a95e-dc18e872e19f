"use client";

import { useRouter } from "next/navigation";
import AddForm from "@/components/common/AddForm";
import { organizationFields } from "@/utils/data/organizations";
import {
  ORGANIZATION_CONSTANTS,
  ORGANIZATION_FIELD_CONSTANTS,
} from "@/utils/constants/organization";
import { useDispatch } from "react-redux";
import {
  createOrganization,
  checkSikkaConnection,
} from "@/redux/Thunks/organization";
import { useToast } from "@/components/ui/toast";
import { extractApiErrorMessage } from "@/utils/errorHandler";
import Loader from "@/components/common/Loader";
import { COMMON_CONSTANTS as CONST } from "@/utils/constants";
import {
  prepareLogoPayload,
  getDefaultLogoAsBase64,
} from "@/utils/methods/file";
import { useState, useEffect } from "react";
import { COMMON_CONSTANTS } from "@/utils/constants";

export default function AddOrganizationPage() {
  const router = useRouter();
  const dispatch = useDispatch();
  const { addToast } = useToast();
  const [isAdding, setIsAdding] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [isCheckingConnection, setIsCheckingConnection] = useState(false);
  const [connectionVerified, setConnectionVerified] = useState(false);
  const [verifiedOfficeId, setVerifiedOfficeId] = useState(null);

  // Page loader overlay - shows when navigating to the page
  useEffect(() => {
    // Simulate initial page load
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 500); // 0.5-1 second loading state

    return () => clearTimeout(timer);
  }, []);

  // Check Sikka connection when office_id reaches 6 characters
  // const handleCheckSikkaConnection = async (officeId) => {
  //   if (
  //     !officeId ||
  //     officeId.length !== ORGANIZATION_CONSTANTS.SIKKA_ID_DIGITS
  //   ) {
  //     setConnectionVerified(false);
  //     setVerifiedOfficeId(null);
  //     return;
  //   }

  //   setIsCheckingConnection(true);
  //   setConnectionVerified(false);
  //   setVerifiedOfficeId(null);

  //   try {
  //     const resultAction = await dispatch(checkSikkaConnection(officeId));
  //     if (checkSikkaConnection.fulfilled.match(resultAction)) {
  //       const data = resultAction.payload;
  //       if (data.success) {
  //         setConnectionVerified(true);
  //         setVerifiedOfficeId(officeId);
  //         addToast(
  //           data.message ||
  //             ORGANIZATION_FIELD_CONSTANTS.OFFICE_ID.CONNECTION
  //               .VERIFIED_SUCCESS,
  //           CONST.TOAST_TYPE.SUCCESS
  //         );
  //       } else {
  //         setConnectionVerified(false);
  //         setVerifiedOfficeId(null);
  //         addToast(
  //           data.message ||
  //             ORGANIZATION_FIELD_CONSTANTS.OFFICE_ID.CONNECTION
  //               .VERIFICATION_FAILED,
  //           CONST.TOAST_TYPE.ERROR
  //         );
  //       }
  //     } else {
  //       setConnectionVerified(false);
  //       setVerifiedOfficeId(null);
  //       const errorMessage = extractApiErrorMessage(resultAction.payload);
  //       addToast(
  //         errorMessage ||
  //           ORGANIZATION_FIELD_CONSTANTS.OFFICE_ID.CONNECTION
  //             .VERIFICATION_FAILED,
  //         CONST.TOAST_TYPE.ERROR
  //       );
  //     }
  //   } catch (error) {
  //     setConnectionVerified(false);
  //     setVerifiedOfficeId(null);
  //     addToast(
  //       error.message ||
  //         ORGANIZATION_FIELD_CONSTANTS.OFFICE_ID.CONNECTION.CHECK_FAILED,
  //       CONST.TOAST_TYPE.ERROR
  //     );
  //   } finally {
  //     setIsCheckingConnection(false);
  //   }
  // };

  // Check Sikka connection when office_id reaches 6 characters
  const handleCheckSikkaConnection = async (officeId) => {
    if (
      !officeId ||
      officeId.length !== ORGANIZATION_CONSTANTS.SIKKA_ID_DIGITS
    ) {
      setConnectionVerified(false);
      setVerifiedOfficeId(null);
      return;
    }

    setIsCheckingConnection(true);
    setConnectionVerified(false);
    setVerifiedOfficeId(null);

    try {
      const resultAction = await dispatch(checkSikkaConnection(officeId));
      if (checkSikkaConnection.fulfilled.match(resultAction)) {
        const data = resultAction.payload;
        if (data.success) {
          setConnectionVerified(true);
          setVerifiedOfficeId(officeId);
          addToast(
            data.message ||
              ORGANIZATION_FIELD_CONSTANTS.OFFICE_ID.CONNECTION
                .VERIFIED_SUCCESS,
            CONST.TOAST_TYPE.SUCCESS
          );
        } else {
          setConnectionVerified(false);
          setVerifiedOfficeId(null);
          addToast(
            data.message ||
              ORGANIZATION_FIELD_CONSTANTS.OFFICE_ID.CONNECTION
                .VERIFICATION_FAILED,
            CONST.TOAST_TYPE.ERROR
          );
        }
      } else {
        setConnectionVerified(false);
        setVerifiedOfficeId(null);
        const errorMessage = extractApiErrorMessage(resultAction.payload);
        addToast(
          errorMessage ||
            ORGANIZATION_FIELD_CONSTANTS.OFFICE_ID.CONNECTION
              .VERIFICATION_FAILED,
          CONST.TOAST_TYPE.ERROR
        );
      }
    } catch (error) {
      setConnectionVerified(false);
      setVerifiedOfficeId(null);
      addToast(
        error.message ||
          ORGANIZATION_FIELD_CONSTANTS.OFFICE_ID.CONNECTION.CHECK_FAILED,
        CONST.TOAST_TYPE.ERROR
      );
    } finally {
      setIsCheckingConnection(false);
    }
  };

  const handleSubmit = async (values, form) => {
    // // Check if operational service is selected and validate Sikka ID
    // const selectedServices = values.services || [];
    // const isOperationalSelected = selectedServices.includes("operational");
    // const officeId = values.office_id || "";

    // if (
    //   isOperationalSelected &&
    //   officeId &&
    //   officeId.length === ORGANIZATION_CONSTANTS.SIKKA_ID_DIGITS
    // ) {
    //   const isVerified = connectionVerified && verifiedOfficeId === officeId;
    //   if (!isVerified) {
    //     form.setError("office_id", {
    //       message:
    //         ORGANIZATION_FIELD_CONSTANTS.OFFICE_ID.VALIDATION.MUST_BE_VERIFIED,
    //     });
    //     return; // Prevent submission
    //   }
    // }

    // Check if operational service is selected and validate Sikka ID
    const selectedServices = values.services || [];
    const isOperationalSelected = selectedServices.includes("operational");
    const officeId = values.office_id || "";

    if (
      isOperationalSelected &&
      officeId &&
      officeId.length === ORGANIZATION_CONSTANTS.SIKKA_ID_DIGITS
    ) {
      const isVerified = connectionVerified && verifiedOfficeId === officeId;
      if (!isVerified) {
        form.setError("office_id", {
          message:
            ORGANIZATION_FIELD_CONSTANTS.OFFICE_ID.VALIDATION.MUST_BE_VERIFIED,
        });
        return; // Prevent submission
      }
    }

    setIsAdding(true);
    try {
      const payload = { ...values };
      if (values.logo instanceof File) {
        payload.logo = await prepareLogoPayload(values.logo);
      } else {
        // If no logo is uploaded, use default logo-dark.png
        payload.logo = await getDefaultLogoAsBase64();
      }

      const resultAction = await dispatch(createOrganization(payload));
      if (createOrganization.fulfilled.match(resultAction)) {
        addToast(
          resultAction.payload?.message ||
            ORGANIZATION_CONSTANTS.ADD_PAGE.SUCCESS_MESSAGE,
          CONST.TOAST_TYPE.SUCCESS
        );
        router.push("/masters/org");
      } else {
        // Extract error message from the error response
        const errorMessage = extractApiErrorMessage(resultAction.payload);
        addToast(errorMessage, CONST.TOAST_TYPE.ERROR);
      }
    } catch (error) {
      addToast(extractApiErrorMessage(error), CONST.TOAST_TYPE.ERROR);
      addToast(extractApiErrorMessage(error), CONST.TOAST_TYPE.ERROR);
    } finally {
      setIsAdding(false);
    }
  };

  // Page loader overlay - using consistent Loader component
  if (isLoading) {
    return (
      <Loader message={ORGANIZATION_CONSTANTS.ADD_PAGE.LOADER_MESSAGE} show />
    );
  }

  return (
    <AddForm
      heading={ORGANIZATION_CONSTANTS.ADD_PAGE.HEADING}
      subTitle={ORGANIZATION_CONSTANTS.ADD_PAGE.SUBTITLE}
      onBack={() => router.push("/masters/org")}
      backLabel={ORGANIZATION_CONSTANTS.ADD_PAGE.BACK_LABEL}
      title={ORGANIZATION_CONSTANTS.ADD_PAGE.TITLE}
      fields={organizationFields}
      onSubmit={handleSubmit}
      submitLabel={ORGANIZATION_CONSTANTS.ADD_PAGE.SUBMIT_LABEL}
      isSubmitting={isAdding}
      fieldCallbacks={{
        office_id: handleCheckSikkaConnection,
      }}
      connectionStatus={{
        isVerified: connectionVerified,
        isChecking: isCheckingConnection,
        verifiedOfficeId: verifiedOfficeId,
      }}
      onFileValidationError={(message) => addToast(message, "error")}
    />
  );
}
