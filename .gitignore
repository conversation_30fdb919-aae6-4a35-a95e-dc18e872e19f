# Node
node_modules/
**/node_modules/

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
frontend/.next/
frontend/.parcel-cache/

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Directory for instrumented libs generated by jscoverage/JSCover
lib-cov

# Coverage directory used by tools like istanbul
coverage/
coverage/**/*
.nyc_output

# Build outputs
dist/
build/
out/
public/build/

# Environment variables
.env
.env.*
env.local
env.*.local

# IDEs and editors
.vscode/
/.idea/
*.suo
*.ntvs*
*.njsproj
*.sln
*.swp
*~

# Mac
.DS_Store

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini

# Docker
.dockerignore

# Kubernetes
.kube/
*.kubeconfig

# npm package lock files (keep whichever package manager you use - add to repo if needed)
package-lock.json
pnpm-lock.yaml
yarn.lock

# Pa<PERSON><PERSON>, Next.js, Vite and other tool caches
.parcel-cache
.next
.turbo
.cache
.vite

# Next.js build output
next-env.d.ts

# TypeScript
*.tsbuildinfo
*.d.ts

# Logs and temp
.cache
tmp/
temp/

# local env files for services
**/config/local.*

# Editorconfig
.editorconfig

# OS generated files
Icon?

# yarn berry
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz

# PM2
.pm2

# Secrets
secrets/
*.pem
*.key

# Certificates
certs/

# local development databases
*.sqlite
*.sqlite3

# Logs from services
logs/

# Coverage and test output
test-output/

# Docker volumes and local data
volumes/

# VS Code workspace settings
*.code-workspace

# Ignore service logs folder
services/**/logs/
services/**/logs/*

# Ignore build outputs for services
services/**/build/
services/**/dist/

# Ignore uploads folders
services/**/uploads/
services/**/uploads/*

# Ignore node build in frontend
frontend/.next/
frontend/out/

