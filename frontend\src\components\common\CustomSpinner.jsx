import React from "react";
import { Spin } from "antd";
import { LoadingOutlined } from "@ant-design/icons";
import styles from "./CustomSpinner.module.css";

const CustomSpinner = ({
  size = "large",
  tip = "Loading...",
  className = "",
  spinning = true,
  children,
  showTipStandalone = true,
  ...props
}) => {
  const iconSize = size === "large" ? 24 : size === "small" ? 14 : 20;
  const antIcon = <LoadingOutlined style={{ fontSize: iconSize }} spin />;

  if (children) {
    return (
      <Spin
        indicator={antIcon}
        tip={tip}
        className={className}
        spinning={spinning}
        {...props}
      >
        {children}
      </Spin>
    );
  }

  const containerClassName = [styles.spinnerContainer, className]
    .filter(Boolean)
    .join(" ");

  return (
    <div className={containerClassName}>
      <Spin indicator={antIcon} size={size} spinning={spinning} {...props} />
      {showTipStandalone && tip && (
        <div className={styles.spinnerTip}>{tip}</div>
      )}
    </div>
  );
};

export default CustomSpinner;
