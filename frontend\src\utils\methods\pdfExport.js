import { message } from "antd";
import axios from "axios";
import tokenStorage from "@/lib/tokenStorage";
import { SERVICE_PORTS } from "@/utils/constants/api";

// System API key for file-service authentication
const SYSTEM_API_KEY = process.env.NEXT_PUBLIC_SYSTEM_API_KEY;
const FILE_SERVICE_URL = SERVICE_PORTS.FILE;

/**
 * Create axios instance for file service with proper authentication
 * File service uses x-api-key header + Bearer token
 */
const getFileServiceClient = () => {
  const instance = axios.create({
    baseURL: FILE_SERVICE_URL,
    headers: {
      "Content-Type": "application/json",
      ...(SYSTEM_API_KEY ? { "x-api-key": SYSTEM_API_KEY } : {}),
    },
    withCredentials: true,
    timeout: 30000,
  });

  instance.interceptors.request.use(
    (config) => {
      const accessToken = tokenStorage.getAccessToken();
      if (accessToken && !tokenStorage.isTokenExpired(accessToken)) {
        config.headers.Authorization = `Bearer ${accessToken}`;
      }
      return config;
    },
    (error) => Promise.reject(error)
  );

  return instance;
};

/**
 * Service type to normalized name mapping (matches backend normalizeServiceName)
 */
const SERVICE_NAME_MAP = {
  finance: "financial",
  financial: "financial",
  operations: "operational",
  operational: "operational",
  payroll: "payroll",
  pms: "payroll",
};

/**
 * Normalize service name to match backend Documents table format
 * @param {string} service - Service type
 * @returns {string} Normalized service name
 */
const normalizeServiceName = (service) => {
  if (!service || typeof service !== "string") {
    return service;
  }
  const serviceLower = service.toLowerCase();
  return SERVICE_NAME_MAP[serviceLower] || serviceLower;
};

const MONTH_NAMES = [
  "January",
  "February",
  "March",
  "April",
  "May",
  "June",
  "July",
  "August",
  "September",
  "October",
  "November",
  "December",
];

/**
 * Service type to API endpoint mapping
 */
const SERVICE_ENDPOINTS = {
  finance: "/report/finance/pdf",
  financial: "/report/finance/pdf",
  payroll: "/report/payroll/pdf",
  operations: "/report/operations/pdf",
  operational: "/report/operations/pdf",
};

/**
 * Format amount with K notation
 * @param {number} value - The value to format
 * @returns {string} Formatted value
 */
export const formatAmountWithK = (value) => {
  if (value === null || value === undefined || isNaN(Number(value))) {
    return "0";
  }
  const num = Number(value);
  const absNum = Math.abs(num);
  if (absNum >= 1000) {
    const formatted = (num / 1000).toFixed(2);
    return formatted.replace(/\.00$/, "K").replace(/(\.\d+?)0+$/, "$1K");
  }
  return num.toFixed(2);
};

/**
 * Generate filename for PDF download
 * @param {string} title - Report title
 * @param {string} organizationName - Organization name
 * @param {string} monthName - Month name
 * @param {number|string} year - Year
 * @returns {string} Generated filename
 */
const generateFilename = (title, organizationName, monthName, year) => {
  const parts = [
    title.replace(/\s+/g, "_"),
    organizationName?.replace(/\s+/g, "_") || "Report",
    monthName?.replace(/\s+/g, "_"),
    year,
  ].filter(Boolean);
  return `${parts.join("_")}.pdf`;
};

/**
 * Get display title based on service type
 * @param {string} serviceType - Service type
 * @returns {string} Display title
 */
const getServiceTitle = (serviceType) => {
  const titles = {
    finance: "Finance_Report",
    financial: "Finance_Report",
    payroll: "Payroll_Report",
    operations: "Operations_Report",
    operational: "Operations_Report",
  };
  return titles[serviceType] || "Report";
};

/**
 * Export dashboard to PDF by calling the backend report service API
 * @param {Object} params - Export parameters
 * @param {string} params.organizationId - Organization ID (required)
 * @param {string} params.organizationName - Organization name (optional)
 * @param {number|string} params.month - Month (1-12)
 * @param {number|string} params.year - Year
 * @param {string} params.serviceType - Service type (finance, payroll, operations)
 * @param {Function} params.onLoadingChange - Loading state callback
 * @param {boolean} params.storeToBlob - Whether to store PDF to blob storage (default: true)
 */
export const exportDashboardToPdf = async ({
  organizationId,
  organizationName,
  month,
  year,
  serviceType,
  onLoadingChange,
  storeToBlob = true,
}) => {
  const hideLoading = message.loading(
    "Generating PDF... This may take a moment.",
    0
  );

  if (onLoadingChange) {
    onLoadingChange(true);
  }

  const cleanup = () => {
    hideLoading();
    onLoadingChange?.(false);
  };

  try {
    // Validate required parameters
    if (!organizationId) {
      message.error("Organization ID is required");
      cleanup();
      return;
    }

    if (!month || !year) {
      message.error("Month and year are required");
      cleanup();
      return;
    }

    // Normalize service type and get endpoint
    const normalizedServiceType = serviceType?.toLowerCase();
    const endpoint = SERVICE_ENDPOINTS[normalizedServiceType];

    if (!endpoint) {
      message.error(`Unknown service type: ${serviceType}`);
      cleanup();
      return;
    }

    // Create axios instance for report service
    const axios = api(SERVICE_PORTS.REPORT);

    // Make API call to generate PDF
    const response = await axios.get(endpoint, {
      params: {
        organization_id: organizationId,
        organization_name: organizationName,
        month: Number(month),
        year: Number(year),
        store_to_blob: storeToBlob ? "true" : "false",
      },
      responseType: "blob",
      timeout: 120000, // 2 minute timeout for PDF generation
    });

    // Generate filename
    const monthName =
      month && month >= 1 && month <= 12 ? MONTH_NAMES[Number(month) - 1] : "";
    const filename = generateFilename(
      getServiceTitle(normalizedServiceType),
      organizationName,
      monthName,
      year
    );

    // Create blob and download
    const blob = new Blob([response.data], { type: "application/pdf" });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);

    cleanup();
    message.success("PDF exported successfully!");
  } catch (error) {
    console.error("Error exporting PDF:", error);
    cleanup();

    // Handle specific error cases
    if (error.response?.status === 400) {
      message.error("Invalid request parameters. Please check your selection.");
    } else if (error.response?.status === 404) {
      message.error("Report data not found for the selected period.");
    } else if (error.code === "ECONNABORTED") {
      message.error("Request timed out. Please try again.");
    } else {
      message.error(
        error.response?.data?.message ||
          "Failed to export PDF. Please try again."
      );
    }
  }
};

/**
 * Download report PDF from Azure Blob Storage via Documents API
 * @param {Object} params - Download parameters
 * @param {string} params.organizationId - Organization ID (required)
 * @param {string} params.organizationName - Organization name (optional, used for filename)
 * @param {number|string} params.month - Month (1-12)
 * @param {number|string} params.year - Year
 * @param {string} params.serviceType - Service type (finance, payroll, operations)
 * @param {Function} params.onLoadingChange - Loading state callback
 */
export const downloadReportFromBlob = async ({
  organizationId,
  organizationName,
  month,
  year,
  serviceType,
  onLoadingChange,
}) => {
  const hideLoading = message.loading(
    "Fetching report... This may take a moment.",
    0
  );

  if (onLoadingChange) {
    onLoadingChange(true);
  }

  const cleanup = () => {
    hideLoading();
    onLoadingChange?.(false);
  };

  try {
    // Validate required parameters
    if (!organizationId) {
      message.error("Organization ID is required");
      cleanup();
      return;
    }

    if (!month || !year) {
      message.error("Month and year are required");
      cleanup();
      return;
    }

    if (!serviceType) {
      message.error("Service type is required");
      cleanup();
      return;
    }

    // Normalize service type to match backend Documents table format
    const normalizedService = normalizeServiceName(serviceType);

    // Create axios instance for file service with proper x-api-key auth
    const fileServiceClient = getFileServiceClient();

    // Query Documents table for matching report
    // Note: Base URL already includes /api, so use /document (not /api/document)
    const documentsResponse = await fileServiceClient.get("/document", {
      params: {
        organization_id: organizationId,
        service: normalizedService,
        month: Number(month),
        year: Number(year),
      },
    });

    const documents = documentsResponse.data?.data;

    // Check if document was found
    if (!documents || documents.length === 0) {
      message.error(
        "Report not found for the selected period. Please ensure the report has been generated."
      );
      cleanup();
      return;
    }

    // Get the most recent document (first one, as results are ordered by created_at DESC)
    // Note: Use 'docRecord' to avoid shadowing global 'document' object
    const docRecord = documents[0];
    const blobStoragePath = docRecord.blob_storage_path;
    const fileName = docRecord.file_name;

    if (!blobStoragePath) {
      message.error("Report file path not found in document record.");
      cleanup();
      return;
    }

    // Get SAS URL for the blob from file service
    const sasResponse = await fileServiceClient.get("/document/sas-url", {
      params: {
        blob_path: blobStoragePath,
      },
    });

    const sasUrl = sasResponse.data?.data?.sasUrl || sasResponse.data?.sasUrl;

    if (!sasUrl) {
      message.error("Failed to generate download URL for the report.");
      cleanup();
      return;
    }

    // Generate filename for download
    const monthName =
      month && month >= 1 && month <= 12 ? MONTH_NAMES[Number(month) - 1] : "";
    const downloadFilename =
      fileName ||
      generateFilename(
        getServiceTitle(serviceType?.toLowerCase()),
        organizationName,
        monthName,
        year
      );

    // Direct download using anchor tag - bypasses CORS since it's browser navigation
    // The SAS URL already includes authentication, so no fetch needed
    if (typeof window !== "undefined" && typeof document !== "undefined") {
      const link = document.createElement("a");
      link.href = sasUrl;
      link.download = downloadFilename;
      link.target = "_blank"; // Open in new tab as fallback
      link.rel = "noopener noreferrer";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      cleanup();
      message.success("Report download started!");
    } else {
      // Fallback: open in new window if document is not available
      window.open(sasUrl, "_blank");
      cleanup();
      message.success("Report download started!");
    }
  } catch (error) {
    console.error("Error downloading report from blob:", error);
    cleanup();

    // Handle specific error cases
    if (error.response?.status === 400) {
      message.error("Invalid request parameters. Please check your selection.");
    } else if (error.response?.status === 404) {
      message.error("Report not found for the selected period.");
    } else if (error.code === "ECONNABORTED") {
      message.error("Request timed out. Please try again.");
    } else {
      message.error(
        error.response?.data?.message ||
          error.message ||
          "Failed to download report. Please try again."
      );
    }
  }
};
