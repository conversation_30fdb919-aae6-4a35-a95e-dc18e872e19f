import { sequelize } from "../models/index.js";
import models from "../models/index.js";
import { LOGGER_NAMES } from "../utils/constants/log.constants.js";
import { createLogger } from "../utils/logger.utils.js";
import { getOrganizationSchemaName, getMonthDateRange } from "../utils/repository.utils.js";

const logger = createLogger(LOGGER_NAMES.REPORT_REPOSITORY);



/**
 * Fetch balance sheet data for a specific month and year
 * @param {string} schemaName - Organization schema name
 * @param {number} month - Month (1-12)
 * @param {number} year - Year
 * @returns {Promise<Array>} Balance sheet data
 */
const getBalanceSheetData = async (schemaName, month, year) => {
  try {
    logger.info(
      `Fetching balance sheet data from schema: ${schemaName} for month: ${month}, year: ${year}`
    );

    const { startDate, endDate } = getMonthDateRange(month, year);

    // Query to fetch balance sheet reports and line items
    const query = `
      SELECT 
        r.id as report_id,
        r.report_name,
        r.report_basis,
        r.start_date,
        r.end_date,
        r.currency,
        li.id as line_item_id,
        li.account_name,
        li.account_id,
        li.amount,
        li.section,
        li.subsection,
        li.account_type,
        li.path
      FROM "${schemaName}".qb_balance_sheet_reports r
      INNER JOIN "${schemaName}".qb_balance_sheet_line_items li ON r.id = li.report_id
      WHERE r.end_date >= :startDate AND r.end_date <= :endDate
      ORDER BY r.end_date DESC, li.section, li.subsection, li.account_name
    `;

    const results = await sequelize.query(query, {
      replacements: { startDate, endDate },
      type: sequelize.QueryTypes.SELECT,
    });

    logger.info(
      `Retrieved ${results.length} balance sheet line items from schema: ${schemaName}`
    );

    return results;
  } catch (error) {
    logger.error(
      `Error in BalanceSheetRepository.getBalanceSheetData:`,
      error
    );
    throw error;
  }
};

export default {
  getOrganizationSchemaName,
  getBalanceSheetData,
};
