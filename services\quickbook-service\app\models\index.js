import sequelize from "../../config/postgres.config.js";
import GLAccountMasterModel from "./qb_gl_account_master.model.js";
import QbBalanceSheetReportModel from "./qb_balance_sheet_report.model.js";
import QbBalanceSheetLineItemModel from "./qb_balance_sheet_line_item.model.js";
import QbCashFlowReportModel from "./qb_cash_flow_report.model.js";
import QbCashFlowLineModel from "./qb_cash_flow_line.model.js";
import QbCashFlowTotalModel from "./qb_cash_flow_total.model.js";
import QbPnLReportModel from "./qb_pnl_report.model.js";
import QbPnLLineModel from "./qb_pnl_line.model.js";
import QbPnLSummaryModel from "./qb_pnl_summary.model.js";
import QbTrialBalanceReportModel from "./qb_trial_balance_report.model.js";
import QbTrialBalanceColumnModel from "./qb_trial_balance_column.model.js";
import QbTrialBalanceRowModel from "./qb_trial_balance_row.model.js";

// Initialize all models
const GLAccountMaster = GLAccountMasterModel(sequelize);
const BalanceSheetReport = QbBalanceSheetReportModel(sequelize);
const BalanceSheetLineItem = QbBalanceSheetLineItemModel(sequelize);
const CashFlowReport = QbCashFlowReportModel(sequelize);
const CashFlowLine = QbCashFlowLineModel(sequelize);
const CashFlowTotal = QbCashFlowTotalModel(sequelize);
const PnLReport = QbPnLReportModel(sequelize);
const PnLLine = QbPnLLineModel(sequelize);
const PnLSummary = QbPnLSummaryModel(sequelize);
const TrialBalanceReport = QbTrialBalanceReportModel(sequelize);
const TrialBalanceColumn = QbTrialBalanceColumnModel(sequelize);
const TrialBalanceRow = QbTrialBalanceRowModel(sequelize);

const models = {
  gl_account_master: GLAccountMaster,
  BalanceSheetReport,
  BalanceSheetLineItem,
  CashFlowReport,
  CashFlowLine,
  CashFlowTotal,
  PnLReport,
  PnLLine,
  PnLSummary,
  TrialBalanceReport,
  TrialBalanceColumn,
  TrialBalanceRow,
};

// Set up associations
PnLReport?.hasMany(PnLLine, {
  foreignKey: "report_id",
  as: "lines",
});

PnLReport?.hasMany(PnLSummary, {
  foreignKey: "report_id",
  as: "summaries",
});

BalanceSheetReport?.hasMany(BalanceSheetLineItem, {
  foreignKey: "report_id",
  as: "lineItems",
});

CashFlowReport?.hasMany(CashFlowLine, {
  foreignKey: "report_id",
  as: "lines",
});

CashFlowReport?.hasOne(CashFlowTotal, {
  foreignKey: "report_id",
  as: "totals",
});

export {
  GLAccountMaster,
  BalanceSheetReport,
  BalanceSheetLineItem,
  CashFlowReport,
  CashFlowLine,
  CashFlowTotal,
  PnLReport,
  PnLLine,
  PnLSummary,
  TrialBalanceReport,
  TrialBalanceColumn,
  TrialBalanceRow,
  sequelize,
};

export default models;
