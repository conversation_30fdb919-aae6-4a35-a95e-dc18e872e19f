// app/routes/document.routes.js
import express from "express";
import multer from "multer";
import {
  storeDocument,
  getDocuments,
  getDocumentById,
  updateDocumentById,
  storePdfDocument,
  getSasUrl,
} from "../controllers/document.controller.js";
import { authMiddleware } from "../middleware/auth.middleware.js";

const router = express.Router();

// Configure multer for PDF file uploads (memory storage)
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 50 * 1024 * 1024, // 50MB limit
  },
  fileFilter: (req, file, cb) => {
    if (file.mimetype === "application/pdf") {
      cb(null, true);
    } else {
      cb(new Error("Only PDF files are allowed"), false);
    }
  },
});

// POST /api/document - Store document metadata
// Body: organization_id, blob_storage_path, file_name (required)
// Body: service, month, year, file_size, mime_type, metadata (optional)
router.post("/", authMiddleware, storeDocument);

// POST /api/document/store-pdf - Store PDF to Azure Blob Storage
// Accepts multipart/form-data with file OR JSON with base64 pdfBuffer
// Body: organization_id, service, month, year (required)
// Body: organization_name, file_name (optional)
router.post(
  "/store-pdf",
  authMiddleware,
  upload.single("file"),
  storePdfDocument
);

// GET /api/document - Get documents for organization
// Query: organization_id (required), service, month, year (optional)
router.get("/", authMiddleware, getDocuments);

// GET /api/document/sas-url - Get SAS URL for blob download
// Query: blob_path (required) - The blob storage path
// Note: This route must be defined BEFORE /:id to prevent matching
router.get("/sas-url", authMiddleware, getSasUrl);

// GET /api/document/:id - Get document by ID (for status checking)
// Returns: document with summaryStatus, summaryError, hasSummary, summary
router.get("/:id", authMiddleware, getDocumentById);

// PATCH /api/document/:id - Update document metadata/summary (internal)
router.patch("/:id", authMiddleware, updateDocumentById);

export default router;
