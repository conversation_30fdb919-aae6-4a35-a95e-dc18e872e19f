import axios from "axios";
import { HARDCODED_STRINGS } from "./constants/strings.constants.js";
import { QUICKBOOKS_DEFAULTS } from "./constants/config.constants.js";

export const getApiCall = async (url, token) => {
  const response = await axios.get(url, {
    headers: {
      Authorization: `Bearer ${token}`,
      Accept: "application/json",
    },
  });
  return response;
};

/**
 * Make an HTTP GET request with proper error handling
 * @param {string} url - Request URL
 * @param {Object} headers - Request headers
 * @param {number} timeout - Request timeout in milliseconds
 * @returns {Promise<Object>} Response data
 * @throws {Error} Error with status code information
 */
export const makeApiRequest = async (
  url,
  headers,
  timeout = QUICKBOOKS_DEFAULTS.REQUEST_TIMEOUT_MS
) => {
  try {
    const response = await axios.get(url, { headers, timeout });

    // Success response (200-299)
    if (response.status >= 200 && response.status < 300) {
      return response.data;
    }

    // Handle non-success status codes
    const error = new Error(
      `API request failed with status ${response.status}`
    );
    error.status = response.status;
    error.data = response.data;
    throw error;
  } catch (error) {
    // Handle axios errors
    if (error.response) {
      // Server responded with error status (400-599)
      const statusCode = error.response.status;
      const errorMessage = new Error(
        error.response.data?.message ||
          `API request failed with status ${statusCode}`
      );
      errorMessage.status = statusCode;
      errorMessage.data = error.response.data;

      // Categorize errors by status code
      if (statusCode >= 400 && statusCode < 500) {
        // Client errors (400-499)
        if (statusCode === 422) {
          errorMessage.type = "VALIDATION_ERROR";
        } else {
          errorMessage.type = "CLIENT_ERROR";
        }
      } else if (statusCode >= 500) {
        // Server errors (500-599)
        errorMessage.type = "SERVER_ERROR";
      }

      throw errorMessage;
    } else if (error.request) {
      // Request made but no response received
      const timeoutError = new Error("Request timeout - no response received");
      timeoutError.status = 408;
      timeoutError.type = "TIMEOUT_ERROR";
      throw timeoutError;
    } else {
      // Error setting up request
      const setupError = new Error(error.message || "Failed to setup request");
      setupError.status = 500;
      setupError.type = "SETUP_ERROR";
      throw setupError;
    }
  }
};

/**
 * Get QuickBooks API query for the specified table
 * @param {string} tableName - Name of the table
 * @returns {string} API query string
 */
export const getQuickbookApiQuery = (tableName) =>
  `/query?query=select * from ${tableName}`;

/**
 * Map QuickBooks account data to local schema
 * @param {Array} accounts - Array of QuickBooks account objects
 * @returns {Array} Array of mapped account objects
 */
export const getMappedAccounts = (accounts) =>
  accounts.map((acc) => ({
    id: acc.Id,
    name: acc.Name,
    sub_account: acc.SubAccount,
    fully_qualified_name: acc.FullyQualifiedName,
    active: acc.Active,
    classification: acc.Classification,
    account_type: acc.AccountType,
    account_sub_type: acc.AccountSubType,
    current_balance: acc.CurrentBalance,
    current_balance_with_sub_accounts: acc.CurrentBalanceWithSubAccounts,
    currency_value: acc.CurrencyRef?.value,
    currency_name: acc.CurrencyRef?.name,
    domain: acc.domain,
    sparse: acc.sparse,
    sync_token: acc.SyncToken,
    create_time: acc.MetaData?.CreateTime,
    last_updated_time: acc.MetaData?.LastUpdatedTime,
  }));

const formatToDayMonthYearTime = (time) => {
  const date = time instanceof Date ? time : new Date(time || Date.now());
  if (Number.isNaN(date.getTime())) return "";

  const formatTwoDigits = (number) => String(number).padStart(2, "0");

  const day = formatTwoDigits(date.getDate());
  const month = formatTwoDigits(date.getMonth() + 1);
  const year = date.getFullYear();

  const hours = formatTwoDigits(date.getHours());
  const minutes = formatTwoDigits(date.getMinutes());
  return `${day}/${month}/${year} ${hours}:${minutes}`;
};

const getErrorMessage = (error) => {
  if (!error) {
    return HARDCODED_STRINGS.SOMETHING_WENT_WRONG;
  }

  if (typeof error === "string") {
    return error;
  }

  if (error instanceof Error && error.message) {
    return error.message;
  }

  return String(error);
};

export const getSyncFailureEmailContent = (error, organizationId) => ({
  integrationName: HARDCODED_STRINGS.QUICKBOOKS,
  formattedTimestamp: formatToDayMonthYearTime(new Date()),
  errorMessage: getErrorMessage(error),
  organizationId: organizationId,
});
