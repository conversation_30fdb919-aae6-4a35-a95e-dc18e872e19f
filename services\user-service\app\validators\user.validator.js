import { body } from "express-validator";
import <PERSON><PERSON> from "joi";
import {
  VALIDATION_MESSAGES,
  REQUEST_BODY_FIELDS,
  REGEX_PATTERNS,
} from "../utils/constants.util.js";

// Password regex: at least one letter, one number, one special character
const PASSWORD_REGEX = REGEX_PATTERNS.PASSWORD;

/**
 * Common password validation rule
 * @param {string} fieldName - The field name to validate (e.g., 'password' or 'newPassword')
 * @param {string} requiredMessage - The message to show when field is required
 * @returns {object} Express validator chain
 */
const passwordValidation = (fieldName, requiredMessage) => {
  return body(fieldName)
    .notEmpty()
    .withMessage(requiredMessage)
    .bail()
    .isLength({ min: 8 })
    .withMessage(VALIDATION_MESSAGES.PASSWORD_MIN_LENGTH)
    .bail()
    .matches(/[a-z]/)
    .withMessage(VALIDATION_MESSAGES.PASSWORD_LOWERCASE_REQUIRED)
    .bail()
    .matches(/[A-Z]/)
    .withMessage(VALIDATION_MESSAGES.PASSWORD_UPPERCASE_REQUIRED)
    .bail()
    .matches(/\d/)
    .withMessage(VALIDATION_MESSAGES.PASSWORD_NUMBER_REQUIRED)
    .bail()
    .matches(/[@$!%*#?&]/)
    .withMessage(VALIDATION_MESSAGES.PASSWORD_SPECIAL_CHAR_REQUIRED);
};

// Base validation rules that are common between create and update
const baseValidationRules = {
  full_name: body(REQUEST_BODY_FIELDS.FULL_NAME)
    .notEmpty()
    .withMessage(VALIDATION_MESSAGES.NAME_REQUIRED)
    .isLength({ min: 2, max: 50 })
    .withMessage(VALIDATION_MESSAGES.NAME_LENGTH),
  email: body(REQUEST_BODY_FIELDS.EMAIL)
    .notEmpty()
    .withMessage(VALIDATION_MESSAGES.FIELD_REQUIRED)
    .isEmail()
    .withMessage(VALIDATION_MESSAGES.INVALID_EMAIL),
  phone_number: body(REQUEST_BODY_FIELDS.PHONE)
    .optional()
    .isMobilePhone()
    .withMessage(VALIDATION_MESSAGES.INVALID_PHONE),
};

// User validation rules
export const userValidations = {
  // Create user validation
  createUser: [
    ...Object.values(baseValidationRules),
    passwordValidation(
      REQUEST_BODY_FIELDS.PASSWORD,
      VALIDATION_MESSAGES.FIELD_REQUIRED
    ),
  ],

  // Update user schema (for Joi validation)
  updateUserSchema: {
    name: Joi.string()
      .min(2)
      .max(50)
      .pattern(REGEX_PATTERNS.NAME_PATTERN)
      .messages({
        "string.min": VALIDATION_MESSAGES.NAME_MIN,
        "string.max": VALIDATION_MESSAGES.NAME_MAX,
        "string.pattern.base": VALIDATION_MESSAGES.NAME_PATTERN,
      }),
    email: Joi.string().email().messages({
      "string.email": VALIDATION_MESSAGES.EMAIL_JOI,
    }),
    phone_number: Joi.string().pattern(REGEX_PATTERNS.PHONE_PATTERN).messages({
      "string.pattern.base": VALIDATION_MESSAGES.PHONE_PATTERN,
    }),
  },

  // Get user by ID validation
  getUserById: [
    body(REQUEST_BODY_FIELDS.USER_ID)
      .notEmpty()
      .withMessage(VALIDATION_MESSAGES.FIELD_REQUIRED)
      .isUUID()
      .withMessage(VALIDATION_MESSAGES.INVALID_UUID),
  ],

  // Delete user validation
  deleteUser: [
    body(REQUEST_BODY_FIELDS.USER_ID)
      .notEmpty()
      .withMessage(VALIDATION_MESSAGES.FIELD_REQUIRED)
      .isUUID()
      .withMessage(VALIDATION_MESSAGES.INVALID_UUID),
  ],

  // Get users by organization validation
  getUsersByOrganization: [
    body(REQUEST_BODY_FIELDS.ORGANIZATION_ID)
      .notEmpty()
      .withMessage(VALIDATION_MESSAGES.FIELD_REQUIRED)
      .isUUID()
      .withMessage(VALIDATION_MESSAGES.INVALID_UUID),
  ],

  // Get users by role validation
  getUsersByRole: [
    body(REQUEST_BODY_FIELDS.ROLE_ID)
      .notEmpty()
      .withMessage(VALIDATION_MESSAGES.FIELD_REQUIRED)
      .isUUID()
      .withMessage(VALIDATION_MESSAGES.INVALID_UUID),
  ],

  // Search users validation
  searchUsers: [
    body(REQUEST_BODY_FIELDS.QUERY)
      .notEmpty()
      .withMessage(VALIDATION_MESSAGES.FIELD_REQUIRED)
      .isLength({ min: 2, max: 100 })
      .withMessage(VALIDATION_MESSAGES.SEARCH_QUERY_LENGTH),
  ],

  // Get user count validation
  getUserCount: [
    body(REQUEST_BODY_FIELDS.ORGANIZATION_ID)
      .optional()
      .isUUID()
      .withMessage(VALIDATION_MESSAGES.INVALID_UUID),
  ],

  // Get user by email validation
  getUserByEmail: [
    body(REQUEST_BODY_FIELDS.EMAIL)
      .notEmpty()
      .withMessage(VALIDATION_MESSAGES.FIELD_REQUIRED)
      .isEmail()
      .withMessage(VALIDATION_MESSAGES.INVALID_EMAIL),
  ],

  // Forgot password validation
  forgotPassword: [
    body(REQUEST_BODY_FIELDS.EMAIL)
      .notEmpty()
      .withMessage(VALIDATION_MESSAGES.FIELD_REQUIRED)
      .isEmail()
      .withMessage(VALIDATION_MESSAGES.INVALID_EMAIL),
  ],

  // Validate OTP validation
  validateOtp: [
    body(REQUEST_BODY_FIELDS.EMAIL)
      .notEmpty()
      .withMessage(VALIDATION_MESSAGES.FIELD_REQUIRED)
      .isEmail()
      .withMessage(VALIDATION_MESSAGES.INVALID_EMAIL),
    body(REQUEST_BODY_FIELDS.OTP)
      .notEmpty()
      .withMessage(VALIDATION_MESSAGES.FIELD_REQUIRED)
      .isLength({ min: 6, max: 6 })
      .withMessage(VALIDATION_MESSAGES.OTP_MUST_BE_6_DIGITS)
      .isNumeric()
      .withMessage(VALIDATION_MESSAGES.OTP_MUST_BE_NUMERIC),
  ],
  // Change password validation
  changePassword: [
    body(REQUEST_BODY_FIELDS.USER_ID)
      .notEmpty()
      .withMessage(VALIDATION_MESSAGES.FIELD_REQUIRED)
      .isUUID()
      .withMessage(VALIDATION_MESSAGES.INVALID_UUID),
    body(REQUEST_BODY_FIELDS.OLD_PASSWORD)
      .notEmpty()
      .withMessage(VALIDATION_MESSAGES.OLD_PASSWORD_REQUIRED),
    passwordValidation(
      REQUEST_BODY_FIELDS.NEW_PASSWORD,
      VALIDATION_MESSAGES.NEW_PASSWORD_REQUIRED
    ),
  ],
  // Reset password validation
  resetPassword: [
    passwordValidation(
      REQUEST_BODY_FIELDS.NEW_PASSWORD,
      VALIDATION_MESSAGES.NEW_PASSWORD_REQUIRED
    ),
    body(REQUEST_BODY_FIELDS.CONFIRM_PASSWORD)
      .notEmpty()
      .withMessage(VALIDATION_MESSAGES.FIELD_REQUIRED)
  ],
};

export default userValidations;
