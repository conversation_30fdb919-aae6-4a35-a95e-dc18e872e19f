const SCHEMA_NAME = "Authentication";
const TABLE_NAME = "app_organization";
const TABLE_REFERENCE = { tableName: TABLE_NAME, schema: SCHEMA_NAME };
const SERVICES_ENUM_NAME = "enum_app_organization_services";
const SERVICES_ENUM_WITH_SCHEMA = `enum_${SCHEMA_NAME}_${TABLE_NAME}_services`;

const ensureSchemaExists = async (queryInterface) => {
  await queryInterface.sequelize.query(
    `CREATE SCHEMA IF NOT EXISTS "${SCHEMA_NAME}"`
  );
};

const dropServicesEnum = async (queryInterface) => {
  await queryInterface.sequelize.query(
    `DO $$
    BEGIN
      IF EXISTS (SELECT 1 FROM pg_type WHERE typname = '${SERVICES_ENUM_NAME}') THEN
        DROP TYPE "${SERVICES_ENUM_NAME}";
      ELSIF EXISTS (SELECT 1 FROM pg_type WHERE typname = '${SERVICES_ENUM_WITH_SCHEMA}') THEN
        DROP TYPE "${SERVICES_ENUM_WITH_SCHEMA}";
      END IF;
    END$$;`
  );
};

export const up = async (queryInterface, Sequelize) => {
  await ensureSchemaExists(queryInterface);

  await queryInterface.createTable(
    TABLE_REFERENCE,
    {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true,
      },
      name: {
        type: Sequelize.TEXT,
        allowNull: false,
      },
      email: {
        type: Sequelize.TEXT,
        allowNull: false,
      },
      phone: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      website: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      description: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      services: {
        type: Sequelize.ARRAY(
          Sequelize.ENUM("financial", "operational", "payroll")
        ),
        allowNull: false,
        defaultValue: [],
      },
      is_active: {
        type: Sequelize.BOOLEAN,
        defaultValue: true,
      },
      schema_name: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      is_deleted: {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
      },
      created_at: {
        type: Sequelize.DATE,
        defaultValue: Sequelize.NOW,
      },
      created_by: {
        type: Sequelize.UUID,
        allowNull: true,
      },
      updated_at: {
        type: Sequelize.DATE,
      },
      updated_by: {
        type: Sequelize.UUID,
        allowNull: true,
      },
      office_id: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      realm_id: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
    },
    {
      schema: SCHEMA_NAME,
      timestamps: true,
      createdAt: "created_at",
      updatedAt: "updated_at",
    }
  );

  const indexDefinitions = [
    {
      name: "idx_organization_email",
      fields: ["email"],
      unique: true,
      where: { is_deleted: false },
    },
    {
      name: "idx_organization_name",
      fields: ["name"],
    },
    {
      name: "idx_organization_services",
      fields: ["services"],
      using: "gin",
    },
    {
      name: "idx_organization_created_at",
      fields: ["created_at"],
    },
    {
      name: "idx_organization_is_deleted",
      fields: ["is_deleted"],
    },
  ];

  await Promise.all(
    indexDefinitions.map((definition) =>
      queryInterface.addIndex(TABLE_REFERENCE, definition)
    )
  );
};

export const down = async (queryInterface) => {
  await queryInterface.dropTable(TABLE_REFERENCE);
  await dropServicesEnum(queryInterface);
};
