import PayrollKpiService from "../services/payroll_kpi.service.js";
import SalaryByDepartmentService from "../services/salary_by_department.service.js";
import TaxBreakdownService from "../services/tax_breakdown.service.js";
import DeductionsBreakdownService from "../services/deductions_breakdown.service.js";
import { LOGGER_NAMES } from "../utils/constants/log.constants.js";
import { createLogger } from "../utils/logger.utils.js";
import {
  validateRequiredParams,
  handleControllerError,
  sendSuccessResponse,
} from "../utils/controller.utils.js";

const logger = createLogger(LOGGER_NAMES.REPORT_CONTROLLER);

/**
 * Calculate combined payroll data by aggregating all payroll endpoints
 * (payroll KPIs, salary by department, tax breakdown, deductions breakdown)
 * using the same query parameters.
 *
 * Expected query params: organization_id, month, year
 */
const calculatePayroll = async (req, res) => {
  try {
    const { organization_id, month, year } = req.query;

    logger.info(
      `Calculating payroll data for org: ${organization_id}, month: ${month}, year: ${year}`
    );

    // Validate required parameters
    const validationError = validateRequiredParams(req.query, [
      "organization_id",
      "month",
      "year",
    ]);
    if (validationError) {
      return res.status(400).json(validationError);
    }

    const commonPayload = {
      organization_id,
      month,
      year,
    };

    // Call all underlying services in parallel
    const [
      payrollKpiData,
      salaryByDepartmentData,
      taxBreakdownData,
      deductionsBreakdownData,
    ] = await Promise.all([
      PayrollKpiService.getPayrollKpiData(commonPayload),
      SalaryByDepartmentService.getSalaryByDepartmentData(commonPayload),
      TaxBreakdownService.getTaxBreakdownData(commonPayload),
      DeductionsBreakdownService.getDeductionsBreakdownData(commonPayload),
    ]);

    const combinedData = {
      kpis: payrollKpiData,
      salaryByDepartment: salaryByDepartmentData,
      taxBreakdown: taxBreakdownData,
      deductionsBreakdown: deductionsBreakdownData,
    };

    sendSuccessResponse(
      res,
      "Payroll data calculated successfully",
      combinedData
    );
  } catch (error) {
    logger.error("Error calculating payroll data:", error);
    handleControllerError(error, res, "Error calculating payroll data");
  }
};

export default {
  calculatePayroll,
};

