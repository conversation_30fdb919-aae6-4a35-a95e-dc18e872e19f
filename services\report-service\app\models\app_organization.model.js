import { DataTypes } from "sequelize";

const AppOrganizationModel = (sequelize) => {
  const AppOrganization = sequelize.define(
    "app_organization",
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true,
      },
      name: {
        type: DataTypes.TEXT,
        allowNull: false,
      },
      email: {
        type: DataTypes.TEXT,
        allowNull: false,
        validate: {
          isEmail: true,
        },
      },
      schema_name: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      is_deleted: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
      },
      realm_id: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
    },
    {
      tableName: "app_organization",
      schema: "Authentication",
      timestamps: true,
      createdAt: "created_at",
      updatedAt: "updated_at",
    }
  );

  return AppOrganization;
};

export default AppOrganizationModel;
