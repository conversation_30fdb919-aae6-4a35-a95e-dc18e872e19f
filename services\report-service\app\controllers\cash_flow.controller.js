import CashFlowService from "../services/cash_flow.service.js";
import { LOGGER_NAMES } from "../utils/constants/log.constants.js";
import { createLogger } from "../utils/logger.utils.js";
import { validateRequiredParams, handleControllerError, sendSuccessResponse } from "../utils/controller.utils.js";

const logger = createLogger(LOGGER_NAMES.REPORT_CONTROLLER);

/**
 * Get cash flow data by group for organization by month and year
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getCashFlowByGroup = async (req, res) => {
  try {
    const { organization_id, month, year } = req.query;

    logger.info(
      `Fetching cash flow data for org: ${organization_id}, month: ${month}, year: ${year}`
    );

    // Validate required parameters
    const validationError = validateRequiredParams(req.query, ['organization_id', 'month', 'year']);
    if (validationError) {
      return res.status(400).json(validationError);
    }

    // Fetch cash flow data
    const cashFlowData = await CashFlowService.getCashFlowByGroup({
      organization_id,
      month,
      year,
    });

    // Return successful response
    sendSuccessResponse(res, "Cash flow data fetched successfully", cashFlowData);
  } catch (error) {
    logger.error("Error fetching cash flow data:", error);
    handleControllerError(error, res, "Error fetching cash flow data");
  }
};

export default {
  getCashFlowByGroup,
};
