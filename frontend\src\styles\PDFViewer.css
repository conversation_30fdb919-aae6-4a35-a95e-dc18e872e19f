/* PDF Viewer Styles */

/* Page Background - Softer neutral grey */
.pdf-viewer-container {
  background-color: #F4F4F5 !important;
}

/* PDF Report Frame - Professional shadow and styling */
.dashboard-pdf-frame {
  background-color: #F9FAFB !important; /* Very light grey instead of pure white */
  box-shadow: 
    0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04) !important; /* shadow-xl */
  border: 1px solid #E5E7EB !important;
}

/* PDF Canvas - Pure white background for charts and tables */
.react-pdf__Page__canvas {
  background-color: #FFFFFF !important;
}

/* PDF Viewer Styles */
.react-pdf__Page {
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  justify-content: flex-start !important;
  position: relative !important;
  overflow: visible !important;
  padding: 0 !important;
  margin: 0 !important;
}

.react-pdf__Page__canvas {
  display: block !important;
  max-width: 100% !important;
  height: auto !important;
  margin: 0 auto !important;
  border-radius: 12px;
  box-shadow: 
    0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04),
    0 0 0 1px rgba(0, 0, 0, 0.05);
  position: relative !important;
  overflow: visible !important;
  background: white;
}

.react-pdf__Page__textContent {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  transform: scale(1) !important;
  transform-origin: 0 0 !important;
  pointer-events: none !important;
  width: 100% !important;
  height: 100% !important;
}

.react-pdf__Page__annotations {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  transform: scale(1) !important;
  transform-origin: 0 0 !important;
  width: 100% !important;
  height: 100% !important;
}

.react-pdf__Document {
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
}

/* Ensure proper text rendering */
.react-pdf__Page__textContent span {
  color: transparent !important;
  position: absolute !important;
  white-space: pre !important;
  cursor: text !important;
  transform-origin: 0% 0% !important;
  font-family: inherit !important;
}

/* Fix for text layer positioning */
.react-pdf__Page__textContent .textLayer {
  position: absolute !important;
  left: 0 !important;
  top: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  overflow: hidden !important;
  opacity: 0.2 !important;
  line-height: 1 !important;
  width: 100% !important;
  height: 100% !important;
}

/* Fix for annotation layer */
.react-pdf__Page__annotations .annotationLayer {
  position: absolute !important;
  left: 0 !important;
  top: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  overflow: hidden !important;
  pointer-events: auto !important;
  width: 100% !important;
  height: 100% !important;
}

/* Additional fixes for proper PDF rendering */
.react-pdf__Page__textContent .textLayer > span {
  position: absolute !important;
  white-space: pre !important;
  color: transparent !important;
  font-family: sans-serif !important;
  transform-origin: 0% 0% !important;
  left: 0 !important;
  top: 0 !important;
  cursor: text !important;
  user-select: text !important;
}

/* Ensure PDF content is properly contained */
.react-pdf__Page__textContent .textLayer {
  position: absolute !important;
  left: 0 !important;
  top: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  overflow: hidden !important;
  opacity: 0.2 !important;
  line-height: 1 !important;
  text-align: left !important;
  transform-origin: 0% 0% !important;
  z-index: 2 !important;
}

/* Fix for annotation layer positioning */
.react-pdf__Page__annotations .annotationLayer {
  position: absolute !important;
  left: 0 !important;
  top: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  overflow: hidden !important;
  pointer-events: auto !important;
  z-index: 3 !important;
}

/* Ensure PDF container doesn't clip content */
.react-pdf__Document {
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  overflow: visible !important;
  width: 100% !important;
  height: auto !important;
}

/* Fix for any clipping issues */
.pdf-viewer-container {
  overflow: visible !important;
  height: auto !important;
  min-height: 100vh !important;
  width: 100% !important;
}

/* Ensure PDF page is fully visible */
.react-pdf__Page__canvas {
  max-height: none !important;
  object-fit: contain !important;
}
