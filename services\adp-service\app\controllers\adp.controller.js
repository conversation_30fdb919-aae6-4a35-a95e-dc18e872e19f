// Packages
import path from "path";
import fs from "fs/promises";

// Services
import { adpService } from "../services/adp.service.js";

// Utils
import {
  successResponse,
  errorResponse,
} from "../../../../shared/utils/response.util.js";
import * as status from "../../../../shared/utils/status_code.util.js";
import * as constants from "../../../../shared/utils/constants.util.js";
import { createLogger } from "../utils/logger.util.js";
import { sendAdpSyncFailureEmail } from "../utils/adp.util.js";

const logger = createLogger("ADP_CONTROLLER");

/**
 * Sync Data
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
export const syncData = async (req, res) => {
  try {
    logger.info("Sync data request received", {
      fileName: req.file?.originalname,
      schemaName: req.body?.schemaName,
      orgId: req.body?.orgId,
      email: req.body?.email,
    });

    if (!req.file) {
      logger.warn("Sync data request failed: No file uploaded");
      return res
        .status(status.STATUS_CODE_BAD_REQUEST)
        .json(errorResponse(constants.CONSTANT_MESSAGES.NO_FILE_UPLOADED));
    }
    const { schemaName, orgId } = req.body;

    if (!schemaName) {
      logger.warn("Sync data request failed: Schema name required");
      return res.status(status.STATUS_CODE_BAD_REQUEST).json({
        success: false,
        message: constants.CONSTANT_MESSAGES.SCHEMA_NAME_REQUIRED,
      });
    }

    const uploadsDir = path.join(process.cwd(), "uploads");

    await fs.mkdir(uploadsDir, { recursive: true });

    const filePath = path.join(
      uploadsDir,
      Date.now() + "-" + req.file.originalname
    );

    logger.info("Writing uploaded file", { filePath });
    await fs.writeFile(filePath, req.file.buffer);

    logger.info("Starting ADP data sync", { filePath, schemaName, orgId });
    const result = await adpService.syncData(filePath, schemaName, orgId);

    logger.info("ADP data sync completed successfully", {
      insertedRecords: result.insertedRecords,
    });

    // Trigger payroll PDF generation after successful sync (ADP = payroll)
    if (result.success && orgId) {
      try {
        // Extract file name to parse month/year
        const baseName = path.basename(req.file.originalname, path.extname(req.file.originalname));
        const parts = baseName.split("_");
        const payrollMonth = parts[parts.length - 1];
        
        // Extract month and year from payrollMonth (e.g., "January2025")
        const axios = (await import("axios")).default;
        const monthNames = ["January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December"];
        
        let month = null;
        let year = null;
        
        for (let i = 0; i < monthNames.length; i++) {
          if (payrollMonth.includes(monthNames[i])) {
            month = i + 1;
            
            // Extract year: get everything after the month name, then extract the last 4 digits
            // This handles formats like "30September2025" or "September2025"
            const afterMonth = payrollMonth.substring(payrollMonth.indexOf(monthNames[i]) + monthNames[i].length);
            const yearMatch = afterMonth.match(/(\d{4})/); // Extract 4-digit year
            
            if (yearMatch) {
              year = parseInt(yearMatch[1], 10);
            }
            break;
          }
        }

        // Validate month and year are valid numbers
        if (!month || !year || isNaN(year) || year < 2000 || year > 3000) {
          logger.warn("Could not extract valid month/year from filename for PDF generation", { 
            payrollMonth, 
            month, 
            year,
            isValidYear: !isNaN(year) && year >= 2000 && year <= 3000
          });
        } else {
          // Get organization name
          let organization_name = null;
          try {
            const orgResponse = await axios.get(
              `${process.env.USER_SERVICE_URL}/api/organization/${orgId}`
            );
            organization_name = orgResponse.data?.data?.organization_name || null;
          } catch (orgError) {
            logger.warn("Could not fetch organization name for PDF generation", {
              error: orgError.message,
            });
          }

          const reportServiceUrl = process.env.REPORT_SERVICE_URL;
          if (reportServiceUrl) {
            logger.info(
              `Triggering payroll PDF generation for organization: ${orgId}, month: ${month}, year: ${year}`
            );

            await axios.get(`${reportServiceUrl}/report/payroll/pdf`, {
              params: {
                organization_id: orgId,
                organization_name,
                month,
                year,
                store_to_blob: "true",
              },
              timeout: 60000, // 60 second timeout
            });

            logger.info(
              `✅ Payroll PDF generated and stored successfully for organization: ${orgId}`
            );
          } else {
            logger.warn("Skipping payroll PDF generation: REPORT_SERVICE_URL not configured");
          }
        }
      } catch (pdfError) {
        logger.error(
          `Failed to generate payroll PDF for organization ${orgId}:`,
          {
            error: pdfError.message,
            stack: pdfError.stack,
          }
        );
        // Don't throw - PDF generation failure should not block sync response
      }
    }

    if (!result.success) {
      return res
        .status(result.statusCode || status.STATUS_CODE_BAD_REQUEST)
        .json(errorResponse(result.message));
    }

    return res
      .status(status.STATUS_CODE_SUCCESS)
      .json(
        successResponse(constants.CONSTANT_MESSAGES.PAYROLL_DATA_SYNCED, result)
      );
  } catch (error) {
    logger.error("ADP data sync failed", {
      error: error.message,
      stack: error.stack,
    });

    // Send failure email notification
    const email = req.body?.email;
    if (!email) {
      logger.warn("Sync data request failed: Email required");
      return res.status(status.STATUS_CODE_BAD_REQUEST).json({
        success: false,
        message: constants.CONSTANT_MESSAGES.EMAIL_REQUIRED,
      });
    }
    await sendAdpSyncFailureEmail({
      error,
      fileName: req.file?.originalname,
      recipientEmail: email,
    });

    return res
      .status(status.STATUS_CODE_INTERNAL_SERVER_ERROR)
      .json(errorResponse(error.message));
  }
};
