// app/utils/error.utils.js
import {
  STATUS_CODE_BAD_REQUEST,
  STATUS_CODE_INTERNAL_SERVER_ERROR,
} from "./status_code.utils.js";
import {
  ERROR_MESSAGES,
} from "./constants/error.constants.js";

/**
 * Error handler utility
 * @param {Error} error - Error object
 * @returns {Object} Handled error with status code and message
 */
export const errorHandler = (error) => {
  try {
    // Early return for Sequelize validation errors
    if (
      error.name === ERROR_MESSAGES.SEQUELIZE_VALIDATION_ERROR ||
      error.name === ERROR_MESSAGES.SEQUELIZE_UNIQUE_CONSTRAINT_ERROR
    ) {
      return {
        statusCode: STATUS_CODE_BAD_REQUEST,
        message: error.message,
        details: error.errors?.map((err) => err.message).join(", "),
      };
    }

    // Early return for Sequelize database errors
    if (error.name === ERROR_MESSAGES.SEQUELIZE_DATABASE_ERROR) {
      return {
        statusCode: STATUS_CODE_INTERNAL_SERVER_ERROR,
        message: ERROR_MESSAGES.DATABASE_ERROR,
        details: error.message,
      };
    }

    // REFACTORED: No fallback operators - use error message directly
    // Guard clause: validate error message exists
    const errorMessage = error.message ? error.message : ERROR_MESSAGES.UNEXPECTED_ERROR;
    
    return {
      statusCode: STATUS_CODE_INTERNAL_SERVER_ERROR,
      message: errorMessage,
      details: error.message,
    };
  } catch (handlerError) {
    // Fallback if error handling itself fails
    // REFACTORED: Use handler error message if available, otherwise original
    const detailsMessage = handlerError.message ? handlerError.message : error.message;
    
    return {
      statusCode: STATUS_CODE_INTERNAL_SERVER_ERROR,
      message: ERROR_MESSAGES.UNEXPECTED_ERROR,
      details: detailsMessage,
    };
  }
};

