import { createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";
import tokenStorage from "@/lib/tokenStorage";
import {
  setFolderAvailability,
  addCheckingFolder,
  removeCheckingFolder,
  addFolderCheck,
} from "../Slice/folderAvailability";

export const checkFolderAvailability = createAsyncThunk(
  "folderAvailability/check",
  async ({ clientId, clientName }, { dispatch, getState }) => {
    const state = getState();
    const folderChecks = state.folderAvailability?.folderChecks || [];

    if (folderChecks.includes && folderChecks.includes(clientId)) {
      return { clientId, hasFolders: false };
    }

    dispatch(addFolderCheck(clientId));
    dispatch(addCheckingFolder(clientId));

    try {
      const FILE_SERVICE_URL = process.env.NEXT_PUBLIC_FILE_SERVICE_URL;
      const SYSTEM_API_KEY = process.env.NEXT_PUBLIC_SYSTEM_API_KEY;

      if (!FILE_SERVICE_URL) {
        dispatch(setFolderAvailability({ clientId, hasFolders: false }));
        dispatch(removeCheckingFolder(clientId));
        return { clientId, hasFolders: false };
      }

      const baseUrl = FILE_SERVICE_URL.replace(/\/+$/, "").replace(
        /\/api$/,
        ""
      );
      const accessToken = tokenStorage.getAccessToken();
      const params = new URLSearchParams({ orgId: clientId });
      if (clientName) {
        params.append("orgname", encodeURIComponent(clientName));
      }
      // Add cache-busting timestamp to always get fresh data
      params.append("_t", Date.now().toString());

      const response = await axios.get(
        `${baseUrl}/api/files/report-folders?${params.toString()}`,
        {
          headers: {
            "Content-Type": "application/json",
            ...(SYSTEM_API_KEY && { "x-api-key": SYSTEM_API_KEY }),
            ...(accessToken && { Authorization: `Bearer ${accessToken}` }),
          },
          timeout: 5000,
        }
      );

      const folders = response.data?.data?.folders;
      const hasFolders =
        response.data?.success === true &&
        Array.isArray(folders) &&
        folders.length > 0 &&
        folders.some(
          (folder) =>
            folder && Array.isArray(folder?.months) && folder.months.length > 0
        );

      dispatch(setFolderAvailability({ clientId, hasFolders }));
      dispatch(removeCheckingFolder(clientId));
      return { clientId, hasFolders };
    } catch (error) {
      dispatch(setFolderAvailability({ clientId, hasFolders: false }));
      dispatch(removeCheckingFolder(clientId));
      return { clientId, hasFolders: false };
    }
  }
);
