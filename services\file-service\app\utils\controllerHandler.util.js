// app/utils/controllerHandler.util.js
/**
 * Common controller handler utilities
 * Reduces duplicate code in controllers
 */

import { successResponse, errorResponse } from "./response.util.js";
import { STATUS_CODE_INTERNAL_SERVER_ERROR } from "./status_code.utils.js";
import { errorHandler } from "./error.utils.js";
import logger from "../../config/logger.config.js";

/**
 * Handle service response in controller
 * @param {Object} serviceResponse - Service response object
 * @param {Object} res - Express response object
 * @param {string} successLogMessage - Log message for success
 * @param {string} errorLogMessage - Log message for error
 * @returns {void}
 */
export const handleServiceResponse = (
  serviceResponse,
  res,
  successLogMessage = null,
  errorLogMessage = null
) => {
  if (serviceResponse.success) {
    if (successLogMessage) {
      logger.info(successLogMessage);
    }
    return res
      .status(serviceResponse.statusCode)
      .json(successResponse(serviceResponse.message, serviceResponse.data));
  }

  if (errorLogMessage) {
    logger.warn(errorLogMessage);
  }
  return res
    .status(serviceResponse.statusCode)
    .json(errorResponse(serviceResponse.message, serviceResponse.error));
};

/**
 * Handle controller errors
 * @param {Error} error - Error object
 * @param {Object} res - Express response object
 * @param {string} logMessage - Log message
 * @param {string} defaultMessage - Default error message
 * @returns {void}
 */
export const handleControllerError = (
  error,
  res,
  logMessage = null,
  defaultMessage = "An error occurred"
) => {
  if (logMessage) {
    logger.error(`${logMessage}: ${error.message}`);
  } else {
    logger.error(`Controller error: ${error.message}`);
  }

  const handledError = errorHandler(error);
  const statusCode = handledError.statusCode
    ? handledError.statusCode
    : STATUS_CODE_INTERNAL_SERVER_ERROR;
  const message = handledError.message ? handledError.message : defaultMessage;
  const details = handledError.details ? handledError.details : error.message;

  return res.status(statusCode).json(errorResponse(message, details));
};

/**
 * Validate required fields
 * @param {Object} fields - Object with field names as keys and values
 * @param {Array} requiredFields - Array of required field names
 * @returns {Object|null} Error response object or null if valid
 */
export const validateRequiredFields = (fields, requiredFields) => {
  const missingFields = requiredFields.filter((field) => !fields[field]);
  if (missingFields.length > 0) {
    return {
      isValid: false,
      message: `Missing required fields: ${missingFields.join(", ")}`,
      missingFields,
    };
  }
  return { isValid: true };
};

