// import { createSlice } from "@reduxjs/toolkit";
// import {
//   triggerPowerBiWorkflow,
//   triggerFinanceWorkflow,
//   triggerOperationsWorkflow,
//   triggerPayrollWorkflow,
// } from "../Thunks/powerBiWorkflow";

// const initialState = {
//   loading: false,
//   error: null,
//   lastTriggered: null,
//   workflowResponse: null,
// };

// const powerBiWorkflowSlice = createSlice({
//   name: "powerBiWorkflow",
//   initialState,
//   reducers: {
//     clearError: (state) => {
//       state.error = null;
//     },
//     clearWorkflowResponse: (state) => {
//       state.workflowResponse = null;
//       state.lastTriggered = null;
//     },
//     reset: (state) => {
//       return initialState;
//     },
//   },
//   extraReducers: (builder) => {
//     builder
//       // Trigger Finance Workflow
//       .addCase(triggerFinanceWorkflow.pending, (state) => {
//         state.loading = true;
//         state.error = null;
//       })
//       .addCase(triggerFinanceWorkflow.fulfilled, (state, action) => {
//         state.loading = false;
//         state.error = null;
//         state.lastTriggered = new Date().toISOString();
//         state.workflowResponse = action.payload;
//       })
//       .addCase(triggerFinanceWorkflow.rejected, (state, action) => {
//         state.loading = false;
//         state.error = action.payload || "Failed to trigger Finance workflow";
//         state.workflowResponse = null;
//       })
//       // Trigger Operations Workflow
//       .addCase(triggerOperationsWorkflow.pending, (state) => {
//         state.loading = true;
//         state.error = null;
//       })
//       .addCase(triggerOperationsWorkflow.fulfilled, (state, action) => {
//         state.loading = false;
//         state.error = null;
//         state.lastTriggered = new Date().toISOString();
//         state.workflowResponse = action.payload;
//       })
//       .addCase(triggerOperationsWorkflow.rejected, (state, action) => {
//         state.loading = false;
//         state.error = action.payload || "Failed to trigger Operations workflow";
//         state.workflowResponse = null;
//       })
//       // Trigger Payroll Workflow
//       .addCase(triggerPayrollWorkflow.pending, (state) => {
//         state.loading = true;
//         state.error = null;
//       })
//       .addCase(triggerPayrollWorkflow.fulfilled, (state, action) => {
//         state.loading = false;
//         state.error = null;
//         state.lastTriggered = new Date().toISOString();
//         state.workflowResponse = action.payload;
//       })
//       .addCase(triggerPayrollWorkflow.rejected, (state, action) => {
//         state.loading = false;
//         state.error = action.payload || "Failed to trigger Payroll workflow";
//         state.workflowResponse = null;
//       })
//       // Generic trigger (for backward compatibility)
//       .addCase(triggerPowerBiWorkflow.pending, (state) => {
//         state.loading = true;
//         state.error = null;
//       })
//       .addCase(triggerPowerBiWorkflow.fulfilled, (state, action) => {
//         state.loading = false;
//         state.error = null;
//         state.lastTriggered = new Date().toISOString();
//         state.workflowResponse = action.payload;
//       })
//       .addCase(triggerPowerBiWorkflow.rejected, (state, action) => {
//         state.loading = false;
//         state.error = action.payload || "Failed to trigger Power BI workflow";
//         state.workflowResponse = null;
//       });
//   },
// });

// export const { clearError, clearWorkflowResponse, reset } =
//   powerBiWorkflowSlice.actions;
// export default powerBiWorkflowSlice.reducer;
