"use strict";

const TABLE_REFERENCE = { tableName: "document", schema: "Authentication" };
const COLUMN_NAME = "summary";

module.exports = {
  up: async function (queryInterface, Sequelize) {
    // Check if column exists
    const tableDescription = await queryInterface.describeTable(
      TABLE_REFERENCE.tableName,
      { schema: TABLE_REFERENCE.schema }
    );

    if (!tableDescription[COLUMN_NAME]) {
      // Column doesn't exist, add it as JSONB
      await queryInterface.addColumn(
        TABLE_REFERENCE,
        COLUMN_NAME,
        {
          type: Sequelize.JSONB,
          allowNull: true,
          comment: "Document summary JSON (title, sections)",
        }
      );
      return;
    }

    // Column exists, check if it's already JSONB
    const columnType = tableDescription[COLUMN_NAME].type;
    if (columnType === "jsonb" || columnType === "JSONB") {
      // Already JSONB, just update comment
      await queryInterface.changeColumn(
        TABLE_REFERENCE,
        COLUMN_NAME,
        {
          type: Sequelize.JSONB,
          allowNull: true,
          comment: "Document summary JSON (title, sections)",
        }
      );
      return;
    }

    // Drop and recreate column (loses existing data but ensures clean migration)
    // This is safer than trying to cast TEXT to JSONB which can fail
    await queryInterface.removeColumn(TABLE_REFERENCE, COLUMN_NAME);
    
    await queryInterface.addColumn(
      TABLE_REFERENCE,
      COLUMN_NAME,
      {
        type: Sequelize.JSONB,
        allowNull: true,
        comment: "Document summary JSON (title, sections)",
      }
    );
  },

  down: async function (queryInterface, Sequelize) {
    const tableDescription = await queryInterface.describeTable(
      TABLE_REFERENCE.tableName,
      { schema: TABLE_REFERENCE.schema }
    );

    if (!tableDescription[COLUMN_NAME]) {
      return;
    }

    // Drop and recreate as TEXT
    await queryInterface.removeColumn(TABLE_REFERENCE, COLUMN_NAME);
    
    await queryInterface.addColumn(
      TABLE_REFERENCE,
      COLUMN_NAME,
      {
        type: Sequelize.TEXT,
        allowNull: true,
        comment: "Document summary text",
      }
    );
  },
};

