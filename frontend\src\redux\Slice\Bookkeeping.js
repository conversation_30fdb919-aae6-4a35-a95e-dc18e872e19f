import { createSlice } from "@reduxjs/toolkit";
import {
  syncFinancialData,
  syncOperationalData,
} from "@/redux/Thunks/bookkeeping";

const initialState = {
  financial: {
    loading: false,
    error: null,
    lastSynced: null,
  },
  operational: {
    loading: false,
    error: null,
    lastSynced: null,
  },
};

const bookkeepingSlice = createSlice({
  name: "bookkeeping",
  initialState,
  reducers: {
    clearFinancialError: (state) => {
      state.financial.error = null;
    },
    clearOperationalError: (state) => {
      state.operational.error = null;
    },
    clearAllErrors: (state) => {
      state.financial.error = null;
      state.operational.error = null;
    },
  },
  extraReducers: (builder) => {
    // Financial sync
    builder
      .addCase(syncFinancialData.pending, (state) => {
        state.financial.loading = true;
        state.financial.error = null;
      })
      .addCase(syncFinancialData.fulfilled, (state, action) => {
        state.financial.loading = false;
        state.financial.lastSynced = new Date().toISOString();
        state.financial.error = null;
      })
      .addCase(syncFinancialData.rejected, (state, action) => {
        state.financial.loading = false;
        state.financial.error = action.payload;
      });

    // Operational sync
    builder
      .addCase(syncOperationalData.pending, (state) => {
        state.operational.loading = true;
        state.operational.error = null;
      })
      .addCase(syncOperationalData.fulfilled, (state, action) => {
        state.operational.loading = false;
        state.operational.lastSynced = new Date().toISOString();
        state.operational.error = null;
      })
      .addCase(syncOperationalData.rejected, (state, action) => {
        state.operational.loading = false;
        state.operational.error = action.payload;
      });
  },
});

export const { clearFinancialError, clearOperationalError, clearAllErrors } =
  bookkeepingSlice.actions;
export default bookkeepingSlice.reducer;
