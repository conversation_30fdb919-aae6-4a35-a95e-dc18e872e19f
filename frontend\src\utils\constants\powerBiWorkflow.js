// Power BI Workflow Constants
export const POWERBI_WORKFLOW_CONSTANTS = {
  // Success Messages
  TIMEOUT_SUCCESS:
    "Dashboard generation initiated successfully. Processing in background...",
  TRIGGER_SUCCESS: (displayName) =>
    `Workflow triggered successfully for ${displayName}. Dashboard will be ready shortly.`,
  PROCESSING_MESSAGE:
    "Report generation in progress. Please wait while we finish processing your dashboard.",

  // Error Messages
  INTERNAL_ERROR:
    "An internal server error occurred while triggering the dashboard. Please try again later.",
  INVALID_REQUEST: "Invalid request to Power BI workflow",
  UNAUTHORIZED:
    "Unauthorized access to Power BI workflow. Please check your API key.",
  FORBIDDEN:
    "Forbidden: You do not have permissions to trigger this Power BI workflow.",
  NOT_FOUND:
    "Power BI workflow not found. Please verify the workflow configuration.",
  NETWORK_ERROR: "Network error - unable to reach Power BI workflow service",
  DEFAULT_ERROR: "Failed to trigger Power BI workflow",

  // Timeout Detection Keywords (error codes and message patterns)
  TIMEOUT_KEYWORDS: [
    "ECONNABORTED",
    "ETIMEDOUT",
    "timeout",
    "timed out",
    "request timeout",
  ],
  PROCESSING_KEYWORDS: ["in progress", "processing", "initiated", "background"],
};

export default POWERBI_WORKFLOW_CONSTANTS;
