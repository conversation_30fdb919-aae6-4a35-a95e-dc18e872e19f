import { NUMERIC_EXTRACTION_REGEX } from "./constants/pdf.constants.js";

export const convertAbsValueToThousands = (value) => {
  if (value === null || value === undefined || isNaN(value)) {
    return 0;
  }
  return Math.abs(parseFloat(value)) / 1000;
};

export const parseNumericString = (value, defaultValue = "0") => {
  if (value === null || value === undefined) {
    return parseFloat(defaultValue) || 0;
  }

  const stringValue = String(value);
  const cleanedValue = stringValue.replace(NUMERIC_EXTRACTION_REGEX, "");
  const parsed = parseFloat(cleanedValue);

  return isNaN(parsed) ? parseFloat(defaultValue) || 0 : parsed;
};

export default {
  convertAbsValueToThousands,
  parseNumericString,
};