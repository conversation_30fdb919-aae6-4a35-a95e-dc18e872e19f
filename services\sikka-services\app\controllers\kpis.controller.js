import { createLogger } from "../utils/logger.util.js";
import {
  STATUS_CODE_BAD_REQUEST,
  STATUS_CODE_INTERNAL_SERVER_ERROR,
  STATUS_CODE_SUCCESS,
} from "../utils/status_code.util.js";
import {
  LOGGER_NAMES,
  KPI_KEYS,
  ERROR_MESSAGES,
} from "../utils/constants.util.js";
import { fetchKpi } from "../services/kpis.service.js";
import { errorResponse, successResponse } from "../utils/response.util.js";
import { getKpiConfig, isValidKpiKey } from "../utils/kpis.util.js";

const logger = createLogger(LOGGER_NAMES.KPIS_CONTROLLER);

/**
 * Generic KPI controller handler
 * @param {string} kpi_key - KPI key (e.g., 'account_receivables')
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const handleKpiRequest = async (kpi_key, req, res) => {
  try {
    // Validate KPI key
    if (!isValidKpiKey(kpi_key)) {
      return res
        .status(STATUS_CODE_BAD_REQUEST)
        .json(errorResponse(`${ERROR_MESSAGES.INVALID_KPI_KEY} ${kpi_key}`));
    }

    const config = getKpiConfig(kpi_key);

    // Log the request
    logger.info(config.logAction.fetching);

    // Extract common parameters
    const { office_id, schema_name, start_date, end_date } = req.body;

    // Fetch and store KPI data
    const response = await fetchKpi(
      kpi_key,
      office_id,
      start_date,
      end_date,
      schema_name
    );

    // Log success
    logger.info(config.logAction.success);

    // Return success response
    return res
      .status(STATUS_CODE_SUCCESS)
      .json(successResponse(config.message.fetched, response));
  } catch (error) {
    const config = getKpiConfig(kpi_key);
    logger.error(config.logAction.failed, {
      error: error.message,
    });

    // Return error response
    return res
      .status(STATUS_CODE_INTERNAL_SERVER_ERROR)
      .json(errorResponse(error.message));
  }
};

/**
 * Generic KPI controller factory
 * Creates controller functions for all KPIs
 */
const createKpiController = (kpiKey) => {
  return async (req, res) => {
    return await handleKpiRequest(kpiKey, req, res);
  };
};

// Export individual controllers for backward compatibility
export const accountReceivables = createKpiController(
  KPI_KEYS.ACCOUNT_RECEIVABLES
);
export const treatmentAnalysis = createKpiController(
  KPI_KEYS.TREATMENT_ANALYSIS
);
export const hygieneReappointment = createKpiController(
  KPI_KEYS.HYGIENE_REAPPOINTMENT
);
export const avgDailyProduction = createKpiController(
  KPI_KEYS.AVG_DAILY_PRODUCTION
);
export const newPatients = createKpiController(KPI_KEYS.NEW_PATIENTS);
export const noShowAppointments = createKpiController(
  KPI_KEYS.NO_SHOW_APPOINTMENTS
);
export const directRestorations = createKpiController(
  KPI_KEYS.DIRECT_RESTORATIONS
);
export const totalProductionPerDay = createKpiController(
  KPI_KEYS.TOTAL_PRODUCTION_PER_DAY
);
export const totalProductionByDentist = createKpiController(
  KPI_KEYS.TOTAL_PRODUCTION_BY_DENTIST
);
export const totalProductionByHygienist = createKpiController(
  KPI_KEYS.TOTAL_PRODUCTION_BY_HYGIENIST
);
export const grossCollection = createKpiController(KPI_KEYS.GROSS_COLLECTION);
