import { ERROR_MESSAGES } from "../utils/constants/error.constants.js";
import { getSession } from "../utils/memoryStore.js";
import { normalizeSessionId } from "../utils/helpers.js";

/**
 * OPTIMIZED: Session validation middleware with early return and caching
 * Validates sessionId and attaches session to request object
 */
export const validateSessionId = (req, res, next) => {
  // OPTIMIZATION: Early validation - check sessionId exists before normalization
  const sessionId = req.body?.sessionId;
  if (!sessionId || typeof sessionId !== "string" || !sessionId.trim()) {
    return res
      .status(400)
      .json({ error: ERROR_MESSAGES.CHAT.SESSION_ID_REQUIRED });
  }

  // OPTIMIZATION: Normalize and validate in one step
  const normalizedSessionId = normalizeSessionId(sessionId);
  if (!normalizedSessionId) {
    return res
      .status(400)
      .json({ error: ERROR_MESSAGES.CHAT.SESSION_ID_REQUIRED });
  }

  // OPTIMIZATION: Get session (already optimized with expiration check)
  const session = getSession(normalizedSessionId);
  if (!session) {
    return res
      .status(404)
      .json({ error: ERROR_MESSAGES.CHAT.SESSION_NOT_FOUND_OR_EXPIRED });
  }

  // OPTIMIZATION: Attach session to request to avoid re-fetching in controllers
  req.chatSession = session;
  req.body.sessionId = normalizedSessionId;
  return next();
};
