"use client";

import { useState, useCallback, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useDispatch, useSelector } from "react-redux";

import { login } from "@/redux/Thunks/Authentication.js";
import { clearError } from "@/redux/Slice/Authentication";

import { useToast } from "@/components/ui/toast";
import { AUTH_CONSTANTS } from "@/utils/constants/auth";
import "../auth.css";

import { COMMON_CONSTANTS as CONST } from "@/utils/constants";
import { ROUTES } from "@/utils/constants/routes";
import Image from "next/image";

export default function LoginPage() {
  const [formData, setFormData] = useState({
    email: "",
    password: "",
  });
  const dispatch = useDispatch();
  const router = useRouter();
  const { addToast } = useToast();

  const [showPassword, setShowPassword] = useState(false);
  const [isLoggingIn, setIsLoggingIn] = useState(false);
  const [isRedirecting, setIsRedirecting] = useState(false);
  const [emailError, setEmailError] = useState("");
  const [passwordError, setPasswordError] = useState("");
  const { loading } = useSelector((state) => state.auth);

  const isFormDisabled = loading || isLoggingIn || isRedirecting;

  useEffect(() => {
    router.prefetch(ROUTES.LISTING);
  }, [router]);

  const handleLogin = async () => {
    try {
      const user = await dispatch(
        login({
          email: formData.email,
          password: formData.password,
        })
      ).unwrap();

      if (user) {
        setIsLoggingIn(false);
        setIsRedirecting(true);

        addToast(AUTH_CONSTANTS.SUCCESS.LOGIN, CONST.TOAST_TYPE.SUCCESS);

        router.replace(ROUTES.LISTING);
        return;
      }
    } catch (errorMessage) {
      const errorText =
        typeof errorMessage === "string"
          ? errorMessage
          : errorMessage?.message || AUTH_CONSTANTS.ERRORS.LOGIN_FAILED;
      addToast(errorText, CONST.TOAST_TYPE.ERROR);
      setIsLoggingIn(false);
      setIsRedirecting(false);
      throw errorMessage;
    }
  };

  const validateForm = () => {
    let isValid = true;
    setEmailError("");
    setPasswordError("");

    // Email validation
    if (!formData.email) {
      setEmailError("Email is required");
      isValid = false;
    } else if (!AUTH_CONSTANTS.PATTERNS.EMAIL.test(formData.email)) {
      setEmailError(
        "Please enter a valid email address (e.g., <EMAIL>)"
      );
      isValid = false;
    }

    // Password validation
    if (!formData.password) {
      setPasswordError("Password is required");
      isValid = false;
    } else if (formData.password.length < 6) {
      setPasswordError("Password must be at least 6 characters long");
      isValid = false;
    }

    return isValid;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    dispatch(clearError());
    setEmailError("");
    setPasswordError("");
    setIsLoggingIn(true);
    setIsRedirecting(false);

    if (!validateForm()) {
      setIsLoggingIn(false);
      return;
    }

    try {
      await handleLogin();
    } catch (error) {
      setIsLoggingIn(false);
      setIsRedirecting(false);
    }
  };

  const handleChange = useCallback((e) => {
    const { name, value, type, checked } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: type === "checkbox" ? checked : value,
    }));
  }, []);

  return (
    <div className={`auth-layout ${isRedirecting ? "fade-out" : ""}`}>
      <div className="left-column">
        <div className="brand">
          <Image
            src={AUTH_CONSTANTS.IMAGES.LOGO}
            alt={AUTH_CONSTANTS.BRANDING.COMPANY_NAME}
            width={140}
            height={96}
            priority
          />
        </div>

        <div className="illustration-wrapper">
          <Image
            src={AUTH_CONSTANTS.IMAGES.LOGIN_ILLUSTRATION}
            alt="Login Illustration"
            width={540}
            height={540}
            loading="lazy"
            style={{ width: "100%", height: "auto", maxHeight: "100%" }}
            sizes="(max-width: 768px) 100vw, 50vw"
          />
        </div>
        <Image
          src={AUTH_CONSTANTS.IMAGES.BACKGROUND}
          alt="Frame Background"
          fill
          sizes="(max-width: 768px) 100vw, 50vw"
          priority
          className="background-graphic"
        />
      </div>

      <div className="right-column">
        <div className="form-container">
        <div className="page-header">
          <h1>{AUTH_CONSTANTS.LOGIN.PAGE_TITLE}</h1>
          <p>{AUTH_CONSTANTS.LOGIN.PAGE_SUBTITLE}</p>
        </div>

          <form className="login-form" onSubmit={handleSubmit}>
            <div className="field-group">
              <label htmlFor="email">{AUTH_CONSTANTS.LABELS.EMAIL}</label>
              <div className="input-wrapper">
                <span className="input-icon">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                    <path
                      d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207"
                      stroke="currentColor"
                      strokeWidth="1.6"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                  </svg>
                </span>
                <input
                  type="email"
                  id="email"
                  name="email"
                  value={formData.email}
                  onChange={handleChange}
                  required
                  disabled={isFormDisabled}
                  className="auth-input"
                  placeholder={AUTH_CONSTANTS.PLACEHOLDERS.EMAIL}
                  aria-disabled={isFormDisabled}
                />
              </div>
              {emailError && <p className="input-error">{emailError}</p>}
            </div>

            <div className="field-group">
              <label htmlFor="password">{AUTH_CONSTANTS.LABELS.PASSWORD}</label>
              <div className="input-wrapper">
                <span className="input-icon">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                    <path
                      d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
                      stroke="currentColor"
                      strokeWidth="1.6"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                  </svg>
                </span>
                <input
                  type={showPassword ? "text" : "password"}
                  id="password"
                  name="password"
                  value={formData.password}
                  onChange={handleChange}
                  required
                  disabled={isFormDisabled}
                  className="auth-input"
                  placeholder={AUTH_CONSTANTS.PLACEHOLDERS.PASSWORD}
                  aria-disabled={isFormDisabled}
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="password-toggle"
                  disabled={isFormDisabled}
                  aria-disabled={isFormDisabled}
                >
                  <svg
                    width="20"
                    height="20"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                  >
                    {showPassword ? (
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"
                      />
                    ) : (
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                      />
                    )}
                  </svg>
                </button>
              </div>
              {passwordError && <p className="input-error">{passwordError}</p>}
            </div>

            <button
              type="submit"
              disabled={isFormDisabled}
              aria-disabled={isFormDisabled}
            >
              {isRedirecting
                ? "Redirecting..."
                : loading || isLoggingIn
                  ? AUTH_CONSTANTS.BUTTONS.SIGNING_IN
                  : AUTH_CONSTANTS.BUTTONS.LOGIN}
            </button>
          </form>
          <div className="form-support">
            <p>
              {AUTH_CONSTANTS.SUPPORT.NEED_HELP}{" "}
              <a href={`mailto:${AUTH_CONSTANTS.SUPPORT.EMAIL}`}>
                {AUTH_CONSTANTS.SUPPORT.EMAIL}
              </a>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
