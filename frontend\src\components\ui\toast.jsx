"use client";

import { useState, useEffect } from "react";
import { createContext, useContext } from "react";

// Toast Context
const defaultToastContext = {
  toasts: [],
  addToast: () => {},
  removeToast: () => {},
};

const ToastContext = createContext(defaultToastContext);

// Toast Provider
export const ToastProvider = ({ children }) => {
  const [toasts, setToasts] = useState([]);

  const addToast = (message, type = "error", duration = 3000) => {
    const id = Date.now();
    const newToast = { id, message, type, duration };
    setToasts((prev) => [...prev, newToast]);
    // Auto remove toast after duration
    setTimeout(() => {
      removeToast(id);
    }, duration);
  };

  const removeToast = (id) => {
    setToasts((prev) => prev.filter((toast) => toast.id !== id));
  };

  const value = {
    toasts,
    addToast,
    removeToast,
  };

  return (
    <ToastContext.Provider value={value}>
      {children}
      <ToastContainer />
    </ToastContext.Provider>
  );
};

// Toast Container
const ToastContainer = () => {
  const { toasts, removeToast } = useContext(ToastContext);

  return (
    <div className="fixed top-4 right-4 z-[9999] space-y-3 pointer-events-none w-full max-w-md px-4 sm:px-0 sm:w-auto">
      {toasts.map((toast) => (
        <Toast key={toast.id} toast={toast} onRemove={removeToast} />
      ))}
    </div>
  );
};

// Individual Toast Component
const Toast = ({ toast, onRemove }) => {
  const [isVisible, setIsVisible] = useState(false);
  const [progress, setProgress] = useState(100);

  useEffect(() => {
    setTimeout(() => setIsVisible(true), 100);

    const interval = setInterval(() => {
      setProgress((prev) => {
        const newProgress = prev - 100 / (toast.duration / 50);
        return newProgress <= 0 ? 0 : newProgress;
      });
    }, 50);

    return () => clearInterval(interval);
  }, [toast.duration]);

  const getToastStyles = (type) => {
    switch (type) {
      case "error":
        return {
          bg: "bg-red-50/95 backdrop-blur-sm border-l-4 border-red-500",
          text: "text-red-900",
          icon: "text-red-600",
          iconBg: "bg-red-100",
          progress: "bg-red-500",
          shadow: "shadow-lg shadow-red-500/20",
        };
      case "success":
        return {
          bg: "bg-green-50/95 backdrop-blur-sm border-l-4 border-green-500",
          text: "text-green-900",
          icon: "text-green-600",
          iconBg: "bg-green-100",
          progress: "bg-green-500",
          shadow: "shadow-lg shadow-green-500/20",
        };
      case "warning":
        return {
          bg: "bg-amber-50/95 backdrop-blur-sm border-l-4 border-amber-500",
          text: "text-amber-900",
          icon: "text-amber-600",
          iconBg: "bg-amber-100",
          progress: "bg-amber-500",
          shadow: "shadow-lg shadow-amber-500/20",
        };
      case "info":
        return {
          bg: "bg-indigo-50/95 backdrop-blur-sm border-l-4 border-indigo-600",
          text: "text-indigo-900",
          icon: "text-indigo-600",
          iconBg: "bg-indigo-100",
          progress: "bg-indigo-600",
          shadow: "shadow-lg shadow-indigo-500/20",
        };
      default:
        return {
          bg: "bg-gray-50/95 backdrop-blur-sm border-l-4 border-gray-400",
          text: "text-gray-900",
          icon: "text-gray-600",
          iconBg: "bg-gray-100",
          progress: "bg-gray-500",
          shadow: "shadow-lg shadow-gray-500/20",
        };
    }
  };

  const styles = getToastStyles(toast.type);

  const getIcon = (type) => {
    const iconClass = "w-5 h-5";
    switch (type) {
      case "error":
        return (
          <svg
            className={iconClass}
            fill="currentColor"
            viewBox="0 0 20 20"
            aria-hidden="true"
          >
            <path
              fillRule="evenodd"
              d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
              clipRule="evenodd"
            />
          </svg>
        );
      case "success":
        return (
          <svg
            className={iconClass}
            fill="currentColor"
            viewBox="0 0 20 20"
            aria-hidden="true"
          >
            <path
              fillRule="evenodd"
              d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
              clipRule="evenodd"
            />
          </svg>
        );
      case "warning":
        return (
          <svg
            className={iconClass}
            fill="currentColor"
            viewBox="0 0 20 20"
            aria-hidden="true"
          >
            <path
              fillRule="evenodd"
              d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
              clipRule="evenodd"
            />
          </svg>
        );
      case "info":
        return (
          <svg
            className={iconClass}
            fill="currentColor"
            viewBox="0 0 20 20"
            aria-hidden="true"
          >
            <path
              fillRule="evenodd"
              d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
              clipRule="evenodd"
            />
          </svg>
        );
      default:
        return (
          <svg
            className={iconClass}
            fill="currentColor"
            viewBox="0 0 20 20"
            aria-hidden="true"
          >
            <path
              fillRule="evenodd"
              d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
              clipRule="evenodd"
            />
          </svg>
        );
    }
  };

  const handleRemove = () => {
    setIsVisible(false);
    setTimeout(() => onRemove(toast.id), 300);
  };

  return (
    <div
      className={`
        transform transition-all duration-300 ease-out
        ${
          isVisible
            ? "translate-x-0 opacity-100 scale-100"
            : "translate-x-full opacity-0 scale-95"
        }
        w-full bg-white ${styles.bg} ${styles.shadow} rounded-xl pointer-events-auto
        overflow-hidden border border-gray-200/50
      `}
      role="alert"
      aria-live="assertive"
    >
      <div className="p-4">
        <div className="flex items-start gap-3">
          <div
            className={`flex-shrink-0 rounded-lg p-2 ${styles.iconBg} transition-colors duration-200`}
          >
            <div className={`${styles.icon} flex items-center justify-center`}>
              {getIcon(toast.type)}
            </div>
          </div>
          <div className="flex-1 min-w-0 pt-0.5">
            <p
              className={`text-sm font-semibold leading-relaxed break-words ${styles.text}`}
              style={{ wordBreak: "break-word" }}
            >
              {toast.message}
            </p>
          </div>
          <div className="flex-shrink-0">
            <button
              onClick={handleRemove}
              className={`
                inline-flex items-center justify-center
                rounded-lg p-1.5
                ${styles.text} opacity-60
                hover:opacity-100 hover:bg-gray-100/50
                focus:outline-none focus:ring-2 focus:ring-offset-1 focus:ring-indigo-500
                transition-all duration-200 ease-in-out
              `}
              aria-label="Close notification"
            >
              <span className="sr-only">Close</span>
              <svg
                className="h-4 w-4"
                viewBox="0 0 20 20"
                fill="currentColor"
                aria-hidden="true"
              >
                <path
                  fillRule="evenodd"
                  d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                  clipRule="evenodd"
                />
              </svg>
            </button>
          </div>
        </div>
      </div>

      {/* Progress Bar */}
      <div className="h-0.5 bg-gray-200/50 overflow-hidden">
        <div
          className={`h-full ${styles.progress} transition-all duration-75 ease-linear`}
          style={{ width: `${progress}%` }}
        />
      </div>
    </div>
  );
};

// Custom hook to use toast
export const useToast = () => useContext(ToastContext);

export default ToastProvider;
