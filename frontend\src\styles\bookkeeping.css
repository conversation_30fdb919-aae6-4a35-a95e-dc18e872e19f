/* Bookkeeping Component Styles */

/* Main Container */
.bookkeeping-container {
  width: 100%;
  min-height: 100vh;
  background: linear-gradient(to bottom, rgb(249 250 251), rgb(243 244 246));
  overflow-x: hidden;
  overflow-y: auto;
  padding: 0.75rem 1rem; /* Reduced top padding */
  display: flex;
  flex-direction: column;
}

/* Small screens (mobile) */
@media (min-width: 375px) {
  .bookkeeping-container {
    padding: 1.25rem;
  }
}

/* Medium screens (tablet) */
@media (min-width: 640px) {
  .bookkeeping-container {
    padding: 1.5rem;
  }
}

/* Large screens (desktop) */
@media (min-width: 1024px) {
  .bookkeeping-container {
    padding: 2rem;
  }
}

/* Extra large screens */
@media (min-width: 1280px) {
  .bookkeeping-container {
    padding: 2.5rem;
  }
}

/* Wrapper */
.bookkeeping-wrapper {
  max-width: 85rem;
  margin-left: auto;
  margin-right: auto;
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

@media (min-width: 640px) {
  .bookkeeping-wrapper {
    gap: 2rem;
  }
}

/* Controls Row */
.bookkeeping-controls-row {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
  margin-bottom: 0;
}

@media (min-width: 768px) {
  .bookkeeping-controls-row {
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
  }
}

/* Heading Component */
.bookkeeping-heading {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  padding-bottom: 1.5rem;
  border-bottom: 2px solid rgb(229 231 235);
  flex-shrink: 0;
  width: 100%;
  margin-bottom: 0;
}

@media (min-width: 768px) {
  .bookkeeping-heading {
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    gap: 1.5rem;
    padding-bottom: 1.5rem;
  }
}

.bookkeeping-heading-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #4F46E5; /* Primary indigo - solid color */
  margin-bottom: 0.25rem; /* Reduced spacing */
  line-height: 1.3;
  word-wrap: break-word;
}

@media (min-width: 640px) {
  .bookkeeping-heading-title {
    font-size: 1.875rem;
  }
}

@media (min-width: 1024px) {
  .bookkeeping-heading-title {
    font-size: 2.25rem;
  }
}

.bookkeeping-heading-subtitle {
  color: #64748B; /* Lighter subtle gray */
  font-size: 0.875rem;
  line-height: 1.5;
  word-wrap: break-word;
  font-weight: 400; /* Lighter font weight */
}

@media (min-width: 640px) {
  .bookkeeping-heading-subtitle {
    font-size: 0.9375rem;
  }
}

.bookkeeping-heading-back-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  border: 1px solid #4F46E5; /* Thin indigo border */
  color: #4F46E5; /* Primary indigo text */
  background-color: transparent; /* Ghost button */
  padding: 0.625rem 1.25rem;
  border-radius: 0.75rem; /* 12px consistent radius */
  font-weight: 500;
  font-size: 0.875rem;
  transition: all 0.2s ease-in-out;
  box-shadow: none; /* Remove shadow for ghost style */
  width: 100%;
  min-height: 2.75rem;
  align-self: flex-start;
}

@media (min-width: 768px) {
  .bookkeeping-heading-back-btn {
    width: auto;
    min-width: 10rem;
    align-self: center;
  }
}

.bookkeeping-heading-back-btn:hover {
  color: #4F46E5; /* Keep indigo text */
  background-color: rgba(79, 70, 229, 0.05); /* Very light lavender fill */
  border-color: #4F46E5; /* Keep indigo border */
  box-shadow: none; /* No shadow on hover */
  transform: none; /* No transform */
}

/* Client Info Card */
.bookkeeping-client-info {
  width: 100%;
  background-color: white;
  border: 1px solid rgb(229 231 235);
  border-left: 4px solid #4F46E5; /* Primary indigo */
  border-radius: 0.75rem; /* 12px consistent radius */
  padding: 1.25rem;
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -2px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease-in-out;
  flex-shrink: 0;
  height: fit-content;
}

@media (min-width: 640px) {
  .bookkeeping-client-info {
    padding: 1.5rem;
  }
}

.bookkeeping-client-info:hover {
  box-shadow:
    0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05);
  transform: translateY(-1px);
}

.bookkeeping-client-info-content {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
}

.bookkeeping-client-info-icon {
  flex-shrink: 0;
  width: 2.5rem; /* Slightly smaller */
  height: 2.5rem;
  background: #4F46E5; /* Solid indigo background */
  border-radius: 0.75rem; /* 12px consistent radius */
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow:
    0 2px 4px -1px rgba(79, 70, 229, 0.2),
    0 1px 2px -1px rgba(79, 70, 229, 0.1);
}

.bookkeeping-client-info-icon svg {
  width: 1.25rem; /* Slightly smaller clean white icon */
  height: 1.25rem;
  color: white;
}

.bookkeeping-client-info-details {
  flex: 1;
  min-width: 0;
  overflow: hidden;
}

.bookkeeping-client-info-label {
  font-size: 0.75rem;
  font-weight: 500; /* Lighter font weight for descriptive text */
  color: rgb(107 114 128);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-bottom: 0.375rem;
}

.bookkeeping-client-info-name {
  font-size: 1rem;
  font-weight: 700; /* Bold for active data points */
  color: #4F46E5; /* Primary indigo */
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-bottom: 0.5rem;
  max-width: 100%;
  line-height: 1.4;
}

@media (min-width: 640px) {
  .bookkeeping-client-info-name {
    font-size: 1.125rem;
  }
}

.bookkeeping-client-info-meta {
  margin-top: 0.5rem;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 1rem;
  font-size: 0.875rem;
}

.bookkeeping-client-info-status {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  color: #10B981; /* Darker green for status text */
  font-weight: 600; /* More prominent */
  font-size: 0.875rem;
}

.bookkeeping-client-info-status-dot {
  width: 0.625rem; /* Larger dot for visibility */
  height: 0.625rem;
  border-radius: 9999px;
  background: #10B981; /* Darker green for prominence */
}

.bookkeeping-client-info-last-submission {
  color: rgb(75 85 99); /* text-gray-600 */
}

/* Month Selector */
.bookkeeping-month-selector {
  width: 100%;
  background-color: white;
  border-radius: 0.75rem; /* 12px consistent radius */
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -2px rgba(0, 0, 0, 0.1);
  border: 1px solid rgb(229 231 235);
  padding: 1.25rem;
  transition: all 0.2s ease-in-out;
  flex-shrink: 0;
  height: fit-content;
  display: flex;
  flex-direction: column;
}

@media (min-width: 640px) {
  .bookkeeping-month-selector {
    padding: 1.5rem;
  }
}

.bookkeeping-month-selector:hover {
  box-shadow:
    0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05); /* shadow-lg */
}

.bookkeeping-month-selector-header {
  display: flex;
  align-items: center;
  gap: 0.625rem;
  margin-bottom: 1rem;
}

.bookkeeping-month-selector-icon {
  width: 1.25rem;
  height: 1.25rem;
  color: #4F46E5; /* Primary indigo */
  flex-shrink: 0;
}

.bookkeeping-month-selector-title {
  font-size: 0.9375rem;
  font-weight: 600; /* Semi-bold for section titles */
  color: rgb(17 24 39);
  letter-spacing: 0.025em;
}

@media (min-width: 640px) {
  .bookkeeping-month-selector-title {
    font-size: 1rem;
  }
}

/* Upload Section */
.bookkeeping-upload-section {
  background-color: white;
  border-radius: 0.75rem; /* 12px consistent radius */
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -2px rgba(0, 0, 0, 0.1);
  border: 1px solid rgb(229 231 235);
  padding: 1.5rem;
  transition: all 0.2s ease-in-out;
  width: 100%;
  display: flex;
  flex-direction: column;
  scrollbar-width: thin;
  scrollbar-color: rgb(203 213 225) transparent;
}

@media (min-width: 640px) {
  .bookkeeping-upload-section {
    padding: 1.75rem;
  }
}

@media (min-width: 1024px) {
  .bookkeeping-upload-section {
    padding: 2rem;
  }
}

.bookkeeping-upload-section::-webkit-scrollbar {
  width: 6px;
}

.bookkeeping-upload-section::-webkit-scrollbar-track {
  background: transparent;
}

.bookkeeping-upload-section::-webkit-scrollbar-thumb {
  background-color: rgb(203 213 225);
  border-radius: 3px;
}

/* Small screens */
@media (min-width: 375px) {
  .bookkeeping-upload-section {
    padding: 1rem; /* p-4 */
}
}

/* Medium screens */
@media (min-width: 640px) {
  .bookkeeping-upload-section {
    padding: 1.25rem; /* sm:p-5 */
    overflow: hidden; /* Hide overflow on larger screens */
  }
}

/* Large screens */
@media (min-width: 1024px) {
  .bookkeeping-upload-section {
    padding: 1.5rem; /* lg:p-6 */
  }
}

.bookkeeping-upload-section:hover {
  box-shadow:
    0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05); /* shadow-lg */
}

.bookkeeping-upload-section-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid rgb(243 244 246);
  flex-shrink: 0;
}

.bookkeeping-upload-section-accent {
  width: 0.375rem;
  height: 1.75rem;
  background: linear-gradient(to bottom, rgb(99 102 241), rgb(147 51 234));
  border-radius: 9999px;
  flex-shrink: 0;
}

.bookkeeping-upload-section-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: rgb(17 24 39);
  letter-spacing: -0.025em;
  word-wrap: break-word;
}

@media (min-width: 640px) {
  .bookkeeping-upload-section-title {
    font-size: 1.375rem;
  }
}

.bookkeeping-upload-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
  flex: 1;
  min-height: 0;
  overflow-y: auto;
  overflow-x: hidden;
  scrollbar-width: thin;
  scrollbar-color: rgb(203 213 225) transparent;
  padding-bottom: 0.5rem;
}

@media (min-width: 640px) {
  .bookkeeping-upload-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.25rem;
  }
}

@media (min-width: 1024px) {
  .bookkeeping-upload-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
  }
}

.bookkeeping-upload-grid::-webkit-scrollbar {
  width: 6px;
}

.bookkeeping-upload-grid::-webkit-scrollbar-track {
  background: transparent;
}

.bookkeeping-upload-grid::-webkit-scrollbar-thumb {
  background-color: rgb(203 213 225);
  border-radius: 3px;
}

/* Upload Card */
.bookkeeping-upload-card {
  border: 1px solid rgb(229 231 235); /* Thin border - will be overridden by status */
  border-radius: 0.75rem; /* 12px consistent radius */
  padding: 1.5rem;
  text-align: center;
  transition: all 0.3s ease-in-out;
  background-color: white;
  box-shadow:
    0 1px 3px 0 rgba(0, 0, 0, 0.1),
    0 1px 2px -1px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  min-height: 15rem;
  position: relative;
  overflow: hidden;
}

/* Status-colored borders */
.bookkeeping-upload-card[data-status="synced"] {
  border-color: #10B981; /* Green border for synced */
}

.bookkeeping-upload-card[data-status="not-subscribed"] {
  border-color: #EF4444; /* Red border for not subscribed */
}

.bookkeeping-upload-card[data-status="not-synced"] {
  border-color: #E5E7EB; /* Gray border for not synced */
}

@media (min-width: 640px) {
  .bookkeeping-upload-card {
    padding: 1.75rem;
    min-height: 17rem;
  }
}

.bookkeeping-upload-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 0.25rem; /* h-1 */
  background: linear-gradient(
    to right,
    rgb(99 102 241),
    rgb(147 51 234)
  ); /* from-indigo-500 to-purple-600 */
  opacity: 0;
  transition: opacity 0.3s ease-in-out;
}

.bookkeeping-upload-card:hover {
  border-color: rgb(99 102 241); /* hover:border-indigo-500 */
  box-shadow:
    0 10px 15px -3px rgba(99, 102, 241, 0.15),
    0 4px 6px -2px rgba(99, 102, 241, 0.1); /* shadow-lg with indigo tint */
  transform: translateY(-2px);
}

.bookkeeping-upload-card:hover::before {
  opacity: 1;
}

.bookkeeping-upload-card-icon {
  margin-bottom: 1rem;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-shrink: 0;
  width: 100%;
}

.bookkeeping-upload-card-icon svg {
  width: 2.5rem; /* Larger icon */
  height: 2.5rem;
  color: #818CF8; /* Lighter indigo */
  padding: 0.75rem;
  background-color: rgba(129, 140, 248, 0.1); /* Light lavender circle */
  border-radius: 50%; /* Circular background */
}

.bookkeeping-upload-card-title {
  font-size: 1rem;
  font-weight: 600; /* Semi-bold for titles */
  color: rgb(17 24 39);
  margin-bottom: 1rem;
  line-height: 1.4;
  word-wrap: break-word;
  hyphens: auto;
  width: 100%;
}

@media (min-width: 640px) {
  .bookkeeping-upload-card-title {
    font-size: 1.0625rem;
  }
}

.bookkeeping-upload-card-status {
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  min-height: 1.5rem;
  flex-wrap: wrap;
  width: 100%;
}

.bookkeeping-upload-card-status-dot {
  display: inline-block;
  width: 0.625rem; /* Larger dot for visibility */
  height: 0.625rem;
  border-radius: 9999px; /* rounded-full */
}

.bookkeeping-upload-card-status-dot.red {
  background-color: rgb(248 113 113); /* bg-red-400 */
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.bookkeeping-upload-card-status-dot.green {
  background-color: rgb(74 222 128); /* bg-green-400 */
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.bookkeeping-upload-card-status-dot.gray {
  background-color: rgb(156 163 175); /* bg-gray-400 */
}

.bookkeeping-upload-card-status-badge {
  font-size: 0.8125rem;
  font-weight: 600; /* More prominent */
  padding: 0.375rem 0.75rem;
  border-radius: 0.5rem; /* 8px consistent radius */
  line-height: 1.5;
  word-wrap: break-word;
  text-align: center;
}

@media (min-width: 640px) {
  .bookkeeping-upload-card-status-badge {
    font-size: 0.875rem;
    padding: 0.375rem 0.875rem;
  }
}

.bookkeeping-upload-card-status-badge.red {
  color: #EF4444; /* Clear red */
  background-color: rgba(239, 68, 68, 0.1); /* Light red background */
  font-weight: 600; /* More prominent */
}

.bookkeeping-upload-card-status-badge.green {
  color: #10B981; /* Darker green for prominence */
  background-color: rgba(16, 185, 129, 0.1); /* Light green background */
  font-weight: 600; /* More prominent */
}

.bookkeeping-upload-card-status-badge.gray {
  color: rgb(107 114 128);
  background-color: rgb(249 250 251);
}

.bookkeeping-upload-card-status-badge.yellow {
  color: rgb(161 98 7);
  background-color: rgb(254 252 232);
}

.bookkeeping-upload-card-button {
  margin-top: auto;
  width: 100%;
  background: #4F46E5; /* Primary indigo - solid color */
  color: white;
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -2px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease-in-out;
  border-radius: 0.75rem; /* 12px consistent radius */
  padding: 0.75rem 1rem;
  font-weight: 600;
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  border: none;
  cursor: pointer;
  min-height: 2.75rem;
  height: auto;
  touch-action: manipulation;
}

/* Secondary button style for less critical actions */
.bookkeeping-upload-card-button.secondary {
  background: white;
  color: #4F46E5;
  border: 1px solid #4F46E5;
  box-shadow: none;
}

.bookkeeping-upload-card-button.secondary:hover:not(:disabled) {
  background: rgba(79, 70, 229, 0.05);
  border-color: #4F46E5;
}

@media (min-width: 640px) {
  .bookkeeping-upload-card-button {
    padding: 0.8125rem 1.25rem;
    font-size: 0.9375rem;
  }
}

.bookkeeping-upload-card-button:hover:not(:disabled) {
  background: #4338CA; /* Darker indigo on hover */
  box-shadow:
    0 10px 15px -3px rgba(79, 70, 229, 0.3),
    0 4px 6px -2px rgba(79, 70, 229, 0.2);
  transform: translateY(-1px);
}

.bookkeeping-upload-card-button:disabled {
  background-color: rgb(156 163 175); /* bg-gray-400 */
  cursor: not-allowed;
  opacity: 0.6;
}

.bookkeeping-upload-card-error {
  margin-top: 0.75rem; /* mt-3 */
  font-size: 0.875rem; /* text-sm */
  color: rgb(220 38 38); /* text-red-600 */
}

/* Action Buttons */
.bookkeeping-actions {
  display: flex;
  flex-direction: column;
  align-items: stretch;
  margin-top: 1rem; /* mt-4 - Mobile */
  padding: 1rem; /* p-4 - Mobile */
  background-color: white;
  border-radius: 0.75rem; /* rounded-xl */
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -2px rgba(0, 0, 0, 0.1); /* shadow-md */
  border: 1px solid rgb(229 231 235); /* border-gray-200 */
  gap: 0.75rem; /* gap-3 */
  flex-shrink: 0;
}

/* Small screens */
@media (min-width: 375px) {
  .bookkeeping-actions {
    margin-top: 1.25rem; /* mt-5 */
    padding: 1.25rem; /* p-5 */
  }
}

/* Medium screens */
@media (min-width: 640px) {
  .bookkeeping-actions {
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    margin-top: 1.5rem; /* mt-6 */
    padding: 1.5rem; /* p-6 */
  }
}

.bookkeeping-actions-cancel {
  flex: 1;
  border: 1px solid rgb(229 231 235); /* border-gray-200 */
  color: rgb(75 85 99); /* text-gray-600 */
  background-color: white;
  transition: all 0.2s ease-in-out;
  min-height: 2.75rem; /* Touch-friendly height */
  font-size: 0.875rem; /* text-sm - Mobile */
  touch-action: manipulation;
}

/* Small screens */
@media (min-width: 375px) {
  .bookkeeping-actions-cancel {
    font-size: 0.9375rem; /* text-sm */
  }
}

.bookkeeping-actions-cancel:hover:not(:disabled) {
  border-color: rgb(209 213 219); /* hover:border-gray-300 */
  background-color: rgb(249 250 251); /* hover:bg-gray-50 */
  color: rgb(31 41 55); /* hover:text-gray-800 */
}

/* Medium screens */
@media (min-width: 640px) {
  .bookkeeping-actions-cancel {
    flex: none;
    min-width: 8rem;
  }
}


.bookkeeping-actions-group {
  display: flex;
  flex-direction: column;
  gap: 0.75rem; /* gap-3 */
  flex: 1;
  width: 100%;
}

/* Medium screens */
@media (min-width: 640px) {
  .bookkeeping-actions-group {
    flex-direction: row;
    flex: none;
    width: auto;
    gap: 1rem; /* gap-4 */
  }
}

.bookkeeping-actions-draft {
  flex: 1;
  border: 1px solid rgb(129 140 248); /* border-indigo-400 */
  color: rgb(99 102 241); /* text-indigo-500 */
  background-color: white;
  transition: all 0.2s ease-in-out;
  min-height: 2.75rem; /* Touch-friendly height */
  font-size: 0.875rem; /* text-sm - Mobile */
  touch-action: manipulation;
}

/* Small screens */
@media (min-width: 375px) {
  .bookkeeping-actions-draft {
    font-size: 0.9375rem; /* text-sm */
  }
}

/* Medium screens */
@media (min-width: 640px) {
  .bookkeeping-actions-draft {
    flex: none;
    min-width: 10rem;
  }
}

.bookkeeping-actions-draft:hover:not(:disabled) {
  background: linear-gradient(
    to right,
    rgb(238 242 255),
    rgb(250 245 255)
  ); /* hover:bg-indigo-50 to purple-50 */
  border-color: rgb(99 102 241); /* hover:border-indigo-500 */
  color: rgb(79 70 229); /* hover:text-indigo-600 */
}

.bookkeeping-actions-submit {
  flex: 1;
  background: linear-gradient(
    to right,
    rgb(99 102 241),
    rgb(147 51 234)
  ); /* from-indigo-600 to-purple-600 */
  color: white;
  box-shadow:
    0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05); /* shadow-lg */
  transition: all 0.2s ease-in-out;
  min-height: 2.75rem; /* Touch-friendly height */
  font-size: 0.875rem; /* text-sm - Mobile */
  touch-action: manipulation;
}

/* Small screens */
@media (min-width: 375px) {
  .bookkeeping-actions-submit {
    font-size: 0.9375rem; /* text-sm */
  }
}

/* Medium screens */
@media (min-width: 640px) {
  .bookkeeping-actions-submit {
    flex: none;
    min-width: 10rem;
  }
}

.bookkeeping-actions-submit:hover:not(:disabled) {
  background: linear-gradient(
    to right,
    rgb(79 70 229),
    rgb(126 34 206)
  ); /* hover:from-indigo-700 hover:to-purple-700 */
  box-shadow:
    0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04); /* hover:shadow-xl */
}

.bookkeeping-actions-submit:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Payroll Card Button */
.bookkeeping-payroll-button {
  padding: 0.625rem 0.875rem; /* px-3.5 py-2.5 - Mobile */
  border-radius: 0.5rem; /* rounded-lg - Match upload-card-button */
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease-in-out;
  font-weight: 600; /* font-semibold */
  font-size: 0.8125rem; /* text-sm - Mobile */
  gap: 0.5rem; /* gap-2 */
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -2px rgba(0, 0, 0, 0.1); /* shadow-md */
  margin-top: auto;
  width: 100%;
  border: none;
  cursor: pointer;
  min-height: 2.75rem; /* Touch-friendly height on mobile */
  height: auto;
  touch-action: manipulation; /* Better touch handling */
}

/* Small screens */
@media (min-width: 375px) {
  .bookkeeping-payroll-button {
    padding: 0.625rem 1rem; /* px-4 py-2.5 */
    font-size: 0.875rem; /* text-sm */
  }
}

/* Medium screens */
@media (min-width: 640px) {
  .bookkeeping-payroll-button {
    padding: 0.75rem 1.25rem; /* sm:px-5 sm:py-3 */
    font-size: 0.9375rem; /* sm:text-sm */
    min-height: 2.75rem;
  }
}

.bookkeeping-payroll-button.active {
  background: linear-gradient(135deg, rgb(99 102 241), rgb(79 70 229));
  color: white;
  cursor: pointer;
}

.bookkeeping-payroll-button.active:hover {
  background: linear-gradient(135deg, rgb(79 70 229), rgb(67 56 202));
  box-shadow:
    0 10px 15px -3px rgba(99, 102, 241, 0.3),
    0 4px 6px -2px rgba(99, 102, 241, 0.2);
  transform: translateY(-1px);
}

.bookkeeping-payroll-button.disabled {
  background-color: rgb(156 163 175); /* bg-gray-400 */
  color: rgb(229 231 235); /* text-gray-200 */
  cursor: not-allowed;
  opacity: 0.6;
}

/* Animations */
@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}
