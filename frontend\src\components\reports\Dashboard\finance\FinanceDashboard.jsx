"use client";

import { useRef, useState } from "react";
import { useSelector } from "react-redux";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "antd";
import { FaFileDownload } from "react-icons/fa";

import KPIBox from "../shared/KPIBox";
import InfoCard from "../shared/InfoCard";
import Donut<PERSON>hart from "../shared/DonutChart";
import BarChart from "../shared/BarChart";
import CombinedChart from "../shared/CombinedChart";
import TableCard from "../shared/TableCard";
import {
  mapBalanceSheetData,
  mapCashflowByGroupData,
  mapExpenseBreakdownChart,
  mapIncomeStatementData,
  mapKpiData,
  mapRevExpNetChart,
} from "@/utils/methods/financeReports";
import { downloadReportFromBlob } from "@/utils/methods/pdfExport";

const accent = {
  revenue: "#16a34a",
  expenses: "#dc2626",
  ebitda: "#7c3aed",
  netCashflow: "#0f766e",
  profitMargin: "#2563eb",
};

const MONTHS = [
  "January",
  "February",
  "March",
  "April",
  "May",
  "June",
  "July",
  "August",
  "September",
  "October",
  "November",
  "December",
];

const MONTHS_SHORT = [
  "Jan",
  "Feb",
  "Mar",
  "Apr",
  "May",
  "Jun",
  "Jul",
  "Aug",
  "Sep",
  "Oct",
  "Nov",
  "Dec",
];

export default function FinanceDashboard({
  organizationId,
  organizationName,
  month,
  year,
}) {
  const dashboardRef = useRef(null);
  const {
    balanceSheetData,
    incomeExpenseData,
    kpisData,
    cashFlowData,
    expenseBreakdownData,
    revenueExpenseData,
  } = useSelector((state) => state.reports);

  const balanceSheet = mapIncomeStatementData(balanceSheetData);
  const incomeStatement = mapIncomeStatementData(incomeExpenseData);
  const kpi = mapKpiData(kpisData);
  const charts = {
    cashflowByGroup: mapCashflowByGroupData(cashFlowData),
    revExpNet: mapRevExpNetChart(revenueExpenseData),
    expenseBreakdown: mapExpenseBreakdownChart(expenseBreakdownData),
  };
  const parsedMonthIndex =
    month && Number(month) >= 1 && Number(month) <= 12 ? Number(month) - 1 : 0;
  const monthFullName = MONTHS[parsedMonthIndex];
  const monthShortName = MONTHS_SHORT[parsedMonthIndex];
  const yearLabel = year || new Date().getFullYear();
  const monthYearShort = `${monthShortName}-${String(yearLabel).slice(-2)}`;
  const revExpNetChart = {
    ...charts.revExpNet,
    categories: [monthYearShort],
  };
  const formatCurrencyKWithBrackets = (value) => {
    if (typeof value !== "number") return value;
    const abs = Math.abs(value);
    const label = `$${(abs / 1000).toFixed(0)}K`;
    return value < 0 ? `(${label})` : label;
  };
  const cashflowValues = charts.cashflowByGroup.values || [];
  const hasIncrease = cashflowValues.some((value) => value > 0);
  const hasDecrease = cashflowValues.some((value) => value < 0);
  const cashflowLegend = [
    ...(hasIncrease ? [{ label: "Increase", color: "#34d399" }] : []),
    ...(hasDecrease ? [{ label: "Decrease", color: "#f87171" }] : []),
  ];

  const [isExporting, setIsExporting] = useState(false);

  const handleExportPdf = () => {
    downloadReportFromBlob({
      organizationId,
      organizationName,
      month,
      year,
      serviceType: "finance",
      onLoadingChange: setIsExporting,
    });
  };

  return (
    <div className="flex flex-col gap-5 w-full py-3" ref={dashboardRef}>
      <section className="card-surface w-full flex flex-col gap-1 p-5 lg:p-6 text-center relative">
        <p className="text-xs font-semibold uppercase tracking-wide text-slate-500">
          {organizationName || "Corbin Dental Center"}
        </p>
        <h2 className="text-4xl font-semibold text-slate-900">
          Financial Summary For {monthFullName} - {yearLabel}
        </h2>
        <div className="absolute top-5 right-5">
          <Tooltip title="Export to PDF" placement="bottom">
            <Button
              icon={<FaFileDownload style={{ fontSize: "18px" }} />}
              onClick={handleExportPdf}
              size="large"
              loading={isExporting}
              shape="circle"
              style={{
                backgroundColor: "#dc2626",
                borderColor: "#dc2626",
                color: "white",
              }}
            />
          </Tooltip>
        </div>
      </section>

      {/* KPI Row */}
      <div className="w-full grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-5 gap-3 sm:gap-4 items-stretch">
        <div className="w-full h-full">
          <KPIBox
            title="Revenue"
            amount={kpi.revenue.amount}
            pmValue={kpi.revenue.pm}
            ytdValue={kpi.revenue.ytd}
            change={kpi.revenue.change}
            isPositive={kpi.revenue.isPositive}
            color={accent.revenue}
          />
        </div>
        <div className="w-full h-full">
          <KPIBox
            title="Expenses"
            amount={kpi.expenses.amount}
            pmValue={kpi.expenses.pm}
            ytdValue={kpi.expenses.ytd}
            change={kpi.expenses.change}
            isPositive={kpi.expenses.isPositive}
            color={accent.expenses}
          />
        </div>
        <div className="w-full h-full">
          <KPIBox
            title="Net Income"
            amount={kpi.income.amount}
            pmValue={kpi.income.pm}
            ytdValue={kpi.income.ytd}
            change={kpi.income.change}
            isPositive={kpi.income.isPositive}
            color={accent.income}
          />
        </div>
        <div className="w-full h-full">
          <KPIBox
            title="Net Cashflow"
            amount={kpi.netCashflow.amount}
            pmValue={kpi.netCashflow.pm}
            ytdValue={kpi.netCashflow.ytd}
            change={kpi.netCashflow.change}
            isPositive={kpi.netCashflow.isPositive}
            color={accent.netCashflow}
          />
        </div>
        <div className="w-full h-full">
          <KPIBox
            title="Profit Margin"
            amount={kpi.profitMargin.amount}
            pmValue={kpi.profitMargin.pm}
            ytdValue={kpi.profitMargin.ytd}
            change={kpi.profitMargin.change}
            isPositive={kpi.profitMargin.isPositive}
            color={accent.profitMargin}
          />
        </div>
      </div>

      {/* Charts Section */}
      <div className="w-full grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-3 sm:gap-4">
        <InfoCard title="Cashflow by Group" subtitle="USD">
          {cashflowLegend.length ? (
            <div className="flex items-center justify-center gap-4 text-xs text-slate-600 mb-1.5">
              {cashflowLegend.map((item) => (
                <div
                  key={item.label}
                  className="flex items-center gap-1.5 font-medium"
                >
                  <span
                    className="inline-block w-2.5 h-2.5 rounded-full"
                    style={{ backgroundColor: item.color }}
                    aria-label={item.label}
                  />
                  <span>{item.label}</span>
                </div>
              ))}
            </div>
          ) : null}
          <BarChart
            categories={charts.cashflowByGroup.categories}
            series={[
              {
                name: "Cashflow",
                data: charts.cashflowByGroup.values,
              },
            ]}
            positiveColor="#34d399"
            negativeColor="#f87171"
            valueFormatter={formatCurrencyKWithBrackets}
          />
        </InfoCard>

        <InfoCard
          title="Revenue vs Expenses vs Net Income"
          subtitle="Monthly trend"
        >
          <CombinedChart
            categories={revExpNetChart.categories}
            series={[
              {
                name: "Revenue",
                data: revExpNetChart.revenue,
                color: "#34d399",
              },
              {
                name: "Expenses",
                data: revExpNetChart.expenses,
                color: "#f87171",
              },
              {
                name: "Net Income",
                data: revExpNetChart.netIncome,
                color: "#2f7ed8",
              },
            ]}
            valueFormatter={formatCurrencyKWithBrackets}
          />
        </InfoCard>

        <InfoCard title="Expense Breakdown" subtitle="Payroll vs Other">
          <DonutChart data={charts.expenseBreakdown.data} />
        </InfoCard>
      </div>

      {/* Tables Section */}
      <div className="w-full grid grid-cols-1 lg:grid-cols-2 gap-3 sm:gap-4">
        <TableCard
          title="Balance Sheet"
          subtitle="Consolidated view"
          data={balanceSheet}
        />
        <TableCard
          title="Income & Expense Statement"
          subtitle="By location"
          data={incomeStatement}
        />
      </div>
    </div>
  );
}
