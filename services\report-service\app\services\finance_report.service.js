import KpiService from "./kpi.service.js";
import CashFlowService from "./cash_flow.service.js";
import RevenueExpenseService from "./revenue_expense.service.js";
import BalanceSheetService from "./balance_sheet.service.js";
import IncomeExpenseStatementService from "./income_expense_statement.service.js";
import ExpenseBreakdownService from "./expense_breakdown.service.js";
import { generateHTML, generatePDF } from "../utils/pdf.utils.js";
import { createLogger } from "../utils/logger.utils.js";
import { LOGGER_NAMES } from "../utils/constants/log.constants.js";

const logger = createLogger(LOGGER_NAMES.REPORT_SERVICE);

const generateFinanceReportPDF = async ({ organization_id, organization_name, month, year }) => {
  try {
    logger.info(`Fetching finance data: org=${organization_id}, month=${month}, year=${year}`);
    
    const [kpi, cashFlow, revExp, balSheet, incExp, expBreak] = await Promise.allSettled([
      KpiService.getKpiData({ organization_id, month, year }),
      CashFlowService.getCashFlowByGroup({ organization_id, month, year }),
      RevenueExpenseService.getRevenueExpenseData({ organization_id, month, year }),
      BalanceSheetService.getBalanceSheetData({ org_id: organization_id, month, year }),
      IncomeExpenseStatementService.getIncomeExpenseStatement({ organization_id, month, year }),
      ExpenseBreakdownService.getExpenseBreakdown({ organization_id, month, year })
    ]);

    const getData = (res, path) => res.status === "fulfilled" ? res.value?.[path] : null;
    const cf = getData(cashFlow, 'cash_flow');

    const reportData = {
      organization: organization_name || "Organization",
      month,
      year,
      kpi: getData(kpi, 'kpi'),
      cashFlow: cf ? [
        { category: "Operating", total: cf.operating || 0 },
        { category: "Investing", total: cf.investing || 0 },
        { category: "Financing", total: cf.financing || 0 },
        { category: "Net Cash Flow", total: cf.net_cash_flow || 0 }
      ] : [],
      revenueExpense: revExp.status === "fulfilled" ? {
        revenue: revExp.value?.revenue || "$0",
        expense: revExp.value?.expense || "$0",
        net_income: revExp.value?.net_income || "$0"
      } : null,
      balanceSheet: getData(balSheet, 'data') || [],
      incomeExpense: getData(incExp, 'data') || [],
      expenseBreakdown: getData(expBreak, 'breakdown') || []
    };

    logger.info("Generating PDF from aggregated data");
    return await generatePDF(generateHTML(reportData));
  } catch (error) {
    logger.error("Error generating finance PDF:", error);
    throw error;
  }
};

export default { generateFinanceReportPDF };
