"use client";

import { useState, useRef, useEffect, memo } from "react";
import { Button } from "@/components/ui/button";
import { UploadCloud } from "lucide-react";
import { BOOKCLOSURE_CONSTANTS } from "@/utils/constants/bookclosure";
import { formatDateToMMDDYYYY, getMonthDateRange } from "@/utils/methods";

const UploadCard = memo(function UploadCard({
  title,
  icon,
  onSync,
  isLoading = false,
  lastSync = null,
  isFinancial = false,
  realmId = null,
  schemaName = null,
  selectedMonth = null,
  isSelected = true,
  isSynced = false,
  disableSync = false,
  errorMessage = null,
}) {
  const [files, setFiles] = useState([]);
  const [lastSyncedDate, setLastSyncedDate] = useState(null);
  const prevMonthRef = useRef(selectedMonth);

  useEffect(() => {
    if (prevMonthRef.current !== selectedMonth) {
      setLastSyncedDate(null);
      prevMonthRef.current = selectedMonth;
    }
  }, [selectedMonth]);

  const handleSync = async () => {
    if (onSync) {
      if (isFinancial) {
        await onSync();
      } else {
        await onSync(files);
      }
      if (selectedMonth) {
        const { endDate } = getMonthDateRange(selectedMonth);
        setLastSyncedDate(formatDateToMMDDYYYY(endDate));
      }
    }
  };

  // Determine status for border color
  const getStatus = () => {
    if (!isSelected) return "not-subscribed";
    if (lastSync || lastSyncedDate) return "synced";
    return "not-synced";
  };

  return (
    <div className="bookkeeping-upload-card" data-status={getStatus()}>
      <div className="bookkeeping-upload-card-icon">{icon}</div>
      <h3 className="bookkeeping-upload-card-title">{title}</h3>
      <div className="bookkeeping-upload-card-status">
        {!isSelected ? (
          <>
            <span className="bookkeeping-upload-card-status-dot red"></span>
            <span className="bookkeeping-upload-card-status-badge red">
              {BOOKCLOSURE_CONSTANTS.UPLOAD_SECTION.NOT_SELECTED_LABEL}
            </span>
          </>
        ) : lastSync || lastSyncedDate ? (
          <>
            <span className="bookkeeping-upload-card-status-dot green"></span>
            <span className="bookkeeping-upload-card-status-badge green">
              {BOOKCLOSURE_CONSTANTS.UPLOAD_CARD.LAST_SYNCED_LABEL}{" "}
              {lastSync || lastSyncedDate}
            </span>
          </>
        ) : (
          <>
            <span className="bookkeeping-upload-card-status-dot gray"></span>
            <span className="bookkeeping-upload-card-status-badge gray">
              Not synced yet
            </span>
          </>
        )}
      </div>
      <Button
        onClick={handleSync}
        disabled={
          !isSelected ||
          isLoading ||
          isSynced ||
          disableSync ||
          (isFinancial && (!realmId || !schemaName))
        }
        variant="default"
        className="bookkeeping-upload-card-button"
        leftIcon={<UploadCloud className="w-4 h-4" />}
      >
        {isLoading
          ? BOOKCLOSURE_CONSTANTS.UPLOAD_CARD.SYNC_BUTTON.SYNCING
          : isSynced
            ? BOOKCLOSURE_CONSTANTS.UPLOAD_CARD.SYNC_BUTTON.SYNCED
            : isFinancial && (!realmId || !schemaName)
              ? "Not connected yet"
              : BOOKCLOSURE_CONSTANTS.UPLOAD_CARD.SYNC_BUTTON.SYNC}
      </Button>
      {errorMessage && (
        <p className="bookkeeping-upload-card-error" role="alert">
          {errorMessage}
        </p>
      )}
    </div>
  );
});

UploadCard.displayName = "UploadCard";

export default UploadCard;
