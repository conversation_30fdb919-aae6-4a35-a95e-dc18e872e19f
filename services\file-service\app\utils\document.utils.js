import {
  DOCUMENT_SERVICE_LIST,
  DOCUMENT_VALIDATION_RULES,
} from "./constants/document.constants.js";

const SERVICE_NAME_MAP = {
  finance: "financial",
  financial: "financial",
  operations: "operational",
  operational: "operational",
  payroll: "payroll",
  pms: "payroll",
};

export const normalizeServiceName = (service) => {
  if (!service || typeof service !== "string") {
    return service;
  }

  const serviceLower = service.toLowerCase();
  return SERVICE_NAME_MAP[serviceLower]
    ? SERVICE_NAME_MAP[serviceLower]
    : serviceLower;
};

export const validateService = (service) => {
  return (
    !!service && DOCUMENT_SERVICE_LIST.includes(normalizeServiceName(service))
  );
};

export const validateMonth = (month) => {
  return (
    typeof month === "number" &&
    month >= DOCUMENT_VALIDATION_RULES.MONTH_MIN &&
    month <= DOCUMENT_VALIDATION_RULES.MONTH_MAX
  );
};

export const validateYear = (year) => {
  return (
    typeof year === "number" &&
    year >= DOCUMENT_VALIDATION_RULES.YEAR_MIN &&
    year <= DOCUMENT_VALIDATION_RULES.YEAR_MAX
  );
};

export const sanitizeBlobPath = (path) => {
  if (!path) return null;
  return String(path).trim().replace(/\\/g, "/");
};

export const buildBlobFilePath = (basePath, fileName) => {
  const sanitizedBase = sanitizeBlobPath(basePath);
  if (!sanitizedBase || sanitizedBase.length === 0) {
    return null;
  }

  const trimmedBase = sanitizedBase.replace(/\/*$/, "");

  const lowerCased = sanitizedBase.toLowerCase();
  if (lowerCased.endsWith(".pdf")) {
    return sanitizedBase;
  }

  if (!fileName || String(fileName).trim().length === 0) {
    return null;
  }

  return `${trimmedBase}/${String(fileName).trim()}`;
};

export const splitBlobPathSegments = (basePath) => {
  const sanitizedBasePath = sanitizeBlobPath(basePath);
  return sanitizedBasePath
    ? sanitizedBasePath
        .split("/")
        .filter((segment) => segment && segment.trim().length > 0)
    : [];
};

export const MONTH_NAME_MAP = Object.freeze({
  1: "january",
  2: "february",
  3: "march",
  4: "april",
  5: "may",
  6: "june",
  7: "july",
  8: "august",
  9: "september",
  10: "october",
  11: "november",
  12: "december",
});

export const MONTH_FULL_NAME_MAP = Object.freeze({
  1: "January",
  2: "February",
  3: "March",
  4: "April",
  5: "May",
  6: "June",
  7: "July",
  8: "August",
  9: "September",
  10: "October",
  11: "November",
  12: "December",
});

export const getFullMonthName = (month) => {
  if (typeof month === "number" && month >= 1 && month <= 12) {
    return MONTH_FULL_NAME_MAP[month] || null;
  }
  return null;
};

