"use client";

import { memo, useMemo } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Bot } from "lucide-react";
import { formatMessageContent } from "@/utils/formatMessage";
import { CHAT_MESSAGES } from "@/utils/constants/chat";
import "@/styles/cfo-insights.css";

export const CFOMessageList = memo(function CFOMessageList({
  messages,
  isLoading,
  effectiveMonth,
  selectedPage,
  isOperationsSelected,
  isFinancialSelected,
}) {
  const emptyMessage = useMemo(() => {
    if (!effectiveMonth) return "Start a conversation";
    if (isOperationsSelected) {
      return `Ask about your Operations Page ${selectedPage} Dashboard`;
    }
    if (isFinancialSelected) {
      return `Ask about your Financial Page ${selectedPage} Dashboard`;
    }
    return `Ask about your ${effectiveMonth} Dashboard`;
  }, [effectiveMonth, selectedPage, isOperationsSelected, isFinancialSelected]);

  if (messages.length === 0) {
    return (
      <motion.div
        key="empty-state"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="cfo-insights-empty-state"
      >
        <motion.div
          animate={{ scale: [1, 1.1, 1], rotate: [0, 5, -5, 0] }}
          transition={{ duration: 3, repeat: Infinity, ease: "easeInOut" }}
        >
          <Bot className="cfo-insights-empty-icon" />
        </motion.div>
        <p className="cfo-insights-empty-title">Welcome to CFO Insights!</p>
        <p className="cfo-insights-empty-subtitle">
          {effectiveMonth
            ? emptyMessage
            : CHAT_MESSAGES.UI_TEXT.START_CONVERSATION}
        </p>
      </motion.div>
    );
  }

  return (
    <>
      <AnimatePresence>
        {messages.map((message, index) => (
          <motion.div
            key={message.id || `message-${index}`}
            initial={{ opacity: 0, y: 15, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            transition={{
              duration: 0.25,
              delay: Math.min(index * 0.05, 0.3),
              ease: "easeOut",
            }}
            className={`cfo-insights-message ${message.type}`}
          >
            <motion.div
              className={`cfo-insights-message-bubble ${message.type}`}
              whileHover={{ scale: 1.01 }}
              transition={{ duration: 0.15 }}
            >
              <div
                className={`cfo-insights-message-content ${message.type} ${
                  message.type === "ai" ? "ai-message" : ""
                }`}
                dangerouslySetInnerHTML={{
                  __html: formatMessageContent(message.content),
                }}
              />
              {message.type === "ai" && (
                <p className={`cfo-insights-message-timestamp ${message.type}`}>
                  {message.timestamp}
                </p>
              )}
            </motion.div>
          </motion.div>
        ))}
      </AnimatePresence>

      {isLoading && (
        <motion.div
          key="loading-indicator"
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          className="cfo-insights-loading-indicator"
        >
          <motion.div
            className="cfo-insights-loading-bubble"
            whileHover={{ scale: 1.01 }}
            transition={{ duration: 0.15 }}
          >
            <div className="cfo-insights-loading-content">
              <motion.div
                className="cfo-insights-loading-spinner"
                animate={{ rotate: 360 }}
                transition={{
                  duration: 1,
                  repeat: Infinity,
                  ease: "linear",
                }}
              />
              <span className="cfo-insights-loading-text">Thinking...</span>
            </div>
          </motion.div>
        </motion.div>
      )}
    </>
  );
});
