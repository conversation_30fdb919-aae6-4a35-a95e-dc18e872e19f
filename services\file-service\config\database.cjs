// config/database.cjs
require("dotenv").config();

const parseEnvValue = (value, type = "string", defaultValue = null) => {
  if (!value) return defaultValue;

  switch (type) {
    case "number":
      const num = parseInt(value, 10);
      return isNaN(num) ? defaultValue : num;
    case "boolean":
      return value.toLowerCase() === "true";
    default:
      return value;
  }
};

const getDatabaseConfig = () => {
  const isLocalDatabase =
    process.env.NODE_ENV === "development" &&
    process.env.USE_LOCAL_DB === "true";

  if (isLocalDatabase) {
    return {
      database: process.env.LOCAL_DB_NAME || process.env.DB_NAME,
      username: process.env.LOCAL_DB_USER || process.env.DB_USER,
      password: process.env.LOCAL_DB_PASS || process.env.DB_PASS,
      host: process.env.LOCAL_DB_HOST || process.env.DB_HOST,
      port: parseEnvValue(
        process.env.LOCAL_DB_PORT || process.env.DB_PORT,
        "number",
        5432
      ),
      dialect: "postgres",
      logging: false,
      pool: {
        max: 5,
        min: 0,
        acquire: 30000,
        idle: 10000,
      },
      dialectOptions: {},
      isLocal: true,
    };
  } else {
    const dbSslEnabled =
      typeof process.env.DB_SSL !== "undefined"
        ? parseEnvValue(process.env.DB_SSL, "boolean", true)
        : true;
    return {
      database: process.env.DB_NAME,
      username: process.env.DB_USER,
      password: process.env.DB_PASS,
      host: process.env.DB_HOST,
      port: parseEnvValue(process.env.DB_PORT, "number", 5432),
      dialect: "postgres",
      logging: false,
      pool: {
        max: 10,
        min: 0,
        acquire: 30000,
        idle: 10000,
      },
      dialectOptions: dbSslEnabled
        ? {
            ssl: {
              require: true,
              rejectUnauthorized: false,
            },
          }
        : {},
      isLocal: false,
    };
  }
};

const dbConfig = getDatabaseConfig();

module.exports = {
  development: {
    username: dbConfig.username,
    password: dbConfig.password,
    database: dbConfig.database,
    host: dbConfig.host,
    port: dbConfig.port,
    dialect: dbConfig.dialect,
    dialectOptions: dbConfig.dialectOptions,
    logging: dbConfig.logging,
    pool: dbConfig.pool,
    define: {
      timestamps: true,
      underscored: true,
      freezeTableName: true,
    },
  },
  test: {
    username: dbConfig.username,
    password: dbConfig.password,
    database: dbConfig.database,
    host: dbConfig.host,
    port: dbConfig.port,
    dialect: dbConfig.dialect,
    dialectOptions: dbConfig.dialectOptions,
    logging: false,
    pool: dbConfig.pool,
    define: {
      timestamps: true,
      underscored: true,
      freezeTableName: true,
    },
  },
  production: {
    username: dbConfig.username,
    password: dbConfig.password,
    database: dbConfig.database,
    host: dbConfig.host,
    port: dbConfig.port,
    dialect: dbConfig.dialect,
    dialectOptions: dbConfig.dialectOptions,
    logging: false,
    pool: dbConfig.pool,
    define: {
      timestamps: true,
      underscored: true,
      freezeTableName: true,
    },
  },
};

