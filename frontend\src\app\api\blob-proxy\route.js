// Server-side proxy for external blob URLs (Azure SAS etc.)
// Fetches the external resource server-side and returns it to the client
// This avoids CORS issues and keeps SAS tokens hidden from the browser when desired.

export const dynamic = "force-dynamic";

export async function GET(req) {
  try {
    const { searchParams } = new URL(req.url);
    const url = searchParams.get("url");
    if (!url) {
      return new Response(
        JSON.stringify({ error: "Missing url query parameter" }),
        {
          status: 400,
          headers: { "Content-Type": "application/json" },
        }
      );
    }

    // Basic allowlist of hosts (optional) - adjust as needed
    // const allowedHosts = ['devperfinoreport.blob.core.windows.net'];
    // const parsed = new URL(url);
    // if (!allowedHosts.includes(parsed.hostname)) {
    //   return new Response('Forbidden host', { status: 403 });
    // }

    const resp = await fetch(url, {
      method: "GET",
      // Prevent any caching to ensure latest content
      cache: "no-store",
      headers: {
        Accept: "application/pdf,application/octet-stream;q=0.9,*/*;q=0.8",
      },
    });

    if (!resp.ok) {
      return new Response(
        JSON.stringify({ error: "Upstream fetch failed", status: resp.status }),
        {
          status: 502,
          headers: { "Content-Type": "application/json" },
        }
      );
    }

    const arrayBuffer = await resp.arrayBuffer();

    // Forward useful headers
    const contentType = resp.headers.get("content-type") || "application/pdf";
    const contentDisposition = resp.headers.get("content-disposition");
    const etag = resp.headers.get("etag");

    const headers = new Headers();
    headers.set("Content-Type", contentType);
    headers.set("Content-Length", String(arrayBuffer.byteLength));
    if (contentDisposition)
      headers.set("Content-Disposition", contentDisposition);
    if (etag) headers.set("ETag", etag);
    // Allow the browser to access this resource
    headers.set("Access-Control-Allow-Origin", "*");
    headers.set("Access-Control-Expose-Headers", "Content-Length,Content-Type");

    return new Response(arrayBuffer, {
      status: 200,
      headers,
    });
  } catch (error) {
    return new Response(
      JSON.stringify({ error: "Server error", message: error.message }),
      {
        status: 500,
        headers: { "Content-Type": "application/json" },
      }
    );
  }
}
