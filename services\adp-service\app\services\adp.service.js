// packages
import path from "path";
import fs from "fs";
// import axios from "axios";

// repositories
import { adpRepository } from "../repositories/adp.repository.js";

// utils
import {
  readAndProcessFile,
  extractDatesFromFileName,
  getDynamicColumnMapping,
} from "../utils/excel.util.js";
import { withDb } from "../utils/db.util.js";
import { POSTGRES_CONFIG } from "../../config/db.config.js";
import { createLogger } from "../utils/logger.util.js";
// import { METHOD_TYPES } from "../utils/constants.util.js";
// import { apiRequest } from "../../../../shared/utils/axios.util.js";
import { REGEX_PATTERNS } from "../../../../shared/utils/constants.util.js";
// import { sendJsonFile } from "../../../../shared/utils/file.util.js";

// constants
const file_service_url = process.env.FILE_SERVICE_URL;
const report_url = process.env.REPORT_SERVICE_URL;

const logger = createLogger("ADP_SERVICE");

/**
 * ADP Service
 */
export const adpService = {
  // syncData: async (filePath, schemaName, orgId) => {
  //   logger.info("Starting ADP sync process", { filePath, schemaName, orgId });

  //   if (!fs.existsSync(filePath)) {
  //     logger.error("File not found", { filePath });
  //     throw new Error(`File not found: ${filePath}`);
  //   }

  //   const baseName = path.basename(filePath, path.extname(filePath));
  //   const parts = baseName.split("_");
  //   const companyName = parts[0];
  //   const payrollMonth = parts[parts.length - 1];

  //   logger.info("Parsed file information", { companyName, payrollMonth });

  //   const { from_date, to_date } = extractDatesFromFileName(filePath);
  //   logger.info("Extracted dates from file", { from_date, to_date });

  //   const payrollColumnMapping = getDynamicColumnMapping(filePath);
  //   logger.info("Retrieved dynamic column mapping", {
  //     columnCount: Object.keys(payrollColumnMapping).length,
  //   });

  //   return withDb(async (client) => {
  //     logger.info("Creating database tables", { schemaName });
  //     await adpRepository.createTables(
  //       client,
  //       payrollColumnMapping,
  //       schemaName
  //     );

  //     logger.info("Reading and processing file", { filePath });
  //     const records = readAndProcessFile(
  //       filePath,
  //       payrollColumnMapping,
  //       companyName,
  //       payrollMonth,
  //       from_date,
  //       to_date
  //     );

  //     if (!records || records.length === 0) {
  //       logger.warn("No records found in file", { filePath });
  //       return {
  //         message: `No records found in file ${filePath}`,
  //         insertedRecords: 0,
  //       };
  //     }

  //     logger.info("Inserting records into database", {
  //       recordCount: records.length,
  //       schemaName,
  //     });
  //     await adpRepository.insertData(
  //       client,
  //       records,
  //       payrollColumnMapping,
  //       schemaName
  //     );

  //     logger.info("Updating sync timestamp", { orgId, to_date });
  //     await adpService.updateSyncTime(client, orgId, to_date);

  //     await adpService.updateAuditLogs(client, {
  //       serviceName: "ADP",
  //       organizationId: orgId,
  //       syncStartTime: new Date(),
  //       syncEndTime: new Date(),
  //       executionStatus: "Success",
  //       recordsProcessed: records.length || 0,
  //     });

  //     logger.info("ADP sync completed successfully", {
  //       companyName,
  //       payrollMonth,
  //       insertedRecords: records.length,
  //     });

  //     return {
  //       message: `Data synced successfully for ${companyName} (${payrollMonth})`,
  //       insertedRecords: records.length,
  //       lastSyncedAt: to_date,
  //     };
  //   }, POSTGRES_CONFIG);
  // },

  syncData: async (filePath, schemaName, orgId) => {
    logger.info("Starting ADP sync process", { filePath, schemaName, orgId });

    if (!fs.existsSync(filePath)) {
      logger.error("File not found", { filePath });
      throw new Error(`File not found: ${filePath}`);
    }

    const baseName = path.basename(filePath, path.extname(filePath));
    const parts = baseName.split("_");
    const companyName = parts[0];
    const payrollMonth = parts[parts.length - 1];

    logger.info("Parsed file information", { companyName, payrollMonth });

    const { from_date, to_date } = extractDatesFromFileName(filePath);
    logger.info("Extracted dates from file", { from_date, to_date });

    const payrollColumnMapping = getDynamicColumnMapping(filePath);
    logger.info("Retrieved dynamic column mapping", {
      columnCount: Object.keys(payrollColumnMapping).length,
    });

    return withDb(async (client) => {
      logger.info("Creating database tables", { schemaName });
      await adpRepository.createTables(
        client,
        payrollColumnMapping,
        schemaName
      );

      logger.info("Reading and processing file", { filePath });
      const records = readAndProcessFile(
        filePath,
        payrollColumnMapping,
        companyName,
        payrollMonth,
        from_date,
        to_date
      );

      if (!records || records.length === 0) {
        logger.warn("No records found in file", { filePath });
        return {
          message: `No records found in file ${filePath}`,
          insertedRecords: 0,
        };
      }

      const missingDeptRecords = records.filter(
        (record) => !record.department || record.department.trim() === ""
      );

      if (missingDeptRecords.length > 0) {
        logger.error("❌ Department field missing in some rows", {
          missingCount: missingDeptRecords.length,
          exampleRecord: missingDeptRecords[0],
        });

        return {
          success: false,
          statusCode: 400,
          message: `Department field is missing in the uploaded file. Please correct and re-upload the file.`,
        };
      }

      logger.info("Inserting records into database", {
        recordCount: records.length,
        schemaName,
      });
      await adpRepository.insertData(
        client,
        records,
        payrollColumnMapping,
        schemaName
      );
      const [month, year] = payrollMonth.match(REGEX_PATTERNS.MONTH_YEAR);

      // /**Dashboard api call */
      // const response = await apiRequest(
      //   METHOD_TYPES.GET,
      //   `${report_url}/report/calculate/payroll`,
      //   {
      //     orgId,
      //     month,
      //     year,
      //   }
      // );

      // let reportServiceResponse = response.data || response;

      // // Create JSON file from the response data
      // const jsonFilePath = sendJsonFile(
      //   reportServiceResponse,
      //   `Payroll-${month}-${year}.json`
      // );

      // logger.info("[ADP Controller] Created JSON file for operations KPIs", {
      //   filePath: jsonFilePath,
      // });

      // // Upload JSON file to file-service
      // try {
      //   const formData = new FormData();
      //   const fileStream = fs.createReadStream(jsonFilePath);
      //   const fileName = path.basename(jsonFilePath); // Get filename from path

      //   formData.append("json", fileStream, fileName);
      //   formData.append("orgId", orgId);

      //   const uploadResponse = await axios.post(
      //     `${file_service_url}/onboarding/json`,
      //     formData,
      //     {
      //       headers: {
      //         ...formData.getHeaders(),
      //       },
      //     }
      //   );
      //   logger.info(
      //     "[ADP Controller] Successfully uploaded JSON file to file-service",
      //     {
      //       filePath: jsonFilePath,
      //       uploadResponse: uploadResponse.data,
      //     }
      //   );
      // } catch (uploadError) {
      //   console.log(
      //     "[ADP Controller] Error uploading JSON file to file-service",
      //     uploadError
      //   );
      // }
      /**Dashboard api call */

      logger.info("Updating sync timestamp", { orgId, to_date });

      await adpService.updateSyncTime(client, orgId, to_date);

      // await adpService.updateAuditLogs(client, {
      //   serviceName: "ADP",
      //   organizationId: orgId,
      //   syncStartTime: new Date(),
      //   syncEndTime: new Date(),
      //   executionStatus: "Success",
      //   recordsProcessed: records.length || 0,
      // });

      // logger.info("ADP sync completed successfully", {
      //   companyName,
      //   payrollMonth,
      //   insertedRecords: records.length,
      // });

      return {
        success: true,
        message: `Data synced successfully for ${companyName} (${payrollMonth})`,
        insertedRecords: records.length,
        lastSyncedAt: to_date,
      };
    }, POSTGRES_CONFIG);
  },

  updateSyncTime: async (client, orgId, lastSyncedAt) => {
    logger.info("Updating sync time", { orgId, lastSyncedAt });

    // Update database sync time
    await adpRepository.updateSyncTime(client, orgId, lastSyncedAt);
    logger.info("Database sync time updated", { orgId });
  },

  // /**
  //  * Update audit logs
  //  *
  //  * @param {*} client
  //  * @param {*} orgId
  //  * @param {*} auditLogs
  //  */
  // updateAuditLogs: async (client, auditLogs) => {
  //   logger.info("Updating audit logs", auditLogs);

  //   try {
  //     const baseUrl = process.env.AUDIT_SERVICE_URL;
  //     if (!baseUrl) {
  //       logger.warn("AUDIT_SERVICE_URL is not set; skipping audit log update");
  //       return;
  //     }

  //     // ✅ Correct endpoint (no duplicate /logs)
  //     const url = `${baseUrl.replace(/\/$/, "")}/api/audit/logs`;
  //     // const response = await axios
  //     //   .post(url, auditLogs, {
  //     //     headers: {
  //     //       "Content-Type": "application/json",
  //     //     },
  //     //   })
  //     //   .catch((error) => {
  //     //     // ✅ Improved error logging
  //     //     logger.error("❌ Error while sending audit log", {
  //     //       error: error,
  //     //       urlAttempted: url,
  //     //       status: error.response?.status,
  //     //     });
  //     //   });
  //     const response = await apiRequest("post", url, auditLogs);

  //     // logger.info("✅ Audit logs updated successfully", {
  //     //   organizationId: auditLogs.organizationId,
  //     //   status: response.status,
  //     // });
  //   } catch (error) {
  //     // ✅ Improved error handling
  //     logger.error("❌ Failed to update audit logs", {
  //       error: error,
  //     });
  //   }
  // },
};
