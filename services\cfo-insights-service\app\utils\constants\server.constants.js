export const SERVER_CONSTANTS = {
  CONFIG: {
    NAME: "CFO Insights Service",
    VERSION: "1.0.0",
    DEFAULT_PORT: 3007,
    DEFAULT_ENV: "development",
  },
  CORS: {
    ALLOWED_DOMAIN: "localhost",
    METHODS: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    ALLOWED_HEADERS: [
      "Origin",
      "X-Requested-With",
      "Content-Type",
      "Accept",
      "Authorization",
      "Cache-Control",
      "Pragma",
    ],
    EXPOSED_HEADERS: ["X-Total-Count", "X-Page-Count"],
    CREDENTIALS: true,
    MAX_AGE: 86400,
    PREFLIGHT_CONTINUE: false,
    OPTIONS_SUCCESS_STATUS: 204,
  },
  REQUEST_LIMITS: {
    JSON_LIMIT: "10mb",
    URL_ENCODED_LIMIT: "10mb",
  },
  MESSAGES: {
    HEALTH: {
      SERVICE_HEALTHY: "CFO Insights Service is healthy",
      DAT<PERSON>ASE_CONNECTION_FAILED: "Database connection failed",
      HEALTH_CHECK_FAILED: "Health check failed",
    },
    WELCOME: {
      SERVICE_WELCOME: "Welcome to CFO Insights Service API",
    },
  },
  ROUTES: {
    AVAILABLE: [
      "GET /health",
      "GET /",
      "POST /api/chat/start",
      "POST /api/chat/message",
      "POST /api/chat/summary",
    ],
  },
  PROCESS_SIGNALS: {
    SIGTERM: "SIGTERM",
    SIGINT: "SIGINT",
  },
  ERROR_CODES: {
    EADDRINUSE: "EADDRINUSE",
  },
  ENVIRONMENTS: {
    DEVELOPMENT: "development",
    PRODUCTION: "production",
    TEST: "test",
  },
};
