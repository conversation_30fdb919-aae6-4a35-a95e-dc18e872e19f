"use client";

import React from "react";
import { <PERSON><PERSON><PERSON>, <PERSON> } from "lucide-react";
import "@/styles/dashboard.css";

export default function FloatingActionButton({ onClick, isOpen = false, className = "" }) {
  return (
    <button
      onClick={onClick}
      className={`dashboard-fab ${className}`}
      aria-label={isOpen ? "Close CFO Insights" : "CFO Insights"}
      title={isOpen ? "Close CFO Insights" : "CFO Insights"}
    >
      {isOpen ? (
        <X className="dashboard-fab-icon" />
      ) : (
        <Sparkles className="dashboard-fab-icon" />
      )}
    </button>
  );
}

