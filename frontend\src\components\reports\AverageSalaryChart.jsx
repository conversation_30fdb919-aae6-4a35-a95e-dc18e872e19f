"use client";

import { formatCompactCurrency } from "../../utils/methods/formatters";
import { EChartWrapper } from "./EChartWrapper";

/**
 * Truncate long text with ellipsis
 * @param {string} text - Text to truncate
 * @param {number} maxLength - Maximum character length
 * @returns {string} Truncated text
 */
const truncateText = (text, maxLength = 12) => {
  if (!text || text.length <= maxLength) return text;
  return text.substring(0, maxLength - 1) + "…";
};

export function AverageSalaryChart({ data }) {
  const categories = data.roles.map((role) => role.role);
  const colors = ["#34d399", "#2f7ed8", "#ffc542", "#ff8c5a"];
  const values = data.roles.map((role, index) => ({
    value: role.value,
    itemStyle: {
      color: colors[index % colors.length],
    },
  }));

  const option = {
    color: colors,
    tooltip: {
      trigger: "axis",
      axisPointer: { type: "shadow" },
      formatter: (params) => {
        const param = Array.isArray(params) ? params[0] : params;
        // Show full role name in tooltip
        const fullName = categories[param.dataIndex] || param.axisValue;
        return `${fullName}: ${formatCompactCurrency(param.data ?? param.value)}`;
      },
    },
    grid: { left: "3%", right: "4%", bottom: "20%", containLabel: true },
    xAxis: {
      type: "category",
      data: categories,
      axisLabel: {
        color: "#1e293b",
        fontWeight: 600,
        interval: 0,
        fontSize: 11,
        rotate: 30, // Rotate labels to prevent overlap
        formatter: (value) => truncateText(value, 14),
      },
      axisTick: {
        alignWithLabel: true,
      },
    },
    yAxis: {
      type: "value",
      axisLabel: {
        formatter: (value) => formatCompactCurrency(value),
        color: "#475569",
      },
      splitLine: { lineStyle: { color: "#e2e8f0" } },
    },
    series: [
      {
        type: "bar",
        data: values,
        barMaxWidth: 60,
        itemStyle: { borderRadius: [0, 0, 0, 0] },
        label: {
          show: true,
          position: "top",
          formatter: ({ value }) => formatCompactCurrency(value),
        },
        labelLayout: {
          hideOverlap: true,
        },
      },
    ],
  };

  return <EChartWrapper option={option} />;
}
