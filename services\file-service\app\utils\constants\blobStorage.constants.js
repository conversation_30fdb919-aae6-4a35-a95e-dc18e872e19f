// BLOB STORAGE CONSTANTS
// Static messages for Azure Blob Storage Operations

export const BLOB_STORAGE_MESSAGES = {
  // Configuration Errors
  NOT_CONFIGURED: "Azure Blob Storage is not configured",
  CONNECTION_STRING_REQUIRED: "AZURE_STORAGE_CONNECTION_STRING is not defined in environment variables",
  CONTAINER_NAME_REQUIRED: "AZURE_BLOB_CONTAINER_NAME is not defined in environment variables",
  INVALID_CONNECTION_STRING: "Invalid connection string format - cannot extract account name or key",

  // Operation Messages
  UPLOAD_SUCCESS: "File uploaded successfully",
  DOWNLOAD_SUCCESS: "File downloaded successfully",
  DELETE_SUCCESS: "File deleted successfully",
  LIST_SUCCESS: "Files listed successfully",
  CONTAINER_CREATED: "Container created successfully",
  CONTAINER_EXISTS: "Container already exists",
  SAS_URL_GENERATED: "Generated SAS URL for blob",

  // Error Messages
  UPLOAD_FAILED: "Failed to upload file",
  DOWNLOAD_FAILED: "Failed to download file",
  DELETE_FAILED: "Failed to delete file",
  LIST_FAILED: "Failed to list files",
  FILE_NOT_FOUND: "File not found",
  BLOB_NOT_FOUND: "Blob not found",
  CONTAINER_ERROR: "Error ensuring container exists",
  SAS_URL_GENERATION_FAILED: "Failed to generate SAS URL",
};

export const BLOB_STORAGE_LOG_MESSAGES = {
  CONFIGURATION_WARNING: "Azure Blob Storage not configured",
  CONTAINER_CREATING: "Creating container",
  CONTAINER_CREATED: "Container created successfully",
  CONTAINER_EXISTS: "Container already exists",
  FILE_UPLOADED: "File uploaded successfully",
  FILE_DOWNLOADED: "File downloaded successfully",
  FILE_DELETED: "File deleted successfully",
  FILES_LISTED: "Files listed",
  BLOBS_LISTED: "Listed blobs with prefix",
  SAS_URL_GENERATED: "Generated SAS URL for blob",
  ERROR_UPLOADING: "Error uploading file",
  ERROR_DOWNLOADING: "Error downloading file",
  ERROR_DELETING: "Error deleting file",
  ERROR_LISTING: "Error listing files",
  ERROR_CHECKING_EXISTENCE: "Error checking file existence",
  ERROR_LISTING_BLOBS: "Error listing blobs with prefix",
  ERROR_GENERATING_SAS: "Error generating SAS URL",
  ERROR_ENSURE_CONTAINER: "Error ensuring container exists",
};

export const BLOB_STORAGE_DEFAULTS = {
  CONTENT_TYPE: "application/pdf",
  CACHE_CONTROL: "public, max-age=31536000",
  DEFAULT_EXPIRY_HOURS: 1,
  SAS_PERMISSIONS: "r", // Read only
};

export const INVALID_SEGMENT_REGEX = /[^a-zA-Z0-9-_]/g;

export default {
  BLOB_STORAGE_MESSAGES,
  BLOB_STORAGE_LOG_MESSAGES,
  BLOB_STORAGE_DEFAULTS,
  INVALID_SEGMENT_REGEX,
};

