// app/utils/cache.util.js
/**
 * Simple in-memory cache utility with TTL support
 * Used for caching expensive operations like blob listings
 * REFACTORED: Functional/object composition pattern (no classes)
 */

// Constants for default TTL values
const DEFAULT_TTL_REPORT_FOLDERS = 300000; // 5 minutes
const DEFAULT_TTL_ENV_VARS = 3600000; // 1 hour

/**
 * Create a cache instance using object composition
 * @param {number} defaultTtl - Default TTL in milliseconds
 * @returns {Object} Cache instance with methods
 */
const createCache = (defaultTtl) => {
  const cache = new Map();
  
  return {
    /**
     * Get cached value
     * @param {string} key - Cache key
     * @returns {any|null} Cached value or null if expired/missing
     */
    get(key) {
      const item = cache.get(key);
      
      if (!item) {
        return null;
      }

      const now = Date.now();
      
      // Check if expired
      if (now > item.expiresAt) {
        cache.delete(key);
        return null;
      }

      return item.value;
    },

    /**
     * Set cached value with TTL
     * @param {string} key - Cache key
     * @param {any} value - Value to cache
     * @param {number} ttl - Time to live in milliseconds (optional)
     */
    set(key, value, ttl = defaultTtl) {
      const expiresAt = Date.now() + ttl;
      cache.set(key, { value, expiresAt });
    },

    /**
     * Delete cached value
     * @param {string} key - Cache key
     */
    delete(key) {
      cache.delete(key);
    },

    /**
     * Clear all cache entries
     */
    clear() {
      cache.clear();
    },

    /**
     * Clean expired entries
     * Should be called periodically to prevent memory leaks
     */
    cleanExpired() {
      const now = Date.now();
      for (const [key, item] of cache.entries()) {
        if (now > item.expiresAt) {
          cache.delete(key);
        }
      }
    },

    /**
     * Get cache size
     * @returns {number} Number of entries in cache
     */
    size() {
      return cache.size;
    }
  };
};

// Export singleton instances using functional pattern
export const reportFoldersCache = createCache(DEFAULT_TTL_REPORT_FOLDERS);
export const envCache = createCache(DEFAULT_TTL_ENV_VARS);

// Export factory function for creating custom cache instances
export default createCache;

