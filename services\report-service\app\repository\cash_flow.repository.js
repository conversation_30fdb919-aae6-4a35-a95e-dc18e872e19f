import { sequelize } from "../models/index.js";
import models from "../models/index.js";
import { LOGGER_NAMES } from "../utils/constants/log.constants.js";
import { createLogger } from "../utils/logger.utils.js";
import { getOrganizationSchemaName, getReportId } from "../utils/repository.utils.js";

const logger = createLogger(LOGGER_NAMES.REPORT_REPOSITORY);

/**
 * Get Cash Flow report ID for a specific month and year
 * @param {string} schemaName - Organization schema name
 * @param {number} month - Month (1-12)
 * @param {number} year - Year
 * @returns {Promise<string|null>} Report ID or null
 */
const getCashFlowReportId = async (schemaName, month, year) => {
  return await getReportId(schemaName, 'qb_cash_flow_reports', month, year, 'Cash Flow');
};

/**
 * Get cash flow totals by group from cash flow totals table
 * @param {string} schemaName - Organization schema name
 * @param {string} reportId - Report ID
 * @returns {Promise<Object>} Cash flow totals grouped by operating, investing, financing, net_cash_flow
 */
const getCashFlowTotals = async (schemaName, reportId) => {
  try {
    logger.info(
      `Fetching cash flow totals from schema: ${schemaName} for report ID: ${reportId}`
    );

    const query = `
      SELECT 
        operating,
        investing,
        financing,
        net_cash_flow
      FROM "${schemaName}".qb_cash_flow_totals
      WHERE report_id = :reportId
      LIMIT 1
    `;

    const results = await sequelize.query(query, {
      replacements: { reportId },
      type: sequelize.QueryTypes.SELECT,
    });

    if (results.length === 0) {
      logger.info(
        `No cash flow totals found for report ID: ${reportId} in schema: ${schemaName}`
      );
      return null;
    }

    logger.info(
      `Retrieved cash flow totals from schema: ${schemaName}`
    );

    return results[0];
  } catch (error) {
    logger.error(`Error in CashFlowRepository.getCashFlowTotals:`, error);
    throw error;
  }
};

export default {
  getOrganizationSchemaName,
  getCashFlowReportId,
  getCashFlowTotals,
};
