// app/utils/validators.util.js
import path from "path";
import logger from "../../config/logger.config.js";
import { 
  ONBOARDING_ALLOWED_EXTENSIONS, 
  ONBOARDING_ALLOWED_MIME_TYPES, 
  ONBOARDING_MESSAGES,
  ONBOARDING_JSON_ALLOWED_EXTENSIONS,
  ONBOARDING_JSON_ALLOWED_MIME_TYPES,
} from "./constants/onboarding.constants.js";

/**
 * Validate organization ID
 * @param {string} organizationId - Organization ID to validate
 * @returns {boolean} True if valid
 */
export const validateOrganizationId = (organizationId) => {
  try {
    return (
      organizationId &&
      typeof organizationId === "string" &&
      organizationId.trim().length > 0
    );
  } catch (error) {
    logger.error(`Error validating organization ID: ${error.message}`, { error });
    return false;
  }
};

/**
 * Validate category
 * @param {string} category - Category to validate
 * @returns {boolean} True if valid
 */
export const validateCategory = (category) => {
  try {
    return (
      category &&
      typeof category === "string" &&
      category.trim().length > 0
    );
  } catch (error) {
    logger.error(`Error validating category: ${error.message}`, { error });
    return false;
  }
};

/**
 * Validate month
 * @param {string} month - Month to validate
 * @returns {boolean} True if valid
 */
export const validateMonth = (month) => {
  try {
    return (
      month &&
      typeof month === "string" &&
      month.trim().length > 0
    );
  } catch (error) {
    logger.error(`Error validating month: ${error.message}`, { error });
    return false;
  }
};

/**
 * Validate file path
 * @param {string} filePath - File path to validate
 * @returns {boolean} True if valid
 */
export const validateFilePath = (filePath) => {
  try {
    return (
      filePath &&
      typeof filePath === "string" &&
      filePath.trim().length > 0
    );
  } catch (error) {
    logger.error(`Error validating file path: ${error.message}`, { error });
    return false;
  }
};

export const validateUploadFile = (
  file,
) => {

  const allowedMimeTypes= ONBOARDING_ALLOWED_MIME_TYPES
  const allowedExtensions= ONBOARDING_ALLOWED_EXTENSIONS

  const extensionFromMime = (mimeType) => {
    if (!mimeType) return null;
    const parts = mimeType.split("/");
    return parts.length === 2 ? `.${parts[1]}` : null;
  };

  if (!file?.buffer || !file?.originalname) {
    return ONBOARDING_MESSAGES.MISSING_LOGO_FILE;
  }

  if (
    allowedMimeTypes instanceof Set &&
    allowedMimeTypes.size > 0 &&
    !allowedMimeTypes.has(file.mimetype)
  ) {
    return ONBOARDING_MESSAGES.INVALID_FILE_TYPE;
  }

  if (allowedExtensions instanceof Set && allowedExtensions.size > 0) {
    const extension =
      path.extname(file.originalname || "").toLowerCase() ||
      extensionFromMime(file.mimetype);
    if (!allowedExtensions.has(extension)) {
      return ONBOARDING_MESSAGES.INVALID_FILE_TYPE;
    }
  }

  return null;
};

export const validateUploadJsonFile = (
  file,
) => {

  const allowedMimeTypes= ONBOARDING_JSON_ALLOWED_MIME_TYPES
  const allowedExtensions= ONBOARDING_JSON_ALLOWED_EXTENSIONS

  const extensionFromMime = (mimeType) => {
    if (!mimeType) return null;
    const parts = mimeType.split("/");
    return parts.length === 2 ? `.${parts[1]}` : null;
  };

  if (!file?.buffer || !file?.originalname) {
    return ONBOARDING_MESSAGES.MISSING_JSON_FILE;
  }

  if (
    allowedMimeTypes instanceof Set &&
    allowedMimeTypes.size > 0 &&
    !allowedMimeTypes.has(file.mimetype)
  ) {
    return ONBOARDING_MESSAGES.INVALID_JSON_FILE_TYPE;
  }

  if (allowedExtensions instanceof Set && allowedExtensions.size > 0) {
    const extension =
      path.extname(file.originalname || "").toLowerCase() ||
      extensionFromMime(file.mimetype);
    if (!allowedExtensions.has(extension)) {
      return ONBOARDING_MESSAGES.INVALID_JSON_FILE_TYPE;
    }
  }

  return null;
};
