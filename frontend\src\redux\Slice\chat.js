import { createSlice } from "@reduxjs/toolkit";
import {
  startChatSession,
  sendChatMessage,
  sendSummaryMessage,
} from "../Thunks/chat";
import {
  MESSAGE_TYPES,
  CHAT_ERRORS,
  CHAT_MESSAGES,
} from "../../utils/constants/chat";
import { createMessage, extractAnswerFromResponse } from "../../utils/chat";
import { formatChatResponse, isJsonChatResponse } from "../../utils/formatChatResponse";

// Initial state with better organization
const initialState = {
  // Session state
  session: {
    id: null,
    filename: null,
    organizationId: null,
    organizationName: null,
    isActive: false,
  },

  // Chat data
  messages: [],
  dashboardSummary: null,

  // UI state
  ui: {
    isLoading: false,
    hasWelcomed: false,
    error: null,
  },
};

const chatSlice = createSlice({
  name: "chat",
  initialState,
  reducers: {
    // Error management
    clearError: (state) => {
      state.ui.error = null;
    },

    // Message management
    addMessage: (state, action) => {
      const newMessage = action.payload;
      // Check if message with same ID already exists to prevent duplicates
      const existingMessage = state.messages.find(
        (msg) => msg.id === newMessage.id
      );
      if (!existingMessage) {
        // Also check for duplicate welcome messages by content
        const isDuplicateWelcome =
          newMessage.content?.includes("Welcome to CFO Insights") &&
          state.messages.some((msg) =>
            msg.content?.includes("Welcome to CFO Insights")
          );
        if (!isDuplicateWelcome) {
          state.messages.push(newMessage);
        }
      }
    },

    clearMessages: (state) => {
      state.messages = [];
    },

    // UI state management
    setHasWelcomed: (state, action) => {
      state.ui.hasWelcomed = action.payload;
    },

    setLoading: (state, action) => {
      state.ui.isLoading = action.payload;
    },

    // Session management
    setSession: (state, action) => {
      state.session = { ...state.session, ...action.payload };
    },

    // Dashboard summary management
    setDashboardSummary: (state, action) => {
      state.dashboardSummary = action.payload;
    },

    // Complete reset
    resetChat: (state) => {
      return { ...initialState };
    },
  },
  extraReducers: (builder) => {
    builder
      // Start chat session
      .addCase(startChatSession.pending, (state) => {
        state.ui.isLoading = true;
        state.ui.error = null;
      })
      .addCase(startChatSession.fulfilled, (state, action) => {
        state.ui.isLoading = false;
        state.session.id = action.payload.sessionId;
        state.session.filename = action.payload.filename;
        if (action.payload.organizationId) {
          state.session.organizationId = action.payload.organizationId;
        }
        if (action.payload.organizationName) {
          state.session.organizationName = action.payload.organizationName;
        }
        state.session.isActive = true;
        state.ui.error = null;
      })
      .addCase(startChatSession.rejected, (state, action) => {
        state.ui.isLoading = false;
        state.ui.error = action.payload || CHAT_ERRORS.START_SESSION_FAILED;
      })

      // Send chat message
      .addCase(sendChatMessage.pending, (state) => {
        state.ui.isLoading = true;
        state.ui.error = null;
      })
      .addCase(sendChatMessage.fulfilled, (state, action) => {
        state.ui.isLoading = false;
        state.ui.error = null;

        const messageContent = action.meta.arg?.message;

        // If this is a dashboard summary request, only store it in sidebar (don't add to chat)
        if (
          messageContent &&
          messageContent.includes(CHAT_MESSAGES.DASHBOARD_SUMMARY_REQUEST)
        ) {
          state.dashboardSummary = extractAnswerFromResponse(action.payload);
        } else {
          // For regular messages, add AI response to chat messages
          const extractedResponse = extractAnswerFromResponse(action.payload);
          
          // Format response: if it's JSON format, format it; otherwise use as-is
          let formattedContent;
          if (isJsonChatResponse(extractedResponse)) {
            formattedContent = formatChatResponse(extractedResponse);
          } else {
            formattedContent = typeof extractedResponse === "string" 
              ? extractedResponse 
              : JSON.stringify(extractedResponse);
          }
          
          const aiMessage = createMessage(
            formattedContent,
            MESSAGE_TYPES.AI
          );
          state.messages.push(aiMessage);
        }
      })
      .addCase(sendChatMessage.rejected, (state, action) => {
        state.ui.isLoading = false;
        state.ui.error = action.payload || CHAT_ERRORS.SEND_MESSAGE_FAILED;

        // Add error message to chat
        const payloadMessage =
          typeof action.payload === "string"
            ? action.payload
            : action.payload?.message;
        const errorText =
          payloadMessage || state.ui.error || CHAT_ERRORS.GENERIC_ERROR;
        const errorMessage = createMessage(errorText, MESSAGE_TYPES.AI);
        state.messages.push(errorMessage);
      })
      // Summary API
      .addCase(sendSummaryMessage.pending, (state) => {
        state.ui.isLoading = true;
        state.ui.error = null;
      })
      .addCase(sendSummaryMessage.fulfilled, (state, action) => {
        state.ui.isLoading = false;
        state.ui.error = null;
        // Store summaryData (JSON object) instead of plainAnswer
        state.dashboardSummary = action.payload?.summaryData || action.payload?.answer || null;
      })
      .addCase(sendSummaryMessage.rejected, (state, action) => {
        state.ui.isLoading = false;
        state.ui.error = action.payload || CHAT_ERRORS.GET_SUMMARY_FAILED;
      });
  },
});

export const {
  clearError,
  addMessage,
  clearMessages,
  setHasWelcomed,
  setLoading,
  setSession,
  setDashboardSummary,
  resetChat,
} = chatSlice.actions;

export default chatSlice.reducer;
