// app/models/document.model.js
import { DataTypes } from "sequelize";

const DocumentModel = (sequelize) => {
  const Document = sequelize.define(
    "document",
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true,
      },
      organization_id: {
        type: DataTypes.UUID,
        allowNull: false,
        // No foreign key constraint - organization_id is just a reference field
      },
      blob_storage_path: {
        type: DataTypes.TEXT,
        allowNull: false,
        comment: "Azure blob storage file path",
      },
      service: {
        type: DataTypes.TEXT,
        allowNull: true,
        comment: "Service type (financial, operational, pms)",
      },
      month: {
        type: DataTypes.INTEGER,
        allowNull: true,
        validate: {
          min: 1,
          max: 12,
        },
        comment: "Month (1-12)",
      },
      year: {
        type: DataTypes.INTEGER,
        allowNull: true,
        validate: {
          min: 2000,
          max: 3000,
        },
        comment: "Year (e.g., 2025)",
      },
      file_name: {
        type: DataTypes.TEXT,
        allowNull: false,
        comment: "Original filename",
      },
      file_size: {
        type: DataTypes.BIGINT,
        allowNull: true,
        comment: "File size in bytes",
      },
      mime_type: {
        type: DataTypes.TEXT,
        allowNull: true,
        comment: "File MIME type",
      },
      summary: {
        type: DataTypes.JSONB,
        allowNull: true,
        comment: "Document summary JSON (title, sections)",
      },
      is_active: {
        type: DataTypes.BOOLEAN,
        defaultValue: true,
      },
      is_deleted: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
      },
      metadata: {
        type: DataTypes.JSONB,
        allowNull: true,
        defaultValue: {},
        comment: "Additional metadata",
      },
      created_at: {
        type: DataTypes.DATE,
        defaultValue: DataTypes.NOW,
      },
      created_by: {
        type: DataTypes.UUID,
        allowNull: true,
      },
      updated_at: {
        type: DataTypes.DATE,
      },
      updated_by: {
        type: DataTypes.UUID,
        allowNull: true,
      },
    },
    {
      tableName: "document",
      schema: "Authentication",
      timestamps: true,
      createdAt: "created_at",
      updatedAt: "updated_at",
      indexes: [
        {
          name: "idx_document_organization_id",
          fields: ["organization_id"],
        },
        {
          name: "idx_document_service",
          fields: ["service"],
        },
        {
          name: "idx_document_month_year",
          fields: ["month", "year"],
        },
        {
          name: "idx_document_organization_service_month_year",
          fields: ["organization_id", "service", "month", "year"],
        },
        {
          name: "idx_document_is_deleted",
          fields: ["is_deleted"],
        },
        {
          name: "idx_document_created_at",
          fields: ["created_at"],
        },
        {
          name: "uq_document_org_service_period_file",
          fields: [
            "organization_id",
            "service",
            "month",
            "year",
            "file_name",
          ],
          unique: true,
        },
      ],
    }
  );

  return Document;
};

export default DocumentModel;

