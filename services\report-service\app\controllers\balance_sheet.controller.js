import BalanceSheetService from "../services/balance_sheet.service.js";
import { LOGGER_NAMES } from "../utils/constants/log.constants.js";
import { createLogger } from "../utils/logger.utils.js";
import {
  validateRequiredParams,
  handleControllerError,
  sendSuccessResponse,
} from "../utils/controller.utils.js";

const logger = createLogger(LOGGER_NAMES.REPORT_CONTROLLER);

/**
 * Get balance sheet data for organization by month and year
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getBalanceSheet = async (req, res) => {
  try {
    const { org_id, month, year } = req.query;

    logger.info(
      `Fetching balance sheet data for org: ${org_id}, month: ${month}, year: ${year}`
    );

    // Validate required parameters
    const validationError = validateRequiredParams(req.query, [
      "org_id",
      "month",
      "year",
    ]);
    if (validationError) {
      return res.status(400).json(validationError);
    }

    // Fetch balance sheet data
    const balanceSheetData = await BalanceSheetService.getBalanceSheetData({
      org_id,
      month,
      year,
    });

    // Check if data exists
    if (!balanceSheetData.data || balanceSheetData.data.length === 0) {
      return res.status(200).json({
        success: true,
        message:
          balanceSheetData.message ||
          "No balance sheet data found for the specified period",
        data: balanceSheetData,
      });
    }

    // Return successful response
    res.status(200).json({
      success: true,
      message: "Balance sheet data fetched successfully",
      data: balanceSheetData,
    });
  } catch (error) {
    logger.error("Error fetching balance sheet:", error);
    handleControllerError(error, res, "Error fetching balance sheet data");
  }
};

export default {
  getBalanceSheet,
};
