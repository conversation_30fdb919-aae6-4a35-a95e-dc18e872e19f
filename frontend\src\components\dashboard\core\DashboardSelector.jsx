"use client";

import { memo, useCallback } from "react";
import "@/styles/dashboard.css";

const DASHBOARDS = [
  {
    id: "chp",
    name: "CHP Dashboard",
    color: "blue",
    icon: "M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5",
  },
  {
    id: "dental",
    name: "Dental Dashboard",
    color: "green",
    icon: "M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z",
  },
];

const SAMPLE_DATA = [
  "Finance Reports: June, July, August",
  "Operations: June",
  "Payroll: June, July",
];

export const DashboardSelector = memo(function DashboardSelector({ onSelect }) {
  const handleSelect = useCallback(
    (dashboardId) => {
      onSelect?.(dashboardId);
    },
    [onSelect]
  );

  return (
    <div className="dashboard-selector-container">
      <div className="dashboard-selector-content">
        <div className="dashboard-selector-card">
          <div className="dashboard-selector-header">
            <h1 className="dashboard-selector-title">Select Dashboard</h1>
            <p className="dashboard-selector-subtitle">
              Choose which dashboard you want to view
            </p>
          </div>
          <div className="dashboard-selector-grid">
            {DASHBOARDS.map((db) => (
              <button
                key={db.id}
                onClick={() => handleSelect(db.id)}
                className={`dashboard-selector-button group bg-gradient-to-br from-${db.color}-500 to-${db.color}-600 hover:from-${db.color}-600 hover:to-${db.color}-700`}
              >
                <div className="dashboard-selector-button-content">
                  <div className="dashboard-selector-icon-container">
                    <svg
                      className="dashboard-selector-icon"
                      fill="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path d={db.icon} />
                    </svg>
                  </div>
                  <h3 className="dashboard-selector-name">{db.name}</h3>
                  <p className={`text-${db.color}-100 mb-4`}>
                    View {db.name.split(" ")[0]} financial, operations, and
                    payroll data
                  </p>
                  <div className="space-y-2 text-sm">
                    {SAMPLE_DATA.map((item, i) => (
                      <div
                        key={i}
                        className="flex items-center justify-between"
                      >
                        <span className={`text-${db.color}-100`}>
                          {item.split(":")[0]}:
                        </span>
                        <span className="font-semibold">
                          {item.split(":")[1]}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
                <div className="dashboard-selector-shine" />
              </button>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
});
