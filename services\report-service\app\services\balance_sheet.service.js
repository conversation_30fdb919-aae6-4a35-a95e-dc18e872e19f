import BalanceSheetRepository from "../repository/balance_sheet.repository.js";
import { LOGGER_NAMES } from "../utils/constants/log.constants.js";
import { createLogger } from "../utils/logger.utils.js";
import {
  formatToK,
  parseNumericValue,
  isValidMonth,
  isValidYear,
} from "../utils/format.utils.js";
import income_expense_statementRepository from "../repository/income_expense_statement.repository.js";

const logger = createLogger(LOGGER_NAMES.REPORT_SERVICE);

/**
 * Get balance sheet data aggregated by month and year
 * @param {Object} params - Query parameters
 * @param {string} params.org_id - Organization ID
 * @param {number} params.month - Month (1-12)
 * @param {number} params.year - Year
 * @returns {Promise<Object>} Formatted balance sheet data
 */
const getBalanceSheetData = async ({ org_id, month, year }) => {
  try {
    logger.info(
      `Fetching balance sheet data for org: ${org_id}, month: ${month}, year: ${year}`
    );

    // Validate parameters
    if (!org_id) {
      throw new Error("Organization ID is required");
    }

    if (!month || !isValidMonth(month)) {
      throw new Error("Valid month (1-12) is required");
    }

    if (!year || !isValidYear(year)) {
      throw new Error("Valid year is required");
    }

    // Convert to numbers
    const monthNum = parseInt(month, 10);
    const yearNum = parseInt(year, 10);

    // Get organization schema name
    const schemaName = await BalanceSheetRepository.getOrganizationSchemaName(
      org_id
    );

    if (!schemaName) {
      logger.warn(`Schema not found for organization: ${org_id}`);
      return {
        org_id,
        month: monthNum,
        year: yearNum,
        data: [],
        message: "Organization not found or schema not configured",
      };
    }

    // Fetch balance sheet data from organization schema
    const rawData = await income_expense_statementRepository.getBalanceSheetReport(
      schemaName,
      monthNum,
      yearNum
    );
    function parseReport(rawData) {

      if (
        !rawData ||
        !rawData.Rows ||
        !Array.isArray(rawData.Rows.Row)
      ) {
        return [];
      }

      const sections = rawData.Rows.Row; // Top level sections

      const parseSection = (section) => {
        const result = {
          section: section.group || section.Header?.ColData?.[0]?.value,
          rows: [],
        };

        if (section.Rows?.Row) {
          result.rows = parseRows(section.Rows.Row);
        }

        // Add Summary if exists
        if (section.Summary?.ColData) {
          const summaryAmount = extractAmount(section.Summary.ColData);
          const summaryAccount = section.Summary.ColData[0]?.value;
          result.rows.push({
            type: "summary",
            account: summaryAccount,
            amount: summaryAmount,
          });
        }

        return result;
      };

      const parseRows = (rows) => {
        return rows
          .map((row) => {
            if (row.type === "Data") {
              const account = row.ColData[0]?.value;
              const amount = parseFloat(row.ColData[1]?.value || 0);
              return { account, amount };
            }

            if (row.type === "Section") {
              const header = row.Header?.ColData?.[0]?.value;
              const children = row.Rows?.Row ? parseRows(row.Rows.Row) : [];

              // Add child summary if exists
              if (row.Summary?.ColData) {
                children.push({
                  type: "summary",
                  account: row.Summary.ColData[0]?.value,
                  amount: extractAmount(row.Summary.ColData),
                });
              }

              return {
                account: header,
                children,
              };
            }

            return null;
          })
          .filter(Boolean);
      };

      const extractAmount = (colData) => {
        return parseFloat(colData?.[1]?.value || 0);
      };

      return sections.map(parseSection);
    }

    // ------------------ CALL FUNCTION --------------------
    const transformed = parseReport(rawData); // use your actual data variable here
    // console.log(JSON.stringify(transformed, null, 2));

    // const transformedData = getTotalIncome(rawData);
    // console.log("transformedData", transformedData);

    // If no data found
    // if (!rawData || rawData.length === 0) {
    //   logger.info(
    //     `No balance sheet data found for org: ${org_id}, month: ${month}, year: ${year}`
    //   );
    //   return {
    //     org_id,
    //     month: monthNum,
    //     year: yearNum,
    //     data: [],
    //     message: "No balance sheet data found for the specified period",
    //   };
    // }

    // // Group data by section and subsection
    // const groupedData = groupBalanceSheetData(rawData);

    // // Format the data for graph display
    // const formattedData = formatBalanceSheetForGraph(groupedData);

    // logger.info(
    //   `Successfully fetched balance sheet data for org: ${org_id}, sections: ${formattedData.length}`
    // );

    return {
      org_id,
      month: monthNum,
      year: yearNum,
      currency: rawData?.Rows?.Row?.[0]?.currency || "USD",
      report_basis: rawData?.Rows?.Row?.[0]?.report_basis || "Accrual",
      data: transformed,
    };
  } catch (error) {
    logger.error("Error in BalanceSheetService.getBalanceSheetData:", error);
    throw error;
  }
};

/**
 * Group balance sheet data by section and subsection
 * @param {Array} rawData - Raw balance sheet data from database
 * @returns {Object} Grouped data
 */
const groupBalanceSheetData = (rawData) => {
  const grouped = {};

  rawData.forEach((item) => {
    const section = item.section || "Other";
    const subsection = item.subsection || "General";

    if (!grouped[section]) {
      grouped[section] = {};
    }

    if (!grouped[section][subsection]) {
      grouped[section][subsection] = [];
    }

    grouped[section][subsection].push({
      account_name: item.account_name,
      account_id: item.account_id,
      amount: parseNumericValue(item.amount),
      account_type: item.account_type,
      path: item.path,
    });
  });

  return grouped;
};

/**
 * Format balance sheet data for graph visualization
 * @param {Object} groupedData - Grouped balance sheet data
 * @returns {Array} Formatted data for graphs
 */
const formatBalanceSheetForGraph = (groupedData) => {
  const result = [];

  Object.keys(groupedData).forEach((section) => {
    const subsections = groupedData[section];
    const subsectionArray = [];

    Object.keys(subsections).forEach((subsection) => {
      const accounts = subsections[subsection];

      // Calculate subsection total
      const subsectionTotal = accounts.reduce(
        (sum, account) => sum + account.amount,
        0
      );

      subsectionArray.push({
        name: subsection,
        total: subsectionTotal,
        total_formatted: formatToK(subsectionTotal),
        accounts: accounts.map((account) => ({
          path: account.path,
          name: account.account_name,
          id: account.account_id,
          amount: account.amount,
          amount_formatted: formatToK(account.amount),
          type: account.account_type,
        })),
      });
    });

    // Calculate section total
    const sectionTotal = subsectionArray.reduce(
      (sum, subsection) => sum + subsection.total,
      0
    );

    result.push({
      section: section,
      total: sectionTotal,
      total_formatted: formatToK(sectionTotal),
      subsections: subsectionArray,
    });
  });

  return result;
};

export default {
  getBalanceSheetData,
};
