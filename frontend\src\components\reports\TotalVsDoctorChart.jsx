"use client";

import { formatCompactCurrency } from "../../utils/methods/formatters";
import { EChartWrapper } from "./EChartWrapper";

export function TotalVsDoctorChart({ data }) {
  const categories = data.comparison.map((item) => item.period);
  const payrollSeries = data.comparison.map((item) => item.totalPayroll);
  const doctorSeries = data.comparison.map((item) => item.doctorSalary);

  const option = {
    color: ["#2f7ed8", "#ff8c5a"],
    tooltip: {
      trigger: "axis",
      axisPointer: { type: "shadow" },
      formatter: (params) => {
        return params
          .map(
            (param) =>
              `${param.marker} ${param.seriesName}: ${formatCompactCurrency(param.value)}`
          )
          .join("<br/>");
      },
    },
    legend: {
      top: 0,
    },
    grid: {
      left: "6%",
      right: "4%",
      bottom: "3%",
      containLabel: true,
    },
    xAxis: {
      type: "category",
      data: categories,
      axisLine: { lineStyle: { color: "#cbd5f5" } },
      axisLabel: { color: "#475569" },
    },
    yAxis: {
      type: "value",
      axisLabel: {
        formatter: (value) => formatCompactCurrency(value),
        color: "#475569",
      },
      splitLine: { lineStyle: { color: "#e2e8f0" } },
    },
    series: [
      {
        name: "Total Payroll",
        type: "bar",
        barWidth: 46,
        data: payrollSeries,
        itemStyle: { borderRadius: [0, 0, 0, 0] },
        label: {
          show: true,
          position: "top",
          formatter: ({ value }) => formatCompactCurrency(value),
        },
      },
      {
        name: "Doctor Salary",
        type: "bar",
        barWidth: 46,
        data: doctorSeries,
        itemStyle: { borderRadius: [0, 0, 0, 0] },
        label: {
          show: true,
          position: "top",
          formatter: ({ value }) => formatCompactCurrency(value),
        },
      },
    ],
  };

  return <EChartWrapper option={option} />;
}
