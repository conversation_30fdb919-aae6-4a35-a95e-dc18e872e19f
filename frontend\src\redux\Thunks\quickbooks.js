import api from "@/redux/ApiService/ApiService";

import { SERVICE_PORTS } from "@/utils/constants/api";

import { createAsyncThunk } from "@reduxjs/toolkit";

const axios = api(SERVICE_PORTS.QUICKBOOK);

// Fetch QuickBooks accounts
export const fetchQuickbooksAccounts = createAsyncThunk(
  "quickbooksAccount/fetchAccounts",
  async ({ organization_id }, { rejectWithValue }) => {
    try {
      const response = await axios.get("/quickbooks/accounts", {
        params: { organization_id },
      });
      return response.data; // Return only serializable data, not the full axios response
    } catch (error) {
      return rejectWithValue(error.response?.data);
    }
  }
);

// Update QuickBooks account status
export const updateQuickbooksAccountStatus = createAsyncThunk(
  "quickbooksAccount/updateStatus",
  async ({ id, status }, { rejectWithValue }) => {
    try {
      const response = await axios.patch(`/quickbooks/accounts/${id}/status`, {
        status,
      });
      return response.data; // Return only serializable data, not the full axios response
    } catch (error) {
      return rejectWithValue(error.response?.data);
    }
  }
);

// Sync QuickBooks account
export const syncQuickbooksAccount = createAsyncThunk(
  "quickbooksAccount/syncAccount",
  async ({ id, organization_id }, { rejectWithValue }) => {
    try {
      const response = await axios.post(`/quickbooks/accounts/${id}/sync`, {
        organization_id,
      });
      return response.data; // Return only serializable data, not the full axios response
    } catch (error) {
      return rejectWithValue(error.response?.data);
    }
  }
);

// Delete QuickBooks account
export const deleteQuickbooksAccount = createAsyncThunk(
  "quickbooksAccount/deleteAccount",
  async ({ id }, { rejectWithValue }) => {
    try {
      const response = await axios.delete(`/quickbooks/accounts/${id}`);
      return response.data; // Return only serializable data, not the full axios response
    } catch (error) {
      return rejectWithValue(error.response?.data);
    }
  }
);

// Get QuickBooks OAuth URL
export const getQuickbooksOAuthUrl = createAsyncThunk(
  "quickbooksAccount/getOAuthUrl",
  async (_, { rejectWithValue }) => {
    try {
      const response = await axios.get("/quickbooks/oauth-url");
      return response.data; // Return only serializable data, not the full axios response
    } catch (error) {
      return rejectWithValue(error.response?.data);
    }
  }
);

// Add QuickBooks account with OAuth code
export const addQuickbooksAccount = createAsyncThunk(
  "quickbooksAccount/addAccount",
  async (
    { code, realmId, email, schemaName, organization_id },
    { rejectWithValue }
  ) => {
    try {
      const response = await axios.get("/quickbooks/add", {
        params: { code, realmId, email, schemaName, organization_id },
      });
      return response.data; // Return only serializable data, not the full axios response
    } catch (error) {
      const errorPayload = error.response?.data || {
        status: error.response?.status,
        statusText: error.response?.statusText,
        message: "Failed to add QuickBooks account.",
      };
      return rejectWithValue(errorPayload);
    }
  }
);
