import { sequelize } from "../models/index.js";
import { LOGGER_NAMES } from "./constants/log.constants.js";
import { createLogger } from "./logger.utils.js";

const logger = createLogger(LOGGER_NAMES.REPORT_REPOSITORY);

/**
 * Get records from a dynamic table with date filtering
 * @param {string} schemaName - Organization schema name
 * @param {string} tableName - Table name to query
 * @param {string} startDate - Start date in YYYY-MM-DD format (e.g., "2025-08-01")
 * @returns {Promise<Array>} Query results
 */
export const getRecordsByDateRange = async (
  schemaName,
  tableName,
  startDate,
  month = null
) => {
  try {
    logger.info(
      `Fetching records from table: ${tableName} in schema: ${schemaName} for start_date: ${startDate}`
    );

    // Validate startDate format (basic YYYY-MM-DD check)
    const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
    if (!dateRegex.test(startDate)) {
      throw new Error(
        `Invalid startDate format: ${startDate}. Must be YYYY-MM-DD format (e.g., "2025-08-01").`
      );
    }

    // Build the query
    const query = `
    SELECT *
    FROM "${schemaName}".${tableName}
    WHERE
      ${
        month
          ? `EXTRACT(MONTH FROM start_date) <= ${month}`
          : `start_date = '${startDate}'`
      }
    ORDER BY start_date ASC
  `;
    const results = await sequelize.query(query, {
      replacements: { month, startDate },
      type: sequelize.QueryTypes.SELECT,
    });

    logger.info(
      `Retrieved ${results.length} records from table: ${tableName} in schema: ${schemaName}`
    );

    return results;
  } catch (error) {
    logger.error(
      `Error getting records from table: ${tableName} in schema: ${schemaName}:`,
      error
    );
    throw error;
  }
};

export default {
  getRecordsByDateRange,
};
