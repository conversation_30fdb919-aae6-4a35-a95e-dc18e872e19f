"use client";

import {
  useState,
  useMemo,
  useCallback,
  useEffect,
  useRef,
  useTransition,
} from "react";
import PropTypes from "prop-types";
import { useRouter } from "next/navigation";
import Pagination from "../common/Pagination";
import { LISTING_CONSTANTS } from "@/utils/constants/listing";
import { COMMON_CONSTANTS } from "@/utils/constants/common";
import PageLoader from "../common/PageLoader";
import NoData from "../common/NoData";
import "@/styles/listing.css";
import { useAuthContext } from "@/redux/Providers/AuthProvider";
import { useDispatch, useSelector } from "react-redux";
import TableHeader from "./core/TableHeader";
import ClientTableRow from "./core/ClientTableRow";
import { checkFolderAvailability } from "@/redux/Thunks/folderAvailability";
import { clearCheckingFolders } from "@/redux/Slice/folderAvailability";
import { fetchReportFolders } from "@/redux/Thunks/reportFolders";
import { useToast } from "@/components/ui/toast";

const STATUS_FILTER_ALL = "All Status";
const STATUS_ACTIVE = "Active";
const STATUS_INACTIVE = "Inactive";

export default function ClientTable({
  searchTerm = LISTING_CONSTANTS.SEARCH.ALL,
  statusFilter = LISTING_CONSTANTS.STATUS_FILTER.ALL,
  organizations = [],
  loading = false,
  error = null,
}) {
  const [currentPage, setCurrentPage] = useState(1);
  const [hoveredRow, setHoveredRow] = useState(null);
  const [sortConfig, setSortConfig] = useState({ key: null, direction: "asc" });
  const [loadingStates, setLoadingStates] = useState({});
  const itemsPerPage = LISTING_CONSTANTS.PAGINATION.ITEMS_PER_PAGE;
  const { isAdmin } = useAuthContext();
  const router = useRouter();
  const dispatch = useDispatch();
  const { addToast } = useToast();
  const prefetchedRoutesRef = useRef(new Set());
  const [, startNavigationTransition] = useTransition();

  const { folderAvailability, checkingFolders, folderChecks } = useSelector(
    (state) =>
      state.folderAvailability || {
        folderAvailability: {},
        checkingFolders: [],
        folderChecks: [],
      }
  );
  const checkingFoldersRef = useRef(new Set(checkingFolders || []));
  const folderCheckRef = useRef(new Set(folderChecks || []));

  useEffect(() => {
    checkingFoldersRef.current = new Set(checkingFolders || []);
    folderCheckRef.current = new Set(folderChecks || []);
  }, [checkingFolders, folderChecks]);

  const safePrefetch = useCallback(
    (href) => {
      try {
        const maybePromise = router.prefetch?.(href);
        if (maybePromise?.catch) maybePromise.catch(() => {});
      } catch {}
    },
    [router]
  );

  useEffect(() => {
    safePrefetch("/dashboard");
  }, [safePrefetch]);

  const filteredClients = useMemo(() => {
    if (!organizations?.length) return [];
    const searchLower = searchTerm.toLowerCase();
    const isAllStatus = statusFilter === STATUS_FILTER_ALL;
    const isActiveFilter = statusFilter === STATUS_ACTIVE;
    const isInactiveFilter = statusFilter === STATUS_INACTIVE;

    return organizations.filter((client) => {
      if (!client) return false;
      if (!isAllStatus) {
        const isActive = client.is_active === true;
        if ((isActiveFilter && !isActive) || (isInactiveFilter && isActive))
          return false;
      }
      if (searchTerm && searchTerm !== LISTING_CONSTANTS.SEARCH.ALL) {
        const nameMatch = client.name?.toLowerCase().includes(searchLower);
        const emailMatch = client.email?.toLowerCase().includes(searchLower);
        const schemaMatch = client.schema_name
          ?.toLowerCase()
          .includes(searchLower);
        if (!nameMatch && !emailMatch && !schemaMatch) return false;
      }
      return true;
    });
  }, [organizations, searchTerm, statusFilter]);

  const sortedClients = useMemo(() => {
    if (!filteredClients?.length || !sortConfig.key) return filteredClients;

    const sorted = [...filteredClients];
    const { key, direction } = sortConfig;
    const multiplier = direction === "asc" ? 1 : -1;

    sorted.sort((a, b) => {
      const aVal = a[key];
      const bVal = b[key];

      if (aVal === bVal) return 0;
      if (!aVal) return 1;
      if (!bVal) return -1;

      return (
        String(aVal).toLowerCase().localeCompare(String(bVal).toLowerCase()) *
        multiplier
      );
    });

    return sorted;
  }, [filteredClients, sortConfig]);

  const paginationData = useMemo(() => {
    const totalPages = Math.ceil(sortedClients.length / itemsPerPage);
    const startIndex = (currentPage - 1) * itemsPerPage;
    return {
      totalPages,
      startIndex,
      paginatedClients: sortedClients.slice(
        startIndex,
        startIndex + itemsPerPage
      ),
    };
  }, [sortedClients, currentPage, itemsPerPage]);

  const { totalPages, startIndex, paginatedClients } = paginationData;

  useEffect(() => {
    dispatch(clearCheckingFolders());
  }, [currentPage, dispatch]);

  const handleSort = useCallback((key) => {
    setSortConfig((prev) => ({
      key,
      direction: prev.key === key && prev.direction === "asc" ? "desc" : "asc",
    }));
  }, []);

  const prefetchDashboardForClient = useCallback(
    (client) => {
      if (!client?.id) return;
      const href = `/dashboard?orgId=${client.id}`;
      if (prefetchedRoutesRef.current.has(href)) return;
      prefetchedRoutesRef.current.add(href);
      try {
        const maybePromise = router.prefetch?.(href);
        if (maybePromise?.catch) {
          maybePromise.catch(() => prefetchedRoutesRef.current.delete(href));
        }
      } catch {
        prefetchedRoutesRef.current.delete(href);
      }
    },
    [router]
  );

  const clearLoadingState = useCallback((key) => {
    const clearState = () => {
      setLoadingStates((prev) => {
        const updated = { ...prev };
        delete updated[key];
        return updated;
      });
    };
    setTimeout(clearState, 500);
  }, []);

  const handleViewDetails = useCallback(
    async (client) => {
      if (!client?.id) return;
      const loadingKey = `${client.id}_view`;
      setLoadingStates((prev) => ({ ...prev, [loadingKey]: true }));

      try {
        // Fetch report folders before navigating
        const result = await dispatch(
          fetchReportFolders({
            organizationId: client.id,
            organizationName: client.name,
            forceRefresh: true,
          })
        ).unwrap();

        // Check if folders array is empty or doesn't exist
        const folders = result?.folders || [];
        if (!Array.isArray(folders) || folders.length === 0) {
          clearLoadingState(loadingKey);
          addToast(
            LISTING_CONSTANTS.MESSAGES.NO_DASHBOARDS_AVAILABLE,
            COMMON_CONSTANTS.TOAST_TYPE.INFO,
            LISTING_CONSTANTS.TOAST.DURATION.INFO
          );
          return; // Don't navigate if no dashboards
        }

        // Check if any folder has months (dashboards available)
        const hasMonths = folders.some(
          (folder) => Array.isArray(folder?.months) && folder.months.length > 0
        );

        if (!hasMonths) {
          clearLoadingState(loadingKey);
          addToast(
            LISTING_CONSTANTS.MESSAGES.NO_DASHBOARDS_AVAILABLE,
            COMMON_CONSTANTS.TOAST_TYPE.INFO,
            LISTING_CONSTANTS.TOAST.DURATION.INFO
          );
          return; // Don't navigate if no folders have months
        }

        // Proceed with navigation if folders exist
        const queryParams = new URLSearchParams();
        if (client.name)
          queryParams.append("orgname", encodeURIComponent(client.name));
        if (client.email)
          queryParams.append("email", encodeURIComponent(client.email));
        if (client.id) queryParams.append("orgId", client.id);
        const targetUrl = `/dashboard?${queryParams.toString()}`;
        safePrefetch(targetUrl);
        startNavigationTransition(() => {
          router.push(targetUrl);
          clearLoadingState(loadingKey);
        });
      } catch (error) {
        // Show error toast and don't navigate
        clearLoadingState(loadingKey);
        addToast(
          LISTING_CONSTANTS.MESSAGES.FAILED_TO_LOAD_DASHBOARDS,
          COMMON_CONSTANTS.TOAST_TYPE.ERROR,
          LISTING_CONSTANTS.TOAST.DURATION.ERROR
        );
      }
    },
    [
      router,
      dispatch,
      safePrefetch,
      startNavigationTransition,
      clearLoadingState,
      addToast,
    ]
  );

  const prefetchBookClosure = useCallback(
    (clientId) => {
      if (clientId) {
        safePrefetch(`/book-closure/${clientId}`);
      }
    },
    [safePrefetch]
  );

  const handleBookkeeping = useCallback(
    (client) => {
      const clientId = client?.id;
      if (!clientId) return;
      const loadingKey = `${clientId}_bookkeeping`;
      const targetUrl = `/book-closure/${clientId}`;

      // Set loading state immediately for visual feedback
      setLoadingStates((prev) => ({ ...prev, [loadingKey]: true }));

      // Prefetch the route for faster navigation (if not already prefetched)
      safePrefetch(targetUrl);

      // Use requestAnimationFrame to ensure UI updates before navigation
      requestAnimationFrame(() => {
        startNavigationTransition(() => {
          router.replace(targetUrl);
          // Clear loading state after a short delay to ensure smooth transition
          setTimeout(() => clearLoadingState(loadingKey), 100);
        });
      });
    },
    [router, safePrefetch, startNavigationTransition, clearLoadingState]
  );

  const getClientActions = useCallback(
    (client) => {
      const clientId = client?.id;
      if (!clientId) return [];

      return [
        {
        //   label: LISTING_CONSTANTS.ACTIONS.VIEW_DETAILS,
        //   variant: "outline",
        //   onClick: () => handleViewDetails(client),
        //   loading: !!loadingStates[`${clientId}_view`],
        //   disabled: false,
        //   icon: "view",
        // },
        // {
          label: LISTING_CONSTANTS.ACTIONS.SUBMIT_BOOK_CLOSURE,
          variant: "default",
          onClick: () => handleBookkeeping(client),
          loading: !!loadingStates[`${clientId}_bookkeeping`],
        },
      ];
    },
    //  [loadingStates, handleViewDetails, handleBookkeeping]
    [loadingStates, handleBookkeeping]
  );

  // Prefetch book closure routes for visible clients
  useEffect(() => {
    const prefetchClients = () => {
      for (const client of paginatedClients) {
        if (client?.id) {
          prefetchBookClosure(client.id);
        }
      }
    };
    prefetchClients();
  }, [paginatedClients, prefetchBookClosure]);

  const getErrorMessage = () => {
    if (!error) return null;
    if (typeof error === "string") return error;
    return LISTING_CONSTANTS.MESSAGES.FAILED_TO_LOAD_CLIENTS;
  };

  const renderContent = () => {
    if (loading) {
      return (
        <PageLoader message={LISTING_CONSTANTS.MESSAGES.LOADING_CLIENTS} />
      );
    }

    if (error) {
      return (
        <div className="p-6 text-red-500 font-bold text-center">
          {getErrorMessage()}
        </div>
      );
    }

    if (paginatedClients.length === 0) {
      const hasActiveFilters =
        searchTerm !== LISTING_CONSTANTS.SEARCH.ALL ||
        statusFilter !== STATUS_FILTER_ALL;
      return <NoData hasFilters={hasActiveFilters} />;
    }

    return (
      <>
        <div className="overflow-x-auto overflow-hidden">
          <table className="listing-table">
            <colgroup>
              <col className="listing-table-col-name" />
              <col className="listing-table-col-status" />
              <col className="listing-table-col-sync" />
              <col className="listing-table-col-actions" />
            </colgroup>
            <TableHeader onSort={handleSort} sortConfig={sortConfig} />
            <tbody>
              {paginatedClients.map((client, index) => {
                const isLastRow = index === paginatedClients.length - 1;
                const isHovered = hoveredRow === client.id;
                const actions = getClientActions(client);

                return (
                  <ClientTableRow
                    key={client.id}
                    client={client}
                    isLastRow={isLastRow}
                    isHovered={isHovered}
                    onMouseEnter={() => {
                      setHoveredRow(client.id);
                      prefetchDashboardForClient(client);
                      // Prefetch book closure route on hover for smoother navigation
                      prefetchBookClosure(client.id);
                    }}
                    onMouseLeave={() => setHoveredRow(null)}
                    actions={actions}
                  />
                );
              })}
            </tbody>
          </table>
        </div>
        {isAdmin && (
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={setCurrentPage}
            itemsPerPage={itemsPerPage}
            totalItems={filteredClients.length}
            startIndex={startIndex}
            endIndex={startIndex + itemsPerPage}
          />
        )}
      </>
    );
  };

  return <div className="overflow-hidden">{renderContent()}</div>;
}

ClientTable.propTypes = {
  searchTerm: PropTypes.string,
  statusFilter: PropTypes.string,
  organizations: PropTypes.arrayOf(
    PropTypes.shape({
      id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
      name: PropTypes.string,
      email: PropTypes.string,
      schema_name: PropTypes.string,
      is_active: PropTypes.bool,
    })
  ),
  loading: PropTypes.bool,
  error: PropTypes.oneOfType([PropTypes.string, PropTypes.object]),
};
