import bcrypt from "bcryptjs";
import {
  SECURITY_LOG_PASSWORD,
  SECURITY_PASSWORD_MESSAGES,
  SECURITY_PASSWORD_CONFIG,
  LOGGER_COMPONENT_NAMES,
  REGEX_PATTERNS,
  EMAIL_TEMPLATES,
  OTP_CONFIG,
  TIME_CONSTANTS,
  LOG_ACTIONS,
  USER_MESSAGES,
  ERROR_CODES,
  MODEL_FIELDS,
} from "./constants.util.js";
import { createLogger } from "./logger.util.js";
import { sendEmail } from "../../../../shared/utils/email.util.js";
import { throwErrorWithCode } from "./error.util.js";

const logger = createLogger(LOGGER_COMPONENT_NAMES.PASSWORD_UTILS);

export const hashPassword = async (password) => {
  try {
    const hashedPassword = await bcrypt.hash(
      password,
      SECURITY_PASSWORD_CONFIG.SALT_ROUNDS
    );
    logger.info(SECURITY_LOG_PASSWORD.HASH_SUCCESS);
    return hashedPassword;
  } catch (error) {
    logger.error(SECURITY_LOG_PASSWORD.HASH_ERROR, error);
    throw new Error(SECURITY_LOG_PASSWORD.HASH_ERROR);
  }
};

export const sendWelcomeMail = async ({
  recipientEmail,
  recipientName,
  plainPassword,
  orgName,
  loginUrl,
  baseUrl,
}) => {
  try {
    const fromEmail = process.env.NOTIFICATION_EMAIL;

    if (!fromEmail) {
      logger.warn(EMAIL_TEMPLATES.WELCOME_SENDER_MISSING);
      return false;
    }

    await sendEmail({
      to: recipientEmail,
      from: fromEmail,
      subject: EMAIL_TEMPLATES.WELCOME_SUBJECT,
      text: EMAIL_TEMPLATES.WELCOME_TEXT({
        recipientName,
        recipientEmail,
        plainPassword,
        orgName,
        loginUrl,
      }),
      html: EMAIL_TEMPLATES.WELCOME_HTML({
        recipientName,
        recipientEmail,
        plainPassword,
        orgName,
        loginUrl,
        baseUrl,
      }),
    });

    logger.info(EMAIL_TEMPLATES.WELCOME_SEND_SUCCESS);
    return true;
  } catch (error) {
    logger.warn(EMAIL_TEMPLATES.WELCOME_SEND_FAILURE, {
      error: error.message,
    });
    return false;
  }
};

export const validatePasswordStrength = (password, policy) => {
  if (
    password.length < policy.minLength ||
    password.length > policy.maxLength
  ) {
    return {
      isValid: false,
      message: SECURITY_PASSWORD_MESSAGES.PASSWORD_LENGTH(
        policy.minLength,
        policy.maxLength
      ),
    };
  }
  if (policy.requireUppercase && !REGEX_PATTERNS.UPPERCASE.test(password)) {
    return {
      isValid: false,
      message: SECURITY_PASSWORD_MESSAGES.PASSWORD_UPPERCASE,
    };
  }
  if (policy.requireLowercase && !REGEX_PATTERNS.LOWERCASE.test(password)) {
    return {
      isValid: false,
      message: SECURITY_PASSWORD_MESSAGES.PASSWORD_LOWERCASE,
    };
  }
  if (policy.requireNumbers && !REGEX_PATTERNS.NUMBERS.test(password)) {
    return {
      isValid: false,
      message: SECURITY_PASSWORD_MESSAGES.PASSWORD_NUMBER,
    };
  }
  if (
    policy.requireSpecialChars &&
    !REGEX_PATTERNS.SPECIAL_CHARS.test(password)
  ) {
    return {
      isValid: false,
      message: SECURITY_PASSWORD_MESSAGES.PASSWORD_SPECIAL,
    };
  }
  return { isValid: true };
};

/**
 * Generate a random 6-digit OTP
 * @returns {string} 6-digit OTP string
 */
export const generateOTP = () => {
  const otp = Math.floor(
    OTP_CONFIG.MIN_VALUE +
      Math.random() * (OTP_CONFIG.MIN_VALUE * OTP_CONFIG.RANGE_MULTIPLIER)
  ).toString();
  logger.info(LOG_ACTIONS.OTP_GENERATED, { otpLength: otp.length });
  return otp;
};

/**
 * Calculate OTP expiry date (15 minutes from now)
 * @returns {Date} OTP expiry date
 */
export const getOTPExpiry = () => {
  const expiryDate = new Date();
  expiryDate.setMinutes(expiryDate.getMinutes() + OTP_CONFIG.EXPIRY_MINUTES);
  return expiryDate;
};

/**
 * Send forgot password OTP email using SendGrid template
 * @param {Object} params - Email parameters
 * @param {string} params.recipientEmail - Recipient email address
 * @param {string} params.recipientName - Recipient name
 * @param {string} params.otp - 6-digit OTP
 * @returns {Promise<void>}
 */
export const sendForgotPasswordMail = async ({
  recipientEmail,
  recipientName,
  otp,
}) => {
  const {
    NOTIFICATION_EMAIL: fromEmail,
    FORGOT_PASSWORD_TEMPLATE_ID: templateId,
  } = process.env;

  if (!fromEmail) {
    logger.warn(EMAIL_TEMPLATES.FORGOT_PASSWORD_SENDER_MISSING);
    return;
  }

  if (!templateId) {
    logger.warn(EMAIL_TEMPLATES.FORGOT_PASSWORD_TEMPLATE_ID_MISSING);
    return;
  }

  const content = {
    recipientName,
    otp,
    expiryMinutes: OTP_CONFIG.EXPIRY_MINUTES,
  };

  try {
    await sendEmail({
      to: recipientEmail,
      from: fromEmail,
      templateId: templateId,
      data: content,
    });
    logger.info(EMAIL_TEMPLATES.FORGOT_PASSWORD_SEND_SUCCESS);
  } catch (error) {
    logger.warn(EMAIL_TEMPLATES.FORGOT_PASSWORD_SEND_FAILURE, {
      error: error.message,
      email: recipientEmail,
    });
  }
};

/**
 * Calculate password expiration date based on DAYS_TO_EXPIRE_PASSWORD environment variable
 * @returns {Date|null} Expiration date or null if not configured
 */
export const calculatePasswordExpiration = () => {
  const daysToExpire = Number.parseInt(process.env.DAYS_TO_EXPIRE_PASSWORD, 10);

  return !Number.isNaN(daysToExpire) && daysToExpire > 0
    ? new Date(Date.now() + daysToExpire * TIME_CONSTANTS.MILLISECONDS_PER_DAY)
    : null;
};

export const comparePassword = async (plainPassword, hashedPassword) => {
  try {
    const isMatch = await bcrypt.compare(plainPassword, hashedPassword);
    if (isMatch) {
      logger.info(SECURITY_LOG_PASSWORD.COMPARE_SUCCESS);
    }
    return isMatch;
  } catch (error) {
    logger.error(SECURITY_LOG_PASSWORD.COMPARE_ERROR, error);
    throw new Error(SECURITY_LOG_PASSWORD.COMPARE_ERROR);
  }
};

/**
 * Validate password change requirements
 * @param {Object} params - Validation parameters
 * @param {string} params.oldPassword - Old password
 * @param {string} params.newPassword - New password
 * @param {string} params.hashedPassword - User's current hashed password
 * @returns {Promise<void>} Throws error if validation fails
 */
export const validatePasswordChange = async ({
  oldPassword,
  newPassword,
  hashedPassword,
}) => {
  // Verify old password
  const isOldPasswordValid = await comparePassword(oldPassword, hashedPassword);

  if (!isOldPasswordValid) {
    throwErrorWithCode(
      USER_MESSAGES.INVALID_OLD_PASSWORD,
      ERROR_CODES.AUTHENTICATION_ERROR
    );
  }

  if (newPassword === oldPassword) {
    throwErrorWithCode(
      USER_MESSAGES.PASSWORDS_CANNOT_BE_SAME,
      ERROR_CODES.VALIDATION_ERROR
    );
  }
};

/**
 * Validate OTP expiry and match
 * @param {Object} params - Validation parameters
 * @param {Object} params.user - User object
 * @param {string} params.otp - OTP to validate
 * @param {string} params.email - User email for logging
 * @returns {void} Throws error if validation fails
 */
export const validateOtpExpiryAndMatch = ({ user, otp, email }) => {
  // Check if OTP has expired
  const now = new Date();
  const otpExpiry = new Date(user[MODEL_FIELDS.OTP_EXPIRY]);

  if (otpExpiry < now) {
    throwErrorWithCode(USER_MESSAGES.OTP_EXPIRED, ERROR_CODES.VALIDATION_ERROR);
  }

  // Check if OTP matches
  if (user[MODEL_FIELDS.OTP] !== otp) {
    throwErrorWithCode(USER_MESSAGES.OTP_INVALID, ERROR_CODES.VALIDATION_ERROR);
  }
};

/**
 * Validate OTP for a user
 * @param {Object} params - Validation parameters
 * @param {Object} params.user - User object
 * @param {string} params.otp - OTP to validate
 * @param {string} params.email - User email for logging
 * @returns {void} Throws error if validation fails
 */
export const validateUserOtp = ({ user, otp, email }) => {
  // Check if user exists
  if (!user) {
    throwErrorWithCode(USER_MESSAGES.NOT_FOUND, ERROR_CODES.NOT_FOUND_ERROR);
  }

  // Check if user is deleted
  if (user.is_deleted) {
    throwErrorWithCode(
      USER_MESSAGES.NOT_FOUND_OR_DELETED,
      ERROR_CODES.NOT_FOUND_ERROR
    );
  }

  // Check if OTP exists
  if (!user[MODEL_FIELDS.OTP]) {
    throwErrorWithCode(
      USER_MESSAGES.OTP_NOT_FOUND,
      ERROR_CODES.VALIDATION_ERROR
    );
  }
};

export default {
  hashPassword,
  sendwelcomemai: sendWelcomeMail,
  validatePasswordStrength,
  generateOTP,
  getOTPExpiry,
  sendForgotPasswordMail,
  validateUserOtp,
  validateOtpExpiryAndMatch,
  validatePasswordChange,
};
