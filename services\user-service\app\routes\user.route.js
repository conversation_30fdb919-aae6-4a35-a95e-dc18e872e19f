import express from "express";
import * as userController from "../controllers/user.controller.js";
import userValidations from "../validators/user.validator.js";
import { validate } from "../middleware/validate.middleware.js";

const router = express.Router();

// Create a new user
router.post(
  "/",
  validate(userValidations.createUser),
  userController.createUser
);

// Get all users
router.get("/", userController.getAllUsers);

// Get user by email (must be before /:id route)
router.get("/email/:email", userController.getUserByEmail);

// Forgot password (must be before /:id route)
router.post(
  "/forgot-password",
  validate(userValidations.forgotPassword),
  userController.forgotPassword
);

// Validate OTP (must be before /:id route)
router.post(
  "/validate-otp",
  validate(userValidations.validateOtp),
  userController.validateOtp
);

// Change password (must be before /:id route)
router.put(
  "/change-password",
  validate(userValidations.changePassword),
  userController.changePassword
);

// Reset password (must be before /:id route)
router.put(
  "/reset-password/:id",
  validate(userValidations.resetPassword),
  userController.resetPassword
);

// Get user by ID (parameterized routes should be last)
router.get("/:id", userController.getUserById);

// Update user by ID (parameterized routes should be last)
router.put("/:id", userController.updateUser);

// Delete user by ID (parameterized routes should be last)
router.delete("/:id", userController.deleteUser);

export default router;
