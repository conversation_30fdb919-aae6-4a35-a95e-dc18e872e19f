// Book Closure Page Constants
export const BOOKCLOSURE_CONSTANTS = {
  // Page Information
  PAGE_TITLE: "Monthly Book Closure",
  PAGE_SUBTITLE:
    "Sync and submit monthly financial, operational, and payroll data",
  BACK_BUTTON_TEXT: "Back to Listing",

  // Client Info
  CLIENT_INFO: {
    STATUS_LABEL: "Status:",
    LAST_SUBMISSION_LABEL: "Last Submission:",
  },

  // Month Selector
  MONTH_SELECTOR: {
    TITLE: "Transaction Month",
  },

  // Upload Section
  UPLOAD_SECTION: {
    TITLE: "Upload Data",
    NOT_SELECTED_LABEL: "Not Subscribed",
    CARDS: {
      FINANCIAL: {
        KEY: "financial",
        TITLE: "Financial Data",
        DESCRIPTION: "Upload bank statements, invoices, receipts",
      },
      OPERATIONAL: {
        KEY: "operational",
        TITLE: "Operational Data",
        DESCRIPTION: "Sync operational reports, metrics",
      },
      PAYROLL: {
        KEY: "payroll",
        TITLE: "ADP/Payroll Data",
        DESCRIPTION: "Upload payroll reports, ADP data",
      },
    },
  },

  SERVICES_VALUES: {
    FINANCIAL: "financial",
    OPERATIONS: "operational",
    PAYROLL: "payroll",
  },

  // Upload Card
  UPLOAD_CARD: {
    LAST_SYNCED_LABEL: "Last synced:",
    SYNC_BUTTON: {
      SYNCING: "Syncing...",
      SYNCED: "Synced Data",
      SYNC: "Sync Data",
    },
  },

  // Action Buttons
  ACTION_BUTTONS: {
    SAVE_AS_DRAFT: "Save as Draft",
    SAVING: "Saving Draft...",
    CANCEL: "Cancel",
    SUBMIT: "Submit Book Closure",
    SUBMITTING: "Submitting...",
  },

  // Toast Messages
  TOAST_MESSAGES: {
    DRAFT_SAVED: "Book closure data saved as draft successfully.",
    DATA_SUBMITTED:
      "Book closure data submitted successfully. Dashboard will be updated shortly.",
    SYNC_SUCCESS: "Data synchronized successfully.",
    SYNC_ERROR: "Failed to synchronize data. Please try after some time.",
    UPLOAD_SUCCESS: "File uploaded successfully.",
    UPLOAD_ERROR:
      "File upload failed. Please check the file format and try again.",
    WORKFLOW_SUCCESS:
      "Dashboard generation initiated. Please check back in a few minutes.",
  },

  // Month Names
  MONTH_NAMES: {
    ABBREVIATED: [
      "Jan",
      "Feb",
      "Mar",
      "Apr",
      "May",
      "Jun",
      "Jul",
      "Aug",
      "Sep",
      "Oct",
      "Nov",
      "Dec",
    ],
    FULL: [
      "January",
      "February",
      "March",
      "April",
      "May",
      "June",
      "July",
      "August",
      "September",
      "October",
      "November",
      "December",
    ],
    MAPPING: {
      Jan: "January",
      Feb: "February",
      Mar: "March",
      Apr: "April",
      May: "May",
      Jun: "June",
      Jul: "July",
      Aug: "August",
      Sep: "September",
      Oct: "October",
      Nov: "November",
      Dec: "December",
    },
  },

  // File Types
  FILE_TYPES: {
    PDF: "application/pdf",
    XLSX: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    DEFAULT_FILE_SIZE: 1024000,
  },

  // Services
  SERVICES: {
    FINANCE: "Finance",
    OPERATIONS: "Operations",
    PAYROLL: "Payroll",
  },
};
