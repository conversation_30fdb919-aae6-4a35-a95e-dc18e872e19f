import { DataTypes } from "sequelize";

const QbEbitdaAccountMap = (sequelize) => {
  const AccountMap = sequelize.define(
    "EbitdaAccountMap",
    {
      id: {
        type: DataTypes.BIGINT,
        primaryKey: true,
        autoIncrement: true,
        allowNull: false,
      },
      account_id: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      account_name_pattern: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      account_sub_type: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      ebitda_category: {
        type: DataTypes.TEXT,
        allowNull: false,
        defaultValue: [],
      },
      priority: {
        type: DataTypes.INTEGER,
        defaultValue: 100,
        allowNull: true,
      },
      created_at: {
        type: DataTypes.DATE,
        defaultValue: DataTypes.NOW,
        allowNull: true,
        field: "created_at",
      },
    },
    {
      tableName: "qb_ebitda_account_map",
      timestamps: false,
      createdAt: false,
      updatedAt: false,
    }
  );

  return AccountMap;
};

export default QbEbitdaAccountMap;

