import { DataTypes } from "sequelize";

const GrossCollectionModel = (sequelize) =>
  sequelize.define(
    "sikka_gross_collections",
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true,
      },

      practice_id: {
        type: DataTypes.STRING,
        allowNull: false,
      },

      value: {
        type: DataTypes.STRING,
        allowNull: false,
      },

      practice_name: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      start_date: {
        type: DataTypes.DATEONLY,
        allowNull: false,
      },
      end_date: {
        type: DataTypes.DATEONLY,
        allowNull: false,
      },
    },
    {
      tableName: "sikka_gross_collections",
      timestamps: true,
      createdAt: "created_at",
      updatedAt: "updated_at",
    }
  );

export default GrossCollectionModel; 
