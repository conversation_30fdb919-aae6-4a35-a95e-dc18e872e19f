import { Pn<PERSON><PERSON>ort, Pn<PERSON>ine, PnLSummary } from "../models/index.js";
import logger from "../../config/logger.config.js";
import { createInSchema, createBulkInSchema } from "./database.utils.js";
import { PNL_LOGS } from "./constants/log.constants.js";

function parseAmount(v) {
  if (v == null) return null;
  const s = String(v).replace(/,/g, "").trim();
  return s ? (Number.isFinite(Number(s)) ? Number(s) : null) : null;
}

function flattenPnL(pnlJson) {
  const flat = [];
  const summaries = [];

  function walkRows(node, pathParts = []) {
    if (!node) return;
    const rows = node.Row || [];
    for (const row of rows) {
      const rowType = row.type;
      let newPath = pathParts.slice();

      if (row.Header?.ColData?.length) {
        const headerLabel = row.Header.ColData[0]?.value || "";
        if (headerLabel) newPath.push(headerLabel);
      }

      if (rowType === "Data") {
        const columnData = row.ColData || [];
        flat.push({
          path: newPath.join(" > "),
          account_label: columnData[0]?.value || "",
          account_id: columnData[0]?.id ? String(columnData[0].id) : null,
          amount: parseAmount(columnData[1]?.value),
        });
      }

      if (rowType === "Section" && row.Summary?.ColData?.length >= 2) {
        const summaryData = row.Summary.ColData;
        summaries.push({
          path: newPath.join(" > "),
          label: summaryData[0]?.value || "",
          amount: parseAmount(summaryData[1]?.value || ""),
          group: row.group || "",
        });
      }

      if (row.Rows) walkRows(row.Rows, newPath);
    }
  }

  walkRows(pnlJson?.Rows || {}, []);
  return { flat, summaries };
}

async function loadAccountMap() {
  return new Map();
}

// Account name to category mapping
const ACCOUNT_CATEGORY_MAP = {
  // Operating Expenses
  "Cleaning - office": ["Operating Expenses", "Cleaning - office"],
  "Clinical Tool Licensure": ["Operating Expenses", "Clinical Tool Licensure"],
  "Equipment Maint. & Repair": ["Operating Expenses", "Equipment Maint. & Repair"],
  "Legal & Accounting": ["Operating Expenses", "Legal & Accounting"],
  "Medical Supplies": ["Operating Expenses", "Medical Supplies"],
  "Patient Notification Tools": ["Operating Expenses", "Patient Notification Tools"],
  "Office Expenses": ["Operating Expenses", "Office Expenses"],
  "Practice Mgmt Software Lease": ["Operating Expenses", "Practice Mgmt Software Lease"],
  Rent: ["Operating Expenses", "Rent"],
  "Telephone Office": ["Operating Expenses", "Telephone Office"],
  Utilities: ["Operating Expenses", "Utilities"],
  Vaccines: ["Operating Expenses", "Vaccines"],
  "Website Expense": ["Operating Expenses", "Website Expense"],
  "Workmans Comp Ins": ["Operating Expenses", "Workmans Comp Ins"],

  // Admin
  "Doctor Admin Income Expense - Canner": ["Admin", "Doctor Admin Income Expense - Canner"],
  "Doctor Admin Income Expense - Ghosh": ["Admin", "Doctor Admin Income Expense - Ghosh"],
  "Doctor Admin Income Expense - Pierce": ["Admin", "Doctor Admin Income Expense - Pierce"],
  "Doctor Admin Income Expense - Scherer": ["Admin", "Doctor Admin Income Expense - Scherer"],
  "Doctor Admin Income Expense - Shah": ["Admin", "Doctor Admin Income Expense - Shah"],

  // Dues, Subscriptions & License
  "Dues, Sub & Lic Deb Ghosh": ["Dues, Subscriptions & License", "Dues, Sub & Lic Deb Ghosh"],
  "Dues, Subs & Lic - Dr. Canner": ["Dues, Subscriptions & License", "Dues, Subs & Lic - Dr. Canner"],
  "Dues, Subs & Lic - Dr. George": ["Dues, Subscriptions & License", "Dues, Subs & Lic - Dr. George"],
  "Dues, Subs & Lic - Dr. Pierce": ["Dues, Subscriptions & License", "Dues, Subs & Lic - Dr. Pierce"],
  "Dues, Subs & Lic - Dr. Scherer": ["Dues, Subscriptions & License", "Dues, Subs & Lic - Dr. Scherer"],
  "Dues, Subs & Lic - Dr. Shah": ["Dues, Subscriptions & License", "Dues, Subs & Lic - Dr. Shah"],
  "Dues, Subs & Lic - Dr. Shaikh": ["Dues, Subscriptions & License", "Dues, Subs & Lic - Dr. Shaikh"],

  // Payroll Tax Expense - Payroll Expenses
  "Payroll Tax - Associate Dr. Shaikh": ["Payroll Tax Expense", "Payroll Expenses", "Payroll Tax - Associate Dr. Shaikh"],
  "Payroll Tax - Dr. Canner": ["Payroll Tax Expense", "Payroll Expenses", "Payroll Tax - Dr. Canner"],
  "Payroll Tax - Dr. Ghosh": ["Payroll Tax Expense", "Payroll Expenses", "Payroll Tax - Dr. Ghosh"],
  "Payroll Tax - Dr. Pierce": ["Payroll Tax Expense", "Payroll Expenses", "Payroll Tax - Dr. Pierce"],
  "Payroll Tax - Dr. Scherer": ["Payroll Tax Expense", "Payroll Expenses", "Payroll Tax - Dr. Scherer"],
  "Payroll Tax - Dr. Shah": ["Payroll Tax Expense", "Payroll Expenses", "Payroll Tax - Dr. Shah"],
  "Payroll Tax - Office Staff": ["Payroll Tax Expense", "Payroll Expenses", "Payroll Tax - Office Staff"],

  // Payroll Tax Expense - Salary Expense
  "Payroll - Office Staff": ["Payroll Tax Expense", "Salary Expense", "Payroll - Office Staff"],
  "Salary - Associate Dr. Shaikh": ["Payroll Tax Expense", "Salary Expense", "Salary - Associate Dr. Shaikh"],
  "Salary - Dr. Canner": ["Payroll Tax Expense", "Salary Expense", "Salary - Dr. Canner"],
  "Salary - Dr. Ghosh": ["Payroll Tax Expense", "Salary Expense", "Salary - Dr. Ghosh"],
  "Salary - Dr. Pierce": ["Payroll Tax Expense", "Salary Expense", "Salary - Dr. Pierce"],
  "Salary - Dr. Scherer": ["Payroll Tax Expense", "Salary Expense", "Salary - Dr. Scherer"],
  "Salary - Dr. Shah": ["Payroll Tax Expense", "Salary Expense", "Salary - Dr. Shah"],

  // Other Expenses
  "Bank Charge": ["Other Expenses", "Bank Charge"],
  "Meals & Entertainment": ["Other Expenses", "Meals & Entertainment"],
  "Profit Sharing": ["Other Expenses", "Profit Sharing"],
  Refunds: ["Other Expenses", "Refunds"],
  Reimbursement: ["Other Expenses", "Reimbursement"],
  "Service fee": ["Other Expenses", "Service fee"],
  "Interest Expense": ["Other Expenses", "Interest Expense"],

  // Health Insurance Expense (Medical)
  "Health Insurance - B. Scherer": ["Health Insurance Expense (Medical)", "Health Insurance - B. Scherer"],
  "Health Insurance - Employee": ["Health Insurance Expense (Medical)", "Health Insurance - Employee"],
  "Health Insurance - J. Canner": ["Health Insurance Expense (Medical)", "Health Insurance - J. Canner"],
};

function transformToPowerBIKPI(flat, accountMap) {
  const powerbiKpi = {
    "Operating Expenses": {
      "Cleaning - office": null,
      "Clinical Tool Licensure": null,
      "Equipment Maint. & Repair": null,
      "Legal & Accounting": null,
      "Medical Supplies": null,
      "Patient Notification Tools": null,
      "Office Expenses": null,
      "Practice Mgmt Software Lease": null,
      Rent: null,
      "Telephone Office": null,
      Utilities: null,
      Vaccines: null,
      "Website Expense": null,
      "Workmans Comp Ins": null,
      "Total Operating Expense": null,
    },
    Admin: {
      "Doctor Admin Income Expense - Canner": null,
      "Doctor Admin Income Expense - Ghosh": null,
      "Doctor Admin Income Expense - Pierce": null,
      "Doctor Admin Income Expense - Scherer": null,
      "Doctor Admin Income Expense - Shah": null,
      "Total Admin Expense": null,
    },
    "Dues, Subscriptions & License": {
      "Dues, Sub & Lic Deb Ghosh": null,
      "Dues, Subs & Lic - Dr. Canner": null,
      "Dues, Subs & Lic - Dr. George": null,
      "Dues, Subs & Lic - Dr. Pierce": null,
      "Dues, Subs & Lic - Dr. Scherer": null,
      "Dues, Subs & Lic - Dr. Shah": null,
      "Dues, Subs & Lic - Dr. Shaikh": null,
      "Total Dues, Subs & Lic": null,
    },
    "Payroll Tax Expense": {
      "Payroll Expenses": {
        "Payroll Tax - Associate Dr. Shaikh": null,
        "Payroll Tax - Dr. Canner": null,
        "Payroll Tax - Dr. Ghosh": null,
        "Payroll Tax - Dr. Pierce": null,
        "Payroll Tax - Dr. Scherer": null,
        "Payroll Tax - Dr. Shah": null,
        "Payroll Tax - Office Staff": null,
        "Total Payroll Tax": null,
      },
      "Salary Expense": {
        "Payroll - Office Staff": null,
        "Salary - Associate Dr. Shaikh": null,
        "Salary - Dr. Canner": null,
        "Salary - Dr. Ghosh": null,
        "Salary - Dr. Pierce": null,
        "Salary - Dr. Scherer": null,
        "Salary - Dr. Shah": null,
        "Total Salary": null,
      },
    },
    "Other Expenses": {
      "Bank Charge": null,
      "Meals & Entertainment": null,
      "Profit Sharing": null,
      Refunds: null,
      Reimbursement: null,
      "Service fee": null,
      "Interest Expense": null,
      "Total Other expense": null,
    },
    "Health Insurance Expense (Medical)": {
      "Health Insurance - B. Scherer": null,
      "Health Insurance - Employee": null,
      "Health Insurance - J. Canner": null,
      "Total Medical Expense": null,
    },
  };

  const expenseLines = flat.filter((r) => (r.path || "").startsWith("Expenses"));

  for (const line of expenseLines) {
    const accountName = (line.account_id && accountMap.get(line.account_id)) || line.account_label;
    const amount = line.amount;

    if (accountName && amount != null) {
      const mapping = ACCOUNT_CATEGORY_MAP[accountName];
      if (mapping) {
        if (mapping.length === 2) {
          powerbiKpi[mapping[0]][mapping[1]] = amount;
        } else if (mapping.length === 3) {
          powerbiKpi[mapping[0]][mapping[1]][mapping[2]] = amount;
  }
      }
    }
  }

  calculateCategoryTotals(powerbiKpi);
  return powerbiKpi;
}

/**
 * Get the category name for a given account name
 * @param {string} accountName - The account name to categorize
 * @returns {string|null} - The category name or null if not categorized
 */
function getCategoryForAccount(accountName) {
  if (!accountName) return null;

  const mapping = ACCOUNT_CATEGORY_MAP[accountName];
  return mapping ? mapping[0] : "Miscellaneous";
}

function calculateCategoryTotals(powerbiKpi) {
  const calculateTotal = (category, totalKey, excludeKeys = []) => {
    const total = Object.entries(category)
      .filter(([key]) => !excludeKeys.includes(key) && key !== totalKey)
      .reduce((sum, [, value]) => sum + (value != null ? value : 0), 0);
    category[totalKey] = total || null;
  };

  calculateTotal(powerbiKpi["Operating Expenses"], "Total Operating Expense");
  calculateTotal(powerbiKpi["Admin"], "Total Admin Expense");
  calculateTotal(powerbiKpi["Dues, Subscriptions & License"], "Total Dues, Subs & Lic");
  calculateTotal(powerbiKpi["Payroll Tax Expense"]["Payroll Expenses"], "Total Payroll Tax");
  calculateTotal(powerbiKpi["Payroll Tax Expense"]["Salary Expense"], "Total Salary");
  calculateTotal(powerbiKpi["Other Expenses"], "Total Other expense");
  calculateTotal(powerbiKpi["Health Insurance Expense (Medical)"], "Total Medical Expense");
}

async function insertReportHeader(
  pnl,
  powerbiKpiJson,
  realmId,
  schemaName = null,
  existingReportId = null
) {
  const h = pnl.Header || {};
  const reportData = {
    report_name: h.ReportName || null,
    report_basis: h.ReportBasis || null,
    start_date: h.StartPeriod || null,
    end_date: h.EndPeriod || null,
    currency: h.Currency || null,
    generated_at: h.Time ? new Date(h.Time) : new Date(),
    raw: JSON.stringify(pnl),
    powerbi_kpi_json: JSON.stringify(powerbiKpiJson),
    realm_id: realmId,
  };

  if (existingReportId) {
    if (schemaName) {
      const { updateInSchema } = await import("./database.utils.js");
      await updateInSchema(PnLReport, schemaName, existingReportId, reportData);
    } else {
      await PnLReport.update(reportData, { where: { id: existingReportId } });
    }
    return existingReportId;
  }

  const report = schemaName
    ? await createInSchema(PnLReport, schemaName, reportData)
    : await PnLReport.create(reportData);
  return report.id;
}

async function insertLines(
  reportId,
  rows,
  accountMap,
  realmId,
  schemaName = null
) {
  if (!rows.length) return;

  const prepared = rows.map((r) => {
    const accountName = (r.account_id && accountMap.get(r.account_id)) || r.account_label;
    return {
      report_id: reportId,
      path: r.path,
      account_name: accountName,
      account_id: r.account_id,
      amount: r.amount,
      category: getCategoryForAccount(accountName),
      realm_id: realmId,
    };
  });

  const bulkInsert = schemaName
    ? (data) => createBulkInSchema(PnLLine, schemaName, data, { ignoreDuplicates: true })
    : (data) => PnLLine.bulkCreate(data, { validate: false, ignoreDuplicates: true });

  await bulkInsert(prepared);
}

async function insertSummaries(
  reportId,
  summaries,
  realmId,
  schemaName = null
) {
  if (!summaries.length) return;

  const prepared = summaries.map((s) => ({
    report_id: reportId,
    group: s.group,
    label: s.label,
    path: s.path,
    amount: s.amount,
    realm_id: realmId,
  }));

  const bulkInsert = schemaName
    ? (data) => createBulkInSchema(PnLSummary, schemaName, data, { ignoreDuplicates: true })
    : (data) => PnLSummary.bulkCreate(data, { validate: false, ignoreDuplicates: true });

  await bulkInsert(prepared);
}

export async function processPnLData(
  pnl,
  realmId,
  schemaName = null,
  existingReportId = null
) {
  try {
    const { flat, summaries } = flattenPnL(pnl);
    const accountMap = await loadAccountMap();
    const powerbiKpiJson = transformToPowerBIKPI(flat, accountMap);

    const reportId = await insertReportHeader(
      pnl,
      powerbiKpiJson,
      realmId,
      schemaName,
      existingReportId
    );

    await insertLines(reportId, flat, accountMap, realmId, schemaName);
    await insertSummaries(reportId, summaries, realmId, schemaName);

    const calculateTotal = (filterFn) =>
      flat
        .filter(filterFn)
        .reduce((sum, r) => sum + (Number.isFinite(r.amount) ? r.amount : 0), 0);

    const totIncome = calculateTotal((r) => (r.path || "").startsWith("Income"));
    const totExpenses = calculateTotal((r) => (r.path || "").startsWith("Expenses"));
    const calcNet = totIncome - totExpenses;
    const reportedNet = summaries.find((s) => s.group === "NetIncome")?.amount ?? null;

    return {
      reportId,
      totals: {
        totalIncome: totIncome,
        totalExpenses: totExpenses,
        netIncome: calcNet,
        reportedNetIncome: reportedNet,
      },
      balanceCheck: {
        calculated: calcNet,
        reported: reportedNet,
        balanced: Math.abs(calcNet - (reportedNet || 0)) < 0.01,
      },
      powerbiKpi: powerbiKpiJson,
    };
  } catch (error) {
    logger.error(PNL_LOGS.PROCESS_DATA_FAILED, {
      error: error.message,
      realmId,
      status: 500,
    });
    throw error;
  }
}

export async function getPnLReportsByRealmId(realmId, options = {}) {
  try {
    const reports = await PnLReport.findAll({
      where: { realm_id: realmId },
      include: [
        { model: PnLLine, as: "lines" },
        { model: PnLSummary, as: "summaries" },
      ],
      order: [["created_at", "DESC"]],
      ...options,
    });

    return reports;
  } catch (error) {
    logger.error(PNL_LOGS.GET_REPORTS_FAILED, {
      error: error.message,
      realmId,
      status: 500,
    });
    throw error;
  }
}

export {
  flattenPnL,
  transformToPowerBIKPI,
  getCategoryForAccount,
  parseAmount,
};
