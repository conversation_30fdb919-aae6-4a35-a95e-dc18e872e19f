// AUTH CONSTANTS
// Static messages for Authentication Middleware

export const AUTH_MESSAGES = {
  // Success Messages
  API_KEY_VERIFIED: "API key verified successfully",

  // Error Messages
  UNAUTHORIZED: "Unauthorized",
  INVALID_API_KEY: "Invalid API key",
  API_KEY_REQUIRED: "x-api-key header is required",
  API_KEY_MISSING: "API key missing from request",
  INVALID_API_KEY_PROVIDED: "Invalid API key provided",

  // Configuration Errors
  SYSTEM_API_KEY_NOT_CONFIGURED: "System API key not configured in environment variables",
  API_KEY_VERIFICATION_FAILED: "API key verification failed",
  SERVER_CONFIGURATION_ERROR: "Server configuration error",
  API_KEY_SERVICE_NOT_CONFIGURED: "API key verification service is not configured",
};

export const AUTH_LOG_MESSAGES = {
  API_KEY_VERIFIED: "API key verified successfully",
  API_KEY_MISSING: "API key missing from request",
  API_KEY_NOT_CONFIGURED: "System API key not configured in environment variables",
  INVALID_API_KEY: "Invalid API key provided",
  AUTH_MIDDLEWARE_ERROR: "Auth middleware error",
};

export default {
  AUTH_MESSAGES,
  AUTH_LOG_MESSAGES,
};

