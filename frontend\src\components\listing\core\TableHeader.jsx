"use client";

import { LISTING_CONSTANTS } from "@/utils/constants/listing";
import "@/styles/listing.css";

export default function TableHeader({ onSort, sortConfig }) {
  const getSortIcon = (columnKey) => {
    const isActive = sortConfig.key === columnKey;
    const isAsc = sortConfig.direction === "asc";
    if (!isActive) {
      return (
        <svg
          className="listing-table-header-sort-icon"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M8 9l4-4 4 4m0 6l-4 4-4-4"
          />
        </svg>
      );
    }
    return isAsc ? (
      <svg
        className="listing-table-header-sort-icon listing-table-header-sort-icon-active"
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M5 15l7-7 7 7"
        />
      </svg>
    ) : (
      <svg
        className="listing-table-header-sort-icon listing-table-header-sort-icon-active"
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M19 9l-7 7-7-7"
        />
      </svg>
    );
  };

  return (
    <thead>
      <tr className="listing-table-header">
        <th
          className="listing-table-header-cell"
          onClick={() => onSort("name")}
        >
          <div className="listing-table-header-sort-container">
            {LISTING_CONSTANTS.TABLE_HEADERS.CLIENT_NAME}
            {getSortIcon("name")}
          </div>
        </th>
        <th className="listing-table-header-cell listing-table-header-cell-center">
          {LISTING_CONSTANTS.TABLE_HEADERS.STATUS}
        </th>
        <th className="listing-table-header-cell listing-table-header-cell-center">
          Last sync
        </th>
        <th className="listing-table-header-cell listing-table-header-cell-right">
          {LISTING_CONSTANTS.TABLE_HEADERS.ACTIONS}
        </th>
      </tr>
    </thead>
  );
}
