import ExpenseBreakdownService from "../services/expense_breakdown.service.js";
import { LOGGER_NAMES } from "../utils/constants/log.constants.js";
import { createLogger } from "../utils/logger.utils.js";
import { validateRequiredParams, handleControllerError, sendSuccessResponse } from "../utils/controller.utils.js";

const logger = createLogger(LOGGER_NAMES.REPORT_CONTROLLER);

/**
 * Get expense breakdown for organization by month and year
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getExpenseBreakdown = async (req, res) => {
  try {
    const { organization_id, month, year } = req.query;

    logger.info(
      `Fetching expense breakdown for org: ${organization_id}, month: ${month}, year: ${year}`
    );

    // Validate required parameters
    const validationError = validateRequiredParams(req.query, ['organization_id', 'month', 'year']);
    if (validationError) {
      return res.status(400).json(validationError);
    }

    // Fetch expense breakdown data
    const expenseBreakdownData = await ExpenseBreakdownService.getExpenseBreakdown({
      organization_id,
      month,
      year,
    });

    // Return successful response
    sendSuccessResponse(res, "Expense breakdown fetched successfully", expenseBreakdownData);
  } catch (error) {
    logger.error("Error fetching expense breakdown:", error);
    handleControllerError(error, res, "Error fetching expense breakdown");
  }
};

export default {
  getExpenseBreakdown,
};
