// app/utils/memoryStore.js

/**
 * A tiny store for:
 * 1) Cached document text per filename (in-memory only)
 * 2) Chat sessions: { sessionId: { filename, history: [...], expiresAt } } (in-memory only)
 */

const docTextCache = new Map(); // filename -> text (not persisted)
const sessions = new Map(); // sessionId -> { filename, history, sasUrl, blobName, expiresAt, organizationId, organizationName, year, month, service }

// Session expiration time (24 hours in milliseconds)
const SESSION_EXPIRY_MS = 24 * 60 * 60 * 1000;

export function getCachedDocText(filename) {
  return docTextCache.get(filename) || null;
}
export function setCachedDocText(filename, text) {
  docTextCache.set(filename, text);
}

export function createSession({
  sessionId,
  filename,
  sasUrl,
  blobName,
  organizationId = null,
  organizationName = null,
  year = null,
  month = null,
  service = null,
}) {
  const expiresAt = Date.now() + SESSION_EXPIRY_MS;
  sessions.set(sessionId, {
    filename,
    history: [],
    sasUrl,
    blobName,
    organizationId,
    organizationName,
    year,
    month,
    service,
    expiresAt,
  });
  return sessions.get(sessionId);
}
export function getSession(sessionId) {
  const session = sessions.get(sessionId);
  if (!session) return null;

  // Check if session has expired
  if (Date.now() > session.expiresAt) {
    sessions.delete(sessionId);
    return null;
  }

  return session;
}
export function appendHistory(sessionId, role, content) {
  const s = getSession(sessionId); // Use getSession to check expiration
  if (!s) return;
  s.history.push({ role, content });
  // OPTIMIZATION: Keep last 6 messages (reduced from 12) to reduce token usage and improve speed
  if (s.history.length > 6) s.history.splice(0, s.history.length - 6);
}

export function deleteSession(sessionId) {
  return sessions.delete(sessionId);
}

// Function to clean up expired sessions (can be called periodically)
export function cleanupExpiredSessions() {
  const now = Date.now();
  for (const [sessionId, session] of sessions.entries()) {
    if (now > session.expiresAt) {
      sessions.delete(sessionId);
    }
  }
}
