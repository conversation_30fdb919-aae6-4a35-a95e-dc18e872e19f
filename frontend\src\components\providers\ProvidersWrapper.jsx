"use client";

import { AuthProvider } from "@/redux/Providers/AuthProvider";
import ToastProvider from "@/components/ui/toast";
import ReduxProvider from "@/components/providers/ReduxProvider";
import ErrorBoundary from "@/components/ui/error-boundary";

/**
 * Client-side providers wrapper
 * This component wraps all client-side providers to be used in server components
 */
export default function ProvidersWrapper({ children }) {
  return (
    <ErrorBoundary>
      <ReduxProvider>
        <ToastProvider>
          <AuthProvider>{children}</AuthProvider>
        </ToastProvider>
      </ReduxProvider>
    </ErrorBoundary>
  );
}
