"use client";

import React from "react";
import "@/styles/dashboard.css";

export default function CategoryTabs({
  categories = [],
  selectedCategory,
  onCategoryChange,
  className = "",
}) {
  if (!categories || categories.length === 0) {
    return null;
  }

  return (
    <div className={`dashboard-category-tabs-container ${className}`}>
      <div className="dashboard-category-tabs">
        {categories.map((category) => {
          const isSelected = selectedCategory === category.key;
          return (
            <button
              key={category.key}
              onClick={() => onCategoryChange?.(category.key)}
              className={`dashboard-category-tab ${
                isSelected ? "dashboard-category-tab--active" : ""
              }`}
            >
              {category.label || category.key}
            </button>
          );
        })}
      </div>
    </div>
  );
}

