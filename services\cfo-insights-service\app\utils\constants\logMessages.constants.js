export const LOG_CONSTANTS = {
  SERVICE_NAME: "cfo-insights-service",
  LOG_DIRECTORY_RELATIVE_PATH: "../../logs",
  DEFAULT_LOG_LEVEL: "info",
  TIMESTAMP_FORMAT: "YYYY-MM-DD HH:mm:ss",
  GENERAL_LOG_FILENAME: "cfo-insights-%DATE%.log",
  ERROR_LOG_FILENAME: "cfo-insights-error-%DATE%.log",
  GENERAL_LOG_MAX_SIZE: "20m",
  GENERAL_LOG_MAX_FILES: "14d",
  ERROR_LOG_MAX_SIZE: "20m",
  ERROR_LOG_MAX_FILES: "30d",
};

export const LOG_MESSAGES = {
  CHAT: {
    COMPETITOR_SEARCH_FAILED: "Competitor search failed, continuing without competitor data",
    DOCUMENT_CACHE_PREWARM_FAILED: "Failed to pre-warm document cache",
    DOCUMENT_TEXT_FETCH_FAILED: "Document text fetch failed",
    TWO_STEP_SUMMARY_FAILED: "Two-step summary generation failed, falling back to single-step",
    FILE_RESOLUTION_TIMEOUT: "File resolution request timeout",
    FILE_RESOLUTION_NOT_FOUND: "File not found during resolution",
    FILE_RESOLUTION_BAD_REQUEST: "Bad request during file resolution",
    FILE_RESOLUTION_ERROR: "Error during file resolution",
    DOCUMENT_UPDATE_FAILED: "Document update failed",
    DOCUMENT_UPDATE_VALIDATION_ERROR: "Document update validation error",
  },
  SUCCESS: {
    SESSION_CREATED: "Chat session created successfully",
    MESSAGE_SENT: "Message sent successfully",
    SUMMARY_GENERATED: "Summary generated successfully",
    SESSION_ENDED: "Session ended successfully",
    DOCUMENT_RESOLVED: "Document resolved successfully",
    DOCUMENT_UPDATED: "Document updated successfully",
  },
  INFO: {
    DOCUMENT_CACHE_PREWARM_STARTED: "Starting document cache pre-warm",
    COMPETITOR_SEARCH_STARTED: "Starting competitor search",
    SUMMARY_GENERATION_STARTED: "Starting summary generation",
    FILE_RESOLUTION_STARTED: "Starting file resolution",
    DOCUMENT_UPDATE_STARTED: "Starting document update",
  },
};


