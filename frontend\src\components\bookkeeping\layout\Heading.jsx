"use client";

import { memo } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import { BOOKCLOSURE_CONSTANTS } from "@/utils/constants/bookclosure";

const Heading = memo(function Heading({ onBack }) {
  return (
    <div className="bookkeeping-heading">
      <div>
        <h1 className="bookkeeping-heading-title">
          {BOOKCLOSURE_CONSTANTS.PAGE_TITLE}
        </h1>
        <p className="bookkeeping-heading-subtitle">
          {BOOKCLOSURE_CONSTANTS.PAGE_SUBTITLE}
        </p>
      </div>
      <Button
        variant="outline"
        onClick={onBack}
        className="bookkeeping-heading-back-btn"
      >
        <ArrowLeft className="w-4 h-4" />
        {BOOKCLOSURE_CONSTANTS.BACK_BUTTON_TEXT}
      </Button>
    </div>
  );
});

Heading.displayName = "Heading";

export default Heading;
