// app/utils/env.util.js
/**
 * Environment variable cache utility
 * Prevents redundant process.env reads
 * REFACTORED: No fallback operators - assumes env vars are validated externally
 */
import { envCache } from "./cache.util.js";
import { ERROR_MESSAGES } from "./constants/error.constants.js";

// Cache keys
const ENV_KEYS = {
  FILE_SERVICE_URL: 'FILE_SERVICE_URL',
  SYSTEM_API_KEY: 'SYSTEM_API_KEY',
  CHAT_SERVICE_API_URL: 'CHAT_SERVICE_API_URL',
  AUTH_SERVICE_BASE_URL: 'AUTH_SERVICE_BASE_URL',
};

/**
 * Get cached environment variable with fallback support
 * @param {string} key - Environment variable key
 * @param {string} fallback - Fallback value if env var is not set
 * @returns {string} Environment variable value or fallback
 */
export const getCachedEnv = (key, fallback = null) => {
  try {
    // Check cache first
    const cached = envCache.get(key);
    if (cached !== null) {
      return cached;
    }

    // Get from process.env directly
    const value = process.env[key];

    // Use fallback if value is undefined, null, or empty string
    const finalValue = (value !== undefined && value !== null && value !== '') ? value : fallback;

    // If no value and no fallback provided, throw error
    if (finalValue === null) {
      throw new Error(
        `${ERROR_MESSAGES.ENVIRONMENT_VARIABLE_MISSING}: ${key}`
      );
    }

    // Cache the value (1 hour TTL)
    envCache.set(key, finalValue, 3600000);

    return finalValue;
  } catch (error) {
    // Re-throw if it's our validation error, otherwise handle cache error
    if (error.message.includes('not configured')) {
      throw error;
    }

    // Fallback to process.env if cache fails (system-level error)
    const value = process.env[key];
    if (value !== undefined && value !== null && value !== '') {
      return value;
    }

    // If still no value, throw error
    throw new Error(
      `${ERROR_MESSAGES.ENVIRONMENT_VARIABLE_MISSING}: ${key}`
    );
  }
};

/**
 * Get FILE_SERVICE_URL with caching
 * @returns {string} FILE_SERVICE_URL from environment
 */
export const getFileServiceUrl = () => {
  return getCachedEnv(ENV_KEYS.FILE_SERVICE_URL);
};

/**
 * Get SYSTEM_API_KEY with caching
 * @returns {string} SYSTEM_API_KEY from environment
 */
export const getSystemApiKey = () => {
  return getCachedEnv(ENV_KEYS.SYSTEM_API_KEY);
};

/**
 * Get CHAT_SERVICE_API_URL with caching
 * @returns {string} CHAT_SERVICE_API_URL from environment
 */
export const getChatServiceApiUrl = () => {
  return getCachedEnv(ENV_KEYS.CHAT_SERVICE_API_URL, "http://localhost:3007/api");
};

/**
 * Get AUTH_SERVICE_BASE_URL with caching
 * @returns {string} AUTH_SERVICE_BASE_URL from environment
 */
export const getAuthServiceBaseUrl = () => {
  return getCachedEnv(ENV_KEYS.AUTH_SERVICE_BASE_URL, "http://localhost:3001");
};

