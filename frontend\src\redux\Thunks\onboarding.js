import { createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";
import tokenStorage from "@/lib/tokenStorage";

const FILE_SERVICE_URL = process.env.NEXT_PUBLIC_FILE_SERVICE_URL;
const SYSTEM_API_KEY = process.env.NEXT_PUBLIC_SYSTEM_API_KEY;

const getFileServiceClient = () => {
  if (!FILE_SERVICE_URL) {
    throw new Error(
      "NEXT_PUBLIC_FILE_SERVICE_URL environment variable is not configured"
    );
  }

  if (!SYSTEM_API_KEY) {
    throw new Error(
      "NEXT_PUBLIC_SYSTEM_API_KEY environment variable is not configured"
    );
  }

  // Normalize base URL - remove trailing slash if present
  const baseURL = FILE_SERVICE_URL.replace(/\/+$/, "");

  const instance = axios.create({
    baseURL: baseURL,
    headers: {
      "Content-Type": "multipart/form-data",
    },
    withCredentials: true,
    timeout: 120000, // 2 minutes timeout for file uploads
  });

  instance.interceptors.request.use(
    (config) => {
      config.headers["x-api-key"] = SYSTEM_API_KEY;

      const accessToken = tokenStorage.getAccessToken();
      if (accessToken && !tokenStorage.isTokenExpired(accessToken)) {
        config.headers.Authorization = `Bearer ${accessToken}`;
      }
      return config;
    },
    (error) => Promise.reject(error)
  );

  return instance;
};

/**
 * Upload organization PDF to file-service onboarding endpoint
 * @param {Object} payload - Upload parameters
 * @param {string} payload.orgId - Organization ID (required)
 * @param {string} payload.orgName - Organization name (optional, will be fetched if not provided)
 * @param {string} payload.service - Service type: "Finance", "Operations", or "Payroll" (required)
 * @param {number} payload.year - Year (e.g., 2025) (required)
 * @param {number} payload.month - Month number 1-12 (required)
 * @param {File} payload.file - PDF file to upload (required)
 */
export const uploadOnboardingPdf = createAsyncThunk(
  "onboarding/uploadPdf",
  async (
    { orgId, orgName, service, year, month, file },
    { rejectWithValue }
  ) => {
    try {
      // Validate required fields
      if (!orgId) {
        throw new Error("Organization ID is required");
      }
      if (!service) {
        throw new Error("Service is required");
      }
      if (!year || year < 2000 || year > 3000) {
        throw new Error("Valid year (2000-3000) is required");
      }
      if (!month || month < 1 || month > 12) {
        throw new Error("Valid month (1-12) is required");
      }
      if (!file) {
        throw new Error("PDF file is required");
      }

      // Validate file type
      if (file.type !== "application/pdf" && !file.name.toLowerCase().endsWith(".pdf")) {
        throw new Error("Only PDF files are allowed");
      }

      // Create FormData
      const formData = new FormData();
      formData.append("pdf", file);
      formData.append("orgId", orgId);
      formData.append("service", service);
      formData.append("year", year.toString());
      formData.append("month", month.toString());

      // Add orgName if provided
      if (orgName) {
        formData.append("orgName", orgName);
      }

      // Get axios client and upload
      const apiClient = getFileServiceClient();
      const response = await apiClient.post("/onboarding/pdf", formData);

      return {
        success: true,
        data: response.data,
        blobPath: response.data?.data?.blobPath,
        blobUrl: response.data?.data?.blobUrl,
      };
    } catch (error) {
      const errorMessage =
        error.response?.data?.message ||
        error.response?.data?.error ||
        error.message ||
        "Failed to upload PDF";
      return rejectWithValue(errorMessage);
    }
  }
);

