/* Reports Filters Container */
.reports-filters-container {
  display: flex;
  flex-direction: column;
  gap: 1.75rem;
}

/* Filters Grid */
.reports-filters-grid {
  display: grid;
  grid-template-columns: repeat(4, minmax(220px, 1fr));
  gap: 1.25rem;
}

@media (max-width: 768px) {
  .reports-filters-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 1100px) {
  .reports-filters-grid {
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  }
}

.reports-button-inline {
  display: grid;
  grid-template-columns: repeat(4, minmax(220px, 1fr));
  gap: 1.25rem;
  align-items: end;
}

.reports-button-inline .reports-button-spacer {
  grid-column: 1 / 4;
}

@media (max-width: 1100px) {
  .reports-button-inline {
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  }

  .reports-button-inline .reports-button-spacer {
    display: none;
  }
}

@media (max-width: 768px) {
  .reports-button-inline {
    grid-template-columns: 1fr;
  }
}

/* Filter Group */
.reports-filter-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.reports-filter-label {
  font-size: 0.875rem;
  font-weight: 600;
  color: #374151;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Services Selector */
.reports-services-selector {
  position: relative;
}

.reports-services-button {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 0.75rem;
  padding: 0.65rem 0.95rem;
  border-radius: 0.6rem;
  border: 1px solid #e2e8f0;
  background-color: #ffffff;
  color: #374151;
  font-size: 0.94rem;
  font-weight: 500;
  box-shadow: 0 1px 3px rgba(15, 23, 42, 0.08);
  cursor: pointer;
  transition: all 0.15s ease;
}

.reports-services-button:hover:not(:disabled) {
  box-shadow: 0 2px 8px rgba(15, 23, 42, 0.12);
  background-color: #f8fafc;
}

.reports-services-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.reports-services-button-content {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  flex: 1;
}

.reports-services-arrow {
  color: #9ca3af;
  flex-shrink: 0;
  transition: transform 0.2s ease;
}

.reports-services-selector.open .reports-services-arrow {
  transform: rotate(180deg);
}

.reports-services-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  margin-top: 0.5rem;
  background-color: white;
  border-radius: 0.6rem;
  border: 1px solid #e2e8f0;
  box-shadow: 0 8px 20px rgba(15, 23, 42, 0.14);
  z-index: 10;
  overflow: hidden;
  animation: slideDown 0.2s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.reports-service-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1rem;
  cursor: pointer;
  transition: background-color 0.15s ease;
  border-bottom: 1px solid #f1f5f9;
}

.reports-service-item:last-child {
  border-bottom: none;
}

.reports-service-item:hover {
  background-color: #f3f4f6;
}

.reports-service-checkbox {
  width: 1.125rem;
  height: 1.125rem;
  cursor: pointer;
  accent-color: #5b5bd6;
}

.reports-service-item span {
  font-size: 0.95rem;
  color: #374151;
  font-weight: 500;
}

/* Date Input */
.reports-date-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.reports-date-icon {
  position: absolute;
  left: 0.75rem;
  color: #9ca3af;
  pointer-events: none;
}

.reports-date-input {
  width: 100%;
  padding: 0.75rem 1rem 0.75rem 2.5rem;
  border-radius: 0.75rem;
  border: none;
  background-color: white;
  color: #374151;
  font-size: 0.95rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.reports-date-input:hover:not(:disabled) {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.reports-date-input:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(108, 99, 255, 0.1), 0 0 0 1px #6c63ff;
}

.reports-date-input:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background-color: #f9fafb;
}

/* Button Group */
.reports-button-group {
  display: flex;
  gap: 1rem;
  justify-content: flex-start;
}

.reports-generate-button {
  padding: 0.7rem 1.75rem;
  background-color: #5b5bd6;
  color: white;
  font-weight: 600;
  border-radius: 0.6rem;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 8px rgba(91, 91, 214, 0.25);
}

.reports-generate-button:hover:not(:disabled) {
  background-color: #4f4fc7;
  box-shadow: 0 4px 14px rgba(91, 91, 214, 0.3);
}

.reports-generate-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}
