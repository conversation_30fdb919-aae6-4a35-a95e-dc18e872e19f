"use client";

import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import AddForm from "@/components/common/AddForm";
import { userFields } from "@/utils/data/users";
import { USER_CONSTANTS } from "@/utils/constants/user";
import { fetchOrganizations } from "@/redux/Thunks/organization.js";
import { createUser } from "@/redux/Thunks/userThunks.js";
import Loader from "@/components/common/Loader";
import { ROLE_CONSTANTS } from "@/utils/constants";
import { useToast } from "@/components/ui/toast";
import { extractApiErrorMessage } from "@/utils/errorHandler";

export default function AddUserPage() {
  const router = useRouter();
  const dispatch = useDispatch();
  const { addToast } = useToast();
  const { organizations, loading: orgLoading } = useSelector(
    (state) => state.organizations
  );
  const [fields, setFields] = useState(userFields);
  const [isAdding, setIsAdding] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [initialValues, setInitialValues] = useState({
    organization_id: "",
    email: "",
    password: "",
    full_name: "",
    phone_number: "",
  });

  // Fetch organizations on component mount
  useEffect(() => {
    dispatch(fetchOrganizations());
  }, [dispatch]);

  // Page loader overlay - shows when navigating to the page
  useEffect(() => {
    // Simulate initial page load
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 500); // 0.5 second loading state

    return () => clearTimeout(timer);
  }, []);

  // Update fields with organization options when organizations are loaded
  useEffect(() => {
    if (organizations && organizations.length > 0) {
      const updatedFields = userFields.map((field) => {
        if (field.name === "organization_id") {
          return {
            ...field,
            options: organizations.map((org) => ({
              label: org.name || org.organization_name,
              value: org.id,
            })),
          };
        }
        return field;
      });
      setFields(updatedFields);
    }
  }, [organizations]);

  const handleSubmit = async (values, form) => {
    // Prepare the data for the API request
    const userData = {
      organization_id: values.organization_id,
      email: values.email,
      password: values.password,
      full_name: values.full_name,
      phone_number: values.phone_number,
      role: ROLE_CONSTANTS.ROLE_TYPES.USER,
    };

    setIsAdding(true);
    try {
      const resultAction = await dispatch(createUser(userData));
      if (createUser.fulfilled.match(resultAction)) {
        addToast("Successfully added user", "success");
        router.push("/masters/user");
      } else {
        // Extract error message from the error response
        const errorMessage = extractApiErrorMessage(resultAction.payload);
        addToast(errorMessage, "error");
      }
    } catch (error) {
      addToast(extractApiErrorMessage(error), "error");
    } finally {
      setIsAdding(false);
    }
  };

  // Page loader overlay - using consistent Loader component
  if (isLoading || orgLoading) {
    return <Loader message="Loading form..." show={true} />;
  }

  return (
    <AddForm
      heading={USER_CONSTANTS.ADD_PAGE.HEADING}
      subTitle={USER_CONSTANTS.ADD_PAGE.SUBTITLE}
      onBack={() => router.push("/masters/user")}
      backLabel={USER_CONSTANTS.ADD_PAGE.BACK_LABEL}
      title={USER_CONSTANTS.ADD_PAGE.TITLE}
      fields={fields}
      initialValues={initialValues}
      onSubmit={handleSubmit}
      submitLabel={USER_CONSTANTS.ADD_PAGE.SUBMIT_LABEL}
      isSubmitting={isAdding}
    />
  );
}
