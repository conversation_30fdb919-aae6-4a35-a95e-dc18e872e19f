"use strict";

import {
  initializeSendGrid,
  getSendGridMail,
} from "../config/sendgrid.config.js";
import { EMAIL_MESSAGES } from "./constants.util.js";

/**
 * Send an email using SendGrid
 * @param {Object} emailParams - Email parameters
 * @param {string} emailParams.to - Recipient email address
 * @param {string} emailParams.from - Sender email address
 * @param {string} [emailParams.templateId] - SendGrid template ID (for template emails)
 * @param {Object} [emailParams.data] - Template data (required if using templateId)
 * @param {string} [emailParams.subject] - Email subject (required if not using template)
 * @param {string} [emailParams.text] - Plain text content (required if not using template)
 * @param {string} [emailParams.html] - HTML content (required if not using template)
 * @returns {Promise<Object>} SendGrid response
 * @throws {Error} If email sending fails
 */
export const sendEmail = async (emailParams) => {
  const { to, from, templateId, data, subject, text, html } = emailParams;

  // Basic validation
  if (!to) {
    throw new Error(EMAIL_MESSAGES.RECIPIENT_EMAIL_REQUIRED);
  }
  if (!from) {
    throw new Error(EMAIL_MESSAGES.SENDER_EMAIL_REQUIRED);
  }

  // Initialize SendGrid
  initializeSendGrid();
  const sgMail = getSendGridMail();

  // Build message
  const message = { to, from };

  if (templateId) {
    // Template-based email
    if (!data || typeof data !== "object") {
      throw new Error(EMAIL_MESSAGES.TEMPLATE_DATA_REQUIRED);
    }
    message.templateId = templateId;
    message.dynamicTemplateData = {
      ...data,
      ...(subject ? { subject } : {}),
    };
    if (subject) {
      message.subject = subject;
    }
  } else {
    // Traditional email
    if (!subject) {
      throw new Error(EMAIL_MESSAGES.SUBJECT_REQUIRED);
    }
    if (!text && !html) {
      throw new Error(EMAIL_MESSAGES.CONTENT_REQUIRED);
    }
    message.subject = subject;
    if (text) message.text = text;
    if (html) message.html = html;
  }

  // Send email
  try {
    return await sgMail.send(message);
  } catch (error) {
    // Format and rethrow error
    if (error.response?.body?.errors) {
      const errorMessages = error.response.body.errors
        .map((err) => err.message)
        .join("; ");
      throw new Error(
        `SendGrid API Error: ${error.response.statusCode} - ${errorMessages}`
      );
    }
    throw error;
  }
};

export default { sendEmail };
