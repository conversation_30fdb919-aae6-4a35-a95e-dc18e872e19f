"use client";

import { useState, useEffect, Suspense, useRef } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import PageWrapper from "@/components/common/PageWrapper";
import DeleteModal from "@/components/common/DeleteModal";
import { ORGANIZATION_CONSTANTS } from "@/utils/constants/organization";
import { useDispatch, useSelector } from "react-redux";
import { fetchOrganizations } from "@/redux/Thunks/organization.js";
import { addQuickbooksAccount } from "@/redux/Thunks/quickbooks.js";
import { clearError as clearQuickbooksError } from "@/redux/Slice/QuickbookAccount";
import PageLoader from "@/components/common/PageLoader";
import CustomSpinner from "@/components/common/CustomSpinner";
import { stats } from "@/utils/data/configuration";
import { useToast } from "@/components/ui/toast";
import {
  extractApiErrorMessage,
  extractErrorMessage,
} from "@/utils/errorHandler";
import { BOOKCLOSURE_CONSTANTS } from "@/utils/const";

function OrganizationPageContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [deleteModal, setDeleteModal] = useState({ isOpen: false, item: null });
  const [isDeleting, setIsDeleting] = useState(false);
  const [typeFilter, setTypeFilter] = useState("All");
  const dispatch = useDispatch();
  const { addToast } = useToast();

  // Track if we're currently processing a QuickBooks callback to prevent duplicate API calls
  const isProcessingRef = useRef(false);
  // Track which code we've already processed to prevent duplicate calls
  const processedCodeRef = useRef(null);

  // Check for QuickBooks callback status
  useEffect(() => {
    const quickbooksStatus = searchParams.get("quickbooks");

    if (quickbooksStatus === "success") {
      alert("QuickBooks connected successfully!");
      // Clean up URL parameters
      router.replace("/masters/org");
    } else if (quickbooksStatus === "error") {
      alert("Failed to connect QuickBooks. Please try again.");
      // Clean up URL parameters
      router.replace("/masters/org");
    }
  }, [searchParams, router]);

  // Handle QuickBooks OAuth callback with code and realmId
  useEffect(() => {
    const code = searchParams.get("code");
    const realmId = searchParams.get("realmId");

    // Prevent duplicate API calls - check if this specific code was already processed
    if (
      code &&
      realmId &&
      !isProcessingRef.current &&
      processedCodeRef.current !== code
    ) {
      isProcessingRef.current = true;
      processedCodeRef.current = code;
      const storedOrgData = localStorage.getItem("quickbooks_org_data");
      let orgData = {
        schemaName: "",
        email: "",
        organization_id: 1,
      };

      if (storedOrgData) {
        try {
          const parsedData = JSON.parse(storedOrgData);
          orgData = {
            schemaName:
              parsedData.schemaName ||
              parsedData.name?.toLowerCase().replace(/\s+/g, "_"),
            email: parsedData.email,
            organization_id: parsedData.id,
          };

          localStorage.removeItem("quickbooks_org_data");
        } catch (parseError) {
          // Error parsing stored organization data
        }
      }

      // Clean up URL parameters immediately to prevent re-triggering the effect
      router.replace("/masters/org");

      // Dispatch the Redux action to add QuickBooks account
      dispatch(
        addQuickbooksAccount({
          code,
          realmId,
          ...orgData,
        })
      )
        .then((result) => {
          if (result.type.endsWith("/fulfilled")) {
            addToast("QuickBooks account connected successfully!", "success");
            dispatch(fetchOrganizations());
          } else {
            // Extract error message from the error response
            const errorMessage = extractApiErrorMessage(result.payload);
            addToast(errorMessage, "error");
          }
        })
        .catch((error) => {
          const errorMessage =
            extractErrorMessage(error) ||
            "Failed to connect QuickBooks account. Please try again.";
          addToast(errorMessage, "error");
        })
        .finally(() => {
          // Reset the processing flag after completion
          isProcessingRef.current = false;
          // Clear the QuickBooks error from Redux state to prevent blank page
          dispatch(clearQuickbooksError());
        });
    } else if (!code && processedCodeRef.current) {
      // Clear the processed code ref when URL no longer has code parameter
      processedCodeRef.current = null;
    }
  }, [searchParams, dispatch, router, addToast]);

  const CLIENT_ID = process.env.NEXT_PUBLIC_QUICKBOOKS_CLIENT_ID;
  const REDIRECT_URI = process.env.NEXT_PUBLIC_QUICKBOOKS_REDIRECT_URI;

  const STATE = process.env.NEXT_PUBLIC_QUICKBOOKS_STATE;
  const AUTH_URL = `https://appcenter.intuit.com/connect/oauth2?client_id=${CLIENT_ID}&redirect_uri=${REDIRECT_URI}&response_type=code&scope=com.intuit.quickbooks.accounting&state=${STATE}`;

  // Redux: fetch organizations
  const { organizations, loading, error } = useSelector(
    (state) => state.organizations
  );
  const { loading: quickbooksLoading } = useSelector(
    (state) => state.quickbooksAccount
  );

  useEffect(() => {
    dispatch(fetchOrganizations());
  }, [dispatch]);

  // Action handlers
  const handleView = (item) => {
    router.push(`/masters/org/view/${item.id}`);
  };

  const handleEdit = (item) => {
    router.push(`/masters/org/edit/${item.id}`);
  };

  const handleDelete = (item) => {
    setDeleteModal({ isOpen: true, item });
  };

  const confirmDelete = async () => {
    setIsDeleting(true);
    try {
      await new Promise((resolve) => setTimeout(resolve, 1000));
      setDeleteModal({ isOpen: false, item: null });
    } catch (deleteError) {
      // Error deleting organization
    } finally {
      setIsDeleting(false);
    }
  };

  // Table columns configuration
  const columns = [
    {
      key: "name",
      title: "Organization Name",
      width: "20%",
    },
    {
      key: "email",
      title: "Email",
      width: "17%",
      render: (value) =>
        value ? (
          <span
            className="block max-w-[180px] truncate text-gray-900 whitespace-nowrap"
            title={value}
          >
            {value}
          </span>
        ) : (
          <span className="text-gray-400">-</span>
        ),
    },
    {
      key: "phone",
      title: "Phone",
      width: "13%",
    },
    {
      key: "website",
      title: "Website",
      width: "15%",
      render: (value) =>
        value ? (
          <a
            href={value.startsWith("http") ? value : `https://${value}`}
            target="_blank"
            rel="noopener noreferrer"
            className="block max-w-[180px] truncate text-blue-600 hover:underline whitespace-nowrap"
            title={value}
          >
            {value}
          </a>
        ) : (
          <span className="text-gray-400">-</span>
        ),
    },
    {
      key: "connect",
      title: "Quickbooks",
      width: "10%",
      render: (_, item) => {
        const isConnected = item.is_qb_connected === true;
        const isQuickbooksSelected = item.services.includes(BOOKCLOSURE_CONSTANTS.SERVICES_VALUES.FINANCIAL);
        return (
          <button
            onClick={() => {
              const orgData = {
                id: item.id,
                name: item.name,
                email: item.email,
                schemaName:
                  item.schema_name ||
                  item.name?.toLowerCase().replace(/\s+/g, "_") ||
                  `org_${item.id}`,
              };

              localStorage.setItem(
                "quickbooks_org_data",
                JSON.stringify(orgData)
              );
              window.location.href = AUTH_URL;
            }}
            disabled={!isQuickbooksSelected || isConnected}
            className={`${
              isConnected || !isQuickbooksSelected
                ? "text-gray-400 cursor-not-allowed"
                : "text-blue-600 hover:underline"
            } whitespace-nowrap`}
            title={
              isConnected
                ? "QuickBooks already connected"
                : "Connect QuickBooks"
            }
          >
            {isConnected
              ? "Connected"
              : isQuickbooksSelected
                ? "Connect"
                : "Not subscribed"}
          </button>
        );
      },
    },
  ];

  // Filters configuration
  const filters = [
    {
      key: "type",
      value: typeFilter,
      onChange: setTypeFilter,
      options: Object.values(ORGANIZATION_CONSTANTS.TYPES),
    },
  ];

  let content;
  if (loading || quickbooksLoading) {
    content = (
      <PageLoader
        message={
          quickbooksLoading
            ? "Connecting QuickBooks..."
            : "Loading organizations..."
        }
      />
    );
  } else if (error) {
    content = (
      <div className="p-6 text-red-500 font-bold text-center">
        {typeof error === "string" ? error : "Failed to load organizations."}
      </div>
    );
  } else {
    content = (
      <PageWrapper
        title={ORGANIZATION_CONSTANTS.PAGE_TITLE}
        subtitle={ORGANIZATION_CONSTANTS.PAGE_SUBTITLE}
        searchPlaceholder={ORGANIZATION_CONSTANTS.SEARCH_PLACEHOLDER}
        addButtonText={ORGANIZATION_CONSTANTS.ADD_BUTTON_TEXT}
        addButtonPath={ORGANIZATION_CONSTANTS.ADD_BUTTON_PATH}
        stats={stats}
        data={organizations}
        columns={columns}
        filters={filters}
        itemsPerPage={10}
      />
    );
  }

  return (
    <>
      {content}
      <DeleteModal
        isOpen={deleteModal.isOpen}
        onClose={() => setDeleteModal({ isOpen: false, item: null })}
        onConfirm={confirmDelete}
        title={ORGANIZATION_CONSTANTS.DELETE_MODAL.TITLE}
        description={ORGANIZATION_CONSTANTS.DELETE_MODAL.DESCRIPTION}
        itemName={deleteModal.item?.name}
        isLoading={isDeleting}
      />
    </>
  );
}

export default function OrganizationPage() {
  return (
    <Suspense fallback={<CustomSpinner />}>
      <OrganizationPageContent />
    </Suspense>
  );
}
