import FinanceRepository from "../repository/finance.repository.js";
import { LOGGER_NAMES } from "../utils/constants/log.constants.js";
import { createLogger } from "../utils/logger.utils.js";

const logger = createLogger(LOGGER_NAMES.REPORT_SERVICE);

const getFinanceReport = async (filters) => {
  try {
    logger.info("Fetching finance report with filters:", filters);
    // Business logic here
    const data = await FinanceRepository.getFinanceData(filters);
    return data;
  } catch (error) {
    logger.error("Error in FinanceService.getFinanceReport:", error);
    throw error;
  }
};

export default {
  getFinanceReport,
};
