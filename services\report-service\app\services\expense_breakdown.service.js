import ExpenseBreakdownRepository from "../repository/expense_breakdown.repository.js";
import { LOGGER_NAMES } from "../utils/constants/log.constants.js";
import { createLogger } from "../utils/logger.utils.js";
import { isValid<PERSON>onth, isValidYear, parseNumericValue, formatToKWithDecimals } from "../utils/format.utils.js";

const logger = createLogger(LOGGER_NAMES.REPORT_SERVICE);

/**
 * Group expense line items by category for pie chart
 * @param {Array} lineItems - Expense line items
 * @returns {Array} Grouped expenses by category with formatted amounts
 */
const groupExpensesByCategory = (lineItems) => {
  const categoryMap = new Map();

  lineItems.forEach((item) => {
    const path = item.path || "";
    const amount = parseNumericValue(item.amount);

    // Skip zero or negative amounts
    if (amount <= 0) return;

    // Extract the main expense category from the path
    // Path format: "Expenses > Category Name" or "Expenses > Category > Sub-category"
    let category = "Other Expenses";
    
    if (path.toLowerCase().includes("expense")) {
      const parts = path.split(">");
      if (parts.length >= 2) {
        // Get the second part as the category (first part is "Expenses")
        category = parts[1].trim();
      } else if (parts.length === 1 && parts[0].trim().toLowerCase() !== "expenses") {
        // If there's only one part and it's not just "Expenses", use it as category
        category = parts[0].trim();
      }
    }

    // Aggregate amounts by category
    if (categoryMap.has(category)) {
      categoryMap.set(category, categoryMap.get(category) + amount);
    } else {
      categoryMap.set(category, amount);
    }
  });

  // Convert map to array and format for pie chart
  const result = Array.from(categoryMap.entries()).map(([category, amount]) => {
    const formattedAmount = parseFloat(amount.toFixed(2));
    return {
      category,
      amount: formatToKWithDecimals(formattedAmount),
      value: formattedAmount, // Include raw value for pie chart calculations
    };
  });

  // Sort by value in descending order
  result.sort((a, b) => b.value - a.value);

  // Remove the raw value from response (keep only category and formatted amount)
  return result.map(({ category, amount }) => ({ category, amount }));
};

/**
 * Get expense breakdown data for organization
 * @param {Object} params - Query parameters
 * @param {string} params.organization_id - Organization ID
 * @param {number} params.month - Month (1-12)
 * @param {number} params.year - Year
 * @returns {Promise<Object>} Expense breakdown data formatted for pie chart
 */
const getExpenseBreakdown = async ({ organization_id, month, year }) => {
  try {
    logger.info(
      `Fetching expense breakdown for org: ${organization_id}, month: ${month}, year: ${year}`
    );

    // Validate parameters
    if (!organization_id) {
      throw new Error("Organization ID is required");
    }

    if (!month || !isValidMonth(month)) {
      throw new Error("Valid month (1-12) is required");
    }

    if (!year || !isValidYear(year)) {
      throw new Error("Valid year is required");
    }

    // Convert to numbers
    const monthNum = parseInt(month, 10);
    const yearNum = parseInt(year, 10);

    // Get organization schema name
    const schemaName = await ExpenseBreakdownRepository.getOrganizationSchemaName(
      organization_id
    );

    if (!schemaName) {
      logger.warn(`Schema not found for organization: ${organization_id}`);
      throw new Error("Organization not found or schema not configured");
    }

    // Get report ID for the month
    const reportId = await ExpenseBreakdownRepository.getPnlReportId(
      schemaName,
      monthNum,
      yearNum
    );

    if (!reportId) {
      logger.info(`No P&L report found for month: ${month}, year: ${year}`);
      // Return empty breakdown if no report found
      return {
        organization_id,
        month: monthNum,
        year: yearNum,
        breakdown: [],
      };
    }

    // Get expense line items
    const lineItems = await ExpenseBreakdownRepository.getExpenseLineItems(
      schemaName,
      reportId
    );

    if (!lineItems || lineItems.length === 0) {
      logger.info(`No expense line items found for month: ${month}, year: ${year}`);
      return {
        organization_id,
        month: monthNum,
        year: yearNum,
        breakdown: [],
      };
    }

    // Group expenses by category for pie chart
    const breakdown = groupExpensesByCategory(lineItems);
    logger.info(`Grouped expenses into ${breakdown.length} categories`);

    return {
      organization_id,
      month: monthNum,
      year: yearNum,
      breakdown,
    };
  } catch (error) {
    logger.error("Error in ExpenseBreakdownService.getExpenseBreakdown:", error);
    throw error;
  }
};

export default {
  getExpenseBreakdown,
};
