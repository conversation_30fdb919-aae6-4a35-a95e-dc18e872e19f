import { BalanceSheetReport, BalanceSheetLineItem } from "../models/index.js";
import logger from "../../config/logger.config.js";
import { createInSchema, createBulkInSchema } from "./database.utils.js";

/**
 * Load account map from database
 * @returns {Promise<Map>} Map of account_id to account_name
 */
export async function loadAccountMap() {
  try {
    const map = new Map();
    logger.info("Account map loaded", { count: map.size });
    return map;
  } catch (error) {
    logger.error("Failed to load account map", { error: error.message });
    return new Map();
  }
}

/**
 * Flatten balance sheet data from QuickBooks API response
 * @param {Object} balanceSheet - QuickBooks balance sheet response
 * @returns {Object} Flattened data with flat array and summaries
 */
export function flattenBalanceSheet(balanceSheet) {
  const flat = [];
  const summaries = [];

  function traverse(
    rows,
    pathPrefix = "",
    section = "",
    subsection = "",
    accountType = ""
  ) {
    if (!rows || !Array.isArray(rows)) return;

    for (const row of rows) {
      if (row.Header && row.Rows) {
        const headerValue = row.Header.ColData?.[0]?.value || "";
        const newPath = pathPrefix ? `${pathPrefix} > ${headerValue}` : headerValue;

        // Determine section/subsection/account type based on header value
        const isMainSection = headerValue === "ASSETS" || headerValue === "LIABILITIES AND EQUITY";
        const isSubSection = headerValue === "Liabilities" || headerValue === "Equity";
        const isSubSubSection = headerValue.includes("Assets") || headerValue.includes("Liabilities");

        const newSection = isMainSection || isSubSection ? headerValue : section;
        const newSubsection = isSubSubSection ? headerValue : subsection;
        const newAccountType = !isMainSection && !isSubSection && !isSubSubSection ? headerValue : accountType;

        traverse(row.Rows.Row, newPath, newSection, newSubsection, newAccountType);

        // Add summary if present
        if (row.Summary?.ColData) {
          summaries.push({
            group: row.group || "",
            label: row.Summary.ColData[0]?.value || "",
            path: newPath,
            amount: parseFloat(row.Summary.ColData[1]?.value || "0"),
          });
        }
      } else if (row.ColData && row.type === "Data") {
        flat.push({
          path: pathPrefix,
          account_name: row.ColData[0]?.value || "",
          account_id: row.ColData[0]?.id || null,
          amount: parseFloat(row.ColData[1]?.value || "0"),
          section,
          subsection,
          account_type: accountType,
        });
      }
    }
  }

  const report = balanceSheet.QueryResponse?.Report || balanceSheet.QueryResponse || balanceSheet;
  if (report.Rows?.Row) traverse(report.Rows.Row);

  return { flat, summaries };
}

/**
 * Transform flattened data to PowerBI KPI format
 * @param {Array} flat - Flattened balance sheet data
 * @param {Map} accountMap - Account ID to name mapping
 * @returns {Object} PowerBI KPI JSON structure
 */
export function transformToPowerBIKPI(flat, accountMap) {
  const kpi = {
    report_type: "BalanceSheet",
    generated_at: new Date().toISOString(),
    sections: {
      assets: { current_assets: {}, fixed_assets: {}, other_assets: {}, total: 0 },
      liabilities: { current_liabilities: {}, long_term_liabilities: {}, total: 0 },
      equity: { total: 0, line_items: {} },
    },
    totals: { total_assets: 0, total_liabilities_and_equity: 0 },
  };

  for (const item of flat) {
    const section = item.section?.toLowerCase() || "";
    const subsection = item.subsection?.toLowerCase() || "";
    const accountName = accountMap.get(item.account_id) || item.account_name;

    if (section.includes("assets")) {
      const assetType = subsection.includes("current")
        ? "current_assets"
        : subsection.includes("fixed")
        ? "fixed_assets"
        : "other_assets";
      kpi.sections.assets[assetType][accountName] = item.amount;
      kpi.sections.assets.total += item.amount;
      kpi.totals.total_assets += item.amount;
    } else if (section.includes("liabilities")) {
      const liabilityType = subsection.includes("current") ? "current_liabilities" : "long_term_liabilities";
      kpi.sections.liabilities[liabilityType][accountName] = item.amount;
      kpi.sections.liabilities.total += item.amount;
      kpi.totals.total_liabilities_and_equity += item.amount;
    } else if (section.includes("equity")) {
      kpi.sections.equity.line_items[accountName] = item.amount;
      kpi.sections.equity.total += item.amount;
      kpi.totals.total_liabilities_and_equity += item.amount;
    }
  }

  return kpi;
}

/**
 * Insert or update report header into balance_sheet_reports table
 * @param {Object} balanceSheet - QuickBooks balance sheet response
 * @param {Object} powerbiKpiJson - PowerBI KPI JSON
 * @param {string} realmId - Realm ID
 * @param {string} schemaName - Schema name (optional)
 * @param {number} existingReportId - Existing report ID for update (optional)
 * @returns {Promise<number>} Report ID
 */
export async function insertReportHeader(
  balanceSheet,
  powerbiKpiJson,
  realmId,
  schemaName = null,
  existingReportId = null
) {
  try {
    const report = balanceSheet.QueryResponse?.Report || balanceSheet.QueryResponse || balanceSheet;
    const header = report.Header || {};

    const reportData = {
      report_name: header.ReportName || "BalanceSheet",
      report_basis: header.ReportBasis || "Cash",
      start_date: header.StartPeriod || null,
      end_date: header.EndPeriod || null,
      currency: header.Currency || "USD",
      accounting_standard:
        header.Option?.find((opt) => opt.Name === "AccountingStandard")?.Value || "GAAP",
      generated_at: header.Time ? new Date(header.Time) : new Date(),
      raw: JSON.stringify(balanceSheet),
      powerbi_kpi_json: JSON.stringify(powerbiKpiJson),
      realm_id: realmId,
    };

    let result;
    if (existingReportId) {
      if (schemaName) {
        const { updateInSchema } = await import("./database.utils.js");
        await updateInSchema(BalanceSheetReport, schemaName, existingReportId, reportData);
      } else {
        await BalanceSheetReport.update(reportData, { where: { id: existingReportId } });
      }
      result = { id: existingReportId };
      logger.info("Balance sheet report header updated", {
        reportId: existingReportId,
        reportName: reportData.report_name,
        startDate: reportData.start_date,
        endDate: reportData.end_date,
      });
    } else {
      result = schemaName
        ? await createInSchema(BalanceSheetReport, schemaName, reportData)
        : await BalanceSheetReport.create(reportData);
      logger.info("Balance sheet report header inserted", {
        reportId: result.id,
        reportName: reportData.report_name,
        startDate: reportData.start_date,
        endDate: reportData.end_date,
      });
    }

    return result.id;
  } catch (error) {
    logger.error("Failed to insert/update balance sheet report header", {
      error: error.message,
      stack: error.stack,
    });
    throw error;
  }
}

/**
 * Insert line items into balance_sheet_line_items table
 * @param {number} reportId - Report ID
 * @param {Array} flat - Flattened balance sheet data
 * @param {Map} accountMap - Account ID to name mapping
 * @param {string} realmId - Realm ID
 * @param {string} schemaName - Schema name (optional)
 * @returns {Promise<void>}
 */
export async function insertLineItems(reportId, flat, accountMap, realmId, schemaName = null) {
  if (!flat.length) {
    logger.warn("No line items to insert", { reportId });
    return;
  }

  try {
    const prepared = flat.map((row) => ({
      report_id: reportId,
      path: row.path,
      account_name: accountMap.get(row.account_id) || row.account_name,
      account_id: row.account_id,
      amount: row.amount,
      section: row.section,
      subsection: row.subsection,
      account_type: row.account_type,
      realm_id: realmId,
    }));

    const batchSize = 1000;
    const bulkInsert = schemaName
      ? (batch) => createBulkInSchema(BalanceSheetLineItem, schemaName, batch)
      : (batch) => BalanceSheetLineItem.bulkCreate(batch);

    for (let i = 0; i < prepared.length; i += batchSize) {
      await bulkInsert(prepared.slice(i, i + batchSize));
    }

    logger.info("Balance sheet line items inserted", { reportId, count: prepared.length });
  } catch (error) {
    logger.error("Failed to insert balance sheet line items", {
      error: error.message,
      reportId,
      count: flat.length,
    });
    throw error;
  }
}

/**
 * Process and save balance sheet data using new schema
 * @param {Object} balanceSheet - QuickBooks balance sheet response
 * @param {string} realmId - Realm ID
 * @param {string} schemaName - Schema name (optional)
 * @param {number} existingReportId - Existing report ID for update (optional)
 * @returns {Promise<Object>} Processing result
 */
export async function processBalanceSheetData(
  balanceSheet,
  realmId,
  schemaName = null,
  existingReportId = null
) {
  try {
    logger.info("Processing balance sheet data", { realmId, schemaName, existingReportId });

    const { flat, summaries } = flattenBalanceSheet(balanceSheet);
    logger.info("Balance sheet data flattened", {
      flatCount: flat.length,
      summariesCount: summaries.length,
    });

    const accountMap = await loadAccountMap();
    const powerbiKpiJson = transformToPowerBIKPI(flat, accountMap);
    logger.info("PowerBI KPI JSON generated");

    const reportId = await insertReportHeader(
      balanceSheet,
      powerbiKpiJson,
      realmId,
      schemaName,
      existingReportId
    );

    await insertLineItems(reportId, flat, accountMap, realmId, schemaName);

    // Calculate totals for validation
    const calculateTotal = (filterFn) =>
      flat
        .filter(filterFn)
        .reduce((sum, r) => sum + (Number.isFinite(r.amount) ? r.amount : 0), 0);

    const totalAssets = calculateTotal((r) => (r.section || "").toLowerCase().includes("assets"));
    const totalLiabilities = calculateTotal((r) =>
      (r.section || "").toLowerCase().includes("liabilities")
    );
    const totalEquity = calculateTotal((r) => (r.section || "").toLowerCase().includes("equity"));
    const totalLiabilitiesAndEquity = totalLiabilities + totalEquity;

    const result = {
      reportId,
      lineItemsCount: flat.length,
      summariesCount: summaries.length,
      totals: {
        totalAssets,
        totalLiabilities,
        totalEquity,
        totalLiabilitiesAndEquity,
        isBalanced: Math.abs(totalAssets - totalLiabilitiesAndEquity) < 0.01,
      },
    };

    logger.info("Balance sheet processing completed", result);
    return result;
  } catch (error) {
    logger.error("Failed to process balance sheet data", { error: error.message, realmId });
    throw error;
  }
}

/**
 * Get balance sheet reports by realm ID
 * @param {string} realmId - Realm ID
 * @param {Object} options - Query options
 * @returns {Promise<Array>} Balance sheet reports
 */
export async function getBalanceSheetReportsByRealmId(realmId, options = {}) {
  try {
    const { limit = 10, offset = 0, order = "created_at DESC" } = options;
    const [orderField, orderDirection] = order.split(" ");

    const reports = await BalanceSheetReport.findAll({
      where: { realm_id: realmId },
      include: [
        {
          model: BalanceSheetLineItem,
          as: "lineItems",
          required: false,
        },
      ],
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [[orderField, orderDirection || "ASC"]],
    });

    logger.info("Balance sheet reports retrieved", { realmId, count: reports.length });
    return reports;
  } catch (error) {
    logger.error("Failed to get balance sheet reports", { error: error.message, realmId });
    throw error;
  }
}
