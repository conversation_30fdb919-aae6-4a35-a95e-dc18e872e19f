import { httpPost, httpPostWithRefreshToken } from "./axios.util.js";

const MONTH_NAMES = [
  "January", "February", "March", "April", "May", "June",
  "July", "August", "September", "October", "November", "December"
];

const buildEndpoint = ({ url, baseUrl }) => {
  if (url?.trim()) return url.trim();
  if (!baseUrl?.trim()) {
    throw new Error("Chat service base URL or full URL is required");
  }
  return `${baseUrl.trim().replace(/\/+$/, "")}/chat/start`;
};

const cleanPayload = (payload) => {
  const result = {};
  for (const [key, value] of Object.entries(payload)) {
    if (value != null) result[key] = value;
  }
  return result;
};

const extractData = (response) => {
  const body = response?.data;
  return body?.data || body;
};

const buildBlobStoragePath = ({ orgId, orgName, service, year, month, filename }) => {
  if (!orgId || !orgName || !service || !year || !month || !filename) {
    return null;
  }
  
  // Map normalized service names to proper capitalized folder names
  const serviceFolderMap = {
    'financial': 'Finance',
    'finance': 'Finance',
    'operational': 'Operations',
    'operations': 'Operations',
    'payroll': 'Payroll',
  };
  
  // Get proper folder name, fallback to capitalizing first letter if not in map
  const normalizedService = service.toLowerCase();
  const serviceFolderName = serviceFolderMap[normalizedService] || 
    service.charAt(0).toUpperCase() + service.slice(1).toLowerCase();
  
  const monthName = MONTH_NAMES[month - 1] || month.toString();
  return `${orgId}/${orgName}/Power BI-Reports/${serviceFolderName}/${year}/${monthName}/${filename}`;
};

export const startChatSessionRequest = async (options = {}) => {
  const { filename, orgId, orgName, year, month, service, refreshToken, headers, config, client, useBlobPath = false } = options;
  const requestId = options.requestId || null;
  const logPrefix = requestId ? `[${requestId}]` : '';

  if (!filename?.trim()) {
    throw new Error("Filename is required to start chat session");
  }

  const endpoint = buildEndpoint(options);
  
  let payload;
  if (useBlobPath && orgId && orgName && service && year && month) {
    const blobStoragePath = buildBlobStoragePath({
      orgId,
      orgName,
      service,
      year,
      month,
      filename: filename.trim(),
    });
    
    if (blobStoragePath) {
      payload = {
        filename: filename.trim(),
        blobStoragePath,
      };
    } else {
      payload = cleanPayload({
        filename: filename.trim(),
        orgId,
        orgName,
        year,
        month,
        service,
      });
    }
  } else {
    payload = cleanPayload({
      filename: filename.trim(),
      orgId,
      orgName,
      year,
      month,
      service,
    });
  }

  const requestConfig = { ...config, ...(headers && { headers }) };

  const response = refreshToken
    ? await httpPostWithRefreshToken(
        endpoint,
        payload,
        refreshToken,
        requestConfig,
        client
      )
    : await httpPost(endpoint, payload, requestConfig, client);

  const body = response?.data;
  const data = extractData(response);

  return {
    data,
    body,
    message: body?.message ?? null,
    status: body?.status ?? response?.status ?? null,
    response,
  };
};

export default {
  startChatSessionRequest,
};
