"use client";

import React, { useState, useEffect, useMemo, memo, useCallback, useRef } from "react";
import PropTypes from "prop-types";
import CustomSpinner from "../../common/CustomSpinner";
import { DASHBOARD_CONSTANTS } from "@/utils/constants/dashboard";
import "react-pdf/dist/Page/AnnotationLayer.css";
import "react-pdf/dist/Page/TextLayer.css";
import "@/styles/PDFViewer.css";

let reactPdfModulePromise = null;

if (typeof globalThis.window !== "undefined" && !reactPdfModulePromise) {
  reactPdfModulePromise = import("react-pdf");
}

const calculateScale = (screenWidth) => {
  if (screenWidth < 768) return 0.8;
  if (screenWidth < 1024) return 1;
  return 1.2;
};

const ClientPDFViewer = memo(function ClientPDFViewer({ url, pageToView = 1, onLoadSuccess: onPdfLoadSuccess }) {
  const [numPages, setNumPages] = useState(null);
  const [pageNumber, setPageNumber] = useState(pageToView);
  const [error, setError] = useState(null);
  const [Document, setDocument] = useState(null);
  const [Page, setPage] = useState(null);
  const [scale, setScale] = useState(1);
  const [width, setWidth] = useState(800);
  const prevUrlRef = useRef(null);
  const isValidUrl = typeof url === "string" && url.trim().length > 0;

  useEffect(() => {
    if (typeof pageToView === "number" && !Number.isNaN(pageToView)) {
      setPageNumber(pageToView);
    }
  }, [pageToView]);

  useEffect(() => {
    const calculateDimensions = () => {
      if (typeof globalThis.window === "undefined") return;
      const screenWidth = globalThis.window.innerWidth;
      setScale(calculateScale(screenWidth));
      setWidth(Math.min(screenWidth * 0.8, 1000));
    };

    calculateDimensions();
    globalThis.window.addEventListener("resize", calculateDimensions);
    return () => globalThis.window.removeEventListener("resize", calculateDimensions);
  }, []);

  useEffect(() => {
    let cancelled = false;

    const hydrateReactPdf = async () => {
      try {
        if (!reactPdfModulePromise) {
          reactPdfModulePromise = import("react-pdf");
        }

        const mod = await reactPdfModulePromise;
        if (cancelled) return;

        const { Document: DocumentComponent, Page: PageComponent, pdfjs } = mod;
        if (!DocumentComponent || !PageComponent) {
          throw new Error(DASHBOARD_CONSTANTS.PDF_VIEWER_COMPONENTS_ERROR);
        }
        if (pdfjs?.GlobalWorkerOptions) {
          pdfjs.GlobalWorkerOptions.workerSrc = `//unpkg.com/pdfjs-dist@${pdfjs.version}/build/pdf.worker.min.mjs`;
        }

        setDocument(() => DocumentComponent);
        setPage(() => PageComponent);
      } catch (err) {
        if (cancelled) return;
        const fallbackMessage = err instanceof Error && err.message ? err.message : DASHBOARD_CONSTANTS.PDF_VIEWER_LOAD_ERROR;
        setError(fallbackMessage);
      }
    };

    if (!Document || !Page) {
      setError(null);
      hydrateReactPdf();
    }

    return () => {
      cancelled = true;
    };
  }, [Document, Page]);

  useEffect(() => {
    if (!isValidUrl) {
      prevUrlRef.current = null;
      return;
    }

    if (url !== prevUrlRef.current) {
      prevUrlRef.current = url;
      setError(null);
      setPageNumber(pageToView);
    }
  }, [isValidUrl, pageToView, url]);

  const proxiedUrl = useMemo(() => {
    if (!isValidUrl) return null;
    try {
      if (typeof globalThis.window !== "undefined" && url.startsWith("http")) {
        const target = new URL(url);
        const isSameOrigin = target.origin === globalThis.window.location.origin;
        return isSameOrigin ? url : `/api/blob-proxy?url=${encodeURIComponent(url)}`;
      }
      return url;
    } catch {
      return url;
    }
  }, [isValidUrl, url]);

  const onDocumentLoadSuccess = useCallback(
    ({ numPages: totalPages }) => {
      setNumPages(totalPages);
      setError(null);
      onPdfLoadSuccess?.();
    },
    [onPdfLoadSuccess]
  );

  const onDocumentLoadError = useCallback((err) => {
    const errorMessage = err?.message?.includes("Failed to fetch")
      ? "Failed to fetch PDF. Please check if the file exists and try again."
      : "Failed to load PDF document. The file may be corrupted or unavailable.";
    setError(errorMessage);
  }, []);

  const onPageLoadError = useCallback(() => {
    setError(DASHBOARD_CONSTANTS.PDF_PAGE_LOAD_ERROR);
  }, []);

  if (!isValidUrl) {
    return (
      <div className="w-full h-full flex items-center justify-center bg-white">
        <div className="text-center">
          <div className="text-gray-600 text-lg">No PDF file selected</div>
          <div className="text-gray-500 text-sm mt-2">Please select a month to view the dashboard</div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="w-full h-full flex items-center justify-center bg-white">
        <div className="text-center">
          {/* <div className="text-red-500 text-xl font-semibold mb-4">Error</div>
          <div className="text-gray-600 text-lg">{error}</div> */}
        </div>
      </div>
    );
  }

  if (!Document || !Page || !proxiedUrl) {
    return (
      <div className="w-full h-full flex items-center justify-center bg-white">
        <CustomSpinner tip="" size="large" />
      </div>
    );
  }

  return (
    <div className="pdf-viewer-container w-full h-full flex flex-col items-center justify-start bg-transparent p-3 sm:p-6 overflow-visible">
      <div className="flex-1 flex items-start justify-center w-full max-w-[95rem] overflow-visible relative">
        <div className="bg-white rounded-xl shadow-xl border border-gray-100 p-4 sm:p-6 w-full flex justify-center overflow-visible dashboard-pdf-frame">
          {proxiedUrl && (
            <Document
              key={proxiedUrl}
              file={proxiedUrl}
              onLoadSuccess={onDocumentLoadSuccess}
              onLoadError={onDocumentLoadError}
              loading={null}
              error={null}
            >
              {numPages && numPages > 0 && (
                <Page
                  key={`page_${pageNumber}`}
                  pageNumber={Math.min(Math.max(1, pageNumber), numPages)}
                  onLoadError={onPageLoadError}
                  renderTextLayer={true}
                  renderAnnotationLayer={true}
                  scale={scale}
                  width={width}
                />
              )}
            </Document>
          )}
        </div>
      </div>
    </div>
  );
});

ClientPDFViewer.propTypes = {
  url: PropTypes.string,
  pageToView: PropTypes.number,
  onLoadSuccess: PropTypes.func,
};

const PDFViewer = memo(function PDFViewer({ url, pageToView = 1, onLoadSuccess }) {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  if (!isClient) {
    return (
      <div className="w-full h-full flex items-center justify-center bg-white">
        <CustomSpinner tip="" size="large" />
      </div>
    );
  }

  return <ClientPDFViewer url={url} pageToView={pageToView} onLoadSuccess={onLoadSuccess} />;
});

PDFViewer.propTypes = {
  url: PropTypes.string,
  pageToView: PropTypes.number,
  onLoadSuccess: PropTypes.func,
};

export default PDFViewer;
