/* Summary Popup Styles */

.summary-popup-overlay {
  position: fixed;
  inset: 0;
  z-index: 50;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(4px);
}

.summary-popup-container {
  background-color: white;
  border-radius: 1rem;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  border: 1px solid rgba(243, 244, 246, 0.5);
  max-width: 48rem;
  width: 100%;
  margin-left: 1rem;
  margin-right: 1rem;
  max-height: 85vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.summary-popup-header {
  position: sticky;
  top: 0;
  background: linear-gradient(
    to right,
    rgb(238 242 255),
    rgb(250 245 255),
    rgb(253 244 255)
  );
  border-bottom: 1px solid rgba(243, 244, 246, 0.7);
  z-index: 10;
  padding: 0.625rem 1rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.summary-popup-header-left {
  display: flex;
  align-items: center;
}

.summary-popup-header-left > div {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.summary-popup-title {
  font-size: 0.875rem;
  font-weight: 700;
  background: linear-gradient(to right, rgb(79 70 229), rgb(147 51 234));
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.summary-popup-month-badge {
  font-size: 0.625rem;
  font-weight: 600;
  color: #4F46E5;
  background-color: rgba(79, 70, 229, 0.1);
  padding: 0.25rem 0.5rem;
  border-radius: 0.375rem;
  display: inline-block;
  width: fit-content;
  border: 1px solid rgba(79, 70, 229, 0.2);
  background-clip: unset;
  -webkit-background-clip: unset;
  background: rgba(79, 70, 229, 0.1);
}

.summary-popup-hint {
  font-size: 0.5625rem;
  color: rgb(107 114 128);
  margin-top: 0.125rem;
  font-weight: 500;
}

.summary-popup-close-btn {
  padding: 0.25rem;
  border-radius: 0.5rem;
  transition: all 0.3s ease;
}

.summary-popup-close-btn:hover {
  background-color: rgba(255, 255, 255, 0.6);
}

.summary-popup-close-icon {
  width: 1rem;
  height: 1rem;
  color: rgb(75 85 99);
}

.summary-popup-label {
  position: sticky;
  top: 52px;
  background-color: white;
  z-index: 10;
  border-bottom: 1px solid rgba(243, 244, 246, 1);
  padding: 0.375rem 1rem;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.summary-popup-label-text {
  font-size: 0.75rem;
  font-weight: 600;
  color: rgb(67 56 202);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.summary-popup-label-badge {
  font-size: 0.5rem;
  font-weight: 400;
  color: rgb(99 102 241);
  background-color: rgb(238 242 255);
  padding: 0.125rem 0.375rem;
  border-radius: 9999px;
}

.summary-popup-content {
  flex: 1;
  overflow-y: auto;
  min-height: 0;
  background-color: white;
  padding: 0.75rem 1rem;
}

.summary-popup-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  min-height: 400px;
  gap: 1rem;
}

.summary-popup-loading-spinner {
  position: relative;
  width: 4rem;
  height: 4rem;
}

.summary-popup-loading-ring {
  position: absolute;
  border: 4px solid transparent;
  border-radius: 9999px;
}

.summary-popup-loading-ring-outer {
  inset: 0;
  border-top-color: rgb(168 85 247);
  border-right-color: rgb(196 181 253);
  animation: spin 1s linear infinite;
}

.summary-popup-loading-ring-middle {
  inset: 0.5rem;
  border-top-color: rgb(99 102 241);
  border-right-color: rgb(165 180 252);
  animation: spin-reverse 1.5s linear infinite;
}

.summary-popup-loading-ring-inner {
  inset: 1rem;
  border-top-color: rgb(59 130 246);
  border-right-color: rgb(147 197 253);
  animation: spin 0.8s linear infinite;
}

.summary-popup-loading-dot {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 0.5rem;
  height: 0.5rem;
  background: linear-gradient(to right, rgb(168 85 247), rgb(59 130 246));
  border-radius: 9999px;
  animation: pulse-dot 1.5s ease-in-out infinite;
}

.summary-popup-loading-text {
  color: rgb(55 65 81);
  font-weight: 500;
  font-size: 1rem;
}

.summary-popup-empty {
  text-align: center;
  color: rgb(156 163 175);
  padding: 2rem 0;
}

.summary-popup-empty-text {
  font-size: 0.875rem;
}

.summary-popup-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.5rem 1rem;
  border-top: 1px solid rgba(229, 231, 235, 0.4);
  background: linear-gradient(
    to right,
    rgba(249, 250, 251, 0.6),
    rgba(238, 242, 255, 0.4)
  );
}

.summary-popup-footer-tip {
  font-size: 0.5625rem;
  color: rgb(107 114 128);
}

.summary-popup-footer-close {
  border: 1px solid rgb(199 210 254);
  color: rgb(67 56 202);
  padding: 0.25rem 0.75rem;
  font-size: 0.75rem;
  border-radius: 0.5rem;
  font-weight: 500;
  transition: all 0.2s ease;
}

.summary-popup-footer-close:hover {
  background-color: rgb(238 242 255);
  border-color: rgb(165 180 252);
  transform: scale(1.05);
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes spin-reverse {
  from {
    transform: rotate(360deg);
  }
  to {
    transform: rotate(0deg);
  }
}

@keyframes pulse-dot {
  0%,
  100% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 1;
  }
  50% {
    transform: translate(-50%, -50%) scale(1.5);
    opacity: 0.5;
  }
}

/* Summary Content Styles */
.summary-title {
  font-size: 1.125rem;
  font-weight: 700;
  background: linear-gradient(to right, rgb(79 70 229), rgb(147 51 234));
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  margin: 0 0 1rem 0;
  padding-bottom: 0.75rem;
  border-bottom: 2px solid rgb(199 210 254);
}

.summary-section-heading {
  font-size: 0.875rem;
  font-weight: 600;
  color: rgb(67 56 202);
  margin: 1.25rem 0 0.75rem 0;
  padding-top: 0.5rem;
}

.summary-section-heading:first-of-type {
  margin-top: 0.5rem;
}

/* Table Styles */
.summary-table-wrapper {
  overflow-x: auto;
  margin: 0.5rem 0 1rem 0;
  border-radius: 0.5rem;
  border: 1px solid rgb(229 231 235);
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  background-color: white;
}

.summary-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.75rem;
}

.summary-table-header {
  background: linear-gradient(to right, rgb(79 70 229), rgb(124 58 237));
  color: white;
  font-weight: 600;
  padding: 0.5rem 0.75rem;
  text-align: left;
  border-bottom: 2px solid rgb(67 56 202);
  font-size: 0.6875rem;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.summary-table-header:first-child {
  border-top-left-radius: 0.5rem;
}

.summary-table-header:last-child {
  border-top-right-radius: 0.5rem;
}

.summary-table-row {
  transition: background-color 0.15s ease;
}

.summary-table-row:hover {
  background-color: rgb(238 242 255 / 0.5);
}

.summary-table-row:nth-child(even) {
  background-color: rgb(249 250 251 / 0.5);
}

.summary-table-row:nth-child(even):hover {
  background-color: rgb(238 242 255 / 0.7);
}

.summary-table-cell {
  padding: 0.5rem 0.75rem;
  border-bottom: 1px solid rgb(229 231 235);
  color: rgb(55 65 81);
  font-size: 0.75rem;
  background-color: white;
}

.summary-table-row:last-child .summary-table-cell {
  border-bottom: none;
}

.summary-table-cell-left {
  text-align: left;
}

.summary-table-cell-right {
  text-align: right;
  font-weight: 500;
}

.summary-table-cell-bold {
  font-weight: 600;
  color: rgb(31 41 55);
}

/* List Styles */
.summary-list {
  list-style: none;
  padding: 0;
  margin: 0.5rem 0 1rem 0;
}

.summary-list-item {
  padding: 0.5rem 0 0.5rem 1.25rem;
  color: rgb(55 65 81);
  font-size: 0.8125rem;
  line-height: 1.6;
  position: relative;
  margin-bottom: 0.375rem;
}

.summary-list-item::before {
  content: "•";
  position: absolute;
  left: 0;
  color: rgb(99 102 241);
  font-weight: bold;
  font-size: 1rem;
}

/* Paragraph Styles */
.summary-paragraph {
  color: rgb(55 65 81);
  font-size: 0.8125rem;
  line-height: 1.7;
  margin: 0.5rem 0 1rem 0;
  padding: 0.75rem;
  background-color: rgb(249 250 251);
  border-left: 3px solid rgb(99 102 241);
  border-radius: 0.25rem;
}

/* Content wrapper adjustments */
.summary-popup-content .dashboard-summary-content {
  padding: 0;
}

.summary-popup-content .dashboard-summary-content > *:first-child {
  margin-top: 0;
}

.summary-formatted-content {
  color: rgb(55 65 81);
}

.summary-formatted-content h2,
.summary-formatted-content h3 {
  scroll-margin-top: 60px;
}
