import { DataTypes } from "sequelize";

const AppOrganizationModel = (sequelize) => {
  const AppOrganization = sequelize.define(
    "app_organization",
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true,
      },
      name: {
        type: DataTypes.TEXT,
        allowNull: false,
      },
      email: {
        type: DataTypes.TEXT,
        allowNull: false,
        validate: {
          isEmail: true,
        },
      },
      phone: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      website: {
        type: DataTypes.TEXT,
        allowNull: true,
        validate: {
          isUrl: true,
        },
      },
      description: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      services: {
        type: DataTypes.ARRAY(
          DataTypes.ENUM("financial", "operational", "payroll")
        ),
        allowNull: false,
        defaultValue: [],
        validate: {
          isValidServices(value) {
            const validServices = ["financial", "operational", "payroll"];
            if (!Array.isArray(value)) {
              throw new Error("Services must be an array");
            }
            for (const service of value) {
              if (!validServices.includes(service)) {
                throw new Error(
                  `Invalid service: ${service}. Must be one of: ${validServices.join(
                    ", "
                  )}`
                );
              }
            }
          },
        },
      },
      is_qb_connected: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
      },
      is_active: {
        type: DataTypes.BOOLEAN,
        defaultValue: true,
      },
      schema_name: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      is_deleted: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
      },
      created_at: {
        type: DataTypes.DATE,
        defaultValue: DataTypes.NOW,
      },
      created_by: {
        type: DataTypes.UUID,
        allowNull: true,
      },
      updated_at: {
        type: DataTypes.DATE,
      },
      updated_by: {
        type: DataTypes.UUID,
        allowNull: true,
      },
      office_id: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      realm_id: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      qb_last_synced_at: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      sikka_last_synced_at: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      adp_last_synced_at: {
        type: DataTypes.DATE,
        allowNull: true,
      },
    },
    {
      tableName: "app_organization",
      schema: "Authentication",
      timestamps: true,
      createdAt: "created_at",
      updatedAt: "updated_at",
      indexes: [
        {
          name: "idx_organization_email",
          fields: ["email"],
          unique: true,
          where: {
            is_deleted: false,
          },
        },
        {
          name: "idx_organization_name",
          fields: ["name"],
        },
        {
          name: "idx_organization_services",
          fields: ["services"],
          //   using: "gin",
        },
        {
          name: "idx_organization_created_at",
          fields: ["created_at"],
        },
        {
          name: "idx_organization_is_deleted",
          fields: ["is_deleted"],
        },
      ],
    }
  );

  return AppOrganization;
};

export default AppOrganizationModel;
