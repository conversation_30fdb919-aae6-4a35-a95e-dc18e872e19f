import React, { useMemo } from "react";
import StatsGrid from "../../common/StatsGrid";
import { LISTING_CONSTANTS } from "@/utils/constants/listing";

export default function StatsCards({
  organizations = [],
  loading = false,
  className = "",
}) {
  const stats = useMemo(() => {
    if (!organizations || organizations.length === 0) {
      return [];
    }

    // Calculate total clients
    const totalClients = organizations.length;

    // Calculate active clients
    const activeClients = organizations.filter(
      (org) => org.is_active === true
    ).length;

    // Calculate pending updates (organizations that haven't synced in the last 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    const pendingUpdates = organizations.filter((org) => {
      // Check if any of the sync dates are null or older than 30 days
      const hasQB = org.qb_last_synced_at
        ? new Date(org.qb_last_synced_at) > thirtyDaysAgo
        : false;
      const hasSikka = org.sikka_last_synced_at
        ? new Date(org.sikka_last_synced_at) > thirtyDaysAgo
        : false;
      const hasAdp = org.adp_last_synced_at
        ? new Date(org.adp_last_synced_at) > thirtyDaysAgo
        : false;

      // If organization has services configured but no recent sync
      const hasServices = org.services && org.services.length > 0;
      return hasServices && !hasQB && !hasSikka && !hasAdp;
    }).length;

    // Calculate this month's clients (clients created this month)
    const now = new Date();
    const firstDayOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const thisMonth = organizations.filter((org) => {
      const createdAt = org.created_at ? new Date(org.created_at) : null;
      return createdAt && createdAt >= firstDayOfMonth;
    }).length;

    return [
      {
        id: 1,
        title: LISTING_CONSTANTS.STATS.TOTAL_CLIENTS.TITLE,
        value: totalClients.toString(),
        icon: LISTING_CONSTANTS.STATS.TOTAL_CLIENTS.ICON,
        bgColor: LISTING_CONSTANTS.STATS.TOTAL_CLIENTS.BG_COLOR,
        iconColor: LISTING_CONSTANTS.STATS.TOTAL_CLIENTS.ICON_COLOR,
      },
      {
        id: 2,
        title: LISTING_CONSTANTS.STATS.ACTIVE_CLIENTS.TITLE,
        value: activeClients.toString(),
        icon: LISTING_CONSTANTS.STATS.ACTIVE_CLIENTS.ICON,
        bgColor: LISTING_CONSTANTS.STATS.ACTIVE_CLIENTS.BG_COLOR,
        iconColor: LISTING_CONSTANTS.STATS.ACTIVE_CLIENTS.ICON_COLOR,
      },
      {
        id: 3,
        title: LISTING_CONSTANTS.STATS.PENDING_UPDATES.TITLE,
        value: pendingUpdates.toString(),
        icon: LISTING_CONSTANTS.STATS.PENDING_UPDATES.ICON,
        bgColor: LISTING_CONSTANTS.STATS.PENDING_UPDATES.BG_COLOR,
        iconColor: LISTING_CONSTANTS.STATS.PENDING_UPDATES.ICON_COLOR,
      },
      {
        id: 4,
        title: LISTING_CONSTANTS.STATS.THIS_MONTH.TITLE,
        value: thisMonth.toString(),
        icon: LISTING_CONSTANTS.STATS.THIS_MONTH.ICON,
        bgColor: LISTING_CONSTANTS.STATS.THIS_MONTH.BG_COLOR,
        iconColor: LISTING_CONSTANTS.STATS.THIS_MONTH.ICON_COLOR,
      },
    ];
  }, [organizations]);

  return <StatsGrid stats={stats} loading={loading} className={className} />;
}
