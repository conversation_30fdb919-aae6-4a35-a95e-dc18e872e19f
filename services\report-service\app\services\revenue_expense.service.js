import RevenueExpenseRepository from "../repository/revenue_expense.repository.js";
import income_expense_statementRepository from "../repository/income_expense_statement.repository.js";
import { LOGGER_NAMES } from "../utils/constants/log.constants.js";
import { createLogger } from "../utils/logger.utils.js";
import {
  isValidMonth,
  isValidYear,
  parseNumericValue,
  formatToKWithDecimals,
} from "../utils/format.utils.js";

const logger = createLogger(LOGGER_NAMES.REPORT_SERVICE);

/**
 * Get total for a specific keyword for a specific month
 * @param {string} schemaName - Organization schema name
 * @param {number} month - Month (1-12)
 * @param {number} year - Year
 * @param {string} keyword - Keyword to filter by (e.g., "income", "expense")
 * @returns {Promise<number>} Total for the month
 */
const getMonthTotalByKeyword = async (schemaName, month, year, keyword) => {
  try {
    // Get report raw data for the month
    const pnlReportRaw = await income_expense_statementRepository.getPNLReport(
      schemaName,
      month,
      year
    );

    if (!pnlReportRaw) {
      logger.info(
        `No P&L report found for schema: ${schemaName}, month: ${month}, year: ${year}`
      );
      return 0;
    }

    const fields = {
      revenue: "total income",
      expense: "total expenses",
      income: "net income",
    };

    const fieldname = fields[keyword];
    if (!fieldname) {
      logger.warn(`Unknown keyword: ${keyword}`);
      return 0;
    }

    const extractTotalFromQBReport = (pnlReportRaw, fieldname) => {
      try {
        const rows = pnlReportRaw?.Rows?.Row || [];

        for (const row of rows) {
          const summary = row?.Summary;
          if (!summary) continue;

          const colData = summary.ColData || [];

          // Find summary row matching the field name
          if (colData[0]?.value?.toLowerCase() === fieldname.toLowerCase()) {
            const value = colData[1]?.value || colData[2]?.value;
            return Number(value) || 0;
          }
        }

        return 0; // Return 0 if not found
      } catch (err) {
        logger.error(`Error extracting ${fieldname}:`, err);
        return 0;
      }
    };

    const total = extractTotalFromQBReport(pnlReportRaw, fieldname);
    logger.info(`${keyword} for month ${month}, year ${year}: ${total}`);
    return total || 0;
  } catch (error) {
    logger.error(`Error calculating month ${keyword}:`, error);
    return 0;
  }
};

/**
 * Get revenue and expense data for organization
 * @param {Object} params - Query parameters
 * @param {string} params.organization_id - Organization ID
 * @param {number} params.month - Month (1-12)
 * @param {number} params.year - Year
 * @returns {Promise<Object>} Revenue, expense, and net_income data
 */
const getRevenueExpenseData = async ({ organization_id, month, year }) => {
  try {
    logger.info(
      `Fetching revenue/expense data for org: ${organization_id}, month: ${month}, year: ${year}`
    );

    // Validate parameters
    if (!organization_id) {
      throw new Error("Organization ID is required");
    }

    if (!month || !isValidMonth(month)) {
      throw new Error("Valid month (1-12) is required");
    }

    if (!year || !isValidYear(year)) {
      throw new Error("Valid year is required");
    }

    // Convert to numbers
    const monthNum = parseInt(month, 10);
    const yearNum = parseInt(year, 10);

    // Get organization schema name
    const schemaName = await RevenueExpenseRepository.getOrganizationSchemaName(
      organization_id
    );

    if (!schemaName) {
      logger.warn(`Schema not found for organization: ${organization_id}`);
      throw new Error("Organization not found or schema not configured");
    }

    // Calculate current month revenue (income)
    const revenue = await getMonthTotalByKeyword(
      schemaName,
      monthNum,
      yearNum,
      "revenue"
    );
    logger.info(`Current month revenue calculated: ${revenue}`);

    // Calculate current month expense
    const expense = await getMonthTotalByKeyword(
      schemaName,
      monthNum,
      yearNum,
      "expense"
    );
    logger.info(`Current month expense calculated: ${expense}`);

    // Calculate net income (revenue - expense)
    const netIncome = await getMonthTotalByKeyword(
      schemaName,
      monthNum,
      yearNum,
      "income"
    );
    logger.info(`Current month net income calculated: ${netIncome}`);

    // Format to K notation with two decimal places
    const formattedData = {
      revenue: formatToKWithDecimals(revenue),
      expense: formatToKWithDecimals(expense),
      net_income: formatToKWithDecimals(netIncome),
    };

    logger.info(`Formatted data: ${JSON.stringify(formattedData)}`);

    return formattedData;
  } catch (error) {
    logger.error(
      "Error in RevenueExpenseService.getRevenueExpenseData:",
      error
    );
    throw error;
  }
};

export default {
  getRevenueExpenseData,
};
