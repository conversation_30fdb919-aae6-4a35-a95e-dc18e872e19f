import { createAsyncThunk } from "@reduxjs/toolkit";
import api from "@/redux/ApiService/ApiService";
import { SERVICE_PORTS } from "@/utils/constants/api";

const axios = api(SERVICE_PORTS.REPORT);

// Fetch Operations Overview
export const fetchOperationsOverview = createAsyncThunk(
  "operations/fetchOverview",
  async ({ org_id, month, year }, { rejectWithValue }) => {
    try {
      if (!org_id || !month || !year) {
        return rejectWithValue("org_id, month, and year are required");
      }

      const params = { organization_id: org_id, month, year };
      const response = await axios.get("/report/operations/overview", {
        params,
      });
      console.log(response.data);
      return response.data.data;
    } catch (error) {
      const message =
        error.response?.data?.message ||
        error.message ||
        "Failed to fetch operations overview";
      return rejectWithValue(message);
    }
  }
);

// Fetch Operations Summary
export const fetchOperationsSummary = createAsyncThunk(
  "operations/fetchSummary",
  async ({ org_id, month, year }, { rejectWithValue }) => {
    try {
      if (!org_id || !month || !year) {
        return rejectWithValue("org_id, month, and year are required");
      }

      const params = { organization_id: org_id, month, year };
      const response = await axios.get("/report/operations/summary", {
        params,
      });
      return response.data.data;
    } catch (error) {
      const message =
        error.response?.data?.message ||
        error.message ||
        "Failed to fetch operations summary";
      return rejectWithValue(message);
    }
  }
);

// Fetch Operations Trends
export const fetchOperationsTrends = createAsyncThunk(
  "operations/fetchTrends",
  async ({ org_id, month, year }, { rejectWithValue }) => {
    try {
      if (!org_id || !month || !year) {
        return rejectWithValue("org_id, month, and year are required");
      }

      const params = { organization_id: org_id, month, year };
      const response = await axios.get("/report/operations/trends", {
        params,
      });
      return response.data.data;
    } catch (error) {
      const message =
        error.response?.data?.message ||
        error.message ||
        "Failed to fetch operations trends";
      return rejectWithValue(message);
    }
  }
);


