import { sequelize } from "../models/index.js";
import models from "../models/index.js";
import { LOGGER_NAMES } from "../utils/constants/log.constants.js";
import { createLogger } from "../utils/logger.utils.js";
import { getOrganizationSchemaName, getReportId } from "../utils/repository.utils.js";

const logger = createLogger(LOGGER_NAMES.REPORT_REPOSITORY);

/**
 * Get P&L report ID for a specific month and year
 * @param {string} schemaName - Organization schema name
 * @param {number} month - Month (1-12)
 * @param {number} year - Year
 * @returns {Promise<string|null>} Report ID or null
 */
const getPnlReportId = async (schemaName, month, year) => {
  return await getReportId(schemaName, 'qb_pnl_reports', month, year, 'P&L');
};

/**
 * Get P&L line items for a report
 * @param {string} schemaName - Organization schema name
 * @param {string} reportId - Report ID
 * @returns {Promise<Array>} P&L line items
 */
const getPnlLineItems = async (schemaName, reportId) => {
  try {
    logger.info(
      `Fetching P&L line items from schema: ${schemaName} for report ID: ${reportId}`
    );

    const query = `
      SELECT 
        id,
        path,
        amount
      FROM "${schemaName}".qb_pnl_lines
      WHERE report_id = :reportId
    `;

    const results = await sequelize.query(query, {
      replacements: { reportId },
      type: sequelize.QueryTypes.SELECT,
    });

    logger.info(
      `Retrieved ${results.length} P&L line items from schema: ${schemaName}`
    );

    return results;
  } catch (error) {
    logger.error(`Error in KpiRepository.getPnlLineItems:`, error);
    throw error;
  }
};

/**
 * Get Cash Flow report ID for a specific month and year
 * @param {string} schemaName - Organization schema name
 * @param {number} month - Month (1-12)
 * @param {number} year - Year
 * @returns {Promise<string|null>} Report ID or null
 */
const getCashFlowReportId = async (schemaName, month, year) => {
  return await getReportId(schemaName, 'qb_cash_flow_reports', month, year, 'Cash Flow');
};

/**
 * Get net cash flow from cash flow totals table
 * @param {string} schemaName - Organization schema name
 * @param {string} reportId - Report ID
 * @returns {Promise<number>} Net cash flow value
 */
const getNetCashFlow = async (schemaName, reportId) => {
  try {
    logger.info(
      `Fetching net cash flow from schema: ${schemaName} for report ID: ${reportId}`
    );

    const query = `
      SELECT 
        net_cash_flow
      FROM "${schemaName}".qb_cash_flow_totals
      WHERE report_id = :reportId
      LIMIT 1
    `;

    const results = await sequelize.query(query, {
      replacements: { reportId },
      type: sequelize.QueryTypes.SELECT,
    });

    if (results.length === 0) {
      logger.info(
        `No cash flow totals found for report ID: ${reportId} in schema: ${schemaName}`
      );
      return 0;
    }

    const netCashFlow = parseFloat(results[0].net_cash_flow) || 0;
    logger.info(
      `Retrieved net cash flow: ${netCashFlow} from schema: ${schemaName}`
    );

    return netCashFlow;
  } catch (error) {
    logger.error(`Error in KpiRepository.getNetCashFlow:`, error);
    throw error;
  }
};

export default {
  getOrganizationSchemaName,
  getPnlReportId,
  getPnlLineItems,
  getCashFlowReportId,
  getNetCashFlow,
};
