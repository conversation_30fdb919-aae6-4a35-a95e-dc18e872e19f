import { validationRules } from "@/utils/methods/validation";
import {
  ORGANIZATION_FIELD_CONSTANTS,
  ORGANIZATION_CONSTANTS,
} from "@/utils/constants/organization";
import <PERSON>kkaIdField from "@/components/organization/SikkaIdField";
import { COMMON_CONSTANTS, MESSAGES } from "../constants";

export const organizationFields = [
  {
    name: "name",
    label: ORGANIZATION_FIELD_CONSTANTS.NAME.LABEL,
    type: "text",
    placeholder: ORGANIZATION_FIELD_CONSTANTS.NAME.PLACEHOLDER,
    validation: { required: OR<PERSON><PERSON>ZATION_FIELD_CONSTANTS.NAME.VALIDATION },
  },
  {
    name: "email",
    label: <PERSON>GANI<PERSON>ATION_FIELD_CONSTANTS.EMAIL.LABEL,
    type: "email",
    placeholder: ORGANIZATION_FIELD_CONSTANTS.EMAIL.PLACEHOLDER,
    validation: {
      required: "Email is required",
      pattern: {
        value: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
        message: "Please enter a valid email address",
      },
    },
  },
  {
    name: "phone",
    label: ORGANIZATION_FIELD_CONSTANTS.PHONE.LABEL,
    type: "tel",
    placeholder: ORGANIZATION_FIELD_CONSTANTS.PHONE.PLACEHOLDER,
    validation: {
      required: ORGANIZATION_FIELD_CONSTANTS.PHONE.VALIDATION,
      pattern: {
        value: /^\+?[\d\s\-\(\)]{10,}$/,
        message: "Please enter a valid phone number",
      },
    },
  },
  {
    name: "logo",
    label: ORGANIZATION_FIELD_CONSTANTS.LOGO.LABEL,
    type: "file",
    accept: ".png,.svg",
    showRequired: false, // Explicitly set to false to hide required asterisk
    // validation: {
    //   validate: (file) => {
    //     // Logo is optional, so if no file is provided, validation passes
    //     if (!file) {
    //       return true;
    //     }
    //     const allowedTypes = COMMON_CONSTANTS.FILES.ALLOWED_MIME_TYPES;

    //     if (!allowedTypes.includes(file.type)) {
    //       return ORGANIZATION_FIELD_CONSTANTS.LOGO.TYPE_VALIDATION;
    //     }
    //     if (file.size > COMMON_CONSTANTS.FILES.MAX_INLINE_BYTES) {
    //       return MESSAGES.VALIDATION.FILE_TOO_LARGE;
    //     }
    //     return true;
    //   },
    // },
  },
  {
    name: "website",
    label: ORGANIZATION_FIELD_CONSTANTS.WEBSITE.LABEL,
    type: "text",
    placeholder: ORGANIZATION_FIELD_CONSTANTS.WEBSITE.PLACEHOLDER,
    validation: {
      required: ORGANIZATION_FIELD_CONSTANTS.WEBSITE.VALIDATION,
      validate: (value) => {
        if (!value) return true; // required check handles empty
        // Match HTML5 URL input type validation - accepts URLs with or without protocol
        // Allows: google.com, www.google.com, http://google.com, https://www.google.com
        const urlPattern =
          /^(https?:\/\/)?([\da-z\.-]+)\.([a-z\.]{2,6})([\/\w \.-]*)*\/?$/i;
        if (urlPattern.test(value)) {
          return true;
        }
        return "Please enter a valid URL (e.g., google.com or https://www.google.com)";
      },
    },
  },
  {
    name: "description",
    label: ORGANIZATION_FIELD_CONSTANTS.DESCRIPTION.LABEL,
    type: "textarea",
    placeholder: ORGANIZATION_FIELD_CONSTANTS.DESCRIPTION.PLACEHOLDER,
    colSpan: 2,
    validation: {
      required: ORGANIZATION_FIELD_CONSTANTS.DESCRIPTION.VALIDATION,
      maxLength: {
        value: 2000,
        message: "Description must not exceed 2000 characters",
      },
    },
  },
  {
    name: "services",
    label: ORGANIZATION_FIELD_CONSTANTS.SERVICES.LABEL,
    type: "multiselect",
    options: [
      { label: "Financial", value: "financial" },
      { label: "Operational", value: "operational" },
      { label: "Payroll", value: "payroll" },
    ],
    validation: { required: ORGANIZATION_FIELD_CONSTANTS.SERVICES.VALIDATION },
  },
  {
    name: "office_id",
    type: "text",
    placeholder: ORGANIZATION_FIELD_CONSTANTS.OFFICE_ID.PLACEHOLDER,
    showDefaultError: false, // We handle error display within the custom render function
    render: (field, form, checkConnection, connectionStatus) => (
      <SikkaIdField
        field={field}
        form={form}
        checkConnection={checkConnection}
        connectionStatus={connectionStatus}
      />
    ),
    validation: {
      validate: (value, allValues, formState) => {
        const selected = allValues?.services || [];
        const isOperationalSelected = selected.includes("operational");
        const sikkaIdDigits = ORGANIZATION_CONSTANTS.SIKKA_ID_DIGITS;

        // If operational is not selected, field is optional
        if (!isOperationalSelected) {
          return true;
        }

        // If operational is selected, field is required
        if (!value || value.trim() === "") {
          return "Sikka ID is required when Operational is selected.";
        }

        // Validate exact length of SIKKA_ID_DIGITS characters
        if (value.length !== sikkaIdDigits) {
          return `Sikka ID must be exactly ${sikkaIdDigits} characters.`;
        }
        return true;
      },
    },
  },
];

export const initialValues = {
  name: "",
  email: "",
  phone: "",
  logo: null,
  website: "",
  description: "",
  services: [],
  realm_id: "",
  office_id: "",
};

export const stats = [
  {
    title: "Total Organizations",
    value: 0,
    icon: "building",
    bgColor: "bg-blue-100",
    iconColor: "text-blue-600",
  },
];

export const columns = [
  {
    key: "name",
    title: "Organization Name",
    width: "20%",
  },
  {
    key: "type",
    title: "Type",
    width: "15%",
    render: (value) => renderStatusBadge(value),
  },
  {
    key: "parent",
    title: "Parent",
    width: "15%",
  },
  {
    key: "members",
    title: "Members",
    width: "10%",
  },
  {
    key: "location",
    title: "Location",
    width: "15%",
  },
  {
    key: "status",
    title: "Status",
    width: "10%",
    render: (value) => renderStatusBadge(value),
  },
  {
    key: "createdAt",
    title: "Created",
    width: "10%",
  },
  {
    key: "actions",
    title: "Actions",
    width: "15%",
    sortable: false,
    render: (_, item) =>
      renderActionButtons(item, handleView, handleEdit, handleDelete),
  },
];
