import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>OKS_CONTROLLER_LOGS,
  QUICKBOOKS_ERROR_LOGS,
  QUICKBOOKS_REPORTS_LOGS,
  QUICKBOOKS_SUCCESS_LOGS,
} from "../utils/constants/log.constants.js";
import {
  QUIC<PERSON>BOOKS_DEFAULTS,
  QUICKBOOKS_STATUS,
  QUICKBOOKS_FIELD_NAMES,
  QUICKBOOKS_VALIDATION,
  QUICKBOOKS_LOGGER_NAMES,
} from "../utils/constants/config.constants.js";
import {
  ERROR_MESSAGES,
  QUICKBOOKS_MESSAGES,
  QUICKBOOKS_REPORTS_MESSAGES,
} from "../utils/constants/error.constants.js";
import { HARDCODED_STRINGS } from "../utils/constants/strings.constants.js";
import * as status from "../utils/status_code.utils.js";
import { errorResponse, successResponse } from "../utils/response.util.js";
import { createLogger } from "../utils/logger.utils.js";
import { encrypt } from "../utils/encryption.utils.js";
import quickbooksService, {
  handleOrganizationRealmId,
  updateOrganizationQbSyncTimestamp,
  updateRealmIdForOrganization,
} from "../services/quickbooks.service.js";
import quickbooksRepository from "../repositories/quickbooks.repository.js";
import { processReport } from "./reports.controller.js";
import { updateOrganizationQbConnection } from "../utils/organization-api.util.js";
import { sendQuickBooksReportsSyncFailureEmail } from "../utils/quickbooks.util.js";
import { run as calculateEBITDA } from "../services/calculate_ebitda.service.js";
import { getOrganizationById } from "../utils/organization-api.util.js";

const logger = createLogger(QUICKBOOKS_LOGGER_NAMES.QUICKBOOKS_CONTROLLER);

const QB_CONFIG = {
  clientID: process.env.QUICKBOOKS_CLIENT_ID,
  clientSecret: process.env.QUICKBOOKS_CLIENT_SECRET,
  tokenUrl: process.env.QUICKBOOKS_TOKEN_URL,
  redirectUri: process.env.QUICKBOOKS_REDIRECT_URI,
};

const validateEnvironmentConfig = () => {
  const missingVars = Object.entries(QB_CONFIG)
    .filter(([_, value]) => !value)
    .map(([key]) => key);

  if (missingVars.length > 0) {
    throw new Error(
      `${HARDCODED_STRINGS.MISSING_ENV_VARS}: ${missingVars.join(", ")}`
    );
  }
};

try {
  validateEnvironmentConfig();
} catch (error) {
  logger.error(QUICKBOOKS_CONTROLLER_LOGS.CONFIG_ERROR, {
    error: error.message,
  });
}

const validateRequiredFields = (data, requiredFields) => {
  try {
    return requiredFields.filter((field) => !data[field]);
  } catch (error) {
    logger.error(QUICKBOOKS_ERROR_LOGS.VALIDATE_REQUIRED_FIELDS_ERROR, {
      error: error.message,
      stack: error.stack,
      data,
      requiredFields,
    });
    throw error;
  }
};

const handleDatabaseError = (error) => {
  const errorMappings = {
    [QUICKBOOKS_DEFAULTS.SQL_ERROR_FOREIGN_KEY]: {
      status: status.STATUS_CODE_BAD_REQUEST,
      message: QUICKBOOKS_MESSAGES.FOREIGN_KEY_VIOLATION_ERROR,
    },
    [QUICKBOOKS_DEFAULTS.SQL_ERROR_DUPLICATE]: {
      status: status.STATUS_CODE_BAD_REQUEST,
      message: QUICKBOOKS_MESSAGES.DUPLICATE_QUICKBOOK_ACCOUNT_ERROR,
    },
    [QUICKBOOKS_DEFAULTS.POSTGRES_UNIQUE_VIOLATION]: {
      status: status.STATUS_CODE_CONFLICT,
      message: QUICKBOOKS_MESSAGES.DUPLICATE_QUICKBOOK_ACCOUNT_ERROR,
    },
  };

  return (
    errorMappings[error.number] ||
    errorMappings[error.original?.code] || {
      status: status.STATUS_CODE_INTERNAL_SERVER_ERROR,
      message: error.message || QUICKBOOKS_MESSAGES.INTERNAL_SERVER_ERROR,
    }
  );
};

const sendErrorResponse = (res, error) => {
  try {
    logger.error(QUICKBOOKS_ERROR_LOGS.CONTROLLER_ERROR, {
      error: error.message,
      stack: error.stack,
    });
    const errorInfo = handleDatabaseError(error);
    return res.status(errorInfo.status).json(errorResponse(errorInfo.message));
  } catch (err) {
    logger.error(QUICKBOOKS_ERROR_LOGS.SEND_ERROR_RESPONSE_ERROR, {
      error: err.message,
      stack: err.stack,
      originalError: error,
    });
    return res
      .status(status.STATUS_CODE_INTERNAL_SERVER_ERROR)
      .json(errorResponse(QUICKBOOKS_MESSAGES.INTERNAL_SERVER_ERROR));
  }
};

const updateQbConnectionStatus = async (organizationId) => {
  try {
    logger.info(
      `Updating QB connection status for organization: ${organizationId}`
    );
    const qbConnectionResult = await updateOrganizationQbConnection(
      organizationId
    );

    if (qbConnectionResult.success) {
      logger.info(
        `Successfully updated QB connection status for organization: ${organizationId}`
      );
    } else {
      logger.warn(
        `Failed to update QB connection status for organization ${organizationId}: ${qbConnectionResult.error}`
      );
    }
  } catch (error) {
    logger.warn(
      `Error updating QB connection status for organization ${organizationId}:`,
      {
        error: error.message,
      }
    );
  }
};

export const addQuickbookAccount = async (req, res) => {
  logger.info(QUICKBOOKS_SUCCESS_LOGS.ADDING_ACCOUNT);

  try {
    const { code, realmId, schemaName, email, organization_id } = req.query;

    // Handle organization realm_id operations through service layer
    if (realmId && organization_id) {
      const realmIdResult = await handleOrganizationRealmId(
        realmId,
        organization_id
      );

      if (!realmIdResult.success) {
        // If realm_id is already associated with another organization, return error
        if (realmIdResult.warning) {
          return res
            .status(status.STATUS_CODE_CONFLICT)
            .json(errorResponse(realmIdResult.message || realmIdResult.error));
        }
        // Log warning but don't fail the entire request for other errors
        logger.warn(
          `Realm ID operation completed with warnings: ${realmIdResult.error}`
        );
      }
    }

    // Validate required fields using helper
    const missingFields = validateRequiredFields(
      { code, organization_id },
      QUICKBOOKS_VALIDATION.REQUIRED_FIELDS.ADD_ACCOUNT
    );

    if (missingFields.length > 0) {
      const missingField = missingFields[0];
      const errorMessage =
        missingField === HARDCODED_STRINGS.CODE
          ? QUICKBOOKS_MESSAGES.AUTHORIZATION_CODE_NOT_FOUND
          : QUICKBOOKS_MESSAGES.ORGANIZATION_ID_REQUIRED;

      return res
        .status(status.STATUS_CODE_BAD_REQUEST)
        .json(errorResponse(errorMessage));
    }

    // Exchange authorization code for access token
    const tokenData = await quickbooksService.exchangeAuthCodeForTokens(code);
    const { access_token, refresh_token } = tokenData;

    // Encrypt tokens securely
    const [encryptedAccessToken, encryptedRefreshToken] = await Promise.all([
      encrypt(access_token),
      encrypt(refresh_token),
    ]);

    // Calculate token expiry time (expires_in is in seconds, typically 3600 for QuickBooks)
    const tokenExpiryTime = new Date(
      Date.now() + QUICKBOOKS_DEFAULTS.TOKEN_EXPIRY_MS
    );

    const accountData = {
      [QUICKBOOKS_FIELD_NAMES.ACCESS_TOKEN]: encryptedAccessToken,
      [QUICKBOOKS_FIELD_NAMES.REFRESH_TOKEN]: encryptedRefreshToken,
      [QUICKBOOKS_FIELD_NAMES.EMAIL]: email,
      [QUICKBOOKS_FIELD_NAMES.REALM_ID]: realmId,
      [QUICKBOOKS_FIELD_NAMES.TOKEN_EXPIRY_TIME]: tokenExpiryTime,
      [QUICKBOOKS_FIELD_NAMES.UPDATED_AT]: new Date(),
    };

    await quickbooksRepository.create(accountData, schemaName);

    if (organization_id) {
      await updateQbConnectionStatus(organization_id);
    }

    if (realmId && organization_id) {
      await updateRealmIdForOrganization(organization_id, realmId);
    }

    const accessToken = { access_token: access_token };

    return res
      .status(status.STATUS_CODE_SUCCESS)
      .json(
        successResponse(QUICKBOOKS_MESSAGES.ACCOUNT_AUTHORIZED, accessToken)
      );
  } catch (error) {
    logger.error(QUICKBOOKS_ERROR_LOGS.ERROR_ADDING_ACCOUNT, {
      error: error.message,
    });
    return sendErrorResponse(res, error);
  }
};

export const listQuickbookAccounts = async (req, res) => {
  logger.info(QUICKBOOKS_SUCCESS_LOGS.GETTING_ACCOUNTS);

  try {
    const { active, organization_id } = req.query;

    const whereClause = {
      ...(organization_id && {
        [QUICKBOOKS_FIELD_NAMES.ORGANIZATION_ID]: parseInt(organization_id),
      }),
      ...(!organization_id &&
        req.user?.organization_id && {
          [QUICKBOOKS_FIELD_NAMES.ORGANIZATION_ID]: req.user.organization_id,
        }),
      ...(!organization_id &&
        !req.user?.organization_id &&
        req.user?.id && {
          [QUICKBOOKS_FIELD_NAMES.USER_ID]: req.user.id,
        }),
      ...(active !== undefined && {
        [QUICKBOOKS_FIELD_NAMES.STATUS]:
          active === HARDCODED_STRINGS.BOOLEAN.TRUE,
      }),
    };

    const accounts = await quickbooksRepository.findWithUserInfo(whereClause);

    logger.info(QUICKBOOKS_CONTROLLER_LOGS.FOUND_ACCOUNTS(accounts.length));

    return res.status(status.STATUS_CODE_SUCCESS).json(
      successResponse(QUICKBOOKS_MESSAGES.ACCOUNTS_FETCHED_SUCCESSFULLY, {
        accounts,
      })
    );
  } catch (error) {
    logger.error(QUICKBOOKS_ERROR_LOGS.ERROR_GETTING_ACCOUNTS, {
      error: error.message,
    });
    return sendErrorResponse(res, error);
  }
};

export const addTokens = async (req, res) => {
  logger.info(QUICKBOOKS_SUCCESS_LOGS.ADDING_TOKEN);

  try {
    const { id, refresh_token, access_token } = req.body;

    const missingFields = validateRequiredFields(
      { id, refresh_token, access_token },
      QUICKBOOKS_VALIDATION.REQUIRED_FIELDS.ADD_TOKEN
    );

    if (missingFields.length > 0) {
      return res
        .status(status.STATUS_CODE_BAD_REQUEST)
        .json(errorResponse(QUICKBOOKS_MESSAGES.TOKENS_REQUIRED));
    }

    const accountExists = await quickbooksRepository.exists(id);
    if (!accountExists) {
      return res
        .status(status.STATUS_CODE_NOT_FOUND)
        .json(errorResponse(QUICKBOOKS_MESSAGES.ACCOUNT_NOT_FOUND));
    }

    const [encryptedRefreshToken, encryptedAccessToken] = await Promise.all([
      encrypt(refresh_token),
      encrypt(access_token),
    ]);

    const tokenData = {
      [QUICKBOOKS_FIELD_NAMES.REFRESH_TOKEN]: encryptedRefreshToken,
      [QUICKBOOKS_FIELD_NAMES.ACCESS_TOKEN]: encryptedAccessToken,
      [QUICKBOOKS_FIELD_NAMES.TOKEN_EXPIRY_TIME]: new Date(
        Date.now() + QUICKBOOKS_DEFAULTS.TOKEN_EXPIRY_MS
      ),
    };

    const updatedAccount = await quickbooksRepository.updateTokens(
      id,
      tokenData
    );

    return res.status(status.STATUS_CODE_SUCCESS).json(
      successResponse(QUICKBOOKS_MESSAGES.TOKEN_ADDED_SUCCESSFULLY, {
        account: updatedAccount,
      })
    );
  } catch (error) {
    logger.error(QUICKBOOKS_ERROR_LOGS.ERROR_ADDING_TOKEN, {
      error: error.message,
    });
    return sendErrorResponse(res, error);
  }
};

export const getTokens = async (req, res) => {
  logger.info(QUICKBOOKS_SUCCESS_LOGS.GETTING_TOKENS);

  try {
    const { id } = req.body;

    const missingFields = validateRequiredFields(
      { id },
      QUICKBOOKS_VALIDATION.REQUIRED_FIELDS.GET_TOKENS
    );

    if (missingFields.length > 0) {
      return res
        .status(status.STATUS_CODE_BAD_REQUEST)
        .json(errorResponse(QUICKBOOKS_MESSAGES.ACCOUNT_ID_REQUIRED));
    }

    const quickbookAccount = await quickbooksRepository.findById(id);
    if (!quickbookAccount) {
      return res
        .status(status.STATUS_CODE_NOT_FOUND)
        .json(errorResponse(QUICKBOOKS_MESSAGES.ACCOUNT_NOT_FOUND));
    }

    const updatedAccount = await quickbooksService.refreshTokens(
      quickbookAccount
    );
    if (!updatedAccount) {
      return res
        .status(status.STATUS_CODE_INTERNAL_SERVER_ERROR)
        .json(errorResponse(QUICKBOOKS_MESSAGES.FAILED_TO_REFRESH_TOKENS));
    }

    return res
      .status(status.STATUS_CODE_SUCCESS)
      .json(successResponse(QUICKBOOKS_MESSAGES.NEW_TOKEN_GENERATED));
  } catch (error) {
    logger.error(QUICKBOOKS_ERROR_LOGS.ERROR_ADDING_TOKEN, {
      error: error.message,
    });
    return sendErrorResponse(res, error);
  }
};

export const quickbooksFileSave = async (req, res) => {
  logger.info(QUICKBOOKS_SUCCESS_LOGS.ADDING_FILE);

  const initiatedBy = req.body.isCronJob
    ? QUICKBOOKS_STATUS.AUTOMATIC
    : req.user?.email || HARDCODED_STRINGS.UNKNOWN_USER;

  try {
    const { organization_id, id, isCronJob } = req.body;

    if (!organization_id || !id) {
      return await syncAllQuickBooksAccounts();
    }

    const missingFields = validateRequiredFields(
      { organization_id, id },
      QUICKBOOKS_VALIDATION.REQUIRED_FIELDS.SYNC_DATA
    );

    if (missingFields.length > 0) {
      await quickbooksService.logQuickBookSync(
        initiatedBy,
        QUICKBOOKS_STATUS.FAILED
      );
      return res
        .status(status.STATUS_CODE_BAD_REQUEST)
        .json(
          errorResponse(
            HARDCODED_STRINGS.ORGANIZATION_ID_AND_ACCOUNT_ID_REQUIRED
          )
        );
    }

    const quickbookAccount = await quickbooksRepository.findById(id);
    if (!quickbookAccount) {
      await quickbooksService.logQuickBookSync(
        initiatedBy,
        QUICKBOOKS_STATUS.FAILED
      );
      return res
        .status(status.STATUS_CODE_NOT_FOUND)
        .json(errorResponse(QUICKBOOKS_MESSAGES.ACCOUNT_NOT_FOUND));
    }

    const syncResult = await quickbooksService.syncAccountData(
      quickbookAccount,
      organization_id,
      initiatedBy
    );

    await quickbooksRepository.updateLastSynced(id);

    if (!isCronJob) {
      await quickbooksService.logQuickBookSync(
        initiatedBy,
        QUICKBOOKS_STATUS.COMPLETED
      );
    }

    return res.status(status.STATUS_CODE_SUCCESS).json(
      successResponse(QUICKBOOKS_MESSAGES.DATA_SYNCED_SUCCESSFULLY, {
        uploadedFiles: syncResult,
      })
    );
  } catch (error) {
    await quickbooksService.logQuickBookSync(
      initiatedBy,
      QUICKBOOKS_STATUS.FAILED
    );
    logger.error(QUICKBOOKS_ERROR_LOGS.ERROR_UPLOADING_FILE, {
      error: error.message,
    });
    return sendErrorResponse(res, error);
  }
};

export const statusDisable = async (req, res) => {
  logger.info(QUICKBOOKS_SUCCESS_LOGS.DISABLING_ACCOUNT);

  try {
    const { accountStatus } = req.body;
    const { id } = req.params;

    const quickbookAccount = await quickbooksRepository.findById(id);
    if (!quickbookAccount) {
      return res
        .status(status.STATUS_CODE_NOT_FOUND)
        .json(errorResponse(QUICKBOOKS_MESSAGES.ACCOUNT_NOT_FOUND));
    }

    const updatedAccount = await quickbooksRepository.updateStatus(
      id,
      accountStatus
    );

    const message = accountStatus
      ? QUICKBOOKS_MESSAGES.ACCOUNT_ENABLED_SUCCESSFULLY
      : QUICKBOOKS_MESSAGES.ACCOUNT_DISABLED_SUCCESSFULLY;

    return res
      .status(status.STATUS_CODE_SUCCESS)
      .json(successResponse(message, { quickbookAccount: updatedAccount }));
  } catch (error) {
    logger.error(QUICKBOOKS_ERROR_LOGS.ERROR_DISABLING_ACCOUNT, {
      error: error.message,
    });
    return sendErrorResponse(res, error);
  }
};

export const syncAllQuickBooksAccounts = async () => {
  try {
    const organizations = [];

    for (const org of organizations) {
      try {
        const quickbookAccounts =
          await quickbooksRepository.findByOrganizationId(org.id);

        const syncPromises = quickbookAccounts.map(async (account) => {
          try {
            return await quickbooksService.syncAccountData(
              account,
              org.id,
              QUICKBOOKS_STATUS.AUTOMATIC
            );
          } catch (error) {
            logger.error(QUICKBOOKS_ERROR_LOGS.SYNC_ACCOUNT_DATA_ERROR, {
              error: error.message,
              stack: error.stack,
              accountId: account.id,
              organizationId: org.id,
            });
            throw error;
          }
        });

        await Promise.allSettled(syncPromises);
      } catch (error) {
        logger.error(QUICKBOOKS_ERROR_LOGS.PROCESS_ORGANIZATION_ERROR, {
          error: error.message,
          stack: error.stack,
          organizationId: org.id,
        });
        throw error;
      }
    }
  } catch (error) {
    logger.error(QUICKBOOKS_ERROR_LOGS.SYNC_ALL_ACCOUNTS_ERROR, {
      error: error.message,
      stack: error.stack,
    });
    throw error;
  }
};

export const getTables = async (req, res) => {
  logger.info(QUICKBOOKS_SUCCESS_LOGS.GETTING_MODELS_INFO);

  try {
    const modelsInfo = await quickbooksService.fetchAllModelsInfo();

    logger.info(
      `Successfully retrieved information for ${modelsInfo.totalModels} models`
    );

    return res.status(status.STATUS_CODE_SUCCESS).json(
      successResponse(QUICKBOOKS_MESSAGES.MODELS_INFO_FETCHED_SUCCESSFULLY, {
        modelsInfo,
      })
    );
  } catch (error) {
    logger.error(QUICKBOOKS_ERROR_LOGS.ERROR_GETTING_MODELS_INFO, {
      error: error.message,
    });
    return sendErrorResponse(res, error);
  }
};

const runProcessReportWithBody = (body, reportType) => {
  let result;
  const fakeReq = { body };
  const fakeRes = {
    _status: 200,
    status(s) {
      this._status = s;
      return this;
    },
    json(obj) {
      result = { status: this._status, ...obj };
    },
    send(obj) {
      result = { status: this._status, ...obj };
    },
  };
  return processReport(fakeReq, fakeRes, reportType).then(() => result);
};

export const syncAllReports = async (req, res) => {
  try {
    const { body } = req;
    const reportTypes = [
      HARDCODED_STRINGS.REPORT_TYPES.TRIAL_BALANCE,
      HARDCODED_STRINGS.REPORT_TYPES.PROFIT_AND_LOSS,
      HARDCODED_STRINGS.REPORT_TYPES.BALANCE_SHEET,
      HARDCODED_STRINGS.REPORT_TYPES.CASH_FLOW,
    ];

    const results = await Promise.allSettled(
      reportTypes.map((type) => runProcessReportWithBody(body, type))
    );

    const response = results.map((result, index) => ({
      reportType: reportTypes[index],
      ...("value" in result
        ? result.value
        : {
            success: false,
            error: result.reason?.message || ERROR_MESSAGES.UNKNOWN_ERROR,
          }),
    }));

    // Identify failed reports
    const failedReports = response.filter(
      (report) =>
        report.error ||
        (report.status !== status.STATUS_CODE_SUCCESS &&
          report.success === false)
    );

    const { organization_id, email } = body;

    // Send email notification only if there are failed reports
    if (failedReports.length > 0) {
      // Get error from the first failed report
      const firstFailedReport = failedReports[0];
      const error = firstFailedReport.error || firstFailedReport.message;

      await sendQuickBooksReportsSyncFailureEmail({
        error,
        organization_id,
      });

      if (failedReports.length === 4) {
        return res
          .status(status.STATUS_CODE_INTERNAL_SERVER_ERROR)
          .json(
            errorResponse(
              QUICKBOOKS_REPORTS_MESSAGES.REPORTS.ALL_REPORTS_FAILED
            )
          );
      }
    }

    if (organization_id) {
      try {
        logger.info(`Calculating EBITDA for organization: ${organization_id}`);

        const orgResult = await getOrganizationById(organization_id);
        const schemaName =
          orgResult.success && orgResult.data?.data?.schema_name
            ? orgResult.data.data.schema_name
            : null;

        await calculateEBITDA(null, schemaName);

        logger.info(
          `✅ EBITDA calculation completed for organization: ${organization_id}`
        );
      } catch (ebitdaError) {
        logger.error(
          `Failed to calculate EBITDA for organization ${organization_id}:`,
          {
            error: ebitdaError.message,
            stack: ebitdaError.stack,
          }
        );
      }
    }

    const reportTypeDisplayMap = {
      [HARDCODED_STRINGS.REPORT_TYPES.TRIAL_BALANCE]:
        HARDCODED_STRINGS.REPORT_TYPES.TRIAL_BALANCE_DISPLAY,
      [HARDCODED_STRINGS.REPORT_TYPES.PROFIT_AND_LOSS]:
        HARDCODED_STRINGS.REPORT_TYPES.PROFIT_LOSS_DISPLAY,
      [HARDCODED_STRINGS.REPORT_TYPES.BALANCE_SHEET]:
        HARDCODED_STRINGS.REPORT_TYPES.BALANCE_SHEET_DISPLAY,
      [HARDCODED_STRINGS.REPORT_TYPES.CASH_FLOW]:
        HARDCODED_STRINGS.REPORT_TYPES.CASH_FLOW_DISPLAY,
    };

    // const failedReports = response.filter(
    //   (r) => r.error || (r.status !== 200 && r.success === false)
    // );

    if (failedReports.length === 4) {
      throw new Error(QUICKBOOKS_REPORTS_MESSAGES.REPORTS.ALL_REPORTS_FAILED);
    }

    if (organization_id) {
      logger.info(
        `Updating QuickBooks sync timestamp for organization: ${organization_id}`
      );
      await updateOrganizationQbSyncTimestamp(organization_id, body.endDate);

      // Trigger finance PDF generation and storage after successful sync
      try {
        const endDateObj = new Date(body.endDate);
        const month = endDateObj.getUTCMonth() + 1;
        const year = endDateObj.getUTCFullYear();

        // Get organization details if needed
        const orgResult = await getOrganizationById(organization_id);
        const organization_name =
          orgResult.success && orgResult.data?.data?.organization_name
            ? orgResult.data.data.organization_name
            : null;

        const reportServiceUrl = process.env.REPORT_SERVICE_URL;
        if (reportServiceUrl && month && year) {
          logger.info(
            `Triggering finance PDF generation for organization: ${organization_id}, month: ${month}, year: ${year}`
          );

          const axios = (await import("axios")).default;
          const pdfResponse = await axios.get(
            `${reportServiceUrl}/report/finance/pdf`,
            {
              params: {
                organization_id,
                organization_name,
                month,
                year,
                store_to_blob: "true",
              },
              timeout: 60000, // 60 second timeout
            }
          );

          logger.info(
            `✅ Finance PDF generated and stored successfully for organization: ${organization_id}`
          );
        } else {
          logger.warn(
            "Skipping finance PDF generation due to missing configuration",
            { reportServiceUrl, organization_id, month, year }
          );
        }
      } catch (pdfError) {
        logger.error(
          `Failed to generate finance PDF for organization ${organization_id}:`,
          {
            error: pdfError.message,
            stack: pdfError.stack,
          }
        );
        // Don't throw - PDF generation failure should not block sync response
      }
    }

    if (failedReports.length > 0) {
      const failedReportNames = failedReports.map(
        (r) => reportTypeDisplayMap[r.reportType] || r.reportType
      );

      logger.info("Sending failure email notification", {
        email,
        failedReports: failedReportNames,
      });

      const recipientEmail =
        email || req.user?.email || process.env.NOTIFICATION_EMAIL;

      await sendQuickBooksReportsSyncFailureEmail({
        failedReportNames,
        recipientEmail,
      });
    }

    res.status(200).json({ reports: response });
  } catch (error) {
    logger.error(QUICKBOOKS_ERROR_LOGS.ERROR_SYNCING_REPORTS, {
      error: error.message,
    });

    const recipientEmail =
      req.user?.email ||
      req.body?.email ||
      process.env.NOTIFICATION_EMAIL ||
      process.env.ADMIN_EMAIL;

    await sendQuickBooksReportsSyncErrorEmail({
      error,
      organizationId: req.body?.organization_id,
      recipientEmail,
    });

    return sendErrorResponse(res, error);
  }
};

export default {
  addQuickbookAccount,
  listQuickbookAccounts,
  addTokens,
  getTokens,
  quickbooksFileSave,
  statusDisable,
  syncAllQuickBooksAccounts,
  getTables,
  syncAllReports,
};
