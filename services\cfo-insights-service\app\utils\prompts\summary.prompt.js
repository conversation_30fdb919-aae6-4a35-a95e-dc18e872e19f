/**
 * STEP 1: Financial Data Extraction Prompt
 * Extracts structured financial data as JSO<PERSON> from document context
 * PRIMARY PATH: Used in two-step summary generation
 */
export function buildFinancialDataPrompt(organization = "", service = "") {
  const org = organization || "The Organization";
  const serviceTitle =
    String(service || "").trim().length > 0
      ? `${String(service).trim().charAt(0).toUpperCase()}${String(service)
          .trim()
          .slice(1)}`
      : "Finance";

  return `You are a financial data extraction system for ${org}.

Extract all financial details from the provided context and output valid JSON only.

Use the organization name "${org}" directly in the executiveTakeaway field. DO NOT use placeholders like [ORGANIZATION_NAME] or [PERIOD] - use the actual organization name and period values.

{
  "period": "July 2025",
  "financials": {
    "Revenue": { "current": 328566, "momChange": "10.47%", "ytdTotal": 625980, "trend": "Positive" },
    "Expenses": { "current": 297177, "momChange": "8.79%", "ytdTotal": 570354, "trend": "Stable" },
    "Net Income": { "current": 31389, "momChange": "17.23%", "ytdTotal": 8191, "trend": "Positive" },
    "EBITDA": { "current": 3880, "momChange": "-9.02%", "ytdTotal": 130176, "trend": "Negative" },
    "Net Cashflow": { "current": 89435, "momChange": "119.52%", "ytdTotal": 130176, "trend": "Positive" },
    "Profit Margin": { "current": "9.55%", "momChange": "+1.4pp", "ytdTotal": "8.89%", "trend": "Positive" }
  },
  "keyInsights": [
    "Revenue rose 10.5% MoM led by strong performance of top doctors.",
    "Expenses grew 8.8% MoM due to payroll increases.",
    "Cash flow improved by 119%.",
    "Profit margin improved from 8.15% to 9.55%.",
    "Leverage remains high at 86.8% of assets."
  ],
  "recommendations": [
    { "focusArea": "Revenue Optimization", "recommendation": "Expand telehealth & diagnostic services.", "impact": "+5–8% growth" },
    { "focusArea": "Cost Efficiency", "recommendation": "Review staffing and automate billing.", "impact": "-5% cost" },
    { "focusArea": "Liquidity", "recommendation": "Maintain 3–6 months of cash reserves.", "impact": "Improved solvency" }
  ],
  "executiveTakeaway": "The organization showed robust revenue growth and improved profitability in the reporting period. Focus on sustaining top-line momentum, tightening payroll controls, and strengthening equity to support continued growth and resilience."
}

Output must be valid JSON — no markdown, no extra text.`;
}

/**
 * FINAL STEP: Comprehensive JSON Formatting Prompt for HTML Conversion
 * Purpose: Converts raw financial data (extracted in a prior step) into the EXACT,
 * pre-formatted JSON structure required by the frontend for direct HTML rendering.
 * Output: ONLY valid, minified JSON.
 * Audience: Expert Developer/AI Model (assumes LLM can perform complex data transformation).
 */
export function buildFinalHTMLReadyJSONPrompt(organization = "CHP", service = "") {
  const org = organization || "The Organization";
  const serviceTitle =
    String(service || "").trim().length > 0
      ? `${String(service).trim().charAt(0).toUpperCase()}${String(service)
          .trim()
          .slice(1)}`
      : "Finance";
  // Abbreviation logic from original prompt is sound for the title.
  const orgAbbr = org.length > 20 ? org.split(" ").map(w => w[0]).join("") : org; 

  return `You are a dedicated JSON formatter and transformer for financial reporting. Your task is to accept structured financial data and convert it into a hierarchical JSON format suitable for direct HTML rendering, adhering to all specified rules and formatting requirements.

CRITICAL INSTRUCTION: Output ONLY valid, minified JSON. Do NOT include markdown, code blocks (\`\`\`), or any explanatory text.

### DATA SOURCE & CONTEXT

The input context contains the financial data for the period, including detailed metrics, key insights, strategic recommendations, and an executive takeaway. All data originates from the organization "${org}" and the reporting period (e.g., "July 2025").

### OUTPUT STRUCTURE (MANDATORY)

The output JSON **MUST** contain the following top-level keys and the **EXACT** nested structure:

{
  "title": "[ORG] ${serviceTitle} Dashboard - Executive Insights & Recommendations ([PERIOD])",
  "sections": [
    // 4 mandatory sections in this exact order
    { "heading": "${serviceTitle} Highlights", "type": "table", "data": { "headers": [...], "rows": [...] } },
    { "heading": "Key Insights", "type": "list", "data": [...] },
    { "heading": "Strategic Recommendations", "type": "table", "data": { "headers": [...], "rows": [...] } },
    { "heading": "Executive Takeaway", "type": "paragraph", "data": "..." }
  ]
}

### FORMATTING RULES (STRICT ADHERENCE REQUIRED)

#### 1. Financial Highlights Table (type: "table")

* **Headers (5 Columns):** ["Metric", "[PERIOD]", "MoM Change", "YTD Total", "Trend"] (e.g., "July 2025")

* **Rows (6 Metrics):** Must include **Revenue, Expenses, Net Income, EBITDA, Net Cashflow, Profit Margin** in this order.

* **Currency:** Use **K-notation** ($X.XK or $X.XXK).
    * Example: 328566 becomes **$328.6K**. 625980 becomes **$625.98K**.
    * Example: 3880 becomes **$3.88K**.

* **MoM Change Column (CRITICAL STRING FORMATS):**
    * Positive Currency: **"Up 10.47%"** (with space).
    * Negative Currency: **"Down 9.99%"** (with space).
    * Net Income: **"Up 17.23% Margin"** (append " Margin").
    * Profit Margin: **"+1.4 pp vs LM"** (use "+" prefix and "pp vs LM" suffix).

* **YTD Total Column:**
    * Profit Margin: **"8.89% YTD"** (append " YTD").
    * Missing EBITDA YTD data must be represented by an **empty string (\`"\`\`"\`)**.

#### 2. Key Insights Section (type: "list")

* **Data:** Array of **5 detailed strings**.
* **Content:** Must be analytical, incorporating **specific numbers, percentages, and context** (e.g., doctor names, 61% payroll, 86.8% leverage) as found in the source document.
    * *Example Insight:* "Revenue rose 10.5% MoM, led by strong performance from Dr. Ghosh (21%) and Dr. Canner (17%)."

#### 3. Strategic Recommendations Table (type: "table")

* **Headers (3 Columns):** ["Focus Area", "Recommendation", "Expected Impact"]
* **Rows (5 Recommendations):** Must include the five strategic areas (Revenue Optimization, Cost Efficiency, Profit Improvement, Liquidity Management, Capital Structure).
* **Recommendation Content:** Must be **detailed, multi-sentence recommendations** separated by semicolons (\`;\`).
    * *Example Recommendation:* "Incentivize underperforming doctors; expand telehealth/diagnostic services to capture new segments and maximize clinician utilization."

#### 4. Executive Takeaway (type: "paragraph")

* **Data:** Single string (2-3 sentences).
* **Content:** Must use the **actual organization name** ("${org}") and the period (e.g., "July 2025") directly within the text.

### EXAMPLE OUTPUT TEMPLATE (Reference for Structure and Formatting)

\`\`\`json
{
  "title": "${orgAbbr} ${serviceTitle} Dashboard - Executive Insights & Recommendations (July 2025)",
  "sections": [
    {
      "heading": "${serviceTitle} Highlights",
      "type": "table",
      "data": {
        "headers": ["Metric", "July 2025", "MoM Change", "YTD Total", "Trend"],
        "rows": [
          ["Revenue", "$328.6K", "Up 10.47%", "$625.98K", "Positive"],
          ["Expenses", "$297.2K", "Up 8.79%", "$570.35K", "Stable"],
          ["Net Income", "$31.4K", "Up 17.23% Margin", "$8.19K", "Positive"],
          ["EBITDA", "$3.88K", "Down 9.99%", "", "Negative"], 
          ["Net Cashflow", "$89.4K", "Up 119.52%", "$130.18K", "Positive"],
          ["Profit Margin", "9.55%", "+1.4 pp vs LM", "8.89% YTD", "Positive"]
        ]
      }
    },
    {
      "heading": "Key Insights",
      "type": "list",
      "data": [
        "Revenue rose 10.5% MoM, led by strong performance from Dr. Ghosh (21%) and Dr. Canner (17%).",
        "Expenses grew 8.8% MoM, mainly from payroll (61% of total). Staff cost optimization needed.",
        "Net cashflow up 119% to $89K; liquidity remains strong with ending cash around $699K.",
        "Profit margin increased from 8.15% to 9.55% as revenue growth outpaced cost escalation.",
        "Leverage remains high at 86.8% of assets; reducing liabilities would strengthen solvency."
      ]
    },
    {
      "heading": "Strategic Recommendations",
      "type": "table",
      "data": {
        "headers": ["Focus Area", "Recommendation", "Expected Impact"],
        "rows": [
          ["Revenue Optimization", "Incentivize underperforming doctors; expand telehealth/diagnostic services to capture new segments and maximize clinician utilization.", "****% revenue growth"],
          ["Cost Efficiency", "Review staffing patterns, optimize shifts, and automate billing/admin workflows to reduce redundant hours and manual processing delays.", "Reduce payroll share to ~55%"],
          ["Profit Improvement", "Perform monthly margin benchmarking against prior months and implement action plans for underperforming cost centers.", "Maintain >10% profit margin"],
          ["Liquidity Management", "Preserve 3-6 months of cash (~$900K); centralize cash forecasting and monitor timing of large payables.", "Operational buffer strength"],
          ["Capital Structure", "Gradually reduce short-term liabilities and prioritize building retained earnings to lower leverage and improve solvency ratios.", "Lower leverage, improved solvency"]
        ]
      }
    },
    {
      "heading": "Executive Takeaway",
      "type": "paragraph",
      "data": "${orgAbbr} showed robust revenue growth and improved profitability in July 2025. Focus on sustaining top-line momentum, tightening payroll controls, and strengthening equity to support continued growth and resilience."
    }
  ]
}
\`\`\`

Based on the uploaded image and the specified rules, output the final, valid JSON object.`;
}

/**
 * JSON Format Prompt: Returns structured JSON instead of HTML
 * Used when JSON format is required for API responses
 * This is an alias for buildFinalHTMLReadyJSONPrompt for backward compatibility
 */
export function buildFinancialJSONPrompt(organization = "", service = "") {
  return buildFinalHTMLReadyJSONPrompt(organization, service);
}

/**
 * FALLBACK: Single-step summary prompt (used only if two-step approach fails)
 * This generates JSON format directly from document context
 * @param {string} organization - Organization name
 * @returns {string} - Prompt text for JSON generation
 */
export function buildSummarySystemPrompt(organization = "", service = "") {
  const org = organization || "The Organization";
  const serviceTitle =
    String(service || "").trim().length > 0
      ? `${String(service).trim().charAt(0).toUpperCase()}${String(service)
          .trim()
          .slice(1)}`
      : "Finance";
  const orgAbbr = org.length > 20 ? org.split(" ").map(w => w[0]).join("") : org;
  
  return `You are a financial intelligence reporting system for ${org}.

Generate a professional JSON financial summary report from the provided document context.

CRITICAL REQUIREMENTS:
- Output ONLY valid JSON - no markdown, no code blocks, no explanations
- Extract period from document (e.g., "July 2025")
- Use organization name: ${org}
- Format numbers: use K notation for thousands ($328.6K), include $ signs

OUTPUT FORMAT (MUST MATCH THIS EXACT STRUCTURE):
{
  "title": "${orgAbbr} ${serviceTitle} Dashboard - Executive Insights & Recommendations ([PERIOD])",
  "sections": [
    {
      "heading": "Financial Highlights",
      "type": "table",
      "data": {
        "headers": ["Metric", "[PERIOD]", "MoM Change", "YTD Total", "Trend"],
        "rows": [
          ["Revenue", "$328.6K", "Up 10.47%", "$625.98K", "Positive"],
          ["Expenses", "$297.2K", "Up 8.79%", "$570.35K", "Stable"],
          ["Net Income", "$31.4K", "Up 17.23% Margin", "$8.19K", "Positive"],
          ["EBITDA", "$3.88K", "Down 9.99%", "", "Negative"],
          ["Net Cashflow", "$89.4K", "Up 119.52%", "$130.18K", "Positive"],
          ["Profit Margin", "9.55%", "+1.4 pp vs LM", "8.89% YTD", "Positive"]
        ]
      }
    },
    {
      "heading": "Key Insights",
      "type": "list",
      "data": [
        "Revenue rose 10.5% MoM, led by strong performance from Dr. Ghosh (21%) and Dr. Canner (17%).",
        "Expenses grew 8.8% MoM, mainly from payroll (61% of total). Staff cost optimization needed.",
        "Net cashflow up 119% to $89K; liquidity remains strong with ending cash around $699K.",
        "Profit margin increased from 8.15% to 9.55% as revenue growth outpaced cost escalation.",
        "Leverage remains high at 86.8% of assets; reducing liabilities would strengthen solvency."
      ]
    },
    {
      "heading": "Strategic Recommendations",
      "type": "table",
      "data": {
        "headers": ["Focus Area", "Recommendation", "Expected Impact"],
        "rows": [
          ["Revenue Optimization", "Incentivize underperforming doctors; expand telehealth/diagnostic services to capture new segments and maximize clinician utilization.", "****% revenue growth"],
          ["Cost Efficiency", "Review staffing patterns, optimize shifts, and automate billing/admin workflows to reduce redundant hours and manual processing delays.", "Reduce payroll share to ~55%"]
        ]
      }
    },
    {
      "heading": "Executive Takeaway",
      "type": "paragraph",
      "data": "${orgAbbr} showed robust revenue growth and improved profitability in [PERIOD]. Focus on sustaining top-line momentum, tightening payroll controls, and strengthening equity to support continued growth and resilience."
    }
  ]
}

REQUIRED SECTIONS (ALL 4 MUST BE PRESENT):
1. Financial Highlights (type: "table") - 6 metrics: Revenue, Expenses, Net Income, EBITDA, Net Cashflow, Profit Margin
2. Key Insights (type: "list") - 3-6 detailed insights with specific numbers and context
3. Strategic Recommendations (type: "table") - Focus Area, Recommendation, Expected Impact
4. Executive Takeaway (type: "paragraph") - 2-3 sentence summary

MoM Change formatting:
- Positive: "Up 10.47%" (prefix with "Up ")
- Negative: "Down 9.99%" (prefix with "Down ")
- Net Income: "Up 17.23% Margin" (add " Margin" suffix)
- Profit Margin: "+1.4 pp vs LM" (use "+" prefix, "pp vs LM" suffix)

YTD Total formatting:
- Currency: $625.98K
- Profit Margin: "8.89% YTD" (add " YTD" suffix)
- Empty if not available: "" (empty string)

Output ONLY valid JSON - no explanations.`;
}
