import FinanceReportService from "../services/finance_report.service.js";
import { createLogger } from "../utils/logger.utils.js";
import { LOGGER_NAMES } from "../utils/constants/log.constants.js";
import {
  validateRequiredParams,
  handleControllerError,
} from "../utils/controller.utils.js";
import { storePdfToFileService } from "../utils/pdf-storage.utils.js";

const logger = createLogger(LOGGER_NAMES.REPORT_CONTROLLER);
const MONTHS = [
  "January",
  "February",
  "March",
  "April",
  "May",
  "June",
  "July",
  "August",
  "September",
  "October",
  "November",
  "December",
];

const generateFinancePDF = async (req, res) => {
  try {
    const { organization_id, organization_name, month, year, store_to_blob } =
      req.query;

    const validationError = validateRequiredParams(req.query, [
      "organization_id",
      "month",
      "year",
    ]);
    if (validationError) return res.status(400).json(validationError);

    logger.info(
      `Generating finance PDF: org=${organization_id}, month=${month}, year=${year}`
    );

    const pdf = await FinanceReportService.generateFinanceReportPDF({
      organization_id,
      organization_name,
      month,
      year,
    });

    const filename = `Finance_Report_${
      MONTHS[parseInt(month) - 1]
    }_${year}.pdf`;

    // Store PDF to blob storage if requested
    if (store_to_blob === "true") {
      logger.info(
        `Storing finance PDF to blob storage: org=${organization_id}`
      );
      const storageResult = await storePdfToFileService({
        pdfBuffer: pdf,
        organization_id,
        organization_name,
        service: "finance",
        month: parseInt(month),
        year: parseInt(year),
        fileName: filename,
        refreshToken: req.cookies?.refresh_token,
      });

      if (storageResult.success) {
        logger.info(
          `Finance PDF stored successfully: ${storageResult.data?.blobPath}`
        );
      } else {
        logger.warn(
          `Failed to store finance PDF to blob: ${storageResult.error}`
        );
      }
    }

    res.setHeader("Content-Type", "application/pdf");
    res.setHeader("Content-Disposition", `attachment; filename="${filename}"`);
    res.send(pdf);
  } catch (error) {
    logger.error("Error generating finance PDF:", error);
    handleControllerError(error, res, "Error generating finance report PDF");
  }
};

export default { generateFinancePDF };
