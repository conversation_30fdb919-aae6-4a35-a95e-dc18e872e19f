import React from "react";
import { <PERSON>, <PERSON>, Trash2, <PERSON><PERSON><PERSON><PERSON><PERSON>, Loader2 } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "../ui/dropdown-menu";
import { But<PERSON> } from "../ui/button";
import "@/styles/listing.css";

const getIcon = (iconName) => {
  const iconProps = { className: "h-4 w-4" };
  switch (iconName) {
    case "edit":
      return <Edit {...iconProps} />;
    case "view":
      return <Eye {...iconProps} />;
    case "delete":
      return <Trash2 {...iconProps} />;
    default:
      return null;
  }
};

export default function TableActions({
  actions = [],
  item,
  className = "",
  variant = "dropdown", // "buttons" or "dropdown" - default to dropdown for cleaner UI
}) {
  const getButtonClass = React.useCallback((label, variant) => {
    if (label?.includes("Dashboard") || label?.includes("View Details"))
      return "listing-action-button listing-action-button-dashboard listing-action-button-small";
    if (label?.includes("Book-closure") || label?.includes("Book Closure"))
      return "listing-action-button listing-action-button-bookclosure";
    return variant === "outline"
      ? "listing-action-button listing-action-button-outline listing-action-button-small"
      : "listing-action-button listing-action-button-default listing-action-button-small";
  }, []);

  const handleAction = (action, item) => {
    if (action.onClick) {
      action.onClick(item);
    }
  };

  if (variant === "dropdown") {
    const hasLoading = actions.some((action) => action.loading);

    return (
      <div className={className}>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="sm" disabled={hasLoading}>
              {hasLoading ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <MoreHorizontal className="h-4 w-4" />
              )}
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            {actions.map((action, index) => {
              const isLoading = action.loading || false;

              return (
                <DropdownMenuItem
                  key={index}
                  onClick={() => !isLoading && handleAction(action, item)}
                  className={action.className || ""}
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  ) : action.icon ? (
                    <span className="mr-2">
                      {typeof action.icon === "string"
                        ? getIcon(action.icon)
                        : action.icon}
                    </span>
                  ) : null}
                  {action.label}
                </DropdownMenuItem>
              );
            })}
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    );
  }

  return (
    <div className={`listing-table-actions-container ${className}`}>
      {actions.map((action, index) => {
        const isLoading = action.loading || false;
        const isDisabled = action.disabled || isLoading;
        const buttonClass = getButtonClass(action.label, action.variant);

        return (
          <button
            key={index}
            onClick={() => !isDisabled && handleAction(action, item)}
            className={`${buttonClass} ${action.className || ""}`}
            disabled={isDisabled}
            title={action.title || action.label}
          >
            {isLoading ? (
              <Loader2 className="listing-action-button-icon animate-spin" />
            ) : action.icon ? (
              <span className="listing-action-button-icon">
                {typeof action.icon === "string"
                  ? getIcon(action.icon)
                  : action.icon}
              </span>
            ) : null}
            <span className="listing-action-button-text">{action.label}</span>
          </button>
        );
      })}
    </div>
  );
}
