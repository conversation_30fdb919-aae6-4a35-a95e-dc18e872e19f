"use client";

import React, { useState, useRef, useEffect } from "react";
import { ChevronDown, Check } from "lucide-react";
import "@/styles/dashboard.css";

export default function AnimatedSelect({
  options = [],
  value,
  onChange,
  placeholder = "Select...",
  className = "",
}) {
  const [isOpen, setIsOpen] = useState(false);
  const [highlightedIndex, setHighlightedIndex] = useState(-1);
  const selectRef = useRef(null);
  const dropdownRef = useRef(null);

  const selectedOption = options.find((opt) => opt.value === value);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        selectRef.current &&
        !selectRef.current.contains(event.target) &&
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target)
      ) {
        setIsOpen(false);
        setHighlightedIndex(-1);
      }
    };

    if (isOpen) {
      document.addEventListener("mousedown", handleClickOutside);
      return () => document.removeEventListener("mousedown", handleClickOutside);
    }
  }, [isOpen]);

  // Scroll to selected option when dropdown opens
  useEffect(() => {
    if (isOpen && dropdownRef.current && value) {
      const selectedIndex = options.findIndex((opt) => opt.value === value);
      if (selectedIndex >= 0) {
        const optionElement = dropdownRef.current.children[selectedIndex];
        if (optionElement) {
          // Small delay to ensure dropdown is rendered
          setTimeout(() => {
            optionElement.scrollIntoView({ block: "nearest", behavior: "smooth" });
            setHighlightedIndex(selectedIndex);
          }, 50);
        }
      }
    }
  }, [isOpen, value, options]);

  useEffect(() => {
    if (isOpen && highlightedIndex >= 0 && highlightedIndex < options.length) {
      const optionElement = dropdownRef.current?.children[highlightedIndex];
      if (optionElement) {
        optionElement.scrollIntoView({ block: "nearest", behavior: "smooth" });
      }
    }
  }, [highlightedIndex, isOpen, options.length]);

  const handleSelect = (option) => {
    onChange?.(option.value);
    setIsOpen(false);
    setHighlightedIndex(-1);
  };

  const handleKeyDown = (e) => {
    if (!isOpen) {
      if (e.key === "Enter" || e.key === " " || e.key === "ArrowDown") {
        e.preventDefault();
        setIsOpen(true);
        return;
      }
    }

    if (e.key === "Escape") {
      setIsOpen(false);
      setHighlightedIndex(-1);
      return;
    }

    if (e.key === "ArrowDown") {
      e.preventDefault();
      setHighlightedIndex((prev) =>
        prev < options.length - 1 ? prev + 1 : 0
      );
      return;
    }

    if (e.key === "ArrowUp") {
      e.preventDefault();
      setHighlightedIndex((prev) =>
        prev > 0 ? prev - 1 : options.length - 1
      );
      return;
    }

    if (e.key === "Enter" && highlightedIndex >= 0) {
      e.preventDefault();
      handleSelect(options[highlightedIndex]);
      return;
    }
  };

  return (
    <div className={`animated-select-wrapper ${className}`}>
      <button
        ref={selectRef}
        type="button"
        onClick={() => setIsOpen(!isOpen)}
        onKeyDown={handleKeyDown}
        className={`animated-select-trigger ${isOpen ? "animated-select-trigger--open" : ""}`}
        aria-expanded={isOpen}
        aria-haspopup="listbox"
      >
        <span className="animated-select-value">
          {selectedOption ? selectedOption.label : placeholder}
        </span>
        <ChevronDown
          className={`animated-select-chevron ${isOpen ? "animated-select-chevron--open" : ""}`}
        />
      </button>

      {isOpen && (
        <div
          ref={dropdownRef}
          className="animated-select-dropdown"
          role="listbox"
        >
          {options.map((option, index) => {
            const isSelected = option.value === value;
            const isHighlighted = index === highlightedIndex;

            return (
              <div
                key={option.value}
                role="option"
                aria-selected={isSelected}
                onClick={() => handleSelect(option)}
                onMouseEnter={() => setHighlightedIndex(index)}
                className={`animated-select-option ${
                  isSelected ? "animated-select-option--selected" : ""
                } ${isHighlighted ? "animated-select-option--highlighted" : ""}`}
              >
                <span className="animated-select-option-label">
                  {option.label}
                </span>
                {isSelected && (
                  <Check className="animated-select-check-icon" />
                )}
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
}

