import TaxBreakdownService from "../services/tax_breakdown.service.js";
import { LOGGER_NAMES } from "../utils/constants/log.constants.js";
import { createLogger } from "../utils/logger.utils.js";
import { validateRequiredParams, handleControllerError, sendSuccessResponse } from "../utils/controller.utils.js";

const logger = createLogger(LOGGER_NAMES.REPORT_CONTROLLER);

/**
 * Get tax breakdown data for organization by month and year
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getTaxBreakdown = async (req, res) => {
  try {
    const { organization_id, month, year } = req.query;

    logger.info(
      `Fetching tax breakdown for org: ${organization_id}, month: ${month}, year: ${year}`
    );

    // Validate required parameters
    const validationError = validateRequiredParams(req.query, ['organization_id', 'month', 'year']);
    if (validationError) {
      return res.status(400).json(validationError);
    }

    // Fetch tax breakdown data
    const taxBreakdownData = await TaxBreakdownService.getTaxBreakdownData({
      organization_id,
      month,
      year,
    });

    // Return successful response
    sendSuccessResponse(res, "Tax breakdown fetched successfully", taxBreakdownData);
  } catch (error) {
    logger.error("Error fetching tax breakdown:", error);
    handleControllerError(error, res, "Error fetching tax breakdown data");
  }
};

export default {
  getTaxBreakdown,
};
