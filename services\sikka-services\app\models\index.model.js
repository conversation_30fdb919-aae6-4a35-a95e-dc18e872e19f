import sequelize from "../../config/postgres.js";
import RequestKeyModel from "./tables/sikka.model.js";
import AccountsReceivableModel from "./tables/account_receivables.model.js";
import NewPatientsModel from "./tables/new_patient.model.js";
import TreatmentPlanAnalysisModel from "./tables/treatment_plan.model.js";
import ProductionPerDayModel from "./tables/total_production_per_day.model.js";
import ProductionByDentistModel from "./tables/total_production_by_dentist.js";
import ProductionByHygienistModel from "./tables/total_production_by_hygienist.js";
import AvgDailyProductionModel from "./tables/avg_daily_production.model.js";
import NoShowAppointmentsModel from "./tables/no_show_appointment.model.js";
import HygieneReappointmentModel from "./tables/hygiene_reappointment.model.js";
import DirectRestorationsModel from "./tables/direct_restorations.model.js";
import GrossCollectionModel from "./tables/gross_collection.model.js";

const models = {
  sikka_request_key: RequestKeyModel(sequelize),
  sikka_accounts_receivable: AccountsReceivableModel(sequelize),
  sikka_new_patients: NewPatientsModel(sequelize),
  sikka_treatment_plan_analysis: TreatmentPlanAnalysisModel(sequelize),
  sikka_total_production_per_day: ProductionPerDayModel(sequelize),
  sikka_total_production_by_dentist: ProductionByDentistModel(sequelize),
  sikka_total_production_by_hygienist: ProductionByHygienistModel(sequelize),
  sikka_avg_daily_production: AvgDailyProductionModel(sequelize),
  sikka_no_show_appointments: NoShowAppointmentsModel(sequelize),
  sikka_hygiene_reappointment: HygieneReappointmentModel(sequelize),
  sikka_direct_restoration: DirectRestorationsModel(sequelize),
  sikka_gross_collections: GrossCollectionModel(sequelize),
};

// Set up associations
Object.keys(models).forEach((modelName) => {
  if (models[modelName].associate) {
    models[modelName].associate(models);
  }
});

// Export individual models for convenience
export const {
  sikka_request_key: RequestKey,
  sikka_accounts_receivable: AccountsReceivable,
  sikka_new_patients: NewPatients,
  sikka_treatment_plan_analysis: TreatmentPlanAnalysis,
  sikka_total_production_per_day: TotalProductionPerDay,
  sikka_total_production_by_dentist: TotalProductionByDentist,
  sikka_total_production_by_hygienist: TotalProductionByHygienist,
  sikka_avg_daily_production: AvgDailyProduction,
  sikka_no_show_appointments: NoShowAppointments,
  sikka_hygiene_reappointment: HygieneReappointment,
  sikka_direct_restoration: DirectRestorations,
  sikka_gross_collections: GrossCollections,
} = models;

// Export all models and sequelize instance
export { sequelize };
export default models;
