// app/utils/blobClient.js
import { BlobServiceClient } from "@azure/storage-blob";
import { httpGet } from "../../../../shared/utils/axios.util.js";
import dotenv from "dotenv";
import { ERROR_MESSAGES } from "./constants/error.constants.js";
import { TIMING_CONSTANTS } from "./constants/timing.constants.js";
dotenv.config();

let containerClient = null;

function sanitizeConnectionString(connectionString) {
  const trimmed = connectionString.trim();

  // If the value does not look like a key/value connection string, return trimmed as-is.
  if (!trimmed.includes("=")) {
    return trimmed;
  }

  const parts = trimmed
    .split(";")
    .map((part) => part.trim())
    .filter(Boolean)
    .map((part) => {
      const separatorIndex = part.indexOf("=");
      if (separatorIndex === -1) {
        return part;
      }

      const key = part.slice(0, separatorIndex);
      const value = part.slice(separatorIndex + 1).trim();

      if (key.toLowerCase() === "endpointsuffix") {
        const normalized = value
          .replace(/^https?:\/\//i, "")
          .replace(/\/+$/g, "");

        if (!normalized) {
          throw new Error(
            `${ERROR_MESSAGES.GENERAL.AZURE_CONNECTION_FAILED}: Invalid EndpointSuffix in AZURE_BLOB_CONNECTION_STRING.`
          );
        }

        return `${key}=${normalized}`;
      }

      return `${key}=${value}`;
    });

  return parts.join(";");
}

/**
 * Get or create the Azure Blob Storage container client.
 * Lazy-loaded to ensure environment variables are available.
 */
function getContainerClient() {
  if (!containerClient) {
    const rawConnectionString = process.env.AZURE_BLOB_CONNECTION_STRING;
    const containerName = process.env.AZURE_BLOB_CONTAINER_NAME;

    if (!rawConnectionString) {
      throw new Error(ERROR_MESSAGES.GENERAL.AZURE_CONNECTION_STRING_MISSING);
    }
    if (!containerName) {
      throw new Error(ERROR_MESSAGES.GENERAL.AZURE_CONTAINER_NAME_MISSING);
    }

    const connectionString = sanitizeConnectionString(rawConnectionString);

    try {
      const blobService = BlobServiceClient.fromConnectionString(
        connectionString
      );
      containerClient = blobService.getContainerClient(containerName);
    } catch (error) {
      const baseMessage = `${ERROR_MESSAGES.GENERAL.AZURE_CONNECTION_FAILED}: ${error?.message || "Unknown Azure error."}`;
      const wrappedError = new Error(baseMessage);
      wrappedError.cause = error;
      throw wrappedError;
    }
  }
  return containerClient;
}

/**
 * Convert a blob stream to a Buffer.
 */
async function streamToBuffer(stream) {
  return new Promise((resolve, reject) => {
    const chunks = [];
    stream.on("data", (d) => chunks.push(d));
    stream.on("end", () => resolve(Buffer.concat(chunks)));
    stream.on("error", reject);
  });
}

/**
 * Download a PDF from Azure Blob Storage as a buffer.
 */
export async function downloadPdfBlob(filename) {
  const client = getContainerClient();
  const blobClient = client.getBlobClient(filename);
  const response = await blobClient.download();
  return await streamToBuffer(response.readableStreamBody);
}

/**
 * Download any blob (e.g., JSON) as a buffer.
 */
export async function downloadBlobBuffer(filename) {
  const client = getContainerClient();
  const blobClient = client.getBlobClient(filename);
  const response = await blobClient.download();
  return await streamToBuffer(response.readableStreamBody);
}

/**
 * Download a PDF from a SAS URL.
 */
export async function downloadPdfFromUrl(url) {
  try {
    const response = await httpGet(url, {
      responseType: "arraybuffer",
      timeout: TIMING_CONSTANTS.PDF_DOWNLOAD_MS,
    });

    if (response.status !== 200) {
      throw new Error(
        `${ERROR_MESSAGES.GENERAL.DOWNLOAD_HTTP_FAILED}: HTTP ${response.status}`
      );
    }

    return Buffer.from(response.data);
  } catch (error) {
    throw new Error(
      `${ERROR_MESSAGES.GENERAL.DOWNLOAD_FAILED}: ${error.message}`
    );
  }
}

/**
 * List all PDF files in the container.
 */
export async function listBlobFiles({ suffix = ".pdf" } = {}) {
  const client = getContainerClient();
  const items = [];
  for await (const blob of client.listBlobsFlat()) {
    if (!suffix || blob.name.toLowerCase().endsWith(suffix)) {
      items.push({
        name: blob.name,
        size: blob.properties.contentLength ?? 0,
        lastModified: blob.properties.lastModified ?? null
      });
    }
  }

  // Sort newest first
  return items.sort(
    (a, b) => new Date(b.lastModified) - new Date(a.lastModified)
  );
}
