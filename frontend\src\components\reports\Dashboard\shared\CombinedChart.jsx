"use client";

import ReactEcharts from "echarts-for-react";

/**
 * Truncate long text with ellipsis
 * @param {string} text - Text to truncate
 * @param {number} maxLength - Maximum character length
 * @returns {string} Truncated text
 */
const truncateText = (text, maxLength = 12) => {
  if (!text || typeof text !== "string") return text;
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength - 1) + "…";
};

/**
 * CombinedChart supports multiple bar series for category comparison.
 * Props:
 * - categories: string[]
 * - series: [{ name: string, data: number[], color?: string }]
 */
export default function CombinedChart({
  categories = [],
  series = [],
  valueFormatter,
}) {
  const formatValue = (val) => (valueFormatter ? valueFormatter(val) : val);
  const formatAxis = (val) => formatValue(val);

  // Formatter for category labels - truncate long names
  const formatCategoryLabel = (val) => truncateText(String(val), 14);

  const formatTooltip = (params) => {
    if (!valueFormatter) return undefined;
    if (!Array.isArray(params)) {
      // Show full category name in tooltip
      const fullCategoryName = categories[params?.dataIndex] || params?.name;
      return `${fullCategoryName}: ${valueFormatter(params?.value)}`;
    }
    return params
      .map(
        (item) =>
          `${item.marker}${item.seriesName || ""}: ${valueFormatter(item.value)}`
      )
      .join("<br/>");
  };

  const option = {
    tooltip: { trigger: "axis", formatter: formatTooltip },
    legend: {
      data: series.map((s) => s.name),
      top: 0,
      icon: "circle",
      itemWidth: 10,
      itemHeight: 10,
      textStyle: { color: "#364253ff" },
    },
    grid: { left: 40, right: 16, top: 54, bottom: 50 }, // Increased bottom for rotated labels
    xAxis: {
      type: "category",
      data: categories,
      axisTick: { alignWithLabel: true },
      axisLine: { lineStyle: { color: "#cbd5e1" } },
      axisLabel: {
        formatter: formatCategoryLabel,
        interval: 0,
        rotate: categories.some((c) => String(c).length > 10) ? 30 : 0,
        fontSize: 11,
      },
    },
    yAxis: {
      type: "value",
      axisLine: { lineStyle: { color: "#cbd5e1" } },
      splitLine: { lineStyle: { color: "#e2e8f0" } },
      axisLabel: { formatter: formatAxis },
    },
    series: series.map((s) => ({
      name: s.name,
      type: "bar",
      data: s.data,
      barMaxWidth: 35,
      barCategoryGap: "55%",
      barGap: "55%",
      label: {
        show: true,
        position: "top",
        formatter: ({ value }) => formatValue(value),
      },
      labelLayout: { hideOverlap: true, moveOverlap: "shiftY" },
      itemStyle: s.color ? { color: s.color } : undefined,
      emphasis: { focus: "series" },
    })),
  };

  return (
    <div className="h-80">
      <ReactEcharts style={{ height: "100%" }} option={option} />
    </div>
  );
}
