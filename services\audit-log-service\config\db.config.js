import { Sequelize } from "sequelize";
import dotenv from "dotenv";

dotenv.config();

const sequelize = new Sequelize(
  process.env.DB_NAME || "cpa_dash",
  process.env.DB_USER || "postgres",
  process.env.DB_PASS || "Admin@12345",
  {
    host: process.env.DB_HOST || "localhost",
    dialect: "postgres",
    logging: false, // disable SQL logs
  }
);

export const connectDB = async () => {
  try {
    await sequelize.authenticate();
    console.log("✅ PostgreSQL connected successfully");
  } catch (error) {
    console.error("❌ Unable to connect to PostgreSQL:", error.message);
    process.exit(1);
  }
};

// config/db.config.js
export const POSTGRES_CONFIG = {
  HOST: process.env.PG_HOST || "localhost",
  PORT: process.env.PG_PORT || 5432,
  USER: process.env.PG_USER || "postgres",
  PASSWORD: process.env.PG_PASSWORD || "Admin@12345",
  DATABASE: process.env.PG_DATABASE || "cpa_dash",
};

export default sequelize;
