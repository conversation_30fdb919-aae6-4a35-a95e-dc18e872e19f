import * as status from "../utils/status_code.utils.js";
import logger from "../../config/logger.config.js";
import { QUICKBOOKS_REPORTS_LOGS } from "../utils/constants/log.constants.js";
import { HARDCODED_STRINGS } from "../utils/constants/strings.constants.js";
import axios from "axios";
import { decrypt } from "../utils/encryption.utils.js";
import {
  formatDateOnly,
  createValidatedDateRange,
} from "../utils/date.utils.js";
import reportsRepository from "../repositories/reports.repository.js";
import { getTokensDirectly } from "./quickbooks.service.js";
import {
  processPnLData,
  getPnLReportsByRealmId as getPnLReports,
} from "../utils/pnl.utils.js";
import { insertLines as insertTrialBalanceLines } from "../utils/trial-balance.utils.js";
import {
  processBalanceSheetData,
  getBalanceSheetReportsByRealmId as getBalanceSheetReports,
} from "../utils/balance-sheet.utils.js";
import {
  processCashFlowData,
  getCashFlowReportsByRealmId as getCashFlowReports,
} from "../utils/cash-flow.utils.js";
import { getQuickbooksApiUrl } from "../../config/quickbooks.config.js";

const BATCH_SIZE = 1000;

// API Configuration
const API_CONFIG = {
  headers: { Accept: "application/json" },
  timeout: 30000,
  retries: 1,
};

// Helper functions for cleaner code

const createEndpoint = (realmId, reportName, startDate, endDate) =>
  `/v3/company/${realmId}/reports/${reportName}?start_date=${startDate}&end_date=${endDate}&summarize_column_by=Month`;

const createAuthHeaders = (token) => ({
  ...API_CONFIG.headers,
  Authorization: `Bearer ${token}`,
});

/**
 * Execute API request with retry logic
 */
const executeApiRequest = async (url, headers, reportName) => {
  try {
    logger.debug("Executing API request", {
      url,
      reportName,
      timeout: API_CONFIG.timeout,
    });
    console.log("headers", headers);

    const response = await axios.get(url, {
      headers,
      timeout: API_CONFIG.timeout,
    });
    console.log("response", response);
    logger.info(QUICKBOOKS_REPORTS_LOGS.FETCH_SUCCESS, {
      reportName,
      dataSize: JSON.stringify(response.data).length,
    });
    return response.data;
  } catch (error) {
    logger.error(QUICKBOOKS_REPORTS_LOGS.API_REQUEST_FAILED, {
      reportName,
      error: error.message,
      status: error.response?.status,
    });
    throw error;
  }
};

/**
 * Handle token refresh and retry
 */
const handleTokenRetry = async (
  quickbookAccount,
  reportName,
  startDate,
  endDate
) => {
  logger.warn(QUICKBOOKS_REPORTS_LOGS.RETRY_FOR_TOKEN);

  const updatedUser = await getTokensDirectly(quickbookAccount);
  if (!updatedUser) {
    throw new Error(HARDCODED_STRINGS.REPORT_DEFAULTS.FAILED_TO_REFRESH_TOKEN);
  }

  const newAccessToken = await decrypt(updatedUser.access_token);
  const url = getQuickbooksApiUrl(
    `company/${quickbookAccount.realm_id}/reports/${reportName}?start_date=${startDate}&end_date=${endDate}&summarize_column_by=Month`
  );

  const response = await executeApiRequest(
    url,
    createAuthHeaders(newAccessToken),
    reportName
  );

  logger.info(QUICKBOOKS_REPORTS_LOGS.RETRY_SUCCESS, { reportName });
  return response;
};

const processColumns = (columns, reportId, realmId) => {
  const colMap = new Map();
  const columnData = [];
  let colIndex = 0;

  columns.forEach((col) => {
    const processColData = (subCol, parentTitle = null) => {
      const columnInfo = {
        report_id: reportId,
        col_title: subCol.ColTitle || col.ColTitle,
        col_type: subCol.ColType || col.ColType || HARDCODED_STRINGS.REPORT_DEFAULTS.COL_TYPE,
        col_id: subCol.id || col.ColId || null,
        parent_col_id: null,
        col_order: colIndex++,
        created_at: new Date(),
        updated_at: new Date(),
        parent_col_title: parentTitle,
        realm_id: realmId,
      };

      columnData.push(columnInfo);
      colMap.set(colIndex - 1, {
        col_title: columnInfo.col_title,
        parent_col_title: parentTitle,
        col_type: columnInfo.col_type,
      });
    };

    if (col.ColData?.length) {
      const parentMeta = Object.fromEntries((col.MetaData || []).map((m) => [m.Name, m.Value]));
      col.ColData.forEach((subCol) => {
        processColData(subCol, col.ColTitle);
        if (parentMeta.StartDate) {
          const lastCol = columnData[columnData.length - 1];
          lastCol.period_start = formatDateOnly(parentMeta.StartDate);
          lastCol.period_end = formatDateOnly(parentMeta.EndDate);
        }
      });
    } else {
      processColData(col);
    }
  });

  return { columnData, colMap };
};

const createRowData = (
  rowDataTemplate,
  reportId,
  realmId,
  accountId,
  accountName,
  columnIndex,
  columnInfo,
  value,
  path
) => ({
  ...rowDataTemplate,
  report_id: reportId,
  account_name: accountName,
  column_index: columnIndex,
  value,
  created_at: new Date(),
  updated_at: new Date(),
  col_title: columnInfo.col_title,
  parent_col_title: columnInfo.parent_col_title,
  col_type: columnInfo.col_type,
  realm_id: realmId,
  path,
  ...(rowDataTemplate.quickbooks_account_id !== undefined && { quickbooks_account_id: accountId }),
  ...(rowDataTemplate.account_id !== undefined && { account_id: accountId }),
});

const processRows = (rows, reportId, realmId, colMap, rowDataTemplate) => {
  const rowData = [];

  const processRowRecursively = (row, parentPath = "") => {
    if (row.type === "Section" && row.Rows?.Row) {
      const sectionPath = parentPath
        ? `${parentPath} > ${row.Header?.ColData?.[0]?.value || ""}`
        : row.Header?.ColData?.[0]?.value || "";

      if (row.Header?.ColData?.length > 1) {
        const accountCol = row.Header.ColData[0];
        const accountId = accountCol?.id || null;
        const accountName = accountCol?.value || null;

        row.Header.ColData.slice(1).forEach((colData, i) => {
          if (!colData?.value) return;
          const value = parseFloat(colData.value) || 0;
          if (value === 0) return;

          const columnIndex = i + 1;
          const columnInfo = colMap.get(columnIndex);
          columnInfo &&
            rowData.push(
              createRowData(
                rowDataTemplate,
                reportId,
                realmId,
                accountId,
                accountName,
                columnIndex,
                columnInfo,
                value,
                sectionPath
              )
          );
        });
      }

      row.Rows.Row.forEach((nestedRow) => processRowRecursively(nestedRow, sectionPath));
    } else if (
      row.type === HARDCODED_STRINGS.REPORT_DEFAULTS.ROW_TYPE_DATA &&
      row.ColData?.length
    ) {
      const accountCol = row.ColData[0];
      const accountId = accountCol?.id || null;
      const accountName = accountCol?.value || null;

      row.ColData.slice(1).forEach((colData, i) => {
        if (!colData?.value) return;
        const value = parseFloat(colData.value) || 0;
        if (value === 0) return;

        const columnIndex = i + 1;
        const columnInfo = colMap.get(columnIndex);
        columnInfo &&
          rowData.push(
            createRowData(
              rowDataTemplate,
              reportId,
              realmId,
              accountId,
              accountName,
              columnIndex,
              columnInfo,
              value,
              parentPath
            )
        );
      });
    }
  };

  rows.forEach((row) => processRowRecursively(row));
  return rowData;
};

const saveReportData = async (
  mappedData,
  createReportFn,
  createColumnsFn,
  createRowsFn,
  reportType,
  schemaName = null,
  existingReportId = null
) => {
  const transaction = await reportsRepository.createTransaction();

  try {
    logger.info(QUICKBOOKS_REPORTS_LOGS.SAVING_DATA, {
      reportType,
      columnsCount: mappedData.columnData.length,
      rowsCount: mappedData.rowData.length,
      existingReportId,
    });

    const report = existingReportId
      ? { id: existingReportId }
      : await createReportFn(mappedData.reportData, { transaction }, schemaName);

    if (existingReportId) {
      logger.info(QUICKBOOKS_REPORTS_LOGS.USING_EXISTING_REPORT(reportType, existingReportId), {
        reportType,
        reportId: existingReportId,
      });
    }

    const columnMap = new Map();
    const columnPromises = mappedData.columnData.map(async (column, index) => {
      const findOrCreateFn = reportsRepository[`findOrCreate${reportType}Column`];
      const [columnRecord] = findOrCreateFn
        ? await findOrCreateFn(
            {
              report_id: report.id,
              col_title: column.col_title,
              col_type: column.col_type,
              col_order: column.col_order,
            },
            { ...column, report_id: report.id },
            { transaction },
            schemaName
          )
        : [await createColumnsFn([{ ...column, report_id: report.id }], { transaction }, schemaName)];

      columnMap.set(index, columnRecord.id);
      return columnRecord;
    });

    await Promise.all(columnPromises);

    const rowsToCreate = mappedData.rowData
      .map((row) => {
        const columnId = Array.from(columnMap.entries()).find(([index]) => {
          const originalColumn = mappedData.columnData[index];
          return (
            originalColumn.col_title === row.col_title &&
            originalColumn.parent_col_title === row.parent_col_title &&
            originalColumn.col_type === row.col_type
          );
        })?.[1];

        return columnId ? { ...row, report_id: report.id, column_id: columnId } : null;
      })
      .filter(Boolean);

    const batchPromises = [];
    for (let i = 0; i < rowsToCreate.length; i += BATCH_SIZE) {
      batchPromises.push(
        createRowsFn(rowsToCreate.slice(i, i + BATCH_SIZE), { transaction }, schemaName)
      );
    }

    await Promise.all(batchPromises);
    await reportsRepository.commitTransaction(transaction);

    logger.info(QUICKBOOKS_REPORTS_LOGS.SAVE_SUCCESS, {
      reportType,
      reportId: report.id,
      columnsCount: mappedData.columnData.length,
      rowsCount: rowsToCreate.length,
    });

    return {
      reportId: report.id,
      columnsCount: mappedData.columnData.length,
      rowsCount: rowsToCreate.length,
    };
  } catch (error) {
    await reportsRepository.rollbackTransaction(transaction);
    logger.error(QUICKBOOKS_REPORTS_LOGS.SAVE_FAILED, {
      reportType,
      error: error.message,
    });
    throw error;
  }
};

const saveAsyncReportData = (reportType, processingResult) => {
  logger.info(QUICKBOOKS_REPORTS_LOGS.PROCESSING_SUCCESS, {
    reportType,
    reportId: processingResult.reportId,
    ...(processingResult.totals && { totals: processingResult.totals }),
    ...(processingResult.balanceCheck && { balanceCheck: processingResult.balanceCheck }),
    ...(processingResult.lineItemsCount && { lineItemsCount: processingResult.lineItemsCount }),
    ...(processingResult.linesCount && { linesCount: processingResult.linesCount }),
  });

  return {
    reportId: processingResult.reportId,
    columnsCount: 0,
    rowsCount:
      processingResult.lineItemsCount ||
      processingResult.linesCount ||
      (processingResult.totals ? Object.keys(processingResult.totals).length : 0),
    ...(processingResult.totals && { totals: processingResult.totals }),
    ...(processingResult.balanceCheck && { balanceCheck: processingResult.balanceCheck }),
  };
};

export const reportsService = {
  async getReportDataFromQuickBooks(
    quickbookAccount,
    startDate,
    endDate,
    reportName,
    quickBookAccessToken = null
  ) {
    const ACCESS_TOKEN =
      quickBookAccessToken || (await decrypt(quickbookAccount.access_token));
    const url = getQuickbooksApiUrl(
      `company/${quickbookAccount.realm_id}/reports/${reportName}?start_date=${startDate}&end_date=${endDate}&summarize_column_by=Month`
    );

    // QuickBooks API request

    logger.info(QUICKBOOKS_REPORTS_LOGS.FETCHING, {
      reportName,
      startDate,
      endDate,
      realmId: quickbookAccount.realm_id,
      url,
    });

    try {
      const response = await executeApiRequest(
        url,
        createAuthHeaders(ACCESS_TOKEN),
        reportName
      );

      // QuickBooks API response received

      return response;
    } catch (error) {
      // Only retry on unauthorized errors
      if (error.response?.status === status.STATUS_CODE_UNAUTHORIZED) {
        return await handleTokenRetry(
          quickbookAccount,
          reportName,
          startDate,
          endDate
        );
      }
      throw error;
    }
  },

  mapTrialBalanceToOptimizedStructure(
    quickbooksData,
    reportId,
    realmId,
    fallbackStartDate = null,
    fallbackEndDate = null
  ) {
    try {
      const header = quickbooksData.Header || {};
      const { startDate: startPeriod, endDate: endPeriod } = createValidatedDateRange(
        header.StartPeriod,
        header.EndPeriod,
        fallbackStartDate,
        fallbackEndDate
      );

      const reportData = {
        time: header.Time ? new Date(header.Time) : new Date(),
        report_name: header.ReportName || HARDCODED_STRINGS.REPORT_DEFAULTS.REPORT_NAME_TRIAL_BALANCE,
        report_basis: header.ReportBasis || HARDCODED_STRINGS.REPORT_DEFAULTS.REPORT_BASIS,
        start_period: startPeriod,
        end_period: endPeriod,
        summarize_columns_by:
          header.SummarizeColumnsBy || HARDCODED_STRINGS.REPORT_DEFAULTS.SUMMARIZE_COLUMNS_BY,
        currency: header.Currency || HARDCODED_STRINGS.REPORT_DEFAULTS.CURRENCY,
        realm_id: realmId,
      };

      const columns = quickbooksData?.Columns?.Column || [];
      const { columnData, colMap } = processColumns(columns, reportId, realmId);
      const rows = quickbooksData?.Rows?.Row || [];
      const rowDataTemplate = {
        quickbooks_account_id: null,
        acct_num: null,
        class_id: null,
        account_type: null,
      };
      const rowData = processRows(rows, reportId, realmId, colMap, rowDataTemplate);

      logger.info(QUICKBOOKS_REPORTS_LOGS.MAPPING_SUCCESS, {
        reportType: HARDCODED_STRINGS.REPORT_TYPES.TRIAL_BALANCE,
        columnsCount: columnData.length,
        rowsCount: rowData.length,
      });

      return { reportData, columnData, rowData };
    } catch (error) {
      logger.error(QUICKBOOKS_REPORTS_LOGS.MAPPING_FAILED, {
        reportType: HARDCODED_STRINGS.REPORT_TYPES.TRIAL_BALANCE,
        error: error.message,
      });
      throw error;
    }
  },

  async mapProfitLossToOptimizedStructure(
    quickbooksData,
    reportId,
    realmId,
    fallbackStartDate = null,
    fallbackEndDate = null,
    schemaName = null,
    existingReportId = null
  ) {
    try {
      const result = await processPnLData(quickbooksData, realmId, schemaName, existingReportId);
      return {
        reportData: {
          id: result.reportId,
          realm_id: realmId,
          linesCount: result.totals ? Object.keys(result.totals).length : 0,
          summariesCount: result.balanceCheck ? 1 : 0,
          totals: result.totals,
          balanceCheck: result.balanceCheck,
        },
        columnData: [],
        rowData: [],
        processingResult: result,
      };
    } catch (error) {
      logger.error(QUICKBOOKS_REPORTS_LOGS.MAPPING_FAILED, {
        reportType: HARDCODED_STRINGS.REPORT_TYPES.PROFIT_AND_LOSS,
        error: error.message,
      });
      throw error;
    }
  },

  async mapBalanceSheetToOptimizedStructure(
    quickbooksData,
    reportId,
    realmId,
    fallbackStartDate = null,
    fallbackEndDate = null,
    schemaName = null,
    existingReportId = null
  ) {
    try {
      const result = await processBalanceSheetData(quickbooksData, realmId, schemaName, existingReportId);
      return {
        reportData: {
          id: result.reportId,
          realm_id: realmId,
          lineItemsCount: result.lineItemsCount,
          summariesCount: result.summariesCount,
          totals: result.totals,
        },
        columnData: [],
        rowData: [],
        processingResult: result,
      };
    } catch (error) {
      logger.error(QUICKBOOKS_REPORTS_LOGS.MAPPING_FAILED, {
        reportType: HARDCODED_STRINGS.REPORT_TYPES.BALANCE_SHEET,
        error: error.message,
      });
      throw error;
    }
  },

  async mapCashFlowToOptimizedStructure(
    quickbooksData,
    reportId,
    realmId,
    fallbackStartDate = null,
    fallbackEndDate = null,
    schemaName = null,
    existingReportId = null
  ) {
    try {
      const result = await processCashFlowData(quickbooksData, realmId, schemaName, existingReportId);
      return {
        reportData: {
          id: result.reportId,
          realm_id: realmId,
          linesCount: result.linesCount,
          totals: result.totals,
        },
        columnData: [],
        rowData: [],
        processingResult: result,
      };
    } catch (error) {
      logger.error(QUICKBOOKS_REPORTS_LOGS.MAPPING_FAILED, {
        reportType: HARDCODED_STRINGS.REPORT_TYPES.CASH_FLOW,
        error: error.message,
      });
      throw error;
    }
  },

  async saveTrialBalanceData(mappedData, schemaName = null, existingReportId = null) {
    if (existingReportId) {
      const { updateInSchema } = await import("../utils/database.utils.js");
      const reportData = mappedData.reportData;

      try {
        schemaName
          ? await updateInSchema(TrialBalanceReport, schemaName, existingReportId, reportData)
          : await TrialBalanceReport.update(reportData, { where: { id: existingReportId } });
        logger.info(QUICKBOOKS_REPORTS_LOGS.TRIAL_BALANCE_HEADER_UPDATED(existingReportId), {
          reportId: existingReportId,
        });
        mappedData.reportData.id = existingReportId;
      } catch (error) {
        logger.error(QUICKBOOKS_REPORTS_LOGS.TRIAL_BALANCE_HEADER_UPDATE_FAILED(error.message), {
          error: error.message,
          status: 500,
        });
        throw error;
      }
    }

    const result = await saveReportData(
      mappedData,
      reportsRepository.createTrialBalanceReport,
      reportsRepository.createTrialBalanceColumns,
      reportsRepository.createTrialBalanceRows,
      HARDCODED_STRINGS.REPORT_TYPES.TRIAL_BALANCE,
      schemaName,
      existingReportId
    );

    if (result?.reportId && mappedData?.rowData) {
      try {
        const accountMap = new Map();
        mappedData.rowData.forEach((row) => {
          if (row.quickbooks_account_id && row.account_name) {
            accountMap.set(String(row.quickbooks_account_id), row.account_name);
          }
        });

        await insertTrialBalanceLines(
          result.reportId,
          mappedData.rowData,
          accountMap,
          process.env.LOCAL_DB_NAME,
          mappedData.reportData.realm_id
        );

        logger.info(QUICKBOOKS_REPORTS_LOGS.PROCESSING_SUCCESS, {
          reportType: HARDCODED_STRINGS.REPORT_TYPES.TRIAL_BALANCE,
          linesProcessed: mappedData.rowData.length,
        });
      } catch (error) {
        logger.error(QUICKBOOKS_REPORTS_LOGS.PROCESSING_ERROR, {
          error: error.message,
          reportType: HARDCODED_STRINGS.REPORT_TYPES.TRIAL_BALANCE,
        });
      }
    }

    return result;
  },

  async saveProfitLossData(mappedData, schemaName = null, existingReportId = null) {
    return saveAsyncReportData(HARDCODED_STRINGS.REPORT_TYPES.PROFIT_AND_LOSS, mappedData.processingResult);
  },

  async saveBalanceSheetData(mappedData, schemaName = null, existingReportId = null) {
    return saveAsyncReportData(HARDCODED_STRINGS.REPORT_TYPES.BALANCE_SHEET, mappedData.processingResult);
  },

  async saveCashFlowData(mappedData, schemaName = null, existingReportId = null) {
    return saveAsyncReportData(HARDCODED_STRINGS.REPORT_TYPES.CASH_FLOW, mappedData.processingResult);
  },

  async getTrialBalanceReportsByRealmId(realmId, options = {}) {
    try {
      return await reportsRepository.findTrialBalanceReportsByRealmId(realmId, options);
    } catch (error) {
      logger.error(QUICKBOOKS_REPORTS_LOGS.RETRIEVAL_FAILED, {
        reportType: HARDCODED_STRINGS.REPORT_TYPES.TRIAL_BALANCE,
        realmId,
        error: error.message,
      });
      throw error;
    }
  },

  async getProfitLossReportsByRealmId(realmId, options = {}) {
    try {
      return await getPnLReports(realmId, options);
    } catch (error) {
      logger.error(QUICKBOOKS_REPORTS_LOGS.RETRIEVAL_FAILED, {
        reportType: HARDCODED_STRINGS.REPORT_TYPES.PROFIT_AND_LOSS,
        realmId,
        error: error.message,
      });
      throw error;
    }
  },

  async getBalanceSheetReportsByRealmId(realmId, options = {}) {
    try {
      return await getBalanceSheetReports(realmId, options);
    } catch (error) {
      logger.error(QUICKBOOKS_REPORTS_LOGS.RETRIEVAL_FAILED, {
        reportType: HARDCODED_STRINGS.REPORT_TYPES.BALANCE_SHEET,
        realmId,
        error: error.message,
      });
      throw error;
    }
  },

  async getCashFlowReportsByRealmId(realmId, options = {}) {
    try {
      return await getCashFlowReports(realmId, options);
    } catch (error) {
      logger.error(QUICKBOOKS_REPORTS_LOGS.RETRIEVAL_FAILED, {
        reportType: HARDCODED_STRINGS.REPORT_TYPES.CASH_FLOW,
        realmId,
        error: error.message,
      });
      throw error;
    }
  },
};

export default reportsService;
