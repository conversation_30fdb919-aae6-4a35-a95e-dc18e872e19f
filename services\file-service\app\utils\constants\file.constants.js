// FILE CONSTANTS
// Static messages and validation constants for File Operations

export const FILE_MESSAGES = {
  // Success Messages
  UPLOAD_SUCCESS: "File uploaded successfully",
  DOWNLOAD_SUCCESS: "File downloaded successfully",
  DELETE_SUCCESS: "File deleted successfully",
  LIST_SUCCESS: "Files listed successfully",
  METADATA_SUCCESS: "File metadata retrieved successfully",
  REPORT_FOLDERS_SUCCESS: "Report folders retrieved successfully",
  REPORT_BLOB_URL_SUCCESS: "Report blob URL retrieved successfully",
  COMBINED_REPORTS_SUCCESS: "Combined reports retrieved successfully",
  REPORT_SUMMARY_SUCCESS: "Report summary retrieved successfully",
  REPORT_FILES_SUCCESS: "Report files retrieved successfully",

  // Error Messages
  UPLOAD_FAILED: "Failed to upload file",
  DOWNLOAD_FAILED: "Failed to download file",
  DELETE_FAILED: "Failed to delete file",
  LIST_FAILED: "Failed to list files",
  METADATA_FAILED: "Failed to get file metadata",
  REPORT_FOLDERS_FAILED: "Failed to get report folders",
  REPORT_BLOB_URL_FAILED: "Failed to get report blob URL",
  COMBINED_REPORTS_FAILED: "Failed to get combined reports",
  REPORT_SUMMARY_FAILED: "Failed to get report summary",
  REPORT_FILES_FAILED: "Failed to get report files",

  // Validation Messages
  NO_FILE_PROVIDED: "No file provided",
  FILE_PATH_REQUIRED: "File path is required",
  ORGANIZATION_ID_REQUIRED: "Organization ID is required",
  CATEGORY_REQUIRED: "Category is required",
  MONTH_REQUIRED: "Month is required",
  FOLDER_REQUIRED: "Folder is required",
  FILE_NAME_REQUIRED: "File name is required",
  INVALID_CATEGORY: "Invalid category. Must be one of: Finance, Operations, Payroll",
  REPORT_NOT_FOUND: "Report not found",
  FILE_NOT_FOUND: "File not found",
  NO_PDF_FILE_FOUND: "No PDF file found",

  VALID_ORGANIZATION_ID: "Please provide a valid organizationId",
  VALID_CATEGORY: "Please provide a valid category (Finance, Operations, Payroll)",
  VALID_MONTH: "Please provide a valid month",
};

export const FILE_LOG_MESSAGES = {
  UPLOAD_START: "Starting file upload",
  UPLOAD_COMPLETE: "File uploaded successfully",
  UPLOAD_ERROR: "Error uploading file",
  DOWNLOAD_START: "Starting file download",
  DOWNLOAD_COMPLETE: "File downloaded successfully",
  DOWNLOAD_ERROR: "Error downloading file",
  DELETE_START: "Starting file deletion",
  DELETE_COMPLETE: "File deleted successfully",
  DELETE_ERROR: "Error deleting file",
  LIST_START: "Starting file list",
  LIST_COMPLETE: "Files listed successfully",
  LIST_ERROR: "Error listing files",
  METADATA_START: "Starting metadata retrieval",
  METADATA_COMPLETE: "File metadata retrieved successfully",
  METADATA_ERROR: "Error retrieving file metadata",
  REPORT_FOLDERS_START: "Starting report folders retrieval",
  REPORT_FOLDERS_COMPLETE: "Report folders retrieved successfully",
  REPORT_FOLDERS_ERROR: "Error retrieving report folders",
  REPORT_BLOB_URL_START: "Starting report blob URL retrieval",
  REPORT_BLOB_URL_COMPLETE: "Report blob URL retrieved successfully",
  REPORT_BLOB_URL_ERROR: "Error retrieving report blob URL",
  COMBINED_REPORTS_START: "Starting combined reports retrieval",
  COMBINED_REPORTS_COMPLETE: "Combined reports retrieved successfully",
  COMBINED_REPORTS_ERROR: "Error retrieving combined reports",
  REPORT_SUMMARY_START: "Starting report summary retrieval",
  REPORT_SUMMARY_COMPLETE: "Report summary retrieved successfully",
  REPORT_SUMMARY_ERROR: "Error retrieving report summary",
  REPORT_FILES_START: "Starting report files retrieval",
  REPORT_FILES_COMPLETE: "Report files retrieved successfully",
  REPORT_FILES_ERROR: "Error retrieving report files",
};

export const REPORT_MESSAGES = {
  // Success Messages
  FOLDERS_RETRIEVED: "Report folders retrieved successfully",
  BLOB_URL_RETRIEVED: "Report blob URL retrieved successfully",
  COMBINED_REPORTS_RETRIEVED: "Combined reports retrieved successfully",
  SUMMARY_RETRIEVED: "Report summary retrieved successfully",
  FILES_RETRIEVED: "Report files retrieved successfully",

  // Error Messages
  FOLDERS_RETRIEVAL_FAILED: "Failed to get report folders",
  BLOB_URL_RETRIEVAL_FAILED: "Failed to get report blob URL",
  COMBINED_REPORTS_RETRIEVAL_FAILED: "Failed to get combined reports",
  SUMMARY_RETRIEVAL_FAILED: "Failed to get report summary",
  FILES_RETRIEVAL_FAILED: "Failed to get report files",
  REPORT_FILE_NOT_FOUND: "Report file not found",

  // Validation Messages
  ORGANIZATION_ID_REQUIRED: "Organization ID is required",
  CATEGORY_REQUIRED: "Category is required",
  MONTH_REQUIRED: "Month is required",
  NO_PDF_FOUND: "No PDF file found",
};

export const CATEGORY_NAMES = {
  FINANCE: "Finance",
  OPERATIONS: "Operations",
  PAYROLL: "Payroll",
};

export const CATEGORY_VARIATIONS = {
  financial: CATEGORY_NAMES.FINANCE,
  finance: CATEGORY_NAMES.FINANCE,
  operational: CATEGORY_NAMES.OPERATIONS,
  operations: CATEGORY_NAMES.OPERATIONS,
  payroll: CATEGORY_NAMES.PAYROLL,
  pms: CATEGORY_NAMES.PAYROLL,
};

export const FILE_REGEX = {
  ORGANIZATION_QUOTES: /['"]/g,
};

export default {
  FILE_MESSAGES,
  FILE_LOG_MESSAGES,
  REPORT_MESSAGES,
  CATEGORY_NAMES,
  CATEGORY_VARIATIONS,
  FILE_REGEX,
};

