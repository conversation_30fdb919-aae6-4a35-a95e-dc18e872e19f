import { sendEmail } from "../../../../shared/utils/email.util.js";
import { createLogger } from "./logger.util.js";
import { LOGGER_NAMES, EMAIL_CONSTANTS } from "./constants.util.js";

const logger = createLogger(LOGGER_NAMES.SIKKA_SERVICE);

/**
 * Send failure email notification for Sikka KPI reports sync
 * @param {Object} params - Email parameters
 * @param {string[]} params.failedKpiNames - Array of failed KPI display names
 * @param {string} [params.recipientEmail] - Recipient email address
 * @returns {Promise<void>}
 */
export async function sendSikkaKpiReportsSyncFailureEmail(failed_apis) {
  try {
    const to_email = process.env.NOTIFICATION_EMAIL;
    const from_email = process.env.NOTIFICATION_EMAIL;

    if (!to_email || !from_email) {
      logger.warn(EMAIL_CONSTANTS.MISSING_RECIPIENT_OR_SENDER_EMAIL);
      return;
    }

    await sendEmail({
      to: to_email,
      from: from_email,
      subject: EMAIL_CONSTANTS.SIKKA_KPI_REPORTS_SYNC_FAILED_SUBJECT,
      text: EMAIL_CONSTANTS.SIKKA_KPI_REPORTS_SYNC_FAILED_TEXT_TEMPLATE(
        failed_apis
      ),
      html: EMAIL_CONSTANTS.SIKKA_KPI_REPORTS_SYNC_FAILED_HTML_TEMPLATE(
        failed_apis
      ),
    });

    logger.info(EMAIL_CONSTANTS.SIKKA_SYNC_FAILED_EMAIL_SENT, {
      to_email: to_email,
      failed_kpis: failed_apis,
    });
  } catch (emailError) {
    logger.warn(EMAIL_CONSTANTS.SIKKA_SYNC_FAILED_EMAIL_FAILED, {
      error: emailError.message,
    });
  }
}
