import { sequelize } from "../models/index.js";
import models from "../models/index.js";
import { LOGGER_NAMES } from "../utils/constants/log.constants.js";
import { createLogger } from "../utils/logger.utils.js";
import { getOrganizationSchemaName, getReportId } from "../utils/repository.utils.js";

const logger = createLogger(LOGGER_NAMES.REPORT_REPOSITORY);

/**
 * Get P&L report ID for a specific month and year
 * @param {string} schemaName - Organization schema name
 * @param {number} month - Month (1-12)
 * @param {number} year - Year
 * @returns {Promise<string|null>} Report ID or null
 */
const getPnlReportId = async (schemaName, month, year) => {
  return await getReportId(schemaName, 'qb_pnl_reports', month, year, 'P&L');
};

/**
 * Get expense line items for a report
 * @param {string} schemaName - Organization schema name
 * @param {string} reportId - Report ID
 * @returns {Promise<Array>} Expense line items with path, account_name and amount
 */
const getExpenseLineItems = async (schemaName, reportId) => {
  try {
    logger.info(
      `Fetching expense line items from schema: ${schemaName} for report ID: ${reportId}`
    );

    const query = `
      SELECT 
        id,
        path,
        amount,
        account_name
      FROM "${schemaName}".qb_pnl_lines
      WHERE report_id = :reportId
        AND LOWER(path) LIKE '%expense%'
      ORDER BY path
    `;

    const results = await sequelize.query(query, {
      replacements: { reportId },
      type: sequelize.QueryTypes.SELECT,
    });

    logger.info(
      `Retrieved ${results.length} expense line items from schema: ${schemaName}`
    );

    return results;
  } catch (error) {
    logger.error(`Error in ExpenseBreakdownRepository.getExpenseLineItems:`, error);
    throw error;
  }
};

export default {
  getOrganizationSchemaName,
  getPnlReportId,
  getExpenseLineItems,
};
