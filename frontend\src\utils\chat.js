/**
 * Chat Utilities
 * Helper functions for chat functionality
 */

import {
  MESSAGE_TYPES,
  CHAT_MESSAGES,
  CHAT_MONTH_MAP,
  CHAT_DEFAULTS,
  CHAT_DATE_CONSTANTS,
  CHAT_REGEX,
} from "./constants/chat.js";

const getServiceTypeName = (isOperationsSelected, isPayrollSelected) => {
  if (isOperationsSelected) return "Operational";
  if (isPayrollSelected) return "Payroll";
  return "Financial";
};

/**
 * Generate PDF filename dynamically based on dashboard configuration
 * Constructs filename using format: "{Dashboard} {Category} Dashboard - {Month} 2025.pdf"
 * @param {string} selectedMonth - Selected month (June, July, August, or variations)
 * @param {boolean} isFinancialSelected - Financial dashboard selected
 * @param {boolean} isOperationsSelected - Operations dashboard selected
 * @param {boolean} isPayrollSelected - Payroll dashboard selected
 * @param {string} selectedDashboard - Dashboard type (chp/dental)
 * @returns {string} Generated filename (e.g., "CHP Finance Dashboard - August 2025.pdf")
 */
export const generatePdfFilename = (
  selectedMonth,
  isFinancialSelected,
  isOperationsSelected,
  isPayrollSelected,
  selectedDashboard = "chp"
) => {
  // Determine dashboard name
  const dashboardName =
    selectedDashboard.toLowerCase() === "dental" ? "Dental" : "CHP";

  // Determine category name
  const category = getServiceTypeName(isOperationsSelected, isPayrollSelected);

  // Normalize month name - capitalize first letter
  const normalizedMonth =
    selectedMonth && typeof selectedMonth === "string"
      ? selectedMonth.charAt(0).toUpperCase() +
        selectedMonth.slice(1).toLowerCase()
      : "June"; // default

  // Construct filename dynamically
  // Format: "{Dashboard} {Category} Dashboard - {Month} 2025.pdf"
  const filename = `${dashboardName} ${category} Dashboard - ${normalizedMonth} 2025.pdf`;

  return filename;
};

/**
 * Generate report filename for Reports page based on service type and date
 * Constructs filename using format: "{ServiceType}_Report_{Month}_{Year}.pdf"
 * @param {string} monthNumber - Month number in format "01" to "12"
 * @param {string} year - Year (e.g., "2025")
 * @param {string} serviceType - Service type ("financial", "operational", "payroll")
 * @returns {string|null} Generated filename (e.g., "Finance_Report_January_2025.pdf") or null if invalid month/year
 */
export const generateReportFilename = (monthNumber, year, serviceType) => {
  if (!monthNumber || !year) {
    return null;
  }

  const MONTH_NUMBER_TO_NAME = {
    "01": "January",
    "02": "February",
    "03": "March",
    "04": "April",
    "05": "May",
    "06": "June",
    "07": "July",
    "08": "August",
    "09": "September",
    "10": "October",
    "11": "November",
    "12": "December",
  };

  const SERVICE_TYPE_MAP = {
    financial: "Finance",
    operational: "Operations",
    payroll: "Payroll",
  };

  const monthName = MONTH_NUMBER_TO_NAME[monthNumber] || monthNumber;

  const serviceTypeDisplay = SERVICE_TYPE_MAP[serviceType] || "Report";

  return `${serviceTypeDisplay}_Report_${monthName}_${year}.pdf`;
};

/**
 * Create a chat message object
 * @param {string} content - Message content
 * @param {string} type - Message type (user/ai/system)
 * @param {string} timestamp - Optional timestamp
 * @returns {Object} Message object
 */
export const createMessage = (
  content,
  type = MESSAGE_TYPES.USER,
  timestamp = null
) => ({
  id: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}-${type}`,
  type,
  content,
  timestamp: timestamp || new Date().toLocaleTimeString(),
});

/**
 * Enhance user message with dashboard summary context
 * @param {string} userMessage - Original user message
 * @param {string} dashboardSummary - Dashboard summary context
 * @returns {string} Enhanced message
 */
export const enhanceMessageWithContext = (userMessage, dashboardSummary) => {
  if (!dashboardSummary) return userMessage;

  return `Dashboard Summary Context: ${dashboardSummary}\n\nUser Question: ${userMessage}`;
};

/**
 * Extract answer from API response
 * Handles both JSON format (responseType, narrative, comparisonData) and plain text
 * @param {Object} response - API response object
 * @returns {string|Object} Extracted answer (string for plain text, object for JSON format)
 */
export const extractAnswerFromResponse = (response) => {
  if (!response) return "No response received";

  // Check if response is JSON chat format (responseType, narrative, comparisonData)
  if (response.jsonAnswer) {
    return response.jsonAnswer;
  }

  // Check if response itself is JSON chat format
  if (response.responseType || response.narrative || response.comparisonData) {
    return response;
  }

  // Try different possible response structures for plain text
  const possiblePaths = [
    response.answer,
    response.output,
    response.response,
    response.message,
    response.content,
    response.data?.data?.plainAnswer,
    response.data?.data?.answer,
    response.data?.data?.output,
    response.data?.data?.response,
    response.data?.data?.message,
    response.data?.data?.content,
    response.data?.plainAnswer,
    response.data?.answer,
    response.data?.output,
    response.data?.response,
    response.data?.message,
    response.data?.content,
  ];

  const answer = possiblePaths.find((path) => path && typeof path === "string");
  return answer || "No answer received";
};

/**
 * Format welcome message using the centralized CHAT_MESSAGES.WELCOME template
 * with dynamic content based on selected options and organization name
 * @param {string} selectedMonth - Selected month
 * @param {string} organizationName - Organization name (e.g., "Children's Health Partners")
 * @returns {string} Formatted welcome message
 */
export const formatWelcomeMessage = (
  selectedMonth = CHAT_DEFAULTS.DEFAULT_MONTH,
  organizationName = "Children's Health Partners",
  selectedYear = null,
  isFinancialSelected = true,
  isOperationsSelected = false,
  isPayrollSelected = false
) => {
  const currentYear =
    CHAT_DATE_CONSTANTS.CURRENT_YEAR || new Date().getFullYear();
  const monthNames = CHAT_MONTH_MAP.ABBREVIATIONS_TO_FULL;
  const numericMonthNames = CHAT_MONTH_MAP.NUMERIC_TO_FULL;

  let formattedMonth = selectedMonth || CHAT_DEFAULTS.DEFAULT_MONTH;
  let yearFromSelection = null;

  if (numericMonthNames[formattedMonth]) {
    formattedMonth = numericMonthNames[formattedMonth];
  } else if (formattedMonth.includes("-")) {
    const [abbr, year] = formattedMonth.split("-");
    formattedMonth = monthNames[abbr] || abbr;
    if (year && CHAT_REGEX.YEAR_SUFFIX.test(year)) {
      yearFromSelection =
        year.length === 2 ? Number(`20${year}`) : Number(year);
    }
  } else {
    const yearMatch = formattedMonth.match(CHAT_REGEX.INLINE_YEAR);
    if (yearMatch) {
      yearFromSelection = Number(yearMatch[0]);
      formattedMonth = formattedMonth.replace(yearMatch[0], "");
    }

    formattedMonth = formattedMonth.replace(CHAT_REGEX.DIGIT_SEQUENCE, "").trim();
    if (!formattedMonth) {
      formattedMonth = CHAT_DEFAULTS.DEFAULT_MONTH;
    } else {
      formattedMonth = formattedMonth
        .split(/\s+/)
        .map((part) => part.charAt(0).toUpperCase() + part.slice(1).toLowerCase())
        .join(" ");
    }
  }

  const cleanOrgName = organizationName
    ? organizationName.replace(CHAT_REGEX.ORG_NAME_WRAPPING_QUOTES, "")
    : "Children's Health Partners";

  const lastChar = cleanOrgName.slice(-1).toLowerCase();
  const possessive = (lastChar === 's' || lastChar === "'") ? "'" : "'s";

  const yearToUse = selectedYear || 
    yearFromSelection ||
    currentYear ||
    CHAT_DATE_CONSTANTS.DEFAULT_YEAR ||
    new Date().getFullYear();

  // Determine service type based on selected dashboard
  const serviceType = getServiceTypeName(isOperationsSelected, isPayrollSelected);
  const serviceTypeLower = serviceType.toLowerCase();

  return CHAT_MESSAGES.WELCOME.replace("{organizationName}", cleanOrgName)
    .replace("{possessive}", possessive)
    .replace("{serviceType}", serviceType)
    .replace("{serviceTypeLower}", serviceTypeLower)
    .replace("{selectedMonth}", formattedMonth)
    .replace("{currentYear}", String(yearToUse));
};
