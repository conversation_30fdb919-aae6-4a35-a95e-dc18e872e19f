import IncomeExpenseStatementRepository from "../repository/income_expense_statement.repository.js";
import { LOGGER_NAMES } from "../utils/constants/log.constants.js";
import { createLogger } from "../utils/logger.utils.js";
import {
  parseNumericValue,
  isValidMonth,
  isValidYear,
} from "../utils/format.utils.js";

const logger = createLogger(LOGGER_NAMES.REPORT_SERVICE);

/**
 * Get income and expense statement data
 * @param {Object} params - Query parameters
 * @param {string} params.organization_id - Organization ID
 * @param {number} params.month - Month (1-12)
 * @param {number} params.year - Year
 * @returns {Promise<Object>} Formatted income and expense statement data
 */
const getIncomeExpenseStatement = async ({ organization_id, month, year }) => {
  try {
    logger.info(
      `Fetching income and expense statement for org: ${organization_id}, month: ${month}, year: ${year}`
    );

    // Validate parameters
    if (!organization_id) {
      throw new Error("Organization ID is required");
    }

    if (!month || !isValidMonth(month)) {
      throw new Error("Valid month (1-12) is required");
    }

    if (!year || !isValidYear(year)) {
      throw new Error("Valid year is required");
    }

    // Convert to numbers
    const monthNum = parseInt(month, 10);
    const yearNum = parseInt(year, 10);

    // Get organization schema name
    const schemaName =
      await IncomeExpenseStatementRepository.getOrganizationSchemaName(
        organization_id
      );

    if (!schemaName) {
      logger.warn(`Schema not found for organization: ${organization_id}`);
      return {
        organization_id,
        month: monthNum,
        year: yearNum,
        data: [],
        message: "Organization not found or schema not configured",
      };
    }

    // Get P&L report raw field for the month and year
    const pnlReportRaw = await IncomeExpenseStatementRepository.getPNLReport(
      schemaName,
      monthNum,
      yearNum
    );

    /**
     * Parse QuickBooks P&L Report into hierarchical structure for UI
     */
    function parsePnLToHierarchy(pnlReportRaw) {
      if (
        !pnlReportRaw ||
        !pnlReportRaw.Rows ||
        !Array.isArray(pnlReportRaw.Rows.Row)
      ) {
        return [];
      }

      const topLevelRows = pnlReportRaw.Rows.Row;
      const result = [];

      // Helper to safely read values
      const getHeaderName = (section) =>
        section?.Header?.ColData?.[0]?.value ?? null;
      const getSummary = (section) => section?.Summary?.ColData ?? null;
      const getDataName = (dataRow) => dataRow?.ColData?.[0]?.value ?? null;
      const getDataTotal = (dataRow) => {
        // total usually in ColData[1] or [2] depending on shape; choose [1] first, fallback to last
        if (!dataRow?.ColData) return 0;
        const maybe =
          dataRow.ColData[1]?.value ??
          dataRow.ColData[dataRow.ColData.length - 1]?.value;
        return Number(maybe ?? 0);
      };

      // Recursive processor
      const processNodes = (nodes, container, isTopLevel = false) => {
        for (const node of nodes) {
          const nodeType = node?.type ?? null;

          // If it's a Section node:
          if (nodeType === "Section") {
            const headerName = getHeaderName(node);
            const groupName = node.group ?? null;

            if (isTopLevel) {
              // Create a top-level section (Income, Expenses, GrossProfit, ...)
              const sectionName = headerName ?? groupName ?? null;
              const sectionObj = { section: sectionName, rows: [] };
              result.push(sectionObj);

              // If the section contains nested rows, process them into sectionObj.rows
              if (node.Rows?.Row) {
                processNodes(node.Rows.Row, sectionObj.rows, false);
              }

              // If summary exists on this top-level section, push it as a summary row
              const summary = getSummary(node);
              if (summary && summary[0]?.value) {
                const summaryName = summary[0].value;
                const summaryValue = Number(summary[1]?.value ?? 0);
                sectionObj.rows.push({
                  type: "summary",
                  account: summaryName,
                  amount: summaryValue,
                });
              }
            } else {
              // Nested section inside a parent section: treat as a subsection node
              const subsectionName = headerName ?? groupName ?? "Section";
              const subsection = { account: subsectionName, children: [] };
              container.push(subsection);

              // Process the nested rows into subsection.children
              if (node.Rows?.Row) {
                processNodes(node.Rows.Row, subsection.children, false);
              }

              // Push summary for the subsection if present
              const summary = getSummary(node);
              if (summary && summary[0]?.value) {
                const summaryName = summary[0].value;
                const summaryValue = Number(summary[1]?.value ?? 0);
                subsection.children.push({
                  type: "summary",
                  account: summaryName,
                  amount: summaryValue,
                });
              }
            }
            continue;
          }

          // If it's a Data row (actual account row)
          if (nodeType === "Data" && node.ColData) {
            const name = getDataName(node);
            const amount = getDataTotal(node);
            // push depending on container shape: top-level section rows are arrays of objects,
            // nested subsections use children arrays but same shape
            container.push({ account: name, amount });
            continue;
          }

          // If node has Rows but no explicit type (edge cases), descend into it preserving same container:
          if (node.Rows?.Row) {
            processNodes(node.Rows.Row, container, isTopLevel);
            // After descending, if node has Summary and we're at top-level parent (rare), push summary
            const summary = getSummary(node);
            if (summary && summary[0]?.value) {
              const summaryName = summary[0].value;
              const summaryValue = Number(summary[1]?.value ?? 0);
              container.push({
                type: "summary",
                account: summaryName,
                amount: summaryValue,
              });
            }
            continue;
          }

          // If it's some other Section-like object with Summary (e.g. GrossProfit) but not caught above:
          const summary = getSummary(node);
          if (summary && summary[0]?.value && isTopLevel) {
            // ensure there's at least a section placeholder to attach it if last pushed was a real section:
            const sectionName = node.group ?? summary[0].value;
            const sectionObj = {
              section: sectionName,
              rows: [
                {
                  type: "summary",
                  account: summary[0].value,
                  amount: Number(summary[1]?.value ?? 0),
                },
              ],
            };
            result.push(sectionObj);
          }
        }
      }
      // Start with top-level nodes; top-level container = result (we treat each top-level Section as a section object)
      processNodes(topLevelRows, null, true);

      return result;
    }

    const transformedData = parsePnLToHierarchy(pnlReportRaw);
    // console.log("transformedData", transformedData);

    // const transformPnLReport = (pnlReportRaw) => {
    //   const result = [];

    //   const currencyFormat = (num) =>
    //     Number(num).toLocaleString("en-US", {
    //       minimumFractionDigits: 2,
    //       maximumFractionDigits: 2,
    //     });

    //   const processRows = (rows, parentSectionName = null) => {
    //     rows.forEach((row) => {
    //       // SECTION — Add header title
    //       if (row.type === "Section" && row.Header?.ColData?.length) {
    //         const headerName = row.Header.ColData[0]?.value || "";

    //         if (headerName) {
    //           result.push({ label: headerName, isGroup: true });
    //         }

    //         if (row.Rows?.Row?.length) {
    //           processRows(row.Rows.Row, headerName); // Recursion
    //         }

    //         // Add Summary if available
    //         if (row.Summary?.ColData?.length) {
    //           const title = row.Summary.ColData[0]?.value;
    //           const value = row.Summary.ColData[1]?.value;

    //           if (title && value !== undefined) {
    //             result.push({
    //               label: `Total for ${
    //                 parentSectionName || title.replace("Total ", "")
    //               }`,
    //               value: "$" + currencyFormat(value),
    //               isSummary: true,
    //             });
    //           }
    //         }
    //       }

    //       // DATA — Add individual account rows
    //       if (row.type === "Data" && row.ColData?.length) {
    //         const accountName = row.ColData[0]?.value;
    //         const value = row.ColData[1]?.value;

    //         if (accountName && value !== undefined) {
    //           result.push({
    //             label: accountName,
    //             value: currencyFormat(value),
    //           });
    //         }
    //       }

    //       // If nested more deeply → still recursively fetch
    //       if (row.Rows?.Row?.length) {
    //         processRows(row.Rows.Row, parentSectionName);
    //       }
    //     });
    //   };

    //   const mainRows = pnlReportRaw?.Rows?.Row;
    //   if (mainRows) {
    //     processRows(mainRows);
    //   }

    //   return result;
    // };

    // const simplified = transformPnLReport(pnlReportRaw);

    // console.log("simplified", simplified);

    // console.log("pnlReportRaw", pnlReportRaw);

    // function extractPLRowsWithoutMonth(plData) {
    //   const rows = [];

    //   function traverse(node, group = null) {
    //     if (!node) return;

    //     // update section/group if available
    //     if (node.group) group = node.group;

    //     // extract only Data rows
    //     if (node.type === "Data" && node.ColData?.length >= 3) {
    //       const [info, , totalObj] = node.ColData;
    //       rows.push({
    //         group,
    //         accountName: info.value,
    //         total: parseFloat(totalObj.value) || 0,
    //       });
    //     }

    //     // go deep if nested rows exist
    //     if (node.Rows?.Row) {
    //       node.Rows.Row.forEach((r) => traverse(r, group));
    //     }
    //   }

    //   plData.Rows.Row.forEach((r) => traverse(r));

    //   return rows;
    // }

    // // Usage
    // const simplified = extractPLRowsWithoutMonth(pnlReportRaw);

    // function groupPLData(data) {
    //   const grouped = {};

    //   data.forEach((item) => {
    //     const { group, accountName, total } = item;

    //     if (!grouped[group]) {
    //       grouped[group] = {
    //         type: group,
    //         total: 0,
    //         items: [],
    //       };
    //     }

    //     grouped[group].items.push({
    //       name: accountName,
    //       value: total,
    //     });

    //     grouped[group].total += total;
    //   });

    //   // Convert object → array
    //   return Object.values(grouped);
    // }

    // Usage
    // const groupedData = groupPLData(simplified);
    // console.log(groupedData);

    // console.log(simplified);

    // const parsedPnlReport = parseQBReport(pnlReportRaw);
    // console.log("parsedPnlReport", parsedPnlReport);

    // // Get P&L report ID for the month and year
    // const reportId = await IncomeExpenseStatementRepository.getPnlReportId(
    //   schemaName,
    //   monthNum,
    //   yearNum
    // );

    // if (!reportId) {
    //   logger.info(
    //     `No P&L report found for org: ${organization_id}, month: ${month}, year: ${year}`
    //   );
    //   return {
    //     organization_id,
    //     month: monthNum,
    //     year: yearNum,
    //     data: [],
    //     message: "No P&L report found for the specified period",
    //   };
    // }

    // // Fetch line items from qb_pnl_lines
    // const lineItems =
    //   await IncomeExpenseStatementRepository.getIncomeExpenseLineItems(
    //     schemaName,
    //     reportId
    //   );

    // if (!lineItems || lineItems.length === 0) {
    //   logger.info(
    //     `No line items found for org: ${organization_id}, month: ${month}, year: ${year}`
    //   );
    //   return {
    //     organization_id,
    //     month: monthNum,
    //     year: yearNum,
    //     data: [],
    //     message: "No line items found for the specified period",
    //   };
    // }

    // Classify and format the data
    const formattedData = { name: "Income Expense Statement", data: "123" };

    logger.info(
      `Successfully fetched income and expense statement for org: ${organization_id}`
    );

    return {
      organization_id,
      month: monthNum,
      year: yearNum,
      data: transformedData,
    };
  } catch (error) {
    logger.error(
      "Error in IncomeExpenseStatementService.getIncomeExpenseStatement:",
      error
    );
    throw error;
  }
};

/**
 * Classify and format line items based on path patterns
 * @param {Array} lineItems - Raw line items from database
 * @returns {Array} Formatted and classified data
 */
const classifyAndFormatLineItems = (lineItems) => {
  const result = [];

  // Categories to track
  const categories = {
    otherIncomeTop: [],
    practiceIncome: [],
    otherIncome: [],
    payrollExpenses: [],
    otherExpenses: [],
  };

  // Classify each line item based on path
  lineItems.forEach((item) => {
    const path = item.path || "";
    const amount = parseNumericValue(item.amount);

    // Skip zero amounts
    if (amount === 0) return;

    const lineData = {
      path: path,
      account_name: item.account_name,
      amount: Math.round(amount),
    };

    // Classification based on path patterns
    if (path === "Other Income") {
      // Top-level Other Income
      categories.otherIncomeTop.push(lineData);
    } else if (path.includes("Practice Income")) {
      // Practice Income and its breakdown
      categories.practiceIncome.push(lineData);
    } else if (path === "Income") {
      // Other Income (second occurrence in screenshot)
      categories.otherIncome.push(lineData);
    } else if (path.includes("Payroll Expenses")) {
      // Payroll Expenses and its breakdown
      categories.payrollExpenses.push(lineData);
    } else if (path === "Expenses") {
      // Other Expenses
      categories.otherExpenses.push(lineData);
    }
  });

  // Build the response structure matching the screenshot

  // 1. Other Income (top level)
  if (categories.otherIncomeTop.length > 0) {
    const total = Math.round(
      categories.otherIncomeTop.reduce((sum, item) => sum + item.amount, 0)
    );
    result.push({
      category: "Other Income",
      amount: total,
      items: categories.otherIncomeTop.map((item) => ({
        path: item.path,
        account_name: item.account_name,
        amount: item.amount,
      })),
    });
  }

  // 2. Practice Income with breakdown
  if (categories.practiceIncome.length > 0) {
    const total = Math.round(
      categories.practiceIncome.reduce((sum, item) => sum + item.amount, 0)
    );

    // Separate parent and children
    const parent = categories.practiceIncome.find(
      (item) => item.path === "Income > Practice Income"
    );
    const children = categories.practiceIncome.filter(
      (item) => item.path !== "Income > Practice Income"
    );

    result.push({
      category: "Practice Income",
      amount: total,
      items: children.map((item) => ({
        path: item.path,
        account_name: item.account_name,
        amount: item.amount,
      })),
    });
  }

  // 3. Other Income (second occurrence)
  if (categories.otherIncome.length > 0) {
    const total = Math.round(
      categories.otherIncome.reduce((sum, item) => sum + item.amount, 0)
    );
    result.push({
      category: "Other Income",
      amount: total,
      items: categories.otherIncome.map((item) => ({
        path: item.path,
        account_name: item.account_name,
        amount: item.amount,
      })),
    });
  }

  // 4. Payroll Expenses with breakdown
  if (categories.payrollExpenses.length > 0) {
    const total = Math.round(
      categories.payrollExpenses.reduce((sum, item) => sum + item.amount, 0)
    );

    // Separate parent and children
    const children = categories.payrollExpenses.filter(
      (item) => item.path !== "Expenses > Payroll Expenses"
    );

    result.push({
      category: "Payroll Expenses",
      amount: total,
      items: children.map((item) => ({
        path: item.path,
        account_name: item.account_name,
        amount: item.amount,
      })),
    });
  }

  // 5. Other Expenses
  if (categories.otherExpenses.length > 0) {
    const total = Math.round(
      categories.otherExpenses.reduce((sum, item) => sum + item.amount, 0)
    );
    result.push({
      category: "Other Expenses",
      amount: total,
      items: categories.otherExpenses.map((item) => ({
        path: item.path,
        account_name: item.account_name,
        amount: item.amount,
      })),
    });
  }

  return result;
};

export default {
  getIncomeExpenseStatement,
};
