"use client";

import { useEffect, useCallback, useMemo, memo } from "react";
import { motion } from "framer-motion";
import { Button } from "../../ui/button";
import { X } from "lucide-react";
import { formatMessage } from "@/utils/formatMessage";
import { formatSummaryDataToHTML } from "@/utils/summaryFormatter";
import { SummaryLoadingSpinner } from "./SummaryLoadingSpinner";
import "@/styles/dashboardSummary.css";
import "@/styles/summary-popup.css";

const SummaryPopup = memo(function SummaryPopup({
  isOpen,
  onClose,
  dashboardSummary,
  selectedMonth,
  isFinancialSelected,
  isOperationsSelected,
  isPayrollSelected,
  isLoading = false,
  isSummaryLoading = false,
  organizationName,
}) {
  const formattedContent = useMemo(() => {
    if (!dashboardSummary) return "";
    
    // Check if it's JSON format (summaryData object)
    if (typeof dashboardSummary === "object") {
      // Handle nested summaryData structure
      if (dashboardSummary.summaryData) {
        return formatSummaryDataToHTML(dashboardSummary.summaryData);
      }
      // Handle direct summaryData object
      if (dashboardSummary.title && dashboardSummary.sections) {
        return formatSummaryDataToHTML(dashboardSummary);
      }
    }
    
    // Check if it's a stringified JSON
    if (typeof dashboardSummary === "string") {
      try {
        const parsed = JSON.parse(dashboardSummary);
        // Handle nested structure
        if (parsed.summaryData) {
          return formatSummaryDataToHTML(parsed.summaryData);
        }
        // Handle direct structure
        if (parsed.title && parsed.sections) {
          return formatSummaryDataToHTML(parsed);
        }
      } catch {
        // Not JSON, treat as HTML/text
      }
    }
    
    // Fallback to HTML formatting
    return formatMessage(dashboardSummary);
  }, [dashboardSummary]);

  const DASHBOARD_TYPES = {
    FINANCIAL: "Financial",
    OPERATIONS: "Operations",
    PAYROLL: "Payroll",
    DEFAULT: "Dashboard",
  };

  const dashboardType = useMemo(() => {
    if (isFinancialSelected) return DASHBOARD_TYPES.FINANCIAL;
    if (isOperationsSelected) return DASHBOARD_TYPES.OPERATIONS;
    if (isPayrollSelected) return DASHBOARD_TYPES.PAYROLL;
    return DASHBOARD_TYPES.DEFAULT;
  }, [
    isFinancialSelected,
    isOperationsSelected,
    isPayrollSelected,
    DASHBOARD_TYPES.FINANCIAL,
    DASHBOARD_TYPES.OPERATIONS,
    DASHBOARD_TYPES.PAYROLL,
    DASHBOARD_TYPES.DEFAULT,
  ]);

  const dynamicTitle = useMemo(() => {
    return `${dashboardType} Insights`;
  }, [dashboardType]);

  const handleEscKey = useCallback(
    (event) => {
      if (event.key === "Escape" && isOpen) onClose();
    },
    [isOpen, onClose]
  );

  useEffect(() => {
    if (isOpen) document.addEventListener("keydown", handleEscKey);
    return () => {
      document.removeEventListener("keydown", handleEscKey);
    };
  }, [isOpen, handleEscKey]);

  if (!isOpen) return null;

  const loading =
    isLoading || isSummaryLoading || (isOpen && !dashboardSummary);

  return (
    <div className="summary-popup-overlay" onClick={onClose}>
      <motion.div
        initial={{ opacity: 0, scale: 0.98, y: 32 }}
        animate={{ opacity: 1, scale: 1, y: 0 }}
        exit={{ opacity: 0, scale: 0.98, y: 32 }}
        transition={{ duration: 0.22, ease: "easeOut" }}
        className="summary-popup-container"
        onClick={(e) => e.stopPropagation()}
      >
        <div className="summary-popup-header">
          <div className="summary-popup-header-left">
            <div>
              <h3 className="summary-popup-title">
                {dynamicTitle}
                {selectedMonth && (
                  <span className="summary-popup-month-badge">{selectedMonth}</span>
                )}
              </h3>
              <p className="summary-popup-hint">Press ESC to close</p>
            </div>
          </div>
          <Button
            onClick={onClose}
            variant="ghost"
            size="sm"
            className="summary-popup-close-btn"
            title="Close (ESC)"
          >
            <X className="summary-popup-close-icon" />
          </Button>
        </div>

        <div className="summary-popup-content">
          {loading ? (
            <SummaryLoadingSpinner />
          ) : formattedContent ? (
            <div
              className="dashboard-summary-content summary-formatted-content"
              dangerouslySetInnerHTML={{ __html: formattedContent }}
            />
          ) : (
            <div className="summary-popup-empty">
              <p className="summary-popup-empty-text">No summary available</p>
            </div>
          )}
        </div>

        <div className="summary-popup-footer">
          <div className="summary-popup-footer-tip">
            💡 Tip: Press ESC to close quickly
          </div>
          <Button
            onClick={onClose}
            variant="outline"
            className="summary-popup-footer-close"
            title="Close (ESC)"
          >
            Close
          </Button>
        </div>
      </motion.div>
    </div>
  );
});

export default SummaryPopup;
