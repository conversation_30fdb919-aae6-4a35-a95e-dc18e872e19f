// app/routes/chat.route.js
import express from "express";
import {
  handleStartChat,
  handleChatMessage,
  handleChatSummary,
} from "../controllers/chat.controller.js";
import { validateSessionId } from "../middlewares/sessionValidation.middleware.js";
import { validateStartChat } from "../validators/chat.validator.js";

const router = express.Router();

// OPTIMIZATION: Routes ordered by frequency of use (most common first)
// This helps Express match routes faster
router.post("/message", validateSessionId, handleChatMessage); // { sessionId, message, organization? } (chat mode) - MOST FREQUENT
router.post("/summary", validateSessionId, handleChatSummary); // { sessionId, message? } (summary mode; returns JSON format) - FREQUENT
router.post("/start", validateStartChat, handleStartChat); // { filename, orgId, orgName, year, month } - LESS FREQUENT

export default router;
