// app/routes/file.routes.js
import express from "express";
import {
  getReportFolders,
  getReportFiles,
  getReportSummary,
} from "../controllers/file.controller.js";
import { authMiddleware } from "../middleware/auth.middleware.js";

const router = express.Router();

// GET /api/files/report-folders - Returns folder structure for reports
router.get(
  "/report-folders",
  authMiddleware,
  getReportFolders
);

// GET /api/files/report-files - Returns report files for a specific folder
router.get(
  "/report-files",
  authMiddleware,
  getReportFiles
);

// GET /api/files/report-summary - Returns summary for a specific report PDF
router.get(
  "/report-summary",
  authMiddleware,
  getReportSummary
);

export default router;

