{"name": "frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start -p 3010", "lint": "next lint", "format": "npx prettier --write \"**/*.{js,jsx,ts,tsx,json,css,scss,md}\"", "format:check": "npx prettier --check \"**/*.{js,jsx,ts,tsx,json,css,scss,md}\""}, "dependencies": {"@ant-design/icons": "^6.0.0", "@hookform/resolvers": "^5.2.1", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@reduxjs/toolkit": "^2.8.2", "@sendgrid/mail": "^8.1.6", "antd": "^5.27.1", "axios": "^1.11.0", "canvas": "^3.1.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "echarts": "^6.0.0", "echarts-for-react": "^3.0.5", "framer-motion": "^12.23.12", "html-to-image": "^1.11.13", "html2canvas": "^1.4.1", "js-cookie": "^3.0.5", "jspdf": "^3.0.4", "lucide-react": "^0.534.0", "multer": "^2.0.2", "next": "15.4.5", "next-themes": "^0.4.6", "openai": "^5.12.2", "pdfjs-dist": "^3.11.174", "react": "19.1.0", "react-dom": "19.1.0", "react-error-boundary": "^6.0.0", "react-hook-form": "^7.61.1", "react-icons": "^5.5.0", "react-pdf": "^10.0.1", "react-redux": "^9.2.0", "sonner": "^2.0.6", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "zod": "^3.25.76"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "24.1.0", "@types/react": "19.1.9", "autoprefixer": "^10.4.21", "case-sensitive-paths-webpack-plugin": "^2.4.0", "eslint": "^9", "eslint-config-next": "15.4.5", "eslint-import-resolver-alias": "^1.1.2", "eslint-plugin-import": "^2.32.0", "postcss": "^8.5.6", "prettier": "^3.4.2", "tailwindcss": "^4.1.11", "tw-animate-css": "^1.3.6"}}