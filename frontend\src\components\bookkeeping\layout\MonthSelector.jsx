"use client";

import { memo } from "react";
import { DropdownMenu } from "@/components/ui/dropdown-menu";
import { Calendar } from "lucide-react";
import { BOOKCLOSURE_CONSTANTS } from "@/utils/constants/bookclosure";

const MonthSelector = memo(function MonthSelector({
  selectedMonth,
  onChange,
  months,
}) {
  return (
    <div className="bookkeeping-month-selector">
      <div className="bookkeeping-month-selector-header">
        <Calendar className="bookkeeping-month-selector-icon" />
        <h2 className="bookkeeping-month-selector-title">
          {BOOKCLOSURE_CONSTANTS.MONTH_SELECTOR.TITLE}
        </h2>
      </div>
      <DropdownMenu
        label={selectedMonth}
        options={months.map((month) => ({ label: month, value: month }))}
        selected={selectedMonth}
        onSelect={onChange}
        buttonClassName="w-full px-3 py-2.5 sm:px-4 rounded-lg border border-gray-300 bg-white text-gray-700 hover:border-indigo-400 hover:bg-indigo-50 transition-colors flex justify-between items-center text-sm sm:text-base min-h-[2.75rem] touch-manipulation"
        menuClassName="w-full"
      />
    </div>
  );
});

MonthSelector.displayName = "MonthSelector";

export default MonthSelector;
