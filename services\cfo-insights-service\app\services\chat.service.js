// app/services/chat.service.js
import crypto from "node:crypto";
import { chatWithContext, generateFinancialSummaryJSON } from "../utils/azureOpenAI.js";
import { downloadBlobBuffer } from "../utils/blobClient.js";
import { extractTextFromPdfBuffer } from "../utils/pdfLoader.js";
import { httpGet } from "../../../../shared/utils/axios.util.js";
import {
  createSession,
  getSession,
  appendHistory,
  getCachedDocText,
  setCachedDocText,
  deleteSession,
} from "../utils/memoryStore.js";
import { searchCompetitorData } from "../utils/azureSearchClient.js";
import { ERROR_MESSAGES } from "../utils/constants/error.constants.js";
import { TIMING_CONSTANTS } from "../utils/constants/timing.constants.js";
import { CHAT_CONSTANTS } from "../utils/constants/chat.constants.js";
import { LOG_MESSAGES } from "../utils/constants/logMessages.constants.js";
import { resolveDocumentRecord } from "./documentClient.service.js";
import { toBoolean } from "../utils/helpers.js";
import logger from "../config/logger.config.js";

async function fetchOrganizationName(orgId, enforceExists = false) {
  const baseUrl = process.env.AUTH_SERVICE_URL;
  if (!baseUrl || !orgId) return null;
  const url = `${baseUrl}/organization/${orgId}`;
  try {
    const headers = {};
    if (process.env.AUTH_SERVICE_API_KEY) {
      headers["x-api-key"] = process.env.AUTH_SERVICE_API_KEY;
    }
    const res = await httpGet(url, { headers, timeout: 5000 });
    const name = res?.data?.data?.name || res?.data?.name || null;
    if (!name && enforceExists) {
      throw new Error("Organization not found");
    }
    return name;
  } catch (error) {
    if (enforceExists && error?.response?.status === 404) {
      throw new Error("Organization not found");
    }
    if (enforceExists) {
      throw new Error(error.message || "Failed to fetch organization");
    }
    logger.warn("Failed to fetch organization name from auth service", {
      orgId,
      error: error.message,
      status: error.response?.status,
    });
    return null;
  }
}

/**
 * Helpers
 */

// sanitizeToPlain and toBoolean moved to app/utils/helpers.js

function normalizeValue(value) {
  if (value === undefined || value === null) return null;
  const trimmed = String(value).trim();
  return trimmed.length ? trimmed.toLowerCase() : null;
}

/** Ensure a valid active session, throw if missing */
function ensureSession(sessionId) {
  const session = getSession(sessionId);
  if (!session) throw new Error(ERROR_MESSAGES.CHAT.INVALID_SESSION_ID);
  return session;
}

/**
 * Get document text (with caching). Downloads and extracts full text when needed.
 * This is the primary method for retrieving document content - no vector search, just full text extraction.
 */
async function getDocumentText(blobName) {
  // Check cache first
  const cached = getCachedDocText(blobName);
  if (cached) {
    logger.info("Retrieved document text from cache", { blobName, textLength: cached.length });
    return cached;
  }

  // Download blob from Azure Blob Storage
  logger.info("Downloading blob for text extraction", { blobName });
  const buffer = await downloadBlobBuffer(blobName);

  // Detect file type by extension (JSON vs PDF)
  const isJson = String(blobName || "").toLowerCase().endsWith(".json");

  let text;
  if (isJson) {
    try {
      const raw = buffer.toString("utf8");
      const parsed = JSON.parse(raw);
      text = JSON.stringify(parsed, null, 2);
    } catch (e) {
      logger.error("Failed to parse JSON blob", { blobName, error: e.message });
      throw new Error(`${ERROR_MESSAGES.GENERAL.DOCUMENT_PROCESSING_FAILED}: Invalid JSON content`);
    }
  } else {
    // Extract full text from PDF (fallback)
    logger.info("Extracting text from PDF", { blobName, bufferSize: buffer.length });
    text = await extractTextFromPdfBuffer(buffer);
  }

  if (!text || text.trim().length === 0) {
    throw new Error(`${ERROR_MESSAGES.GENERAL.DOCUMENT_PROCESSING_FAILED}: No text extracted from document`);
  }

  // Cache the extracted text in-memory (private, no external storage)
  setCachedDocText(blobName, text);
  logger.info("Document text extracted and cached", { blobName, textLength: text.length });
  
  return text;
}

/** Add org context to message when in summary mode and not already present */
function enhanceMessageWithOrganization(message, organization, summaryMode) {
  if (!summaryMode || !organization) return message;
  const regex = new RegExp(organization, "i");
  if (regex.test(message)) return message;
  const enhancementTemplate = CHAT_CONSTANTS.SUMMARY_ENHANCEMENT_SUFFIX;
  return `${message}. ${enhancementTemplate.replace(
    "{organization}",
    organization
  )}`;
}

const valuesDiffer = (expected, actual) =>
  normalizeValue(expected) !== normalizeValue(actual);

/** Collect competitor data for summary mode based on user intent and organization */
async function collectCompetitorData(
  processedMessage,
  organization,
  summaryMode,
  skipCompetitorSearch = false
) {
  // OPTIMIZATION: Fast path - return early if no competitor search needed
  if (!summaryMode || skipCompetitorSearch || !organization) {
    return [];
  }

  // OPTIMIZATION: Quick regex check before making API call
  const comparisonMatch = processedMessage.match(/vs\s+([\w\s&]+)/i);
  const hasComparisonIntent =
    comparisonMatch ||
    /(compare|vs|versus|competitor|benchmark|against)/i.test(processedMessage);

  if (!hasComparisonIntent) return [];

  const searchQuery = comparisonMatch?.[1]?.trim() || organization;

  // OPTIMIZATION: Use timeout protection - if search fails, continue without competitor data
  try {
    return await Promise.race([
      searchCompetitorData(searchQuery, TIMING_CONSTANTS.COMPETITOR_SEARCH_MS),
      new Promise((_, reject) => 
        setTimeout(() => reject(new Error('Competitor search timeout')), TIMING_CONSTANTS.COMPETITOR_SEARCH_MS)
      )
    ]);
  } catch (error) {
    // Fail silently - competitor data is optional and shouldn't block the request
    logger.warn(LOG_MESSAGES.CHAT.COMPETITOR_SEARCH_FAILED, { error: error.message });
    return [];
  }
}

/**
 * Start chat session tied to a specific document.
 * @param {string} filename - File name (required)
 * @param {Object} options - Options object
 * @param {string} options.organizationId - Organization ID (required)
 * @param {string} options.organizationName - Organization name (required)
 * @param {number} options.month - Month (1-12, required)
 * @param {number} options.year - Year (required)
 * @param {Object} [options.resolvedDocument] - Pre-resolved document (optional)
 * @param {string} [options.folder] - Folder name (optional)
 * @param {string} [options.months] - Months string (optional)
 */
export async function startChat(filename, options = {}) {
  const {
    organizationId,
    organizationName,
    resolvedDocument,
    month,
    year,
    folder,
    months,
    service,
    blobStoragePath,
    blobBasePath,
  } = options || {};

  // Validate required parameters
  if (!filename || typeof filename !== "string" || !filename.trim()) {
    throw new Error(ERROR_MESSAGES.CHAT.FILENAME_REQUIRED);
  }

  let inferred = null;
  if (blobStoragePath && typeof blobStoragePath === "string") {
    const parts = blobStoragePath.split("/").filter(Boolean);
    // Expected: orgId / orgName / [Reports folder] / service / year / month / file
    // Find index of folder containing "reports" (case-insensitive) - handles "Reports", "Power BI-Reports", etc.
    const reportsIndex = parts.findIndex(part => part.toLowerCase().includes("reports"));
    
    if (reportsIndex >= 0 && parts.length >= reportsIndex + 5) {
      const inferredOrgId = parts[0];
      const inferredOrgName = parts[1];
      const inferredService = parts[reportsIndex + 1];
      const inferredYear = Number.parseInt(parts[reportsIndex + 2], 10);
      const monthToken = parts[reportsIndex + 3];
      const monthIndex =
        CHAT_CONSTANTS.MONTH_NAMES.findIndex(
          (m) => m.toLowerCase() === String(monthToken).toLowerCase()
        ) + 1;
      const inferredMonth = monthIndex > 0 ? monthIndex : Number.parseInt(monthToken, 10);
      inferred = {
        blobName: parts.join("/"),
        organizationId: inferredOrgId,
        organizationName: inferredOrgName,
        service: inferredService,
        year: inferredYear,
        month: inferredMonth,
      };
    } else {
      throw new Error("blobStoragePath is invalid. Expected orgId/orgName/[Reports or Power BI-Reports]/service/year/month/file");
    }
  }

  if (!inferred && blobBasePath && typeof blobBasePath === "string" && year && month) {
    const baseParts = blobBasePath.split("/").filter(Boolean);
    const reportsIndex = baseParts.findIndex(part => part.toLowerCase().includes("reports"));
    if (reportsIndex >= 0 && baseParts.length >= reportsIndex + 2) {
      const inferredOrgId = baseParts[0];
      const inferredOrgName = baseParts[1];
      const inferredService = baseParts[reportsIndex + 1];
      const monthName =
        CHAT_CONSTANTS.MONTH_NAMES[Number.parseInt(month, 10) - 1] || String(month);
      const blobName = `${baseParts.join("/")}/${year}/${monthName}/${filename.trim()}`;
      inferred = {
        blobName,
        organizationId: inferredOrgId,
        organizationName: inferredOrgName,
        service: inferredService,
        year,
        month,
      };
    } else {
      throw new Error("blobBasePath is invalid. Expected orgId/orgName/[Power BI-Reports]/service");
    }
  }

  const finalOrgIdInput = (organizationId || inferred?.organizationId || "").trim();
  let finalOrgNameInput = (organizationName || inferred?.organizationName || "").trim();
  const finalMonthInput = month ?? inferred?.month;
  const finalYearInput = year ?? inferred?.year;
  const finalServiceInput = service || inferred?.service || folder;

  // If orgName missing but orgId present, try to fetch from auth service (enforced when available)
  if (finalOrgIdInput && process.env.AUTH_SERVICE_URL) {
    const fetchedName = await fetchOrganizationName(finalOrgIdInput, true);
    if (!finalOrgNameInput && fetchedName) {
      finalOrgNameInput = fetchedName.trim();
    }
  }

  const allowNoOrg = !!inferred;

  if (!allowNoOrg) {
    if (!finalOrgIdInput) {
      throw new Error(ERROR_MESSAGES.VALIDATION.ORGANIZATION_ID_REQUIRED);
    }
    if (!finalOrgNameInput) {
      throw new Error(ERROR_MESSAGES.VALIDATION.ORGANIZATION_NAME_REQUIRED);
    }
    if (finalMonthInput === undefined || finalMonthInput === null || Number.isNaN(Number.parseInt(finalMonthInput, 10))) {
      throw new Error(ERROR_MESSAGES.VALIDATION.MONTH_REQUIRED);
    }
    if (finalYearInput === undefined || finalYearInput === null || Number.isNaN(Number.parseInt(finalYearInput, 10))) {
      throw new Error(ERROR_MESSAGES.VALIDATION.YEAR_REQUIRED);
    }
  }

  let resolved = resolvedDocument;

  if (!resolved) {
    let resolveError = null;
    let resolution = inferred
      ? {
          blobName: inferred.blobName,
          sasUrl: null,
          fileName: filename.trim(),
          organizationId: inferred.organizationId,
          organizationName: inferred.organizationName,
          month: inferred.month,
          year: inferred.year,
        }
      : null;

    // If we already have blobName from base/path, skip file-service resolution
    if (!resolution) {
      // If FILE_SERVICE_URL missing, force blob path requirement
      if (!process.env.FILE_SERVICE_URL) {
        throw new Error(`${ERROR_MESSAGES.GENERAL.FILE_SERVICE_URL_MISSING}. Provide blobStoragePath or blobBasePath.`);
      }

      const { error: rError, data: rData } =
        await resolveDocumentRecord({
          filename: filename.trim(),
          organizationId: finalOrgIdInput,
          organizationName: finalOrgNameInput,
          month: finalMonthInput,
          year: finalYearInput,
          folder,
          months,
          service: finalServiceInput
        });
      resolveError = rError;
      resolution = rData;
    }

    // Extract folder/service from parameters or filename for error messages
    // Priority: service > folder > extract from filename > default to Finance
    let resolvedFolder = finalServiceInput || folder;
    if (!resolvedFolder && filename) {
      const filenameTrimmed = filename.trim();
      for (const validFolder of CHAT_CONSTANTS.VALID_FOLDERS) {
        if (filenameTrimmed.startsWith(validFolder)) {
          resolvedFolder = validFolder;
          break;
        }
      }
    }
    const folderForPath = resolvedFolder || CHAT_CONSTANTS.VALID_FOLDERS[0]; // Default to Finance if not found

    if (resolveError) {
      // Use the error message from resolveDocumentRecord which already has the correct path
      throw new Error(resolveError);
    }

    if (!resolution) {
      // Build detailed error message with file path
      const monthNum = Number.parseInt(finalMonthInput ?? inferred?.month ?? 0, 10);
      const yearNum = Number.parseInt(finalYearInput ?? inferred?.year ?? 0, 10);
      const monthName = CHAT_CONSTANTS.MONTH_NAMES[monthNum - 1] || monthNum;
      const filePath = `${finalOrgIdInput}/${finalOrgNameInput}/Reports/${finalServiceInput || resolvedFolder}/${yearNum}/${monthName}/`;
      console.log({filePath});
      throw new Error(
        `${ERROR_MESSAGES.GENERAL.FILE_RESOLUTION_FAILED}: File "${filename.trim()}" not found at path: ${filePath}. ${ERROR_MESSAGES.GENERAL.FILE_RESOLUTION_NO_DATA}`
      );
    }

    if (!resolution.blobName) {
      const monthNum = Number.parseInt(finalMonthInput ?? resolution.month ?? 0, 10);
      const yearNum = Number.parseInt(finalYearInput ?? resolution.year ?? 0, 10);
      const monthName = CHAT_CONSTANTS.MONTH_NAMES[monthNum - 1] || monthNum;
      const filePath = `${finalOrgIdInput}/${finalOrgNameInput}/Reports/${folderForPath}/${yearNum}/${monthName}/`;
      
      throw new Error(
        `${ERROR_MESSAGES.GENERAL.FILE_RESOLUTION_INCOMPLETE}: File "${filename.trim()}" found but ${ERROR_MESSAGES.GENERAL.FILE_MISSING_BLOB_METADATA} (sasUrl or blobName) at path: ${filePath}`
      );
    }

    resolved = resolution;
  }

  const resolvedFileName = resolved.fileName || filename.trim();
  const sasUrl = resolved.sasUrl;
  const blobName = resolved.blobName;
  const resolvedOrgIdFromDoc = resolved.organizationId;
  const resolvedOrgNameFromDoc = resolved.organizationName;
  const effectiveMonth = resolved.month ?? finalMonthInput;
  const effectiveYear = resolved.year ?? finalYearInput;

  // Validate organization context matches if provided in document
  if (resolvedOrgIdFromDoc && valuesDiffer(finalOrgIdInput, resolvedOrgIdFromDoc)) {
    throw new Error(
      `${ERROR_MESSAGES.CHAT.ORGANIZATION_CONTEXT_MISMATCH}. Expected: ${finalOrgIdInput}, Found in document: ${resolvedOrgIdFromDoc}`
    );
  }

  if (resolvedOrgNameFromDoc && valuesDiffer(finalOrgNameInput, resolvedOrgNameFromDoc)) {
    throw new Error(
      `${ERROR_MESSAGES.CHAT.ORGANIZATION_CONTEXT_MISMATCH}. Expected: ${finalOrgNameInput}, Found in document: ${resolvedOrgNameFromDoc}`
    );
  }

  const resolvedOrgId = resolvedOrgIdFromDoc || finalOrgIdInput;
  const resolvedOrgName = resolvedOrgNameFromDoc || finalOrgNameInput;

  // Extract folder/service from filename for error messages
  let folderForError = folder;
  if (!folderForError && resolvedFileName) {
    const filenameTrimmed = resolvedFileName.trim();
    for (const validFolder of CHAT_CONSTANTS.VALID_FOLDERS) {
      if (filenameTrimmed.startsWith(validFolder)) {
        folderForError = validFolder;
        break;
      }
    }
  }
  const folderForPath = folderForError || CHAT_CONSTANTS.VALID_FOLDERS[0]; // Default to Finance if not found

  if (!blobName) {
    const monthNum = Number.parseInt(effectiveMonth, 10);
    const yearNum = Number.parseInt(effectiveYear, 10);
    const monthName = CHAT_CONSTANTS.MONTH_NAMES[monthNum - 1] || monthNum;
    const filePath = `${resolvedOrgId}/${resolvedOrgName}/Reports/${folderForPath}/${yearNum}/${monthName}/`;
    
    throw new Error(
      `${ERROR_MESSAGES.GENERAL.FILE_RESOLUTION_INCOMPLETE}: File "${resolvedFileName}" is ${ERROR_MESSAGES.GENERAL.FILE_MISSING_BLOB_METADATA} (sasUrl: ${!!sasUrl}, blobName: ${!!blobName}) at path: ${filePath}`
    );
  }

  // Create session and extract full PDF text immediately
  // This ensures document is fully processed and ready for chat
  const sessionId = crypto.randomUUID();
  const monthNum = Number.parseInt(effectiveMonth, 10);
  const yearNum = Number.parseInt(effectiveYear, 10);
  
  createSession({
    sessionId,
    filename: resolvedFileName,
    sasUrl,
    blobName,
    organizationId: resolvedOrgId,
    organizationName: resolvedOrgName,
    year: yearNum,
    month: monthNum,
    service: finalServiceInput || null,
  });

  // Extract and cache full PDF text (100% extraction)
  // This happens synchronously to ensure text is available for first message
  try {
    await getDocumentText(blobName);
    logger.info("PDF text extracted and cached during session start", { sessionId, blobName });
  } catch (err) {
    logger.error("Failed to extract PDF text during session start", { 
      sessionId, 
      blobName, 
      error: err.message 
    });
    // Don't throw - let first message attempt trigger extraction again
  }
  return {
    sessionId,
    filename: resolvedFileName,
    organizationId: resolvedOrgId,
    organizationName: resolvedOrgName,
  };
}

/**
 * Send message — handles competitor comparison & organization fallback.
 * @param {string} sessionId - Chat session ID
 * @param {string} userMessage - User's message/question
 * @param {string} [organization] - Organization name for context
 * @param {boolean} [summaryMode=false] - If true, use summary prompt; if false, use conversational chat prompt
 * @param {boolean} [skipCompetitorSearch=false] - If true, skip competitor search for faster responses
 * @param {boolean} [jsonFormat=false] - If true, return JSON format instead of HTML (only applies when summaryMode=true)
 */
export async function sendMessage(
  sessionId,
  userMessage,
  organization,
  summaryMode = false,
  skipCompetitorSearch = false,
  jsonFormat = false
) {
  const {
    filename,
    history,
    sasUrl,
    blobName,
    organizationId,
    organizationName,
    service,
  } = ensureSession(sessionId);

  if (
    organization &&
    organizationName &&
    normalizeValue(organization) !== normalizeValue(organizationName)
  ) {
    throw new Error(ERROR_MESSAGES.CHAT.ORGANIZATION_CONTEXT_MISMATCH);
  }

  const isSummary = toBoolean(summaryMode, false);

  // OPTIMIZATION: Check if this is a default summary request BEFORE enhancement
  // This prevents competitor search when org enhancement adds comparison keywords
  const originalMessageHasComparison =
    /(compare|vs|versus|competitor|benchmark|against)/i.test(userMessage);
  const isDefaultSummary = isSummary && !originalMessageHasComparison;
  const shouldSkipCompetitor = skipCompetitorSearch || isDefaultSummary;

  const effectiveOrganization = organization || organizationName || null;

  const processedMessage = enhanceMessageWithOrganization(
    userMessage,
    effectiveOrganization,
    isSummary
  );

  // OPTIMIZATION: Run document fetch and competitor search in parallel with timeout protection
  // Use Promise.allSettled to prevent one failure from blocking the other
  const [docTextResult, competitorDataResult] = await Promise.allSettled([
    getDocumentText(blobName),
    collectCompetitorData(
      processedMessage,
      effectiveOrganization,
      isSummary,
      shouldSkipCompetitor
    ),
  ]);

  // Extract results safely
  const docText = docTextResult.status === 'fulfilled' 
    ? docTextResult.value 
    : (() => {
        logger.error(LOG_MESSAGES.CHAT.DOCUMENT_TEXT_FETCH_FAILED, { error: docTextResult.reason });
        throw new Error(ERROR_MESSAGES.GENERAL.DOCUMENT_PROCESSING_FAILED);
      })();
  
  const competitorData = competitorDataResult.status === 'fulfilled'
    ? competitorDataResult.value
    : (() => {
        logger.warn(LOG_MESSAGES.CHAT.COMPETITOR_SEARCH_FAILED, { error: competitorDataResult.reason?.message });
        return [];
      })();

  const competitorBlock = competitorData.length
    ? `${CHAT_CONSTANTS.COMPETITOR_DATA_HEADER.replace(
        "{count}",
        competitorData.length
      )}\n${competitorData
        .map(
          (s) =>
            `${s.title}\n${s.snippet}\n(Source: ${
              s.url || CHAT_CONSTANTS.COMPETITOR_SOURCE_FALLBACK
            })`
        )
        .join(CHAT_CONSTANTS.COMPETITOR_SECTION_SEPARATOR)}`
    : "";

  const fullContext = `${CHAT_CONSTANTS.DOCUMENT_CONTEXT_HEADER}\n${docText}\n\n${competitorBlock}`;

  let answer;
  
  // SUMMARY MODE: Two-step approach (Primary) → Single-step fallback
  // STEP 1: financialData.prompt.js extracts structured JSON
  // STEP 2: financialHTML.prompt.js converts JSON to HTML (or financialJSON.prompt.js for JSON format)
  // FALLBACK: summary.prompt.js (if two-step fails)
  if (isSummary) {
    try {
      if (jsonFormat) {
        // JSON FORMAT PATH: Two-step generation using financialData.prompt.js + financialJSON.prompt.js
        const jsonResult = await generateFinancialSummaryJSON({
          contextText: fullContext,
          organization: effectiveOrganization,
          service,
          maxTokens: 2000
        });
        // Return JSON object directly (will be handled by controller)
        return {
          jsonAnswer: jsonResult,
          filename,
          organizationId,
          organizationName: effectiveOrganization,
        };
      } else {
        // HTML FORMAT PATH: Use JSON format (generateFinancialSummary is deprecated)
        answer = await generateFinancialSummaryJSON({
          contextText: fullContext,
          organization: effectiveOrganization,
          service,
          maxTokens: 2000
        });
      }
    } catch (error) {
      // FALLBACK PATH: Single-step generation using summary.prompt.js
      // This happens if JSON extraction or HTML formatting fails
      logger.warn(LOG_MESSAGES.CHAT.TWO_STEP_SUMMARY_FAILED, { error: error.message });
      const maxTokens = 1200;
      answer = await chatWithContext({
        contextText: fullContext,
        userQuestion: processedMessage,
        history,
        summaryMode: true, // This triggers buildSummarySystemPrompt() from summary.prompt.js
        organization: effectiveOrganization,
        service,
        maxTokens,
      });
    }
  } else {
    // Use single-step approach for regular chat
    const maxTokens = 3000;
    answer = await chatWithContext({
      contextText: fullContext,
      userQuestion: processedMessage,
      history,
      summaryMode: false,
      organization: effectiveOrganization,
      service,
      maxTokens,
    });
  }

  appendHistory(sessionId, "user", processedMessage);
  
  // Helper: format summary JSON to plain text
  const formatSummaryPlain = (summaryJson) => {
    if (!summaryJson || typeof summaryJson !== "object") return "";
    const lines = [];
    if (summaryJson.title) lines.push(String(summaryJson.title));
    if (Array.isArray(summaryJson.sections)) {
      for (const section of summaryJson.sections) {
        if (section?.heading) lines.push(`\n${section.heading}:`);
        if (section?.type === "list" && Array.isArray(section.data)) {
          section.data.forEach((item) => lines.push(`- ${item}`));
        } else if (section?.type === "paragraph" && section.data) {
          lines.push(String(section.data));
        } else if (section?.type === "table" && section.data?.rows) {
          const headers = section.data.headers || [];
          const rows = section.data.rows;
          rows.forEach((row) => {
            if (Array.isArray(row)) {
              lines.push(row.join(" | "));
            } else if (row && typeof row === "object") {
              lines.push(headers.map((h) => row[h] ?? "").join(" | "));
            }
          });
        }
      }
    }
    return lines.join("\n");
  };

  // Handle JSON response for regular chat (summaryMode=false)
  if (!isSummary && typeof answer === "object" && answer.responseType) {
    appendHistory(sessionId, "assistant", JSON.stringify(answer));
    return {
      jsonAnswer: answer,
      filename,
      organizationId,
      organizationName: effectiveOrganization,
    };
  }
  
  // Handle summary: always return plain text
  if (isSummary) {
    if (typeof answer === "object" && answer !== null) {
      const plain = formatSummaryPlain(answer);
      appendHistory(sessionId, "assistant", plain || JSON.stringify(answer));
      return {
        plainAnswer: plain || JSON.stringify(answer),
        filename,
        organizationId,
        organizationName: effectiveOrganization,
      };
    }
    if (typeof answer === "string") {
      try {
        const { parseJsonSafely } = await import("../utils/jsonCleaner.util.js");
        const parsed = parseJsonSafely(answer);
        if (jsonFormat && parsed && typeof parsed === "object") {
          appendHistory(sessionId, "assistant", JSON.stringify(parsed));
          return {
            jsonAnswer: parsed,
            filename,
            organizationId,
            organizationName: effectiveOrganization,
          };
        }
        const plain = formatSummaryPlain(parsed);
        appendHistory(sessionId, "assistant", plain || answer);
        return {
          plainAnswer: plain || answer,
          filename,
          organizationId,
          organizationName: effectiveOrganization,
        };
      } catch {
        appendHistory(sessionId, "assistant", answer);
        return {
          plainAnswer: answer,
          filename,
          organizationId,
          organizationName: effectiveOrganization,
        };
      }
    }
    appendHistory(sessionId, "assistant", String(answer));
    return {
      plainAnswer: String(answer),
      filename,
      organizationId,
      organizationName: effectiveOrganization,
    };
  }
  
  appendHistory(sessionId, "assistant", String(answer));
  return {
    plainAnswer: String(answer),
    filename,
    organizationId,
    organizationName: effectiveOrganization,
  };
}

/**
 * End chat session — explicitly expire a sessionId
 */
export async function endChat(sessionId) {
  const id = String(sessionId || "").trim();
  if (!id) throw new Error(ERROR_MESSAGES.CHAT.SESSION_ID_REQUIRED);
  const removed = deleteSession(id);
  if (!removed) throw new Error(ERROR_MESSAGES.CHAT.INVALID_SESSION_ID);
  return { success: true, sessionId: id };
}
