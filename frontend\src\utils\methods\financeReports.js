const DEFAULT_CURRENCY = "USD";

export const formatCurrencyNumber = (value, currency = DEFAULT_CURRENCY) => {
  if (value === null || value === undefined || Number.isNaN(Number(value))) {
    return "—";
  }
  const num = Number(value);
  const symbol = currency === "USD" ? "$" : "";
  const formatted = Math.abs(num).toLocaleString("en-US", {
    maximumFractionDigits: 0,
  });
  return num < 0 ? `(${symbol}${formatted})` : `${symbol}${formatted}`;
};

export const formatPercent = (value) => {
  if (value === null || value === undefined || Number.isNaN(Number(value))) {
    return "0%";
  }
  return `${Number(value).toFixed(2)}%`;
};

export const parseKValueToNumber = (value) => {
  if (value === null || value === undefined) return 0;
  if (typeof value === "number") return value;
  if (typeof value === "string") {
    const cleaned = value.replace(/[\$,]/g, "").trim().toUpperCase();
    const isK = cleaned.endsWith("K");
    const numeric = parseFloat(isK ? cleaned.slice(0, -1) : cleaned);
    if (Number.isNaN(numeric)) return 0;
    return isK ? numeric * 1000 : numeric;
  }
  return 0;
};

const parsePercentageChange = (changeString) => {
  if (!changeString || typeof changeString !== "string") return null;
  const trimmed = changeString.trim();
  if (!trimmed) return null;

  const isNegative = trimmed.startsWith("-");
  const cleaned = trimmed.replace(/[+%-]/g, "");
  const numeric = parseFloat(cleaned);

  if (Number.isNaN(numeric)) return null;

  const sign = isNegative ? "-" : "+";
  const formatted = `${sign}${Math.abs(numeric).toFixed(2)}%`;

  return {
    value: isNegative ? -Math.abs(numeric) : Math.abs(numeric),
    formatted,
    isPositive: !isNegative,
  };
};

export const mapCashflowByGroupData = (payload = {}) => {
  const cashFlow =
    payload?.data?.cash_flow ||
    payload?.cash_flow ||
    payload?.data?.cashFlow ||
    {};
  const series = [
    { label: "Operating", key: "operating" },
    { label: "Investing", key: "investing" },
    { label: "Financing", key: "financing" },
    { label: "Net Cashflow", key: "net_cash_flow" },
  ];

  return {
    categories: series.map((item) => item.label),
    values: series.map((item) => Number(cashFlow[item.key] ?? 0)),
  };
};

export const mapRevExpNetChart = (payload = {}) => {
  const data =
    payload?.data?.revenue_expense ||
    payload?.data ||
    payload?.revenue_expense ||
    payload;
  return {
    categories: [""],
    revenue: [parseKValueToNumber(data.revenue)],
    expenses: [parseKValueToNumber(data.expense)],
    netIncome: [parseKValueToNumber(data.net_income)],
  };
};

export const mapExpenseBreakdownChart = (payload = {}) => {
  const breakdown =
    payload?.data?.breakdown ||
    payload?.data?.expense_breakdown?.breakdown ||
    payload?.breakdown ||
    payload?.expense_breakdown?.breakdown ||
    [];
  return {
    data: breakdown.map((item) => ({
      name: item?.category || "Unknown",
      value: parseKValueToNumber(item?.amount),
    })),
  };
};

export const mapKpiData = (payload = {}) => {
  const kpiPayload = payload?.data?.kpi || payload?.kpi || {};
  const revenueTotal = Number(kpiPayload.revenue?.total ?? 0);
  const expenseTotal = Number(kpiPayload.expense?.total ?? 0);
  const revenuePm = Number(kpiPayload.revenue?.pm ?? 0);
  const expensePm = Number(kpiPayload.expense?.pm ?? 0);
  const revenueYtd = Number(kpiPayload.revenue?.ytd ?? 0);
  const expenseYtd = Number(kpiPayload.expense?.ytd ?? 0);
  const revenueChange = parsePercentageChange(kpiPayload.revenue?.pm_change);

  const incomeTotal = Number(kpiPayload.income?.total ?? 0);
  const incomePm = Number(kpiPayload.income?.pm ?? 0);
  const incomeYtd = Number(kpiPayload.income?.ytd ?? 0);
  const incomeChange = parsePercentageChange(kpiPayload.income?.pm_change);

  const cashflowTotal = Number(kpiPayload.cashflow?.net_cashflow ?? 0);
  const cashflowPm = Number(kpiPayload.cashflow?.pm ?? 0);
  const cashflowYtd = Number(kpiPayload.cashflow?.ytd ?? 0);
  const cashflowChange = parsePercentageChange(kpiPayload.cashflow?.pm_change);

  const profitMarginTotal = Number(
    kpiPayload.profit_margin?.profit_margin ?? 0
  );
  const profitMarginPm = Number(kpiPayload.profit_margin?.pm ?? 0);
  const profitMarginYtd = Number(kpiPayload.profit_margin?.ytd ?? 0);
  const profitMarginChange = parsePercentageChange(
    kpiPayload.profit_margin?.pm_change
  );

  const expenseChange = parsePercentageChange(kpiPayload.expense?.pm_change);

  const currency =
    payload?.data?.currency ||
    payload?.currency ||
    kpiPayload?.currency ||
    DEFAULT_CURRENCY;

  return {
    revenue: {
      amount: formatCurrencyNumber(revenueTotal, currency),
      pm: formatCurrencyNumber(revenuePm, currency),
      ytd: formatCurrencyNumber(revenueYtd, currency),
      change: revenueChange?.formatted,
      isPositive: revenueChange?.isPositive,
    },
    expenses: {
      amount: formatCurrencyNumber(expenseTotal, currency),
      pm: formatCurrencyNumber(expensePm, currency),
      ytd: formatCurrencyNumber(expenseYtd, currency),
      change: expenseChange?.formatted,
      isPositive: expenseChange?.isPositive,
    },
    income: {
      amount: formatCurrencyNumber(incomeTotal, currency),
      pm: formatCurrencyNumber(incomePm, currency),
      ytd: formatCurrencyNumber(incomeYtd, currency),
      change: incomeChange?.formatted,
      isPositive: incomeChange?.isPositive,
    },
    netCashflow: {
      amount: formatCurrencyNumber(cashflowTotal, currency),
      pm: formatCurrencyNumber(cashflowPm, currency),
      ytd: formatCurrencyNumber(cashflowYtd, currency),
      change: cashflowChange?.formatted,
      isPositive: cashflowChange?.isPositive,
    },
    profitMargin: {
      amount: formatPercent(profitMarginTotal),
      pm: formatPercent(profitMarginPm),
      ytd: formatPercent(profitMarginYtd),
      change: profitMarginChange?.formatted,
      isPositive: profitMarginChange?.isPositive,
    },
  };
};

const formatCurrency = (value, currency = DEFAULT_CURRENCY) => {
  if (value === null || value === undefined || value === "") return "—";
  const symbol = currency === "USD" ? "$" : "";
  return `${symbol}${value}`;
};

export const mapBalanceSheetData = (payload = {}) => {
  const currency =
    payload?.data?.currency ||
    payload?.currency ||
    payload?.data?.balance_sheet?.currency ||
    payload?.balance_sheet?.currency ||
    DEFAULT_CURRENCY;

  const sectionsCandidate =
    payload?.data?.data ||
    payload?.data?.balance_sheet?.data ||
    payload?.balance_sheet?.data ||
    payload?.data ||
    [];

  const sections = Array.isArray(sectionsCandidate)
    ? sectionsCandidate
    : Array.isArray(sectionsCandidate?.data)
      ? sectionsCandidate.data
      : [];

  return sections.map((section) => ({
    label: section.section || section.name || "Section",
    value: formatCurrencyNumber(section.total ?? 0, currency),
    bold: true,
    children: (section.subsections || []).map((sub) => ({
      label: sub.name || "Subtotal",
      value: formatCurrencyNumber(sub.total ?? 0, currency),
      bold: true,
      children: (sub.accounts || []).map((account) => ({
        label: account.name || account.account_name || "Account",
        value: formatCurrencyNumber(account.amount ?? 0, currency),
      })),
    })),
  }));
};

export const mapIncomeStatementData = (apiResponse) => {
  const result = [];

  const formatAmount = (value) => {
    if (value === "" || value === null || value === undefined) return "";
    const num = Number(value);
    if (isNaN(num)) return value;

    const formatted = Math.abs(num).toLocaleString("en-US", {
      maximumFractionDigits: 0,
    });
    return num < 0 ? `($${formatted})` : `$${formatted}`;
  };

  const pushRow = (label, value, bold) => {
    result.push({
      label: label || "",
      value: formatAmount(value),
      bold,
    });
  };

  const processRows = (rows) => {
    rows?.forEach((row) => {
      if (row.type === "summary") {
        pushRow(row.account, row.amount, true);
        return;
      }

      if (row.children) {
        pushRow(row.account, "", true);
        processRows(row.children);
      } else {
        pushRow(row.account, row.amount, false);
      }
    });
  };

  apiResponse?.data?.data?.forEach((section) => {
    const rows = section.rows ?? [];

    // Rule: Skip section label if only one summary child
    const skipSectionHeading = rows.length === 1 && rows[0].type === "summary";

    if (!skipSectionHeading) {
      pushRow(section.section, "", true);
    }

    processRows(rows);
  });

  return result;
};

export const buildFinanceApiResponsePayload = (payload = {}) => {
  const data = payload?.data ?? payload ?? {};

  return {
    meta: {
      orgId: data?.org_id || data?.organization_id || null,
      month: data?.month ?? null,
      year: data?.year ?? null,
      currency: data?.currency || DEFAULT_CURRENCY,
    },
    kpi: mapKpiData(data?.kpi ? { kpi: data.kpi } : data),
    charts: {
      cashflowByGroup: mapCashflowByGroupData(
        data?.cash_flow ? { cash_flow: data.cash_flow } : data
      ),
      revExpNet: mapRevExpNetChart(
        data?.revenue_expense ? { revenue_expense: data.revenue_expense } : data
      ),
      expenseBreakdown: mapExpenseBreakdownChart(
        data?.expense_breakdown
          ? { expense_breakdown: data.expense_breakdown }
          : data
      ),
    },
    balanceSheet: mapBalanceSheetData(
      data?.balance_sheet ? { balance_sheet: data.balance_sheet } : data
    ),
    incomeStatement: mapIncomeStatementData(
      data?.income_expense_statement
        ? { income_expense_statement: data.income_expense_statement }
        : data
    ),
    raw: data,
  };
};

export const getFinanceDefaults = () => buildFinanceApiResponsePayload({});
