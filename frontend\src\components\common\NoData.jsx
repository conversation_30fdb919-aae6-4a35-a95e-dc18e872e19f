export default function NoData({
  title = "No Data Found",
  message,
  hasFilters = false,
  filterMessage = "Try adjusting your search or filters to find items.",
  emptyMessage = "No items have been added yet. Start by adding your first item.",
  icon: Icon,
  iconPath,
}) {
  // Default icon path (folder icon)
  const defaultIconPath = (
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4"
    />
  );

  // Use explicit message if provided, otherwise use hasFilters logic
  const displayMessage =
    message !== undefined ? message : hasFilters ? filterMessage : emptyMessage;

  return (
    <div className="flex flex-col items-center justify-center py-16 px-4">
      <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mb-4">
        {Icon ? (
          <Icon className="w-12 h-12 text-gray-400" />
        ) : (
          <svg
            className="w-12 h-12 text-gray-400"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            {iconPath || defaultIconPath}
          </svg>
        )}
      </div>
      <h3 className="text-lg font-semibold text-gray-900 mb-2">{title}</h3>
      <p className="text-sm text-gray-500 text-center max-w-sm">
        {displayMessage}
      </p>
    </div>
  );
}
