import { createSlice } from "@reduxjs/toolkit";
import {
  fetchOperationsOverview,
  fetchOperationsSummary,
  fetchOperationsTrends,
} from "../Thunks/operationsThunk";

const initialState = {
  // Overview data
  overviewData: null,
  overviewLoading: false,
  overviewError: null,

  // Summary data
  summaryData: null,
  summaryLoading: false,
  summaryError: null,

  // Trends data
  trendsData: null,
  trendsLoading: false,
  trendsError: null,

  // KPIs data
  kpisData: null,
  kpisLoading: false,
  kpisError: null,

  // General state
  lastFetchedParams: null,
};

const operationsSlice = createSlice({
  name: "operations",
  initialState,
  reducers: {
    resetOperations: () => initialState,
    clearOperationsError: (state) => {
      state.overviewError = null;
      state.summaryError = null;
      state.trendsError = null;
      state.kpisError = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Overview data
      .addCase(fetchOperationsOverview.pending, (state) => {
        state.overviewLoading = true;
        state.overviewError = null;
      })
      .addCase(fetchOperationsOverview.fulfilled, (state, action) => {
        state.overviewLoading = false;
        state.overviewError = null;
        state.overviewData = action.payload;
        state.lastFetchedParams = action.meta?.arg || null;
      })
      .addCase(fetchOperationsOverview.rejected, (state, action) => {
        state.overviewLoading = false;
        state.overviewError =
          action.payload || "Failed to fetch operations overview";
        state.lastFetchedParams = action.meta?.arg || null;
      })

      // Summary data
      .addCase(fetchOperationsSummary.pending, (state) => {
        state.summaryLoading = true;
        state.summaryError = null;
      })
      .addCase(fetchOperationsSummary.fulfilled, (state, action) => {
        state.summaryLoading = false;
        state.summaryError = null;
        state.summaryData = action.payload;
      })
      .addCase(fetchOperationsSummary.rejected, (state, action) => {
        state.summaryLoading = false;
        state.summaryError =
          action.payload || "Failed to fetch operations summary";
      })

      // Trends data
      .addCase(fetchOperationsTrends.pending, (state) => {
        state.trendsLoading = true;
        state.trendsError = null;
      })
      .addCase(fetchOperationsTrends.fulfilled, (state, action) => {
        state.trendsLoading = false;
        state.trendsError = null;
        state.trendsData = action.payload;
      })
      .addCase(fetchOperationsTrends.rejected, (state, action) => {
        state.trendsLoading = false;
        state.trendsError =
          action.payload || "Failed to fetch operations trends";
      });
  },
});

export const { resetOperations, clearOperationsError } =
  operationsSlice.actions;
export default operationsSlice.reducer;
