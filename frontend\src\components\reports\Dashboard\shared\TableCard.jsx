"use client";

import InfoCard from "./InfoCard";

/**
 * TableCard renders hierarchical data with sticky header.
 * Data shape: [{ label, value, bold?, children?: [...] }]
 */
export default function TableCard({ title, subtitle, data = [] }) {
  const renderRows = (rows, depth = 0) => {
    return rows?.flatMap((row, idx) => {
      const currentRow = (
        <tr key={`${row.label}-${idx}-${depth}`}>
          <td
            className="py-2 pr-3 text-sm text-slate-700"
            style={{ paddingLeft: `${depth * 12 + 12}px` }}
          >
            {row.bold ? (
              <span className="font-semibold">{row.label}</span>
            ) : (
              row.label
            )}
          </td>
          <td className="py-2 pr-5 text-right text-sm text-slate-900 border-l-3 border-l-blue-300">
            {row.bold ? (
              <span className="font-semibold">{row.value}</span>
            ) : (
              row.value
            )}
          </td>
        </tr>
      );

      if (Array.isArray(row.children) && row.children.length > 0) {
        return [currentRow, ...renderRows(row.children, depth + 1)];
      }
      return [currentRow];
    });
  };

  return (
    <InfoCard title={title} subtitle={subtitle}>
      <div className="overflow-x-auto">
        <div className="max-h-[520px] overflow-y-auto pr-1">
          <table className="min-w-[320px] w-full text-left">
            <thead className="sticky top-0 bg-white">
            <tr className="border-b-3 border-b-blue-300">
                <th className="py-2 pl-3 pr-3 text-xs font-bold uppercase tracking-wide text-slate-700">
                  Item
                </th>
                <th className="py-2 pr-5 text-right text-xs font-bold uppercase tracking-wide text-slate-700">
                  Amount
                </th>
              </tr>
            </thead>
            <tbody>{renderRows(data)}</tbody>
          </table>
        </div>
      </div>
    </InfoCard>
  );
}
