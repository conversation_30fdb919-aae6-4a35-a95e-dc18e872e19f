"use client";

import { useState, useEffect, useCallback } from "react";
import { useDispatch, useSelector } from "react-redux";
// import Header from "@/components/common/Header";
import {
  fetchOrganizations,
  fetchOrganizationServices,
} from "@/redux/Thunks/organization";
import { getUserById } from "@/redux/Thunks/userThunks";
import ReportsFilters from "@/components/reports/ReportsFilters";
import DashboardContainer from "@/components/reports/Dashboard/DashboardContainer";
import { SidebarProvider } from "@/contexts/SidebarContext";
import FloatingActionButton from "@/components/dashboard/layout/FloatingActionButton";
import CFOInsightsPage from "@/components/cfo-insights/CFOInsightsPage";
import SummaryPopup from "@/components/dashboard/summary/SummaryPopup";
import { generateReportFilename } from "@/utils/chat";
import { setDashboardSummary } from "@/redux/Slice/chat";
import fetchBalanceSheet, {
  fetchCashFlowData,
  fetchExpenseBreakdownData,
  fetchIncomeExpenseData,
  fetchKpisData,
  fetchRevenueExpenseData,
  fetchPayrollKpis,
  fetchPayrollTaxBreakdown,
  fetchPayrollDeductionsBreakdown,
  fetchPayrollSalaryByDepartment,
} from "@/redux/Thunks/reports";
import {
  fetchOperationsOverview,
  fetchOperationsSummary,
  fetchOperationsTrends,
} from "@/redux/Thunks/operationsThunk";
import api from "@/redux/ApiService/ApiService";
import { SERVICE_PORTS } from "@/utils/constants/api";
import { fetchDocumentByFilters } from "@/redux/Thunks/documentThunks";

const axios = api(SERVICE_PORTS.REPORT);

export default function ReportsPage() {
  const dispatch = useDispatch();
  const {
    organizations = [],
    loading: orgsLoading,
    organizationServices = [],
    servicesLoading,
  } = useSelector((state) => state.organizations);

  const { user, currentUser } = useSelector((state) => ({
    user: state.auth?.user,
    currentUser: state.users?.currentUser,
  }));

  const activeUser = currentUser || user;
  const isUserRole = activeUser?.role === "user";

  const [selectedOrganization, setSelectedOrganization] = useState(null);
  const [selectedService, setSelectedService] = useState(null);
  const [selectedMonth, setSelectedMonth] = useState(null);
  const [selectedYear, setSelectedYear] = useState(null);
  const [showReport, setShowReport] = useState(false);
  const [showCFOInsights, setShowCFOInsights] = useState(false);
  const [showSummaryPopup, setShowSummaryPopup] = useState(false);

  const { dashboardSummary, ui: { isLoading: chatLoading = false } = {} } = useSelector(
    (state) => state.chat || {}
  );

  useEffect(() => {
    dispatch(fetchOrganizations());
  }, [dispatch]);

  useEffect(() => {
    if (!isUserRole || selectedOrganization) return;

    const autoSelectOrganization = async () => {
      try {
        let orgId = activeUser.organization_id;

        if (!orgId && activeUser?.id) {
          const userDetails = await dispatch(getUserById(activeUser.id)).unwrap();
          orgId = userDetails?.organization_id;
        }

        if (orgId) {
          setSelectedOrganization(String(orgId));
          dispatch(fetchOrganizationServices(orgId));
        }
      } catch (error) {
        console.error("Error auto selecting organization:", error);
      }
    };

    autoSelectOrganization();
  }, [isUserRole, selectedOrganization, activeUser, dispatch]);

  const handleOrganizationChange = (orgId) => {
    setSelectedOrganization(orgId);
    setSelectedService(null);
    setSelectedMonth(null);
    setSelectedYear(null);
    setShowReport(false);
    // Fetch services for the selected organization
    if (orgId) {
      dispatch(fetchOrganizationServices(orgId));
    }
  };

  const handleServicesChange = (services) => {
    setSelectedService(services);
    setShowReport(false);
    setShowCFOInsights(false);
    setShowCFOInsights(false);
  };

  const handleMonthChange = (month) => {
    setSelectedMonth(month);
    setShowReport(false);
    setShowCFOInsights(false);
    setShowCFOInsights(false);
  };

  const handleYearChange = (year) => {
    setSelectedYear(year);
    setShowReport(false);
    setShowCFOInsights(false);
    setShowCFOInsights(false);
  };

  const handleGenAIClick = useCallback(() => {
    setShowCFOInsights((prev) => !prev);
  }, []);

  const handleBackFromCFOInsights = useCallback(() => {
    setShowCFOInsights(false);
  }, []);

  const handleGenerateReport = (filters) => {
    const org = filters?.organization ?? selectedOrganization;
    const svc = filters?.service ?? selectedService;
    const mnth = filters?.month ?? selectedMonth;
    const yr = filters?.year ?? selectedYear;

    if (!org || !mnth || !yr) {
      return;
    }

    setShowReport(true);
    const payload = {
      org_id: selectedOrganization,
      month: Number(mnth),
      year: Number(yr),
    };

    // Fetch document and store blob_storage_path and summary in localStorage
    dispatch(
      fetchDocumentByFilters({
        organization_id: org,
        service: svc,
        month: Number(mnth),
        year: Number(yr),
      })
    );

    // Only dispatch APIs based on selected service type
    if (svc === "financial") {
      dispatch(fetchBalanceSheet(payload));
      dispatch(fetchIncomeExpenseData(payload));
      dispatch(fetchKpisData(payload));
      dispatch(fetchCashFlowData(payload));
      dispatch(fetchRevenueExpenseData(payload));
      dispatch(fetchExpenseBreakdownData(payload));
    } else if (svc === "payroll") {
      dispatch(fetchPayrollKpis(payload));
      dispatch(fetchPayrollTaxBreakdown(payload));
      dispatch(fetchPayrollDeductionsBreakdown(payload));
      dispatch(fetchPayrollSalaryByDepartment(payload));
    } else if (svc === "operational") {
      dispatch(fetchOperationsOverview(payload));
      dispatch(fetchOperationsSummary(payload));
      dispatch(fetchOperationsTrends(payload));
    }
  };

  const dashboardServiceType = selectedService?.toLowerCase() || "payroll";
  const selectedOrganizationData = organizations.find(
    (org) => String(org.id) === String(selectedOrganization)
  );
  const selectedOrganizationName =
    selectedOrganizationData?.organizationName ||
    selectedOrganizationData?.name ||
    "";

  const isFinancialSelected = selectedService === "financial";
  const isOperationsSelected = selectedService === "operational";
  const isPayrollSelected = selectedService === "payroll";

  const fileName = generateReportFilename(selectedMonth, selectedYear, selectedService);

  const handleViewInsights = useCallback(() => {
    if (!fileName || !selectedOrganizationName) return;

    dispatch(setDashboardSummary(null));
    setShowSummaryPopup(true);

    // Get summary from localStorage
    try {
      const summaryJson = localStorage.getItem("document_summary");
      
      if (summaryJson) {
        const summaryData = JSON.parse(summaryJson);
        
        if (summaryData && summaryData.title && summaryData.sections) {
          dispatch(setDashboardSummary(summaryData));
        } else {
          dispatch(setDashboardSummary("<p>Unable to load insights.</p>"));
        }
      } else {
        dispatch(setDashboardSummary("<p>No insights available. Please generate a report first.</p>"));
      }
    } catch (error) {
      console.error("Error loading summary from localStorage:", error);
      dispatch(setDashboardSummary("<p>Unable to load insights.</p>"));
    }
  }, [dispatch, fileName, selectedOrganizationName]);

  if (showCFOInsights && showReport) {
    return (
      <>
        <div className="min-h-screen bg-[#F3F4F6] flex items-center justify-center py-4">
          <div className="w-full mx-auto" style={{ height: '90vh', maxHeight: '900px' }}>
            <CFOInsightsPage
              onBack={handleBackFromCFOInsights}
              selectedMonth={selectedMonth}
              selectedMonthKey={selectedMonth}
              selectedYear={selectedYear}
              selectedPage={1}
              isFinancialSelected={isFinancialSelected}
              isOperationsSelected={isOperationsSelected}
              isPayrollSelected={isPayrollSelected}
              selectedDashboard={selectedService || ""}
              fileName={fileName}
              organizationName={selectedOrganizationName}
              organizationId={selectedOrganization}
              onDownload={() => {}}
            />
          </div>
        </div>
        <FloatingActionButton onClick={handleGenAIClick} isOpen={showCFOInsights} />
      </>
    );
  }

  return (
    <div className="min-h-screen bg-gray-100">
      {/* <Header clientLogo={getVersionedImage("/clientLogo.png")} /> */}
      <main>
        <div className="w-full">
          <div className="bg-gray-100 rounded-lg shadow-sm p-5 lg:p-6">
            <ReportsFilters
              organizations={organizations}
              selectedOrganization={selectedOrganization}
              onOrganizationChange={handleOrganizationChange}
              selectedService={selectedService}
              onServicesChange={handleServicesChange}
              selectedMonth={selectedMonth}
              onMonthChange={handleMonthChange}
              selectedYear={selectedYear}
              onYearChange={handleYearChange}
              onGenerateReport={handleGenerateReport}
              onViewInsights={handleViewInsights}
              showViewInsights={showReport}
              loading={orgsLoading}
              organizationsLoading={orgsLoading}
              availableServices={organizationServices}
              servicesLoading={servicesLoading}
              disableOrganizationSelect={isUserRole}
            />

            {!showReport ? (
              <div className="mt-8 lg:mt-10 bg-gray-50 rounded-lg p-6 text-center space-y-6">
                <p className="text-gray-500">
                  Select filters and click &quot;Generate Report&quot; to view
                  results
                </p>
                <div className="text-sm text-gray-400 space-y-6">
                  <p>Reports will be displayed here once generated</p>
                </div>
              </div>
            ) : (
              <SidebarProvider>
                <DashboardContainer
                  serviceType={dashboardServiceType}
                  month={selectedMonth}
                  year={selectedYear}
                  organizationId={selectedOrganization}
                  organizationName={selectedOrganizationName}
                />
              </SidebarProvider>
            )}
          </div>
        </div>
      </main>
      {showReport && <FloatingActionButton onClick={handleGenAIClick} isOpen={showCFOInsights} />}
      <SummaryPopup
        isOpen={showSummaryPopup}
        onClose={() => setShowSummaryPopup(false)}
        dashboardSummary={dashboardSummary}
        selectedMonth={selectedMonth}
        isFinancialSelected={isFinancialSelected}
        isOperationsSelected={isOperationsSelected}
        isPayrollSelected={isPayrollSelected}
        isLoading={chatLoading}
        organizationName={selectedOrganizationName}
      />
    </div>
  );
}
