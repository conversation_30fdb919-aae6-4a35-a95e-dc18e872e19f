import * as React from "react";
import { Slot } from "@radix-ui/react-slot";
import { cva } from "class-variance-authority";

import { cn } from "@/utils/methods/cn";

const buttonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-xl text-sm font-medium transition-all duration-200 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",
  {
    variants: {
      variant: {
        default: "bg-[#4F46E5] text-white shadow-md hover:bg-[#4338CA]",
        outline:
          "border border-[#4F46E5] text-[#4F46E5] bg-background-card shadow-md hover:bg-[#4F46E5]/5",
        destructive:
          "bg-semantic-danger text-white shadow-xs hover:bg-[#DC2626] focus-visible:ring-semantic-danger/20",

        secondary:
          "bg-text-subtle/10 text-text-body shadow-xs hover:bg-text-subtle/20",
        ghost:
          "hover:bg-[#4F46E5]/5 hover:text-[#4F46E5]",
        link: "text-[#4F46E5] underline-offset-4 hover:underline",
      },
      size: {
        default: "h-9 px-4 py-2 has-[>svg]:px-3",
        sm: "h-8 rounded-lg gap-1.5 px-3 has-[>svg]:px-2.5",
        lg: "h-10 rounded-lg px-6 has-[>svg]:px-4",
        icon: "size-9",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
);

function Button({
  className,
  variant,
  size,
  asChild = false,
  leftIcon,
  rightIcon,
  ...props
}) {
  const Comp = asChild ? Slot : "button";

  return (
    <Comp
      data-slot="button"
      className={cn(buttonVariants({ variant, size, className }))}
      {...props}
    >
      {leftIcon}
      {props.children}
      {rightIcon}
    </Comp>
  );
}

export { Button, buttonVariants };
