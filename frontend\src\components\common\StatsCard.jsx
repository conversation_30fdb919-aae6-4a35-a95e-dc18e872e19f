import React from "react";
import {
  Users,
  UserCheck,
  UserX,
  Clock,
  TrendingUp,
  Shield,
  Key,
  Settings,
  Building,
  Building2,
  MapPin,
  Calendar,
  CheckCircle,
} from "lucide-react";

// Icon mapping for different stat types
const iconMap = {
  users: <Users className="w-6 h-6" />,
  "user-check": <UserCheck className="w-6 h-6" />,
  "user-x": <UserX className="w-6 h-6" />,
  clock: <Clock className="w-6 h-6" />,
  "trending-up": <TrendingUp className="w-6 h-6" />,
  shield: <Shield className="w-6 h-6" />,
  key: <Key className="w-6 h-6" />,
  settings: <Settings className="w-6 h-6" />,
  building: <Building className="w-6 h-6" />,
  "building-2": <Building2 className="w-6 h-6" />,
  "map-pin": <MapPin className="w-6 h-6" />,
  calendar: <Calendar className="w-6 h-6" />,
  "check-circle": <CheckCircle className="w-6 h-6" />,
};

export default function StatsCard({
  title,
  value,
  icon,
  bgColor = "bg-blue-50",
  iconColor = "text-blue-600",
  className = "",
  onClick,
  loading = false,
}) {
  // Get border color based on background color
  const getBorderColor = () => {
    if (bgColor.includes("blue")) return "border-blue-200";
    if (bgColor.includes("green")) return "border-green-200";
    if (bgColor.includes("yellow")) return "border-yellow-200";
    if (bgColor.includes("red")) return "border-red-200";
    return "border-[#E5E7EB]";
  };

  const cardClasses = `
    ${bgColor} rounded-xl p-5 shadow-sm hover:shadow-md border ${getBorderColor()}
    transition-all duration-200 hover:scale-[1.02] min-h-[100px] flex flex-col justify-between
    ${onClick ? "cursor-pointer" : ""} 
    ${className}
  `;

  const iconElement = icon && iconMap[icon] ? (
    <div className={`flex-shrink-0 ${iconColor}`}>
      {React.cloneElement(iconMap[icon], {
        className: `w-6 h-6 ${iconColor}`,
      })}
    </div>
  ) : null;

  const content = (
    <div className="w-full">
      <div className="flex items-start justify-between mb-3">
        <p className="text-sm font-semibold text-gray-700">
          {title}
        </p>
        {iconElement}
      </div>
      {loading ? (
        <div className="h-10 w-20 bg-gray-200 animate-pulse rounded mt-2"></div>
      ) : (
        <p className="text-3xl font-bold leading-tight text-gray-900">
          {value}
        </p>
      )}
    </div>
  );

  if (onClick) {
    return (
      <button onClick={onClick} className={cardClasses}>
        {content}
      </button>
    );
  }

  return <div className={cardClasses}>{content}</div>;
}
