// Cache for formatted messages to avoid re-processing
const formatCache = new Map();

// Import chat response formatter
import { formatChatResponse, isJsonChatResponse } from "./formatChatResponse";

// Function to decode HTML entities (like &lt; to <, &gt; to >, etc.)
const decodeHtmlEntities = (text) => {
  const textarea = document.createElement("textarea");
  textarea.innerHTML = text;
  return textarea.value;
};

// Function to format message content with proper HTML structure
export const formatMessage = (content) => {
  if (!content) return "";

  // Check if content is a JSON chat response object
  if (typeof content === "object" && isJsonChatResponse(content)) {
    return formatChatResponse(content);
  }

  // Check if content is a stringified JSON chat response
  if (typeof content === "string") {
    try {
      const parsed = JSON.parse(content);
      if (isJsonChatResponse(parsed)) {
        return formatChatResponse(parsed);
      }
    } catch {
      // Not JSON, continue with normal formatting
    }
  }

  // Normalize content first (trim and fix common issues) before checking cache
  let normalizedContent = content.trim();

  // ALWAYS decode HTML entities first - this ensures escaped HTML is properly recognized
  // Decode HTML entities if content appears to be escaped (has &lt;, &gt;, etc.)
  if (
    normalizedContent.includes("&lt;") ||
    normalizedContent.includes("&gt;") ||
    normalizedContent.includes("&amp;") ||
    normalizedContent.includes("&quot;") ||
    normalizedContent.includes("&#39;") ||
    normalizedContent.includes("&#x") ||
    normalizedContent.includes("&apos;") ||
    /&(?:#\d+|#x[0-9a-f]+|[a-z]+);/i.test(normalizedContent)
  ) {
    try {
      // Use multiple decode passes to handle double-encoded entities
      let decoded = normalizedContent;
      let previousDecoded = "";
      const entityRegex = /&(?:#\d+|#x[0-9a-f]+|[a-z]+);/i;

      // Keep decoding until no more entities are decoded (handles double/triple encoding)
      while (decoded !== previousDecoded && entityRegex.test(decoded)) {
        previousDecoded = decoded;
        decoded = decodeHtmlEntities(decoded);
      }
      normalizedContent = decoded;
    } catch (e) {
      // If decoding fails, manually replace common entities
      normalizedContent = normalizedContent
        .replace(/&lt;/g, "<")
        .replace(/&gt;/g, ">")
        .replace(/&amp;/g, "&")
        .replace(/&quot;/g, '"')
        .replace(/&#39;/g, "'")
        .replace(/&apos;/g, "'")
        .replace(/&#x27;/g, "'")
        .replace(/&#x2F;/g, "/")
        .replace(/&#x24;/g, "$")
        .replace(/&nbsp;/g, " ");
    }
  }

  // Check cache first (after normalization)
  if (formatCache.has(normalizedContent)) {
    return formatCache.get(normalizedContent);
  }

  let formattedContent = normalizedContent;

  // Check if content already contains HTML tags (like <h2>, <table>, etc.)
  // This check happens AFTER decoding, so escaped HTML will be properly detected
  const hasHtmlTags = /<[^>]+>/g.test(normalizedContent);

  if (hasHtmlTags) {
    // Content is already HTML - apply our design system and override inline styles
    // Note: HTML entities should already be decoded above, but decode again as a safety measure
    
    // CRITICAL: Fix severely broken HTML tags first (e.g., <h3<td> -> <h3>, <table< td> -> <table>)
    formattedContent = formattedContent
      // Decode HTML entities first (&#x24; -> $, &apos; -> ')
      .replace(/&#x24;/g, "$")
      .replace(/&#x2013;/g, "–")
      .replace(/&apos;/g, "'")
      .replace(/&amp;/g, "&")
      // Fix broken opening tags: <h3<td> -> <h3>, <table< td> -> <table>
      .replace(/<(\w+)<(\w+)([^>]*)>/g, "<$1>")
      // Fix broken closing tags: </th<></td> -> </th></td>, </table<></td> -> </table>
      .replace(/<\/(\w+)<><\/td>/g, "</$1>")
      .replace(/<\/(\w+)<><\/th>/g, "</$1>")
      .replace(/<\/(\w+)<><\/tr>/g, "</$1>")
      .replace(/<\/(\w+)<><\/table>/g, "</$1>")
      // Fix broken tag patterns: <td<td> -> <td>, <th<th> -> <th>, <h3<td> -> <h3>
      .replace(/<(\w+)<(\w+)>/g, "<$1>")
      // Fix broken closing patterns: </td></td> -> </td>, </th></th> -> </th>
      .replace(/<\/(\w+)><\/\1>/g, "</$1>")
      // Fix broken style attributes with = instead of : (e.g., border=1pxsolid -> border:1px solid)
      .replace(/style\s*=\s*["']([^"']*)["']/gi, (match, styleContent) => {
        let fixed = styleContent
          // Fix = instead of : (e.g., border=1pxsolid -> border:1pxsolid)
          .replace(/(\w+)\s*=\s*([^;]+)/g, "$1:$2")
          // Fix missing spaces (e.g., 1pxsolid -> 1px solid, 1pxsolid;#ccc -> 1px solid #ccc)
          .replace(/(\d+px)([a-z]+)/gi, "$1 $2")
          .replace(/(\d+px)\s*([#;])/gi, "$1 $2")
          // Fix missing colons and semicolons
          .replace(/\s*:\s*/g, ":")
          .replace(/\s*;\s*/g, ";")
          // Fix broken color values
          .replace(/#\s*([0-9a-fA-F]+)\s*([0-9a-fA-F]+)/gi, "#$1$2")
          // Fix multiple spaces
          .replace(/\s{2,}/g, " ")
          .trim();
        return `style="${fixed}"`;
      })
      // Fix broken attributes: border="1pxsolid" -> border="1px solid"
      .replace(/(\w+)\s*=\s*["'](\d+px)([a-z]+)["']/gi, '$1="$2 $3"')
      // Fix broken patterns like <td<td style="..."> -> <td style="...">
      .replace(/<(\w+)<(\w+)\s+([^>]*)>/g, "<$1 $3>")
      // Fix missing closing tags for common elements
      .replace(/<h3([^>]*)>([^<]+)<\/h3>/g, "<h3$1>$2</h3>")
      .replace(/<h3([^>]*)>([^<]+)(?!<\/h3>)/g, "<h3$1>$2</h3>")
      // Fix broken table structure - ensure proper opening/closing tags
      .replace(/<table<([^>]*)>/g, "<table$1>")
      .replace(/<\/table<([^>]*)>/g, "</table>")
      .replace(/<thead<([^>]*)>/g, "<thead$1>")
      .replace(/<\/thead<([^>]*)>/g, "</thead>")
      .replace(/<tbody<([^>]*)>/g, "<tbody$1>")
      .replace(/<\/tbody<([^>]*)>/g, "</tbody>")
      .replace(/<tr<([^>]*)>/g, "<tr$1>")
      .replace(/<\/tr<([^>]*)>/g, "</tr>")
      .replace(/<td<([^>]*)>/g, "<td$1>")
      .replace(/<\/td<([^>]*)>/g, "</td>")
      .replace(/<th<([^>]*)>/g, "<th$1>")
      .replace(/<\/th<([^>]*)>/g, "</th>")
      // Fix broken patterns like <ol< -> <ol>, </ol< -> </ol>
      .replace(/<ol<([^>]*)>/g, "<ol$1>")
      .replace(/<\/ol<([^>]*)>/g, "</ol>")
      .replace(/<li<([^>]*)>/g, "<li$1>")
      .replace(/<\/li<([^>]*)>/g, "</li>")
      // Fix broken patterns like <p< -> <p>, </p< -> </p>
      .replace(/<p<([^>]*)>/g, "<p$1>")
      .replace(/<\/p<([^>]*)>/g, "</p>");
    
    // First, fix malformed HTML tags (remove spaces in tag names like < td -> <td)
    formattedContent = formattedContent
      // Remove PHP-like code patterns first (before decoding)
      .replace(/<\?php[\s\S]*?\?>/gi, "")
      .replace(/echo\s+["'][^"']*["']/gi, "")
      .replace(/echo\s+/gi, "")
      .replace(/\{\$rec\[0\]\}/g, "")
      .replace(/\{\$rec\[1\]\}/g, "")
      .replace(/\{\$rec\[2\]\}/g, "")
      .replace(/\{\$[^}]+\}/g, "") // Remove any PHP variables
      .replace(/;\s*echo\s+/gi, "")
      .replace(/;\s*"/gi, '"')
      .replace(/;\s*$/gm, "")
      .replace(/<\?[\s\S]*?\?>/gi, "") // Remove any PHP tags
      // Remove broken table patterns with PHP code
      .replace(/<tr[^>]*>[\s\S]*?echo[\s\S]*?<\/tr>/gi, "")
      .replace(/<td[^>]*>[\s\S]*?echo[\s\S]*?<\/td>/gi, "")
      .replace(/<td[^>]*>[\s\S]*?\{\$rec[\s\S]*?<\/td>/gi, "")
      // Final pass: Decode any remaining escaped HTML entities (safety measure for double-encoding)
      // This ensures tags like &lt;table&gt; become <table> so they can be rendered
      .replace(/&lt;/g, "<")
      .replace(/&gt;/g, ">")
      .replace(/&amp;/g, "&")
      .replace(/&quot;/g, '"')
      .replace(/&#39;/g, "'")
      .replace(/&#x27;/g, "'")
      .replace(/&#x2F;/g, "/")
      .replace(/&nbsp;/g, " ")
      // Fix broken style attributes with spaces (CRITICAL - fixes malformed HTML)
      .replace(/style\s*=\s*["']([^"']*)["']/gi, (match, styleContent) => {
        // First, normalize spaces around colons and semicolons
        let fixed = styleContent
          .replace(/\s*:\s*/g, ":") // Remove spaces around colons
          .replace(/\s*;\s*/g, ";") // Remove spaces around semicolons
          // Fix broken color values like "#1 f4e79" -> "#1f4e79"
          .replace(/#\s*([0-9a-fA-F]+)\s*([0-9a-fA-F]+)/gi, "#$1$2")
          // Fix broken values like "1pxsolid" -> "1px solid" (but preserve existing spaces)
          .replace(/(\d+px)([a-z]+)/gi, (m, px, rest) => {
            // Only fix if there's no space and it's a valid CSS unit/value pattern
            if (!m.includes(" ")) {
              return `${px} ${rest}`;
            }
            return m;
          })
          // Fix multiple spaces to single space
          .replace(/\s{2,}/g, " ")
          .trim();
        return `style="${fixed}"`;
      })
      // Fix broken opening tags
      .replace(/<(\w+)\s+style\s*=\s*["']([^"']*)["']/gi, (match, tag, styleContent) => {
        const fixed = styleContent
          .replace(/\s*:\s*/g, ":")
          .replace(/\s*;\s*/g, ";")
          .replace(/#\s*([0-9a-fA-F]+)\s*([0-9a-fA-F]+)/gi, "#$1$2")
          .replace(/(\d+px)([a-z]+)/gi, (m, px, rest) => (!m.includes(" ") ? `${px} ${rest}` : m))
          .replace(/\s{2,}/g, " ")
          .trim();
        return `<${tag} style="${fixed}"`;
      })
      // Fix number formatting - remove spaces in numbers, percentages, and currency
      .replace(/\$\s*(\d+)\s*\.\s*(\d+)\s*K/gi, "$$$1.$2K")
      .replace(/\$\s*(\d+)\s*\.\s*(\d+)/gi, "$$$1.$2")
      .replace(/\$\s*(\d+)\s*K/gi, "$$$1K")
      .replace(/-\s*\$\s*(\d+)\s*\.\s*(\d+)\s*K/gi, "-$$$1.$2K")
      .replace(/-\s*\$\s*(\d+)\s*\.\s*(\d+)/gi, "-$$$1.$2")
      .replace(/(-?\d+)\s*\.\s*(\d+)\s*%/gi, "$1.$2%")
      .replace(/(-?\d+)\s*%/gi, "$1%")
      .replace(/(-?\d+)\s*\.\s*(\d+)\s*pp/gi, "$1.$2pp")
      .replace(/(-?\d+)\s*pp/gi, "$1pp")
      .replace(/(\d+)\s*\.\s*(\d+)/g, "$1.$2")
      // Remove excessive whitespace and newlines
      .replace(/\n{3,}/g, "\n\n") // Max 2 consecutive newlines
      .replace(/>\s+</g, "><") // Remove whitespace between tags
      .replace(/>\s+/g, ">") // Remove whitespace after opening tags
      .replace(/\s+</g, "<") // Remove whitespace before closing tags
      // Fix broken nested attributes pattern: style= ' border= ' 0' padding= '0' text= 'align= 'left'
      .replace(/(\w+)\s*=\s*['"]\s*([^'"]*?)\s*(\w+)\s*=\s*['"]\s*([^'"]*?)['"]/gi, (match, attr1, content1, attr2, value2) => {
        // If this looks like a broken style attribute with nested properties
        if (attr1 === "style" || attr1 === "border" || attr1 === "padding" || attr1 === "text") {
          // Try to reconstruct as CSS properties
          if (attr2 === "border" || attr2 === "padding" || attr2 === "text") {
            const prop = attr2 === "text" ? "text-align" : attr2;
            return `${prop}:${value2};`;
          }
        }
        // Otherwise, treat as separate attributes
        return `${attr1}="${content1}" ${attr2}="${value2}"`;
      })
      // Fix broken style attributes with multiple broken parts: style= ' border= ' 0' padding= '0' text= 'align= 'left'
      .replace(/style\s*=\s*['"]\s*([^'"]*?)['"]/gi, (match, styleContent) => {
        // Extract broken style properties like "border= ' 0' padding= '0' text= 'align= 'left'"
        let fixed = styleContent
          // Fix nested attribute patterns: border= ' 0' -> border:0;
          .replace(/(\w+)\s*=\s*['"]\s*([^'"]*?)['"]/g, (m, prop, val) => {
            // Handle text-align specially
            if (prop === "text" && val.startsWith("align=")) {
              const alignVal = val.replace(/align\s*=\s*['"]\s*([^'"]*?)['"]/g, "$1");
              return `text-align:${alignVal};`;
            }
            return `${prop}:${val};`;
          })
          // Fix remaining broken patterns
          .replace(/(\w+)\s*=\s*['"]\s*([^'"]*?)['"]/g, "$1:$2;")
          .replace(/\s+/g, " ")
          .replace(/(\d+)\s+px/gi, "$1px")
          .replace(/(#)\s+(\w+)/gi, "$1$2")
          .trim();
        
        // Remove trailing semicolon if present
        fixed = fixed.replace(/;$/, "");
        
        return `style="${fixed}"`;
      })
      // Fix ALL closing tag variations with spaces (e.g., </ table>, < / table>, </table >)
      .replace(/<\s*\/\s*([a-zA-Z][a-zA-Z0-9]*)\s*>/gi, "</$1>")
      .replace(/<\s+\/\s*([a-zA-Z][a-zA-Z0-9]*)\s*>/gi, "</$1>")
      .replace(/<\s*\/\s+([a-zA-Z][a-zA-Z0-9]*)\s*>/gi, "</$1>")
      // Fix opening tags with spaces (e.g., < td -> <td, < table -> <table)
      .replace(/<\s+([a-zA-Z][a-zA-Z0-9]*)/g, "<$1")
      // Fix opening tags with trailing spaces before closing bracket
      .replace(/<\s*([a-zA-Z][a-zA-Z0-9]*)\s+>/g, "<$1>")
      // Fix attributes with extra spaces (e.g., style = -> style=)
      .replace(/(\w+)\s*=\s*['"]/g, '$1="')
      // Fix broken attribute patterns: border = '0' -> border="0"
      .replace(/(\w+)\s*=\s*['"]\s*([^'"]*?)['"]/g, '$1="$2"')
      // Fix values with spaces in style attributes (e.g., border : 1 px -> border:1px)
      .replace(/:\s+/g, ":")
      .replace(/;\s+/g, ";")
      // Fix spaces in CSS values (e.g., "1 px" -> "1px", "# 999" -> "#999")
      .replace(/style="([^"]*)"/gi, (match, styleContent) => {
        const fixedStyle = styleContent
          .replace(/\s+/g, " ") // Normalize multiple spaces to single space
          .replace(/(\d+)\s+px/gi, "$1px") // Fix "1 px" -> "1px"
          .replace(/(#)\s+(\w+)/gi, "$1$2") // Fix "# 999" -> "#999"
          .replace(/\s*:\s*/g, ":") // Fix "border :" -> "border:"
          .replace(/\s*;\s*/g, ";") // Fix ";" spacing
          .trim();
        return `style="${fixedStyle}"`;
      })
      // Normalize single quotes to double quotes and strip any inline styles globally
      .replace(/style='([^']*)'/gi, 'style="$1"')
      .replace(/\sstyle=["'][^"']*["']/gi, "")
      // Fix cases where table cells aren't properly closed (add missing closing tags)
      .replace(/<td([^>]*)>\s*(.+?)\s*<td/gi, "<td$1>$2</td><td")
      .replace(/<td([^>]*)>\s*(.+?)\s*<\/tr>/gi, "<td$1>$2</td></tr>")
      .replace(/<td([^>]*)>\s*(.+?)\s*<\/table>/gi, "<td$1>$2</td></table>")
      // Ensure table rows are properly closed
      .replace(/<tr([^>]*)>\s*(.+?)\s*<tr/gi, "<tr$1>$2</tr><tr")
      .replace(/<tr([^>]*)>\s*(.+?)\s*<\/table>/gi, "<tr$1>$2</tr></table>")
      // Fix broken HTML patterns that appear as text content
      // Pattern: "style= ' border= ' 0' padding= '0' text= 'align= 'left'>Profit Margin"
      // This is a very broken pattern where attributes appear as text
      .replace(/([^\s<])(style\s*=\s*['"]\s*[^'"]*?\s*border\s*=\s*['"]\s*[^'"]*?['"]\s*[^>]*?>)/g, '<td $2')
      // Fix broken table cells that appear as text (e.g., "style= ' border= ' 0'>Profit Margin")
      // This handles cases where broken HTML attributes appear as text content
      .replace(/([^<>\n])(style\s*=\s*['"]\s*[^'"]*?['"]\s*>)/g, '<td $2')
      // Fix broken attributes that appear mid-text: "style= ' border= ' 0' padding= '0'>"
      .replace(/([^<>\n])(\w+\s*=\s*['"]\s*[^'"]*?\s*\w+\s*=\s*['"]\s*[^'"]*?['"]\s*>)/g, '<td $2')
      // Fix the specific broken pattern: "style= ' border= ' 0' padding= '0' text= 'align= 'left'>"
      .replace(/(style\s*=\s*['"]\s*[^'"]*?\s*border\s*=\s*['"]\s*[^'"]*?['"]\s*[^>]*?>)([^\t\n<]+)/g, (match, attrs, content) => {
        // Extract and fix the style attributes
        const fixedAttrs = attrs
          .replace(/style\s*=\s*['"]\s*([^'"]*?)['"]/gi, '')
          .replace(/border\s*=\s*['"]\s*([^'"]*?)['"]/gi, 'border:$1;')
          .replace(/padding\s*=\s*['"]\s*([^'"]*?)['"]/gi, 'padding:$1;')
          .replace(/text\s*=\s*['"]\s*align\s*=\s*['"]\s*([^'"]*?)['"]/gi, 'text-align:$1;')
          .trim();
        return `<td style="${fixedAttrs}">${content}</td>`;
      })
      // Clean up any remaining broken attribute patterns
      .replace(/(\w+)\s*=\s*['"]\s*(\w+)\s*=\s*['"]\s*([^'"]*?)['"]/g, '$2="$3"')
      // Fix table cells that have broken content but should be separate cells
      .replace(/(<td[^>]*>)([^<]*?)(style\s*=\s*['"]\s*[^'"]*?['"]\s*>)/g, '$1$2</td><td $3');

    formattedContent = formattedContent
      // Remove inline styles from h2 and apply our design
      .replace(/<h2([^>]*)>/gi, (match, attrs = "") => {
        const cleanAttrs = attrs.replace(/style=['"][^'"]*['"]/gi, "").trim();
        return `<h2${cleanAttrs ? ` ${cleanAttrs}` : ""} class="text-2xl font-bold text-gray-900 mt-8 mb-2 pb-3 border-b-2 border-indigo-200 first:mt-0">`;
      })
      // Style h3 tags - preserve inline styles for color and font-weight (needed for bold blue headers)
      .replace(/<h3([^>]*)>/gi, (match, attrs = "") => {
        // Extract and preserve style attribute if it contains color or font-weight
        const styleMatch = attrs.match(/style\s*=\s*["']([^"']*)["']/i);
        let preservedStyle = "";
        if (styleMatch) {
          const styleContent = styleMatch[1];
          // Preserve color and font-weight from inline style (needed for section headers)
          if (styleContent.includes("color") || styleContent.includes("font-weight")) {
            preservedStyle = ` style="${styleContent}"`;
          }
        }
        const cleanAttrs = attrs.replace(/style=['"][^'"]*['"]/gi, "").trim();
        return `<h3${cleanAttrs ? ` ${cleanAttrs}` : ""}${preservedStyle} class="text-xl font-semibold text-gray-800 mt-6 mb-2">`;
      })
      // Style tables - wrap in scrollable container, remove inline styles, apply our design
      // Match table tags (case insensitive, handle both single and double quotes)
      .replace(/<table([^>]*)>/gi, (match, attrs = "") => {
        const cleanAttrs = attrs.replace(/style=['"][^'"]*['"]/gi, "").trim();
        return `<div class="overflow-x-auto mt-2 mb-6 rounded-xl border border-gray-300 shadow-lg bg-white"><table${cleanAttrs ? ` ${cleanAttrs}` : ""} class="w-full border-collapse min-w-full">`;
      })
      // Close tables properly - handle case insensitive and ensure closing div
      .replace(/<\/table>/gi, "</table></div>")
      // Style table headers - remove inline styles, apply our design
      .replace(/<th([^>]*)>/gi, (match, attrs = "") => {
        const cleanAttrs = attrs.replace(/style=['"][^'"]*['"]/gi, "").trim();
        return `<th${cleanAttrs ? ` ${cleanAttrs}` : ""} class="bg-gradient-to-r from-indigo-600 to-indigo-700 text-white font-bold text-sm px-4 py-3 text-left border-b-2 border-indigo-800 uppercase tracking-wide">`;
      })
      // Style table cells - remove inline styles, apply our design
      .replace(/<td([^>]*)>/gi, (match, attrs = "") => {
        const cleanAttrs = attrs.replace(/style=['"][^'"]*['"]/gi, "").trim();
        return `<td${cleanAttrs ? ` ${cleanAttrs}` : ""} class="px-4 py-3 border-b border-gray-200 text-gray-700 text-sm bg-white">`;
      })
      // Style table rows - add hover and alternating colors
      .replace(/<tr([^>]*)>/gi, (match, attrs = "") => {
        const cleanAttrs = attrs.replace(/style=['"][^'"]*['"]/gi, "").trim();
        return `<tr${cleanAttrs ? ` ${cleanAttrs}` : ""} class="hover:bg-indigo-50/50 transition-colors duration-150 even:bg-gray-50/30">`;
      })
      // Style ordered lists - remove inline styles
      .replace(/<ol([^>]*)>/gi, (match, attrs = "") => {
        const cleanAttrs = attrs.replace(/style=['"][^'"]*['"]/gi, "").trim();
        return `<ol${cleanAttrs ? ` ${cleanAttrs}` : ""} class="list-decimal list-inside space-y-3 my-5 text-gray-700 ml-4">`;
      })
      // Style list items
      .replace(/<li([^>]*)>/gi, (match, attrs = "") => {
        const cleanAttrs = attrs.replace(/style=['"][^'"]*['"]/gi, "").trim();
        return `<li${cleanAttrs ? ` ${cleanAttrs}` : ""} class="mb-3 text-base leading-relaxed">`;
      })
      // Style paragraphs - remove inline styles
      .replace(/<p([^>]*)>/gi, (match, attrs = "") => {
        const cleanAttrs = attrs.replace(/style=['"][^'"]*['"]/gi, "").trim();
        return `<p${cleanAttrs ? ` ${cleanAttrs}` : ""} class="text-gray-700 mb-4 leading-7 text-base">`;
      })
      // Enhance bold tags - remove inline styles
      .replace(/<b([^>]*)>/gi, (match, attrs = "") => {
        const cleanAttrs = attrs.replace(/style=['"][^'"]*['"]/gi, "").trim();
        return `<strong${cleanAttrs ? ` ${cleanAttrs}` : ""} class="font-bold text-indigo-700">`;
      })
      .replace(/<\/b>/gi, "</strong>")
      // Remove extra spaces between elements
      .replace(/>\s+</g, "><");
  } else {
    // Content is plain text/markdown - convert to HTML
    formattedContent = formattedContent
      // Convert ### headers to h3 tags
      .replace(
        /^### (.+)$/gm,
        '<h3 class="text-lg font-semibold text-gray-800 mb-2 mt-4 first:mt-0">$1</h3>'
      )
      // Convert ## headers to h4 tags
      .replace(
        /^## (.+)$/gm,
        '<h4 class="text-base font-medium text-gray-700 mb-2 mt-3">$1</h4>'
      )
      // Convert # headers to h5 tags
      .replace(
        /^# (.+)$/gm,
        '<h5 class="text-sm font-medium text-gray-600 mb-1 mt-2">$1</h5>'
      )
      // Convert **text** to <strong>text</strong>
      .replace(/\*\*(.*?)\*\*/g, '<strong class="font-semibold">$1</strong>')
      // Convert *text* to <em>text</em>
      .replace(/\*(.*?)\*/g, '<em class="italic">$1</em>')
      // Convert bullet points (•) to proper list items with styling
      .replace(
        /^•\s*(.+)$/gm,
        '<li class="text-sm text-gray-700 mb-1 flex items-start"><span class="text-blue-500 mr-2">•</span>$1</li>'
      )
      // Convert numbered lists
      .replace(
        /^\d+\. (.+)$/gm,
        '<li class="text-sm text-gray-700 mb-1 flex items-start"><span class="text-gray-500 mr-2">$&</span></li>'
      )
      // Convert line breaks to proper spacing
      .replace(/\n/g, "<br />")
      // Group consecutive list items into proper ul tags
      .replace(
        /(<li[^>]*>.*?<\/li>)(<br \/>)(<li[^>]*>.*?<\/li>)/gs,
        '<ul class="space-y-1 mb-3">$1$2$3</ul>'
      )
      // Clean up multiple line breaks
      .replace(/(<br \/>){3,}/g, "<br /><br />");
  }

  // Cache the result (limit cache size to prevent memory leaks)
  if (formatCache.size > 100) {
    formatCache.clear();
  }
  formatCache.set(normalizedContent, formattedContent);

  return formattedContent;
};

// Alias for backward compatibility
export const formatMessageContent = formatMessage;
