import sgMail from "@sendgrid/mail";
import dotenv from "dotenv";
import path from "path";
import { fileURLToPath } from "url";

// Load .env from shared directory (one level up from shared/config)
const __dirname = path.dirname(fileURLToPath(import.meta.url));
dotenv.config({ path: path.resolve(__dirname, "../.env") });

// Cache to store if API key has been set
let apiKeySet = false;

/**
 * Get SendGrid API key from environment variables
 * @returns {string} SendGrid API key
 * @throws {Error} If API key is not set
 */
const getSendGridApiKey = () => {
  const apiKey = process.env.SENDGRID_API_KEY;

  if (!apiKey) {
    throw new Error(
      "SENDGRID_API_KEY environment variable is not set. Please set it in your .env file or environment variables."
    );
  }

  return apiKey;
};

/**
 * Initialize SendGrid with API key
 * This configuration handles SendGrid setup
 * @throws {Error} If API key is not set or invalid
 */
export const initializeSendGrid = () => {
  const apiKey = getSendGridApiKey();

  if (!apiKeySet) {
    sgMail.setApiKey(apiKey);

    // Uncomment the below line if you are sending mail using a regional EU subuser
    // sgMail.setDataResidency('eu');

    apiKeySet = true;
  }
};

/**
 * Get SendGrid mail instance
 * @returns {Object} SendGrid mail instance
 */
export const getSendGridMail = () => {
  return sgMail;
};

export default {
  initializeSendGrid,
  getSendGridMail,
};
