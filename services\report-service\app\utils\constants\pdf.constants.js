// Regex Constants
export const NUMERIC_EXTRACTION_REGEX = /[^0-9.-]/g;
export const HTML_ESCAPE_REGEX = /[&<>"']/g;

// HTML Entity Encoding Regex Patterns
export const HTML_ENTITY_ENCODE_REGEX = {
  AMPERSAND: /&/g,
  DOUBLE_QUOTE: /"/g,
  SINGLE_QUOTE: /'/g,
  LESS_THAN: /</g,
  GREATER_THAN: />/g,
};

// HTML Entity Decoding Regex Patterns
export const HTML_ENTITY_DECODE_REGEX = {
  AMPERSAND: /&amp;/g,
  DOUBLE_QUOTE: /&quot;/g,
  SINGLE_QUOTE: /&#39;/g,
  LESS_THAN: /&lt;/g,
  GREATER_THAN: /&gt;/g,
};

// HTML Entity Encoding Replacements
export const HTML_ENTITY_ENCODE_REPLACEMENTS = {
  AMPERSAND: "&amp;",
  DOUBLE_QUOTE: "&quot;",
  SINGLE_QUOTE: "&#39;",
  LESS_THAN: "&lt;",
  GREATER_THAN: "&gt;",
};

// HTML Entity Decoding Replacements
export const HTML_ENTITY_DECODE_REPLACEMENTS = {
  AMPERSAND: "&",
  DOUBLE_QUOTE: '"',
  SINGLE_QUOTE: "'",
  LESS_THAN: "<",
  GREATER_THAN: ">",
};

// Month Names
export const MONTHS = [
  "January",
  "February",
  "March",
  "April",
  "May",
  "June",
  "July",
  "August",
  "September",
  "October",
  "November",
  "December",
];

export const escapeHtml = (text) => {
  if (text === null || text === undefined) return "";
  const str = String(text);
  const map = {
    "&": "&amp;",
    "<": "&lt;",
    ">": "&gt;",
    '"': "&quot;",
    "'": "&#039;",
  };
  return str.replace(HTML_ESCAPE_REGEX, (match) => map[match]);
};

/**
 * Format currency value for display in PDF
 * @param {number} value - The numeric value to format
 * @returns {string} Formatted currency string (e.g., "$101.2K" or "($4.8K)")
 */
export const formatCurrency = (value) => {
  const num = parseFloat(value) || 0;
  const abs = Math.abs(num);
  const str =
    abs >= 1000 ? `$${(abs / 1000).toFixed(1)}K` : `$${abs.toFixed(0)}`;
  return num < 0 ? `(${str})` : str;
};

export default {
  NUMERIC_EXTRACTION_REGEX,
  HTML_ESCAPE_REGEX,
  HTML_ENTITY_ENCODE_REGEX,
  HTML_ENTITY_DECODE_REGEX,
  HTML_ENTITY_ENCODE_REPLACEMENTS,
  HTML_ENTITY_DECODE_REPLACEMENTS,
  MONTHS,
  escapeHtml,
  formatCurrency,
};