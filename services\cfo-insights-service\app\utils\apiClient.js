import axios from "axios";
import { TIMING_CONSTANTS } from "./constants/timing.constants.js";

const apiClient = axios.create({
  baseURL: process.env.NEXT_PUBLIC_CFO_ENDPOINT,
  headers: { "Content-Type": "application/json" },
  timeout: TIMING_CONSTANTS.HTTP_CLIENT_REQUEST_MS,
});

// Request interceptor
apiClient.interceptors.request.use(
  (config) => {
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor
apiClient.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    return Promise.reject(error);
  }
);

export default apiClient;
