// POWER BI WORKFLOW CONSTANTS
// Static messages, environment keys, and validation constants for Power BI Workflow

export const POWERBI_ENV_KEYS = {
  POWERBI_FINANCE_WORKFLOW: "POWERBI_FINANCE_WORKFLOW",
  POWERBI_OPERATIONAL_WORKFLOW: "POWERBI_OPERATIONAL_WORKFLOW",
  POWERBI_PAYROLL_WORKFLOW: "POWERBI_PAYROLL_WORKFLOW",
  POWER_BI_FINANCE_WORKFLOW_URL: "POWER_BI_FINANCE_WORKFLOW_URL",
  POWER_BI_OPERATIONS_WORKFLOW_URL: "POWER_BI_OPERATIONS_WORKFLOW_URL",
  POWER_BI_PAYROLL_WORKFLOW_URL: "POWER_BI_PAYROLL_WORKFLOW_URL",
};

export const SERVICE_ENV_MAP = {
  Finance: POWERBI_ENV_KEYS.POWERBI_FINANCE_WORKFLOW,
  Operations: POWERBI_ENV_KEYS.POWERBI_OPERATIONAL_WORKFLOW,
  Payroll: POWERBI_ENV_KEYS.POWERBI_PAYROLL_WORKFLOW,
};

export const SERVICE_WORKFLOW_URL_MAP = {
  Finance: POWERBI_ENV_KEYS.POWER_BI_FINANCE_WORKFLOW_URL,
  Operations: POWERBI_ENV_KEYS.POWER_BI_OPERATIONS_WORKFLOW_URL,
  Payroll: POWERBI_ENV_KEYS.POWER_BI_PAYROLL_WORKFLOW_URL,
};

export const VALID_SERVICES = ["Finance", "Operations", "Payroll"];

export const REQUIRED_FIELDS = [
  "organization_id",
  "organization_name",
  "service",
  "month",
  "year",
  "monthYear",
  "file_name",
  "file_size",
  "mime_type",
];

export const POWERBI_MESSAGES = {
  // Success Messages
  WORKFLOW_TRIGGERED_SUCCESS: (service) =>
    `Power BI ${service} workflow triggered successfully`,
  WORKFLOW_TRIGGERED_WITH_STATUS: (service, status) =>
    `Power BI ${service} workflow triggered with status ${status}`,
  
  // Timeout Success Message (timeout is treated as success)
  TIMEOUT_SUCCESS: "Dashboard generation initiated successfully. Processing in background...",

  // Error Messages
  INTERNAL_ERROR: "An internal server error occurred while triggering the dashboard. Please try again later.",
  WORKFLOW_CONFIGURATION_MISSING: (service) =>
    `Power BI workflow configuration missing for service ${service}.`,
  WORKFLOW_URL_NOT_CONFIGURED: (service, envKey) =>
    `Power BI workflow URL not configured for service ${service}. Ensure ${envKey} is set.`,
  WORKFLOW_ENV_CONFIG_ERROR: (service, errorMessage) =>
    `Power BI workflow environment configuration error for service ${service}: ${errorMessage}`,
  WORKFLOW_API_ERROR: (errorMessage) =>
    `Power BI workflow API error: ${errorMessage}`,
  WORKFLOW_REQUEST_FAILED: (errorMessage) =>
    `Power BI workflow request failed: ${errorMessage}`,
  WORKFLOW_TRIGGER_FAILED: (errorMessage) =>
    `Failed to trigger Power BI workflow: ${errorMessage}`,
  WORKFLOW_TRIGGER_FAILED_FOR_SERVICE: (service) =>
    `Failed to trigger Power BI ${service} workflow`,

  // Validation Messages
  ORGANIZATION_ID_REQUIRED: "Organization ID is required",
  ORGANIZATION_NAME_REQUIRED: "Organization name is required",
  SERVICE_REQUIRED: "Service is required",
  MONTH_INFO_REQUIRED: "Month information is required",
  FILE_INFO_REQUIRED: "File information is required",
  INVALID_SERVICE: (services) =>
    `Service must be one of: ${services.join(", ")}`,
  MISSING_FIELDS: (fields) =>
    `Please provide ${fields.join(", ")} in the request body`,

  // Request Messages
  ORGANIZATION_ID_PROVIDE: "Please provide organization_id in the request body",
  ORGANIZATION_NAME_PROVIDE:
    "Please provide organization_name in the request body",
  SERVICE_PROVIDE:
    "Please provide service (Finance, Operations, or Payroll) in the request body",
  MONTH_INFO_PROVIDE:
    "Please provide month, year, and monthYear in the request body",
  FILE_INFO_PROVIDE:
    "Please provide file_name, file_size, and mime_type in the request body",
};

export const POWERBI_LOG_MESSAGES = {
  TRIGGERING_WORKFLOW: (service) =>
    `Triggering Power BI workflow for ${service}`,
  WORKFLOW_TRIGGERED_SUCCESS: (service) =>
    `Power BI ${service} workflow triggered successfully`,
  UNEXPECTED_STATUS_CODE: (status) => `Unexpected status code: ${status}`,
  API_ERROR: (status) => `Power BI workflow API error (${status})`,
  REQUEST_FAILED: "Power BI workflow request failed - no response received",
  TIMEOUT_DETECTED: "Power BI workflow request timed out - treating as success",
  SETUP_ERROR: "Power BI workflow setup error",
  CONTROLLER_ERROR: "Error triggering Power BI workflow",
  UNEXPECTED_ERROR:
    "An unexpected error occurred while triggering the Power BI workflow",
};

export const POWERBI_CONFIG = {
  TIMEOUT: 30000,
  ACCEPTED_STATUS_CODES: [200, 202],
};

// Timeout Detection Constants
export const TIMEOUT_DETECTION = {
  ERROR_CODES: ["ECONNABORTED", "ETIMEDOUT"],
  KEYWORDS: ["timeout", "timed out", "request timeout", "connection timeout"],
};

export default {
  POWERBI_ENV_KEYS,
  SERVICE_ENV_MAP,
  VALID_SERVICES,
  REQUIRED_FIELDS,
  POWERBI_MESSAGES,
  POWERBI_LOG_MESSAGES,
  POWERBI_CONFIG,
  SERVICE_WORKFLOW_URL_MAP,
  TIMEOUT_DETECTION,
};

