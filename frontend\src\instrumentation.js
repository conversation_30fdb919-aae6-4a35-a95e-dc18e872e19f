export async function register() {
  if (process.env.NEXT_RUNTIME === "nodejs") {
    // Handle unhandled rejections to suppress _document warnings
    process.on("unhandledRejection", (reason) => {
      // Suppress PageNotFoundError for _document (not needed in App Router)
      if (
        reason &&
        typeof reason === "object" &&
        reason.code === "ENOENT" &&
        reason.type === "PageNotFoundError" &&
        (reason.message?.includes("_document") ||
          String(reason).includes("_document") ||
          reason.stack?.includes("_document"))
      ) {
        // Silently ignore - this is expected in App Router
        return;
      }
      // Let other unhandled rejections propagate normally
    });
  }
}
