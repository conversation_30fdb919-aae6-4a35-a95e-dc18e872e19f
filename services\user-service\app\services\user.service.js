import { userRepository } from "../repositories/user.repository.js";
import {
  LOG_ACTIONS,
  USER_MESSAGES,
  ERROR_CODES,
  LOGGER_NAMES,
  MODEL_FIELDS,
  API_METHODS,
} from "../utils/constants.util.js";
import {
  hashPassword,
  sendWelcomeMail,
  generateOTP,
  getOTPExpiry,
  sendForgotPasswordMail,
  calculatePasswordExpiration,
  validateUserOtp,
  validatePasswordChange,
  validateOtpExpiryAndMatch,
} from "../utils/methods.util.js";
import { createLogger } from "../utils/logger.util.js";
import { apiRequest } from "../../../../shared/utils/axios.util.js";
import { throwErrorWithCode } from "../utils/error.util.js";

const logger = createLogger(LOGGER_NAMES.USER_SERVICE);

const authServiceUrl = process.env.AUTH_SERVICE_URL;
const frontendUrl = process.env.FRONTEND_URL;

/**
 * User Service
 * Business logic for user operations
 * Optimized to use centralized db_operations and constants
 */
export const userService = {
  /**
   * Create a new user with validation and business logic
   * @param {Object} userData - User data including role_id, organization_id, tenant_id
   * @returns {Promise<Object>} Created user
   */
  async createUser(userData) {
    logger.info(LOG_ACTIONS.CREATING);

    // Validate required fields
    if (!userData[MODEL_FIELDS.EMAIL]) {
      const error = new Error(USER_MESSAGES.EMAIL_REQUIRED);
      error.code = ERROR_CODES.VALIDATION_ERROR;
      throw error;
    }

    if (!userData[MODEL_FIELDS.PASSWORD]) {
      const error = new Error(USER_MESSAGES.PASSWORD_REQUIRED);
      error.code = ERROR_CODES.VALIDATION_ERROR;
      throw error;
    }

    if (!userData[MODEL_FIELDS.FULL_NAME]) {
      const error = new Error(USER_MESSAGES.NAME_REQUIRED);
      error.code = ERROR_CODES.VALIDATION_ERROR;
      throw error;
    }

    // Check for duplicate email using repository
    const emailExists = await userRepository.emailExists(
      userData[MODEL_FIELDS.EMAIL]
    );
    if (emailExists) {
      logger.warn(LOG_ACTIONS.ALREADY_EXISTS);
      const error = new Error(USER_MESSAGES.ALREADY_EXISTS_ERROR);
      error.code = ERROR_CODES.DUPLICATE_ERROR;
      throw error;
    }

    // Prepare complete user data
    const completeUserData = {
      ...userData,
      [MODEL_FIELDS.PASSWORD_HASH]: await hashPassword(
        userData[MODEL_FIELDS.PASSWORD]
      ),
    };

    // Create user using repository
    const newUser = await userRepository.create(completeUserData);
    const getOrgUrl=`${authServiceUrl}/api/organization/${userData.organization_id}`;
    const orgResponse = await apiRequest(API_METHODS.GET, getOrgUrl);


    const orgName = orgResponse.data.name;
    const loginUrl = `${frontendUrl}/login`;

    await sendWelcomeMail({
      recipientEmail: userData.email,
      recipientName: userData.full_name,
      plainPassword: userData.password,
      orgName,
      loginUrl,
      baseUrl: frontendUrl,
    });

    logger.info(LOG_ACTIONS.CREATED_SUCCESS);
    return newUser;
  },

  /**
   * Get all users with pagination support
   * @param {Object} options - Query options including pagination
   * @returns {Promise<Object>} Paginated users result
   */
  async getAllUsers(options = {}) {
    logger.info(LOG_ACTIONS.FETCHING_ALL);

    // Set default pagination values
    const { page = 1, pageSize = 10, ...otherOptions } = options;

    // Use repository for pagination with proper options
    const result = await userRepository.findPaginated({
      page: parseInt(page),
      pageSize: parseInt(pageSize),
      where: { [MODEL_FIELDS.IS_DELETED]: false },
      ...otherOptions,
    });

    logger.info(
      `${LOG_ACTIONS.FETCHED_SUCCESSFULLY}: ${result.data.length} users`
    );
    return result;
  },

  /**
   * Get user by ID with associations
   * @param {number} id - User ID
   * @returns {Promise<Object|null>} User or null
   */
  async getUserById(id) {
    logger.info(LOG_ACTIONS.FETCHING_BY_ID);

    // Get user using repository with associations
    const user = await userRepository.findByField({ id });

    if (user) {
      logger.info(`${LOG_ACTIONS.FETCHED_SUCCESSFULLY}: ID ${id}`);
      return user;
    } else {
      const error = new Error(USER_MESSAGES.NOT_FOUND);
      error.code = ERROR_CODES.NOT_FOUND_ERROR;
      throw error;
    }
  },

  /**
   * Update user by ID
   * @param {number} id - User ID
   * @param {Object} updateData - Data to update
   * @returns {Promise<Object|null>} Updated user or null
   */
  async updateUser(id, updateData) {
    logger.info(LOG_ACTIONS.UPDATING);

    // Check if user exists
    const userExists = await userRepository.exists(id);
    if (!userExists) {
      const error = new Error(USER_MESSAGES.NOT_FOUND_OR_DELETED);
      error.code = ERROR_CODES.NOT_FOUND_ERROR;
      throw error;
    }

    // Check for duplicate email if email is being updated
    if (updateData[MODEL_FIELDS.EMAIL]) {
      const emailExists = await userRepository.emailExists(
        updateData[MODEL_FIELDS.EMAIL]
      );
      if (emailExists) {
        const error = new Error(USER_MESSAGES.ALREADY_EXISTS_ERROR);
        error.code = ERROR_CODES.DUPLICATE_ERROR;
        throw error;
      }
    }

    // Hash password if provided
    if (updateData[MODEL_FIELDS.PASSWORD]) {
      updateData[MODEL_FIELDS.PASSWORD] = await hashPassword(
        updateData[MODEL_FIELDS.PASSWORD]
      );
    }
    // Update user using repository
    const updatedUser = await userRepository.updateById(id, updateData);

    if (updatedUser) {
      logger.info(LOG_ACTIONS.UPDATED_SUCCESS);
    } else {
      logger.warn(LOG_ACTIONS.NOT_FOUND);
    }

    return updatedUser;
  },

  /**
   * Soft delete user by ID
   * @param {number} id - User ID
   * @returns {Promise<boolean>} Success status
   */
  async deleteUser(id) {
    logger.info(LOG_ACTIONS.DELETING);

    // Check if user exists
    const userExists = await userRepository.exists(id);
    if (!userExists) {
      const error = new Error(USER_MESSAGES.NOT_FOUND_OR_DELETED);
      error.code = ERROR_CODES.NOT_FOUND_ERROR;
      throw error;
    }

    // Soft delete user using repository
    const success = await userRepository.softDeleteById(id);

    if (success) {
      logger.info(LOG_ACTIONS.DELETED_SUCCESS);
    } else {
      logger.warn(LOG_ACTIONS.NOT_FOUND);
    }

    return success;
  },

  /**
   * Check if user exists and is not deleted
   * @param {number} id - User ID
   * @returns {Promise<boolean>} Existence status
   */
  async userExists(id) {
    // Validate ID
    if (!id || isNaN(id)) {
      const error = new Error(USER_MESSAGES.INVALID_USER_ID);
      error.code = ERROR_CODES.VALIDATION_ERROR;
      throw error;
    }

    // Check existence using repository
    const exists = await userRepository.exists(parseInt(id));
    return exists;
  },

  /**
   * Find user by email
   * @param {string} email - User email
   * @returns {Promise<Object|null>} User or null
   */
  async getUserByEmail(email) {
    logger.info(LOG_ACTIONS.FETCHING_BY_EMAIL);

    // Validate email
    if (!email) {
      const error = new Error(USER_MESSAGES.EMAIL_REQUIRED);
      error.code = ERROR_CODES.VALIDATION_ERROR;
      throw error;
    }

    // Get user by email using repository
    const user = await userRepository.findByField({ email });

    if (user) {
      logger.info(LOG_ACTIONS.FETCHED_EMAIL);
    } else {
      logger.warn(LOG_ACTIONS.NOT_FOUND_EMAIL);
    }

    return user;
  },

  /**
   * Update user password
   * @param {number} id - User ID
   * @param {string} newPassword - New password
   * @param {Object} options - Additional options
   * @returns {Promise<Object|null>} Updated user or null
   */
  async updateUserPassword(id, newPassword, _options = {}) {
    logger.info(LOG_ACTIONS.UPDATING_PASSWORD);

    // Validate ID and password
    if (!id || isNaN(id)) {
      const error = new Error(USER_MESSAGES.INVALID_USER_ID);
      error.code = ERROR_CODES.VALIDATION_ERROR;
      throw error;
    }

    if (!newPassword) {
      const error = new Error(USER_MESSAGES.PASSWORD_REQUIRED);
      error.code = ERROR_CODES.VALIDATION_ERROR;
      throw error;
    }

    // Check if user exists
    const userExists = await userRepository.exists(parseInt(id));
    if (!userExists) {
      const error = new Error(USER_MESSAGES.NOT_FOUND_OR_DELETED);
      error.code = ERROR_CODES.NOT_FOUND_ERROR;
      throw error;
    }

    // Hash the new password
    const hashedPassword = await hashPassword(newPassword);
    const updateData = {
      password: hashedPassword,
      updated_at: new Date(),
    };

    // Update password using repository
    const updatedUser = await userRepository.updateById(id, updateData);
    return updatedUser;
  },

  /**
   * Handle forgot password request
   * @param {string} email - User email
   * @returns {Promise<boolean>} Success status
   */
  async forgotPassword(email) {
    logger.info(LOG_ACTIONS.FORGOT_PASSWORD_INITIATED, { email });

    const user = await userRepository.findByField({ email });
    if (!user) {
      logger.warn(LOG_ACTIONS.NOT_FOUND_EMAIL, { email });
      return true;
    }

    // Generate OTP
    logger.info(LOG_ACTIONS.GENERATING_OTP, { email });
    const otp = generateOTP();
    const otpExpiry = getOTPExpiry();

    // Store OTP and expiry in database
    logger.info(LOG_ACTIONS.OTP_STORED, { email });
    await userRepository.updateById(user.id, {
      [MODEL_FIELDS.OTP]: otp,
      [MODEL_FIELDS.OTP_EXPIRY]: otpExpiry,
    });

    // Send OTP email
    logger.info(LOG_ACTIONS.SENDING_OTP_EMAIL, { email });
    await sendForgotPasswordMail({
      recipientEmail: user.email,
      recipientName: user.full_name,
      otp,
    });

    logger.info(LOG_ACTIONS.OTP_GENERATED, { email });
  },

  /**
   * Validate OTP for forgot password
   * @param {string} email - User email
   * @param {string} otp - OTP to validate
   * @returns {Promise<boolean>} Success status
   */
  async validateOtp(email, otp) {
    logger.info(LOG_ACTIONS.VALIDATING_OTP, { email });
    const user = await userRepository.findByField({ email });

    const otpParams = { user, otp, email };
    validateUserOtp(otpParams);
    validateOtpExpiryAndMatch(otpParams);

    logger.info(LOG_ACTIONS.OTP_VALIDATED, { email });
  },

  /**
   * Change user password with old password verification
   * @param {string} id - User ID
   * @param {string} oldPassword - Old password
   * @param {string} newPassword - New password
   * @returns {Promise<Object|null>} Updated user or null
   */
  async changePassword(id, oldPassword, newPassword) {
    logger.info(LOG_ACTIONS.UPDATING_PASSWORD);

    // Get user by ID
    const user = await userRepository.findByField({ id });
    if (!user) {
      throwErrorWithCode(
        USER_MESSAGES.NOT_FOUND_OR_DELETED,
        ERROR_CODES.NOT_FOUND_ERROR
      );
    }

    // Validate password change requirements
    await validatePasswordChange({
      oldPassword,
      newPassword,
      hashedPassword: user[MODEL_FIELDS.PASSWORD_HASH],
    });

    // Hash the new password
    const hashedPassword = await hashPassword(newPassword);
    const expiresAt = calculatePasswordExpiration();

    // Update password and set expires_at based on DAYS_TO_EXPIRE_PASSWORD
    const updateData = {
      [MODEL_FIELDS.PASSWORD_HASH]: hashedPassword,
      expires_at: expiresAt,
      updated_at: new Date(),
    };

    // Update password using repository
    const updatedUser = await userRepository.updateById(id, updateData);
    return updatedUser;
  },

  /**
   * Reset user password using user ID (after OTP verification)
   * @param {string} id - User ID
   * @param {string} newPassword - New password
   * @param {string} confirmPassword - Confirm password
   * @returns {Promise<Object|null>} Updated user or null
   */
  async resetPassword(id, newPassword, confirmPassword) {
    logger.info(LOG_ACTIONS.UPDATING_PASSWORD, { id });

    // Validate passwords match
    if (newPassword !== confirmPassword) {
      throwErrorWithCode(
        USER_MESSAGES.PASSWORDS_DONT_MATCH,
        ERROR_CODES.VALIDATION_ERROR
      );
    }

    // Get user by ID
    const user = await userRepository.findByField({ id });
    if (!user) {
      throwErrorWithCode(
        USER_MESSAGES.NOT_FOUND_OR_DELETED,
        ERROR_CODES.NOT_FOUND_ERROR
      );
    }

    // Hash the new password
    const hashedPassword = await hashPassword(newPassword);
    const expiresAt = calculatePasswordExpiration();

    // Update password, clear OTP fields, and set expires_at
    const updateData = {
      [MODEL_FIELDS.PASSWORD_HASH]: hashedPassword,
      [MODEL_FIELDS.OTP]: null,
      [MODEL_FIELDS.OTP_EXPIRY]: null,
      expires_at: expiresAt,
      updated_at: new Date(),
    };

    // Update password using repository
    const updatedUser = await userRepository.updateById(id, updateData);
    return updatedUser;
  },
};

export default userService;
