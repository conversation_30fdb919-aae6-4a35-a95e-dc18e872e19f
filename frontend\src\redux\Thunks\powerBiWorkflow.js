import { createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";
import tokenStorage from "@/lib/tokenStorage";
import { parseMonthString } from "@/utils/methods/bookkeeping";
import { BOOKCLOSURE_CONSTANTS } from "@/utils/constants/bookclosure";
import { POWERBI_WORKFLOW_CONSTANTS } from "@/utils/constants/powerBiWorkflow";

const FILE_SERVICE_URL = process.env.NEXT_PUBLIC_FILE_SERVICE_URL;
const SYSTEM_API_KEY = process.env.NEXT_PUBLIC_SYSTEM_API_KEY;

const getFileServiceClient = () => {
  if (!FILE_SERVICE_URL) {
    throw new Error(
      "NEXT_PUBLIC_FILE_SERVICE_URL environment variable is not configured"
    );
  }

  if (!SYSTEM_API_KEY) {
    throw new Error(
      "NEXT_PUBLIC_SYSTEM_API_KEY environment variable is not configured"
    );
  }

  // Normalize base URL - remove trailing slash if present
  const baseURL = FILE_SERVICE_URL.replace(/\/+$/, "");

  const instance = axios.create({
    baseURL: baseURL,
    headers: {
      "Content-Type": "application/json",
    },
    withCredentials: true,
    timeout: 60000,
  });

  instance.interceptors.request.use(
    (config) => {
      config.headers["x-api-key"] = SYSTEM_API_KEY;

      const accessToken = tokenStorage.getAccessToken();
      if (accessToken && !tokenStorage.isTokenExpired(accessToken)) {
        config.headers.Authorization = `Bearer ${accessToken}`;
      }
      return config;
    },
    (error) => Promise.reject(error)
  );

  return instance;
};

const formatMonthYear = (monthYear, month, year) => {
  try {
    if (monthYear) {
      const parsed = parseMonthString(monthYear);
      if (parsed?.monthYear) {
        return parsed.monthYear;
      }
    }
  } catch (_error) {
    // Fallback to manual normalization below
  }

  const numericMonth = Number.parseInt(month, 10);
  const numericYear = Number.parseInt(year, 10);

  if (
    !Number.isNaN(numericMonth) &&
    numericMonth >= 1 &&
    numericMonth <= 12 &&
    !Number.isNaN(numericYear)
  ) {
    const fullMonthName =
      BOOKCLOSURE_CONSTANTS.MONTH_NAMES.FULL[numericMonth - 1];
    if (fullMonthName) {
      return `${fullMonthName}-${numericYear}`;
    }
  }

  return monthYear || "";
};

const validatePayload = (payload) => {
  if (!payload.organization_id) {
    throw new Error("Organization ID is required");
  }
  if (!payload.organization_name) {
    throw new Error("Organization name is required");
  }
  if (!payload.month || payload.month < 1 || payload.month > 12) {
    throw new Error("Valid month (1-12) is required");
  }
  if (!payload.year || payload.year < 2000 || payload.year > 2100) {
    throw new Error("Valid year is required");
  }
  if (!payload.monthYear) {
    throw new Error("Month year (e.g., 'July-2025') is required");
  }
  if (!payload.file_name) {
    throw new Error("File name is required");
  }
  if (!payload.file_size || payload.file_size <= 0) {
    throw new Error("File size is required and must be greater than 0");
  }
  if (!payload.mime_type) {
    throw new Error("MIME type is required");
  }

  const normalizedMonthYear = formatMonthYear(
    payload.monthYear,
    payload.month,
    payload.year
  );

  if (!normalizedMonthYear) {
    throw new Error("Valid month year (e.g., 'July-2025') is required");
  }

  return normalizedMonthYear;
};

const buildRequestBody = (payload, serviceOverride) => {
  const normalizedMonthYear = validatePayload(payload);
  const service = serviceOverride || payload.service;

  if (!service) {
    throw new Error("Service is required to trigger the Power BI workflow");
  }

  return {
    organization_id: payload.organization_id,
    organization_name: payload.organization_name,
    service,
    month: payload.month,
    year: payload.year,
    monthYear: normalizedMonthYear,
    file_name: payload.file_name,
    file_size: payload.file_size,
    mime_type: payload.mime_type,
  };
};

const isTimeoutError = (error) => {
  const errorMessage = error.message?.toLowerCase() || "";
  const errorCode = error.code?.toUpperCase() || "";

  return (
    POWERBI_WORKFLOW_CONSTANTS.TIMEOUT_KEYWORDS.some((keyword) =>
      errorCode.includes(keyword.toUpperCase())
    ) ||
    POWERBI_WORKFLOW_CONSTANTS.TIMEOUT_KEYWORDS.some((keyword) =>
      errorMessage.includes(keyword.toLowerCase())
    ) ||
    (error.config?.timeout && !error.response && error.request)
  );
};

const handleWorkflowError = (error) => {
  // Check for timeout errors first
  if (isTimeoutError(error)) {
    // Return success-like response for timeout
    return {
      success: true,
      message: POWERBI_WORKFLOW_CONSTANTS.TIMEOUT_SUCCESS,
      timestamp: new Date().toISOString(),
    };
  }

  // Handle other errors
  let errorMessage = POWERBI_WORKFLOW_CONSTANTS.INTERNAL_ERROR;

  if (error.response) {
    const { status, data } = error.response;

    // Extract message from response if available
    if (data?.message) {
      errorMessage = data.message;
    } else {
      switch (status) {
        case 400:
          errorMessage = POWERBI_WORKFLOW_CONSTANTS.INVALID_REQUEST;
          break;
        case 401:
          errorMessage = POWERBI_WORKFLOW_CONSTANTS.UNAUTHORIZED;
          break;
        case 403:
          errorMessage = POWERBI_WORKFLOW_CONSTANTS.FORBIDDEN;
          break;
        case 404:
          errorMessage = POWERBI_WORKFLOW_CONSTANTS.NOT_FOUND;
          break;
        default:
          errorMessage = POWERBI_WORKFLOW_CONSTANTS.INTERNAL_ERROR;
      }
    }
  } else if (error.request) {
    errorMessage = POWERBI_WORKFLOW_CONSTANTS.NETWORK_ERROR;
  } else if (error.message) {
    errorMessage = error.message || POWERBI_WORKFLOW_CONSTANTS.DEFAULT_ERROR;
  }

  return {
    success: false,
    message: errorMessage,
    timestamp: new Date().toISOString(),
  };
};

// const createPowerBiTriggerThunk = (type, serviceOverride = null) =>
//   createAsyncThunk(type, async (payload, { rejectWithValue }) => {
//     try {
//       const requestBody = buildRequestBody(payload, serviceOverride);

//       const apiClient = getFileServiceClient();
//       const response = await apiClient.post("/powerbi/trigger", requestBody);

//       // Extract response data with timestamp
//       const responseData = response.data;

//       return {
//         success: responseData.success !== false,
//         message:
//           responseData.message ||
//           POWERBI_WORKFLOW_CONSTANTS.TRIGGER_SUCCESS("the service"),
//         timestamp: responseData.timestamp || new Date().toISOString(),
//         data: responseData.data,
//         workflowId: responseData.data?.workflowId || null,
//       };
//     } catch (error) {
//       const errorResult = handleWorkflowError(error);

//       // If timeout, return success response (don't reject)
//       if (errorResult.success) {
//         return errorResult;
//       }

//       // For real errors, reject with value
//       return rejectWithValue(errorResult);
//     }
//   });

// export const triggerPowerBiWorkflow = createPowerBiTriggerThunk(
//   "powerBiWorkflow/trigger"
// );

// export const triggerFinanceWorkflow = createPowerBiTriggerThunk(
//   "powerBiWorkflow/triggerFinance",
//   BOOKCLOSURE_CONSTANTS.SERVICES.FINANCE
// );

// export const triggerOperationsWorkflow = createPowerBiTriggerThunk(
//   "powerBiWorkflow/triggerOperations",
//   BOOKCLOSURE_CONSTANTS.SERVICES.OPERATIONS
// );

// export const triggerPayrollWorkflow = createPowerBiTriggerThunk(
//   "powerBiWorkflow/triggerPayroll",
//   BOOKCLOSURE_CONSTANTS.SERVICES.PAYROLL
// );
