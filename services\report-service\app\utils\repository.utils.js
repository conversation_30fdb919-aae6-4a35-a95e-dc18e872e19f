import { sequelize } from "../models/index.js";
import models from "../models/index.js";
import { LOGGER_NAMES } from "./constants/log.constants.js";
import { createLogger } from "./logger.utils.js";

const logger = createLogger(LOGGER_NAMES.REPORT_REPOSITORY);

/**
 * Get organization schema name by organization ID
 * @param {string} orgId - Organization ID
 * @returns {Promise<string|null>} Schema name or null
 */
export const getOrganizationSchemaName = async (orgId) => {
  try {
    const organization = await models.AppOrganization.findOne({
      where: { id: orgId, is_deleted: false },
      attributes: ["schema_name"],
    });

    if (!organization) {
      logger.warn(`Organization not found for ID: ${orgId}`);
      return null;
    }

    return organization.schema_name;
  } catch (error) {
    logger.error(`Error in getOrganizationSchemaName:`, error);
    throw error;
  }
};

/**
 * Construct date range for a specific month and year
 * @param {number} month - Month (1-12)
 * @param {number} year - Year
 * @returns {Object} Object with startDate and endDate
 */
export const getMonthDateRange = (month, year) => {
  const startDate = `${year}-${String(month).padStart(2, "0")}-01`;
  const lastDay = new Date(year, month, 0).getDate();
  const endDate = `${year}-${String(month).padStart(2, "0")}-${String(
    lastDay
  ).padStart(2, "0")}`;

  return { startDate, endDate };
};

/**
 * Get report ID for a specific table, month, and year
 * @param {string} schemaName - Organization schema name
 * @param {string} tableName - Report table name (e.g., 'qb_pnl_reports', 'qb_cash_flow_reports')
 * @param {number} month - Month (1-12)
 * @param {number} year - Year
 * @param {string} logContext - Context for logging (e.g., 'P&L', 'Cash Flow')
 * @returns {Promise<string|null>} Report ID or null
 */
export const getReportId = async (schemaName, tableName, month, year, logContext = 'Report') => {
  try {
    logger.info(
      `Fetching ${logContext} report ID from schema: ${schemaName} for month: ${month}, year: ${year}`
    );

    const { startDate, endDate } = getMonthDateRange(month, year);

    const query = `
      SELECT id as report_id
      FROM "${schemaName}".${tableName}
      WHERE start_date >= :startDate AND end_date <= :endDate
      LIMIT 1
    `;

    const results = await sequelize.query(query, {
      replacements: { startDate, endDate },
      type: sequelize.QueryTypes.SELECT,
    });

    if (results.length === 0) {
      logger.info(
        `No ${logContext} report found for schema: ${schemaName}, month: ${month}, year: ${year}`
      );
      return null;
    }

    logger.info(
      `Found ${logContext} report ID: ${results[0].report_id} for schema: ${schemaName}`
    );
    return results[0].report_id;
  } catch (error) {
    logger.error(`Error getting ${logContext} report ID:`, error);
    throw error;
  }
};

/**
 * Execute a parameterized query on a schema
 * @param {string} schemaName - Organization schema name
 * @param {string} query - SQL query string
 * @param {Object} replacements - Query parameter replacements
 * @param {string} logContext - Context for logging
 * @returns {Promise<Array>} Query results
 */
export const executeSchemaQuery = async (schemaName, query, replacements = {}, logContext = 'Query') => {
  try {
    logger.info(`Executing ${logContext} in schema: ${schemaName}`);

    const results = await sequelize.query(query, {
      replacements,
      type: sequelize.QueryTypes.SELECT,
    });

    logger.info(`Retrieved ${results.length} rows from ${logContext} in schema: ${schemaName}`);

    return results;
  } catch (error) {
    logger.error(`Error executing ${logContext} in schema ${schemaName}:`, error);
    throw error;
  }
};

export default {
  getOrganizationSchemaName,
  getMonthDateRange,
  getReportId,
  executeSchemaQuery,
};
