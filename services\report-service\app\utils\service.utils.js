import { isValidMonth, isValidYear } from "./format.utils.js";

/**
 * Validate common report parameters
 * @param {Object} params - Parameters object
 * @param {string} params.organization_id - Organization ID
 * @param {number|string} params.month - Month (1-12)
 * @param {number|string} params.year - Year
 * @throws {Error} If validation fails
 */
export const validateReportParams = ({
  organization_id,
  org_id,
  month,
  year,
}) => {
  const orgId = organization_id || org_id;

  if (!orgId) {
    throw new Error("Organization ID is required");
  }

  if (!month || !isValidMonth(month)) {
    throw new Error("Valid month (1-12) is required");
  }

  if (!year || !isValidYear(year)) {
    throw new Error("Valid year is required");
  }
};

/**
 * Parse and convert report parameters to numbers
 * @param {Object} params - Parameters object
 * @param {number|string} params.month - Month (1-12)
 * @param {number|string} params.year - Year
 * @returns {Object} Object with monthNum and yearNum
 */
export const parseReportParams = ({ month, year }) => {
  return {
    monthNum: parseInt(month, 10),
    yearNum: parseInt(year, 10),
  };
};

/**
 * Create empty data response for when no data is found
 * @param {string} orgId - Organization ID
 * @param {number} month - Month
 * @param {number} year - Year
 * @param {string} message - Message to include
 * @returns {Object} Empty data response
 */
export const createEmptyDataResponse = (orgId, month, year, message) => {
  return {
    organization_id: orgId,
    org_id: orgId,
    month,
    year,
    data: [],
    message,
  };
};

/**
 * Get start date string for a given month and year
 * @param {number|string} month - Month (1-12)
 * @param {number|string} year - Year
 * @returns {string} Start date in YYYY-MM-DD format
 */
export const getStartDate = (month, year) => {
  return `${year}-${month.toString().padStart(2, "0")}-01`;
};

/**
 * Get previous month start date from a given start date
 * @param {string} startDate - Start date in YYYY-MM-DD format
 * @returns {string} Previous month start date in YYYY-MM-DD format
 */
export const getPreviousMonthStartDate = (startDate) => {
  const date = new Date(startDate);
  date.setMonth(date.getMonth() - 1);
  date.setDate(1);
  return date.toISOString().split("T")[0];
};

/**
 * Calculate percentage change between two values
 * @param {number} currentValue - Current value
 * @param {number} previousValue - Previous value
 * @returns {number} Percentage change (rounded)
 */
export const calculatePercentageChange = (currentValue, previousValue) => {
  if (Math.round(previousValue) === 0) return 0;
  return Math.round(
    ((Math.round(currentValue) - Math.round(previousValue)) /
      Math.round(previousValue)) *
      100
  );
};

/**
 * Calculate percentage distribution for an array of items with values
 * @param {Array<Object>} items - Array of items with value property
 * @param {string} valueKey - Key name for the value property (default: 'value')
 * @returns {Array<Object>} Items with added percentage property
 */
export const calculatePercentageDistribution = (items, valueKey = "value") => {
  const total = items.reduce((sum, item) => sum + (item[valueKey] || 0), 0);
  if (total === 0) {
    return items.map((item) => ({ ...item, percentage: 0 }));
  }
  return items.map((item) => ({
    ...item,
    percentage: Number(((item[valueKey] / total) * 100).toFixed(1)),
  }));
};

/**
 * Group array of objects by a key and sum values
 * @param {Array<Object>} items - Array of objects to group
 * @param {string} groupKey - Key to group by
 * @param {string} valueKey - Key containing the value to sum (default: 'value')
 * @returns {Object} Grouped object with summed values
 */
export const groupByKeyAndSum = (items, groupKey, valueKey = "value") => {
  return items.reduce((acc, item) => {
    const key = item[groupKey];
    const value = Number(item[valueKey] || 0);
    acc[key] = (acc[key] || 0) + value;
    return acc;
  }, {});
};

/**
 * Parse currency string to number by removing currency symbols and commas
 * @param {string} currencyString - Currency string (e.g., "$1,234.56")
 * @returns {number} Parsed number
 */
export const parseCurrencyString = (currencyString) => {
  if (!currencyString) return 0;
  return Number(currencyString.replace(/[$,]/g, "")) || 0;
};

/**
 * Extract name from display_name by splitting on delimiter
 * @param {string} displayName - Display name string (e.g., "Payer - Doctor")
 * @param {number} index - Index to extract (0 for first part, 1 for second part)
 * @param {string} delimiter - Delimiter to split on (default: " - ")
 * @returns {string} Extracted name, trimmed
 */
export const extractNameFromDisplayName = (
  displayName,
  index,
  delimiter = " - "
) => {
  if (!displayName) return "";
  const parts = displayName.split(delimiter);
  return parts[index]?.trim() || "";
};

export default {
  validateReportParams,
  parseReportParams,
  createEmptyDataResponse,
  getStartDate,
  getPreviousMonthStartDate,
  calculatePercentageChange,
  calculatePercentageDistribution,
  groupByKeyAndSum,
  parseCurrencyString,
  extractNameFromDisplayName,
};
