export const ONBOARDING_MESSAGES = {
  LOGO_UPLOAD_SUCCESS: "Organization logo uploaded successfully",
  LOGO_UPLOAD_FAILED: "Failed to upload organization logo",
  PDF_UPLOAD_SUCCESS: "Organization PDF uploaded successfully",
  PDF_UPLOAD_FAILED: "Failed to upload organization PDF",
  JSON_UPLOAD_SUCCESS: "Organization JSON uploaded successfully",
  JSON_UPLOAD_FAILED: "Failed to upload organization JSON",
  FILE_UPLOAD_FAILED: "Failed to upload file",
  INVALID_FILE_TYPE: "Only .png and .svg files are allowed",
  INVALID_PDF_FILE_TYPE: "Only .pdf files are allowed",
  INVALID_JSON_FILE_TYPE: "Only .json files are allowed",
  MISSING_REQUIRED_FIELDS: "Organization ID is required",
  MISSING_FILE: "File is required",
  MISSING_LOGO_FILE: "Logo file is required",
  MISSING_PDF_FILE: "PDF file is required",
  MISSING_JSON_FILE: "JSON file is required",
  FILE_TOO_LARGE: "Logo file exceeds the maximum allowed size of 5MB",
  PDF_FILE_TOO_LARGE: "PDF file exceeds the maximum allowed size",
  JSON_FILE_TOO_LARGE: "JSON file exceeds the maximum allowed size",
  STORAGE_NOT_CONFIGURED: "Blob storage is not configured",
};

export const ONBOARDING_LOG_MESSAGES = {
  CONTROLLER_UPLOAD_START: "[Onboarding] Starting organization logo upload",
  CONTROLLER_UPLOAD_SUCCESS: "[Onboarding] Organization logo upload completed",
  CONTROLLER_UPLOAD_FAILED: "[Onboarding] Organization logo upload failed",
  CONTROLLER_UPLOAD_ERROR:
    "[Onboarding] Unexpected error during organization logo upload",
  CONTROLLER_PDF_UPLOAD_START: "[Onboarding] Starting organization PDF upload",
  CONTROLLER_PDF_UPLOAD_SUCCESS: "[Onboarding] Organization PDF upload completed",
  CONTROLLER_PDF_UPLOAD_FAILED: "[Onboarding] Organization PDF upload failed",
  CONTROLLER_PDF_UPLOAD_ERROR:
    "[Onboarding] Unexpected error during organization PDF upload",
  CONTROLLER_JSON_UPLOAD_START: "[Onboarding] Starting organization JSON upload",
  CONTROLLER_JSON_UPLOAD_SUCCESS: "[Onboarding] Organization JSON upload completed",
  CONTROLLER_JSON_UPLOAD_FAILED: "[Onboarding] Organization JSON upload failed",
  CONTROLLER_JSON_UPLOAD_ERROR:
    "[Onboarding] Unexpected error during organization JSON upload",
  SERVICE_UPLOAD_START:
    "[OnboardingService] Preparing to upload organization logo",
  SERVICE_UPLOAD_SUCCESS:
    "[OnboardingService] Organization logo uploaded successfully",
  SERVICE_UPLOAD_FAILED:
    "[OnboardingService] Organization logo upload failed",
  SERVICE_PDF_UPLOAD_START:
    "[OnboardingService] Preparing to upload organization PDF",
  SERVICE_PDF_UPLOAD_SUCCESS:
    "[OnboardingService] Organization PDF uploaded successfully",
  SERVICE_PDF_UPLOAD_FAILED:
    "[OnboardingService] Organization PDF upload failed",
  SERVICE_JSON_UPLOAD_START:
    "[OnboardingService] Preparing to upload organization JSON",
  SERVICE_JSON_UPLOAD_SUCCESS:
    "[OnboardingService] Organization JSON uploaded successfully",
  SERVICE_JSON_UPLOAD_FAILED:
    "[OnboardingService] Organization JSON upload failed",
  SERVICE_CONTAINER_INIT:
    "[OnboardingService] Ensuring Azure container exists",
};

export const ONBOARDING_ALLOWED_EXTENSIONS = new Set([".png", ".svg"]);
export const ONBOARDING_ALLOWED_MIME_TYPES = new Set([
  "image/png",
  "image/svg+xml",
]);

export const ONBOARDING_ALLOWED_FILE_SIZE_BYTES = 2 * 1024 * 1024; // 5MB default limit

export const ONBOARDING_PDF_ALLOWED_EXTENSIONS = new Set([".pdf"]);
export const ONBOARDING_PDF_ALLOWED_MIME_TYPES = new Set([
  "application/pdf",
]);

export const ONBOARDING_PDF_ALLOWED_FILE_SIZE_BYTES = 10 * 1024 * 1024; // 10MB default limit

export const ONBOARDING_JSON_ALLOWED_EXTENSIONS = new Set([".json"]);
export const ONBOARDING_JSON_ALLOWED_MIME_TYPES = new Set([
  "application/json",
]);

export const ONBOARDING_JSON_ALLOWED_FILE_SIZE_BYTES = 10 * 1024 * 1024; // 10MB default limit

export const ONBOARDING_CONTAINER_NAME = "reports";

export const ONBOARDING_DEFAULT_SEGMENTS = {
  ORG_ID: "org",
  FILE_BASE: "logo",
  PDF_BASE: "pdf",
};

