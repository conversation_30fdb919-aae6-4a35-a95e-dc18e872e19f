export const CHAT_LOG_MESSAGES = {
  START_SESSION: "Starting chat session for filename",
  SESSION_STARTED: "Chat session started successfully",
  SUMMARY_REQUEST: "Getting chat summary for sessionId",
  SUMMARY_SUCCESS: "Chat summary retrieved successfully for session",
  FETCH_ORGANIZATION: "Fetching organization name for ID",
  FILENAME_LOOKUP_FALLBACK:
    "Organization not identified from filename, attempting lookup via auth service",
  ORGANIZATION_FETCH_SUCCESS: "Organization name fetched successfully",
  ORGANIZATION_NOT_FOUND: "Organization name not found in response for ID",
  ORGANIZATION_FETCH_FAILED: "Failed to fetch organization name",
  START_SESSION_ERROR: "Failed to start chat session",
  SUMMARY_ERROR: "Failed to get chat summary",
  ORGANIZATION_LOOKUP_ERROR: "Organization lookup failed with message",
  EXTRACT_ORGANIZATION_ERROR: "Failed to extract organization from filename",
  // New constants for structured logging
  START_CHAT_SESSION: "[START_CHAT_SESSION] Starting chat session",
  SESSION_STARTED_WITH_ID: "[START_CHAT_SESSION] Session started",
  START_CHAT_SESSION_ERROR: "[START_CHAT_SESSION] Error",
  START_CHAT_SESSION_INVALID_RESPONSE: "[START_CHAT_SESSION] Invalid response - missing sessionId",
  GET_CHAT_SUMMARY_START: "[GET_CHAT_SUMMARY] Curl command",
  GET_CHAT_SUMMARY_REQUEST: "[GET_CHAT_SUMMARY] Request",
  GET_CHAT_SUMMARY_RESPONSE: "[GET_CHAT_SUMMARY] Response received",
  GET_CHAT_SUMMARY_SUCCESS: "[GET_CHAT_SUMMARY] Summary received",
  GET_CHAT_SUMMARY_ERROR: "[GET_CHAT_SUMMARY] Error",
  GET_CHAT_SUMMARY_INVALID_RESPONSE: "[GET_CHAT_SUMMARY] Invalid response - missing summaryData",
};

export const DOCUMENT_LOG_MESSAGES = {
  START_STORE_SERVICE: "[DOCUMENT_SERVICE] ===== START: storeDocumentService =====",
  END_STORE_SERVICE: "[DOCUMENT_SERVICE] ===== END: storeDocumentService =====",
  INPUT_DATA: "[DOCUMENT_SERVICE] Input data",
  CHECKING_ORGANIZATION: "[DOCUMENT_SERVICE] Checking organization existence",
  ORGANIZATION_CHECK_RESULT: "[DOCUMENT_SERVICE] Organization check result",
  CREATING_DOCUMENT: "[DOCUMENT_SERVICE] Creating document...",
  UPDATING_DOCUMENT: "[DOCUMENT_SERVICE] Updating document...",
  DOCUMENT_CREATED: "[DOCUMENT_SERVICE] Document created",
  DOCUMENT_UPDATED: "[DOCUMENT_SERVICE] Document updated",
  STARTING_ASYNC_SUMMARY: "[DOCUMENT_SERVICE] ===== Starting asynchronous summary generation =====",
  SUMMARY_GENERATION_PARAMS: "[DOCUMENT_SERVICE] Summary generation parameters",
  STARTING_CHAT_SESSION: "[DOCUMENT_SERVICE] Starting chat session",
  CHAT_SESSION_STARTED: "[DOCUMENT_SERVICE] Chat session started",
  GETTING_SUMMARY: "[DOCUMENT_SERVICE] Getting summary",
  SUMMARY_RECEIVED: "[DOCUMENT_SERVICE] Summary received",
  UPDATING_DOCUMENT_WITH_SUMMARY: "[DOCUMENT_SERVICE] Document updated with summary successfully",
  SUMMARY_GENERATION_FAILED: "[DOCUMENT_SERVICE] Summary generation failed",
  FAILED_START_CHAT_SESSION: "[DOCUMENT_SERVICE] Failed to start chat session",
  FAILED_GET_SUMMARY: "[DOCUMENT_SERVICE] Failed to get summary from chat service",
  STORE_SERVICE_ERROR: "[DOCUMENT_SERVICE] Error in storeDocumentService",
};

export const ORGANIZATION_LOG_MESSAGES = {
  SYSTEM_API_KEY_MISSING:
    "[OrganizationUtil] SYSTEM_API_KEY not configured for organization validation",
};

export const SUMMARY_TRIGGER_LOG_MESSAGES = {
  MISSING_DOCUMENT_ID:
    "[SummaryTrigger] Missing documentId; skipping summary trigger",
  API_KEY_MISSING:
    "[SummaryTrigger] SYSTEM_API_KEY not configured; continuing without header",
  TRIGGER_REQUESTED:
    "[SummaryTrigger] Summary generation requested for document",
  TRIGGER_FAILED:
    "[SummaryTrigger] Failed to trigger summary for document",
};
