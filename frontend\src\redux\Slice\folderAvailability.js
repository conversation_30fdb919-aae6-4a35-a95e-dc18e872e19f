import { createSlice } from "@reduxjs/toolkit";

const initialState = {
  folderAvailability: {},
  checkingFolders: [],
  folderChecks: [],
};

const folderAvailabilitySlice = createSlice({
  name: "folderAvailability",
  initialState,
  reducers: {
    setFolderAvailability: (state, action) => {
      const { clientId, hasFolders } = action.payload;
      state.folderAvailability[clientId] = hasFolders;
    },
    addCheckingFolder: (state, action) => {
      if (!state.checkingFolders.includes(action.payload)) {
        state.checkingFolders.push(action.payload);
      }
    },
    removeCheckingFolder: (state, action) => {
      state.checkingFolders = state.checkingFolders.filter(
        (id) => id !== action.payload
      );
    },
    clearCheckingFolders: (state) => {
      state.checkingFolders = [];
    },
    addFolderCheck: (state, action) => {
      if (!state.folderChecks.includes(action.payload)) {
        state.folderChecks.push(action.payload);
      }
    },
    clearAll: (state) => {
      state.folderAvailability = {};
      state.checkingFolders = [];
      state.folderChecks = [];
    },
  },
});

export const {
  setFolderAvailability,
  addCheckingFolder,
  removeCheckingFolder,
  clearCheckingFolders,
  addFolderCheck,
  clearAll,
} = folderAvailabilitySlice.actions;

export default folderAvailabilitySlice.reducer;
