import { HARDCODED_STRINGS } from "../utils/constants/strings.constants.js";
import { ERROR_MESSAGES } from "../utils/constants/error.constants.js";
import * as status from "../utils/status_code.utils.js";
import logger from "../../config/logger.config.js";
import { getLastMonthDateRange } from "../utils/date.utils.js";
import reportsService from "../services/reports.service.js";
import {
  Reports<PERSON>rror<PERSON>andler,
  ReportsResponseHandler,
  ReportsValidator,
} from "../utils/reports.utils.js";
import { getReportConfig } from "../../config/reports.config.js";
import {
  TrialBalanceReport,
  TrialBalanceColumn,
  TrialBalanceRow,
  PnLReport,
  PnLLine,
  PnLSummary,
  BalanceSheetReport,
  BalanceSheetLineItem,
  CashFlowReport,
  CashFlowLine,
  CashFlowTotal,
} from "../models/index.js";
import { decrypt } from "../utils/encryption.utils.js";
import { quickbooksRepository } from "../repositories/quickbooks.repository.js";
import { Op } from "sequelize";

const REPORT_CONFIGS = {
  [HARDCODED_STRINGS.REPORT_TYPES.TRIAL_BALANCE]: {
    model: TrialBalanceReport,
    related: [TrialBalanceColumn, TrialBalanceRow],
    dateFields: { start: "start_period", end: "end_period" },
  },
  [HARDCODED_STRINGS.REPORT_TYPES.PROFIT_AND_LOSS]: {
    model: PnLReport,
    related: [PnLLine, PnLSummary],
    dateFields: { start: "start_date", end: "end_date" },
  },
  [HARDCODED_STRINGS.REPORT_TYPES.BALANCE_SHEET]: {
    model: BalanceSheetReport,
    related: [BalanceSheetLineItem],
    dateFields: { start: "start_date", end: "end_date" },
  },
  [HARDCODED_STRINGS.REPORT_TYPES.CASH_FLOW]: {
    model: CashFlowReport,
    related: [CashFlowLine, CashFlowTotal],
    dateFields: { start: "start_date", end: "end_date" },
  },
};

const getReportModelConfig = (reportType) => {
  const config = REPORT_CONFIGS[reportType];
  return config
    ? {
        ...config,
        table: config.model.tableName,
        relatedModels: config.related.map((model) => ({
          model,
          foreignKey: "report_id",
          tableName: model.tableName,
        })),
      }
    : null;
};

const escapeSql = (value) => value.replace(/'/g, "''");

const findExistingReports = async (
  config,
  realmId,
  startDate,
  endDate,
  schemaName
) => {
  const { model: ReportModel, dateFields } = config;

  try {
    if (schemaName) {
      const selectClause = Object.keys(ReportModel.rawAttributes)
        .map((attr) => `"${attr}"`)
        .join(", ");
      const query = `
        SELECT ${selectClause}
        FROM "${schemaName}"."${config.table}"
        WHERE "realm_id" = '${escapeSql(realmId)}' 
          AND "${dateFields.start}" = '${escapeSql(startDate)}' 
          AND "${dateFields.end}" = '${escapeSql(endDate)}'
      `;
      const results = await ReportModel.sequelize.query(query, {
        type: ReportModel.sequelize.QueryTypes.SELECT,
      });
      return Array.isArray(results) ? results : [];
    }

    const reports = await ReportModel.findAll({
      where: {
        realm_id: realmId,
        [dateFields.start]: startDate,
        [dateFields.end]: endDate,
      },
    });
    return reports.map((r) => (r.get ? r.get({ plain: true }) : r));
  } catch (error) {
    logger.error("Error querying for existing reports:", {
      error: error.message,
      reportType: config.model.name,
    });
    return [];
  }
};

const deleteRelatedData = async (
  reportId,
  relatedModels,
  ReportModel,
  schemaName
) => {
  for (const relatedModel of relatedModels) {
    try {
      if (schemaName) {
        const deleteQuery = `DELETE FROM "${schemaName}"."${relatedModel.tableName}" WHERE "${relatedModel.foreignKey}" = ${reportId}`;
        await ReportModel.sequelize.query(deleteQuery, {
          type: ReportModel.sequelize.QueryTypes.DELETE,
        });
      } else {
        await relatedModel.model.destroy({
          where: { [relatedModel.foreignKey]: reportId },
        });
      }
      logger.info(`Deleted related ${relatedModel.model.name} records`, {
        reportId,
      });
    } catch (error) {
      logger.error(
        `Error deleting related ${relatedModel.model.name} records:`,
        {
          error: error.message,
        }
      );
    }
  }
};

const checkAndHandleExistingReports = async (
  reportType,
  realmId,
  startDate,
  endDate,
  schemaName = null
) => {
  try {
    const config = getReportModelConfig(reportType);
    if (!config) {
      logger.warn(`Unknown report type: ${reportType}`);
      return { action: "insert" };
    }

    const { model: ReportModel, relatedModels } = config;
    const existingReports = await findExistingReports(
      config,
      realmId,
      startDate,
      endDate,
      schemaName
    );

    logger.info(`Found ${existingReports.length} existing report(s)`, {
      reportType,
      realmId,
      startDate,
      endDate,
      reportIds: existingReports.map((r) => r.id),
    });

    if (existingReports.length === 0) return { action: "insert" };

    if (existingReports.length === 1) {
      const reportId = existingReports[0].id || existingReports[0].get?.("id");
      logger.info(`Found 1 existing report - will update report ${reportId}`, {
        reportType,
        realmId,
      });

      await deleteRelatedData(reportId, relatedModels, ReportModel, schemaName);
      return {
        action: "update",
        reportId,
        ReportModel,
        relatedModels,
        schemaName,
      };
    }

    logger.info(
      `Found ${existingReports.length} existing reports - will delete all and insert new`,
      {
        reportType,
        realmId,
      }
    );

    const reportIds = existingReports
      .map((r) => r.id || r.get?.("id"))
      .filter(Boolean);

    for (const reportId of reportIds) {
      await deleteRelatedData(reportId, relatedModels, ReportModel, schemaName);
    }

    if (schemaName) {
      const deleteQuery = `DELETE FROM "${schemaName}"."${
        config.table
      }" WHERE "id" IN (${reportIds.join(", ")})`;
      await ReportModel.sequelize.query(deleteQuery, {
        type: ReportModel.sequelize.QueryTypes.DELETE,
      });
    } else {
      await ReportModel.destroy({ where: { id: { [Op.in]: reportIds } } });
    }

    logger.info(`Deleted ${reportIds.length} existing report(s)`, {
      deletedReportIds: reportIds,
    });
    return { action: "insert" };
  } catch (error) {
    logger.error("Error checking existing reports:", {
      error: error.message,
      reportType,
      realmId,
    });
    return { action: "insert" };
  }
};

const prepareQuickBooksAccount = async (
  account,
  realmId,
  schemaName,
  quickBookAccessToken
) => ({
  realm_id: realmId,
  access_token: quickBookAccessToken,
  refreshToken: account?.refresh_token
    ? await decrypt(account.refresh_token)
    : null,
  schemaName,
});

const buildResponseData = (
  savedData,
  reportType,
  startDate,
  endDate,
  realmId,
  reqBody
) => ({
  reportId: savedData.reportId,
  columnsCount: savedData.columnsCount,
  rowsCount: savedData.rowsCount,
  reportType,
  dateRange: { startDate, endDate },
  realmId,
  ...(reportType === HARDCODED_STRINGS.REPORT_TYPES.PROFIT_AND_LOSS && {
    autoGeneratedDates: !reqBody.startDate || !reqBody.endDate,
  }),
  ...(reportType === HARDCODED_STRINGS.REPORT_TYPES.BALANCE_SHEET &&
    savedData.totals && {
      totals: savedData.totals,
      balanceCheck: {
        isBalanced: savedData.totals.isBalanced,
        totalAssets: savedData.totals.totalAssets,
        totalLiabilitiesAndEquity: savedData.totals.totalLiabilitiesAndEquity,
      },
    }),
});

export const processReport = async (req, res, reportType) => {
  const config = getReportConfig(reportType);
  if (!config) {
    return ReportsResponseHandler.sendError(
      res,
      `Unsupported report type: ${reportType}`,
      status.STATUS_CODE_BAD_REQUEST
    );
  }

  const { realmId, schemaName } = req.body;
  const { startDate, endDate } = req.body;

  const account = await quickbooksRepository.findByRealmId(
    realmId,
    {},
    schemaName
  );
  logger.info(`Quickbook existing report(s)`, {
    account,
  });
  const quickBookAccesstoken = account?.access_token
    ? await decrypt(account.access_token)
    : null;

  if (!quickBookAccesstoken) {
    return res
      .status(status.STATUS_CODE_UNAUTHORIZED)
      .json(errorResponse(ERROR_MESSAGES.NO_ACCESS_TOKEN));
  }

  logger.info("Processing report request", {
    reportType,
    startDate,
    endDate,
    realmId,
    schemaName,
  });

  try {
    if (
      !ReportsValidator.validateRequiredFields(
        req,
        res,
        reportType,
        config.requiresDates
      )
    ) {
      return;
    }

    if (
      reportType === HARDCODED_STRINGS.REPORT_TYPES.PROFIT_AND_LOSS &&
      (!startDate || !endDate)
    ) {
      const lastMonthRange = getLastMonthDateRange();
      logger.info("Auto-generated dates for P&L report", {
        autoStartDate: startDate || lastMonthRange.startDate,
        autoEndDate: endDate || lastMonthRange.endDate,
      });
    }

    const account = await quickbooksRepository.findByRealmId(
      realmId,
      {},
      schemaName
    );
    const quickBookAccount = await prepareQuickBooksAccount(
      account,
      realmId,
      schemaName,
      quickBookAccesstoken
    );

    logger.info("Processing report request", {
      reportType,
      startDate,
      endDate,
      realmId,
      schemaName,
    });

    const existingReportAction = await checkAndHandleExistingReports(
      reportType,
      realmId,
      startDate,
      endDate,
      schemaName
    );

    const reportData = await reportsService.getReportDataFromQuickBooks(
      quickBookAccount,
      startDate,
      endDate,
      reportType,
      quickBookAccount.access_token
    );
    const existingReportId =
      existingReportAction.action === "update"
        ? existingReportAction.reportId
        : null;

    let mappedData;
    try {
      const mappingArgs = [
        reportData,
        null,
        realmId,
        startDate,
        endDate,
        schemaName,
        existingReportId,
      ];
      mappedData = config.isAsync
        ? await config.mapFunction(...mappingArgs)
        : config.mapFunction(...mappingArgs);
    } catch (mappingError) {
      logger.error("Mapping function failed", {
        error: mappingError.message,
        stack: mappingError.stack,
        reportType,
        realmId,
      });
      return ReportsResponseHandler.sendError(
        res,
        ERROR_MESSAGES.PROCESSING.DATA_MAPPING_FAILED,
        status.STATUS_CODE_INTERNAL_SERVER_ERROR
      );
    }

    if (!mappedData?.reportData) {
      logger.error("Failed to map report data", { reportType, realmId });
      return ReportsResponseHandler.sendError(
        res,
        ERROR_MESSAGES.PROCESSING.DATA_MAPPING_FAILED,
        status.STATUS_CODE_INTERNAL_SERVER_ERROR
      );
    }
    const savedData =
      existingReportAction.action === "update"
        ? config.isAsync
          ? {
              reportId: existingReportAction.reportId,
              columnsCount: 0,
              rowsCount:
                mappedData.processingResult?.lineItemsCount ||
                mappedData.processingResult?.linesCount ||
                0,
              ...(mappedData.processingResult?.totals && {
                totals: mappedData.processingResult.totals,
              }),
              ...(mappedData.processingResult?.balanceCheck && {
                balanceCheck: mappedData.processingResult.balanceCheck,
              }),
            }
          : await config.saveFunction(
              mappedData,
              schemaName,
              existingReportAction.reportId
            )
        : await config.saveFunction(mappedData, schemaName);

    if (!savedData?.reportId) {
      logger.error("Failed to save report data", { reportType });
      return ReportsResponseHandler.sendError(
        res,
        ERROR_MESSAGES.DATABASE.SAVE_FAILED,
        status.STATUS_CODE_INTERNAL_SERVER_ERROR
      );
    }

    const responseData = buildResponseData(
      savedData,
      reportType,
      startDate,
      endDate,
      realmId,
      req.body
    );
    return ReportsResponseHandler.sendSuccess(
      res,
      config.successMessage,
      responseData
    );
  } catch (error) {
    return ReportsErrorHandler.handleApiError(
      error,
      res,
      reportType,
      realmId,
      "processing"
    );
  }
};

export const fetchTrialBalance = (req, res) =>
  processReport(req, res, HARDCODED_STRINGS.REPORT_TYPES.TRIAL_BALANCE);

export const fetchProfitLoss = (req, res) =>
  processReport(req, res, HARDCODED_STRINGS.REPORT_TYPES.PROFIT_AND_LOSS);

export const fetchBalanceSheet = (req, res) =>
  processReport(req, res, HARDCODED_STRINGS.REPORT_TYPES.BALANCE_SHEET);

export const fetchCashFlow = (req, res) =>
  processReport(req, res, HARDCODED_STRINGS.REPORT_TYPES.CASH_FLOW);

const getReportsByRealmId = async (req, res, reportType, serviceFunction) => {
  try {
    const { realmId } = req.params;
    if (!realmId) {
      return ReportsErrorHandler.handleValidationError(
        res,
        "realmId",
        reportType
      );
    }

    const reports = await serviceFunction(realmId, {
      order: HARDCODED_STRINGS.DB_ORDER.CREATED_AT_DESC,
      limit: parseInt(req.query.limit) || 10,
      offset: parseInt(req.query.offset) || 0,
    });

    return ReportsResponseHandler.sendSuccess(
      res,
      `${reportType} reports retrieved successfully`,
      {
        reports,
        realmId,
        count: reports.length,
      }
    );
  } catch (error) {
    return ReportsErrorHandler.handleApiError(
      error,
      res,
      reportType,
      req.params?.realmId,
      "retrieval"
    );
  }
};

export const getTrialBalanceReports = (req, res) =>
  getReportsByRealmId(
    req,
    res,
    HARDCODED_STRINGS.REPORT_TYPES.TRIAL_BALANCE_DISPLAY,
    reportsService.getTrialBalanceReportsByRealmId
  );

export const getProfitLossReports = (req, res) =>
  getReportsByRealmId(
    req,
    res,
    HARDCODED_STRINGS.REPORT_TYPES.PROFIT_LOSS_DISPLAY,
    reportsService.getProfitLossReportsByRealmId
  );

export const getBalanceSheetReports = (req, res) =>
  getReportsByRealmId(
    req,
    res,
    HARDCODED_STRINGS.REPORT_TYPES.BALANCE_SHEET_DISPLAY,
    reportsService.getBalanceSheetReportsByRealmId
  );

export const getCashFlowReports = (req, res) =>
  getReportsByRealmId(
    req,
    res,
    HARDCODED_STRINGS.REPORT_TYPES.CASH_FLOW_DISPLAY,
    reportsService.getCashFlowReportsByRealmId
  );
