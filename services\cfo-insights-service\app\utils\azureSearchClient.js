// app/utils/azureSearchClient.js
import dotenv from "dotenv";
dotenv.config();

import fetch from "node-fetch";

/**
 * Search competitor data with timeout to prevent hanging
 * @param {string} query - Search query
 * @param {number} timeoutMs - Timeout in milliseconds (default: 3000ms)
 * @returns {Promise<Array>} Array of competitor data results
 */
export async function searchCompetitorData(query, timeoutMs = 3000) {
  const endpoint = process.env.AZURE_SEARCH_ENDPOINT;
  const key = process.env.AZURE_SEARCH_API_KEY;
  const index = process.env.AZURE_SEARCH_INDEX;
  if (!endpoint || !key || !index) {
    return [];
  }

  const url = `${endpoint}/indexes/${index}/docs/search?api-version=2023-07-01-Preview`;
  const body = {
    search: query,
    top: 3,
    select: "title,url,snippet",
  };

  try {
    // OPTIMIZATION: Add timeout to prevent hanging
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeoutMs);

    const res = await fetch(url, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "api-key": key,
      },
      body: JSON.stringify(body),
      signal: controller.signal,
    });

    clearTimeout(timeoutId);

    if (!res.ok) {
      return [];
    }

    const data = await res.json();
    if (!data.value) return [];

    return data.value.map((doc) => ({
      title: doc.title,
      url: doc.url,
      snippet: doc.snippet,
    }));
  } catch (error) {
    // Swallow errors and return empty results to avoid breaking caller flow.
    return [];
  }
}
