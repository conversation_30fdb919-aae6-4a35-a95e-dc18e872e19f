"use client";

import { useWatch, useFormContext } from "react-hook-form";
import {
  ORGANIZATION_FIELD_CONSTANTS,
  ORGANIZATION_CONSTANTS,
} from "@/utils/constants/organization";

export default function SikkaIdField({
  field,
  form,
  checkConnection,
  connectionStatus,
}) {
  const formContext = useFormContext();
  const selectedServices =
    useWatch({
      name: "services",
      control: formContext?.control,
      defaultValue: [],
    }) || [];
  const errors =
    formContext?.formState?.errors || form?.formState?.errors || {};
  const fieldError = errors?.office_id;

  // Compute if operational is selected - use this for rendering
  const isOperationalSelected = selectedServices.includes("operational");

  // Clear field and errors when operational is not selected
  if (!isOperationalSelected) {
    // Only clear if there's actually a value or error to avoid unnecessary updates
    if (field.value || fieldError) {
      // Use setTimeout to avoid state updates during render
      setTimeout(() => {
        const formInstance = formContext || form;
        if (formInstance?.setValue) {
          formInstance.setValue("office_id", "", { shouldValidate: false });
        }
        if (formInstance?.clearErrors) {
          formInstance.clearErrors("office_id");
        }
      }, 0);
    }
    return null;
  }

  // Ensure value is always a string to avoid controlled/uncontrolled warning
  const inputValue = field.value ?? "";
  const sikkaIdDigits = ORGANIZATION_CONSTANTS.SIKKA_ID_DIGITS;

  // Check if current office_id is verified
  const isVerified =
    connectionStatus?.isVerified &&
    connectionStatus?.verifiedOfficeId === inputValue &&
    inputValue.length === sikkaIdDigits;
  const isChecking = connectionStatus?.isChecking || false;

  // Handle input change with validation and API call
  const handleChange = (e) => {
    const newValue = e.target.value;
    // Only allow up to SIKKA_ID_DIGITS characters
    if (newValue.length <= sikkaIdDigits) {
      field.onChange(newValue);

      // Make API call when exactly SIKKA_ID_DIGITS characters are entered
      if (newValue.length === sikkaIdDigits && checkConnection) {
        checkConnection(newValue);
      } else if (newValue.length < sikkaIdDigits && checkConnection) {
        checkConnection(newValue);
      }
    }
  };

  return (
    <div>
      <label
        htmlFor="office_id"
        className="block text-sm font-medium text-gray-700 mb-1"
      >
        {ORGANIZATION_FIELD_CONSTANTS.OFFICE_ID?.LABEL || "Sikka ID"}
        <span className="text-red-500 ml-1">*</span>
      </label>
      <div className="relative">
        <input
          id="office_id"
          type="text"
          placeholder={
            ORGANIZATION_FIELD_CONSTANTS.OFFICE_ID?.PLACEHOLDER ||
            `Enter Sikka ID (${sikkaIdDigits} characters)`
          }
          value={inputValue}
          onChange={handleChange}
          onBlur={field.onBlur}
          maxLength={sikkaIdDigits}
          disabled={isChecking}
          className={`w-full h-10 px-3 py-2 pr-10 text-sm bg-white border rounded-lg shadow-sm placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 disabled:cursor-not-allowed disabled:opacity-50 transition-all duration-200 ${
            fieldError
              ? "border-red-500 focus:ring-red-500 focus:border-red-500"
              : isVerified
                ? "border-green-500 focus:ring-green-500 focus:border-green-500"
                : "border-gray-300"
          }`}
        />
        {/* Show verification status icon */}
        {inputValue.length === sikkaIdDigits && (
          <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
            {isChecking ? (
              <svg
                className="animate-spin h-5 w-5 text-indigo-600"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
              >
                <circle
                  className="opacity-25"
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  strokeWidth="4"
                ></circle>
                <path
                  className="opacity-75"
                  fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                ></path>
              </svg>
            ) : isVerified ? (
              <svg
                className="h-5 w-5 text-green-600"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M5 13l4 4L19 7"
                />
              </svg>
            ) : inputValue.length === sikkaIdDigits ? (
              <svg
                className="h-5 w-5 text-red-500"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            ) : null}
          </div>
        )}
      </div>
      {/* Show error message or verification status */}
      {fieldError && (
        <p className="text-red-500 text-xs font-medium mt-1">
          {fieldError.message}
        </p>
      )}
      {!fieldError && inputValue.length === sikkaIdDigits && isVerified && (
        <p className="text-green-600 text-xs font-medium mt-1">
          {ORGANIZATION_FIELD_CONSTANTS.OFFICE_ID.CONNECTION.VERIFIED_SUCCESS}
        </p>
      )}
      {!fieldError &&
        inputValue.length === sikkaIdDigits &&
        !isVerified &&
        !isChecking &&
        connectionStatus && (
          <p className="text-red-500 text-xs font-medium mt-1">
            {
              ORGANIZATION_FIELD_CONSTANTS.OFFICE_ID.CONNECTION
                .VERIFICATION_FAILED
            }
          </p>
        )}
    </div>
  );
}
