export const HTTP_STATUS = {
  OK: 200,
  ACCEPTED: 202,
  BAD_REQUEST: 400,
  NOT_FOUND: 404,
  INTERNAL_SERVER_ERROR: 500,
};

export const RESPONSE_STATUS = {
  SUCCESS: "success",
  ERROR: "error",
};

export const buildSuccessResponse = (data = {}, message) => ({
  status: RESPONSE_STATUS.SUCCESS,
  data,   
  ...(message ? { message } : {}),
});

export const buildErrorResponse = (errorMessage, code, details) => ({
  status: RESPONSE_STATUS.ERROR,
  error: errorMessage,
  ...(code ? { code } : {}),
  ...(details ? { details } : {}),
});

