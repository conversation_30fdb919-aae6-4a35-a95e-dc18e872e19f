import { LOGGER_NAMES } from "../utils/constants/log.constants.js";
import { createLogger } from "../utils/logger.utils.js";
import { getRecordsByDateRange } from "../utils/database.utils.js";
import {
  getPreviousMonthStartDate,
  getStartDate,
  calculatePercentageChange,
} from "../utils/service.utils.js";
import { getOrganizationSchemaName } from "../utils/repository.utils.js";
import cleanCurrency from "../../../../shared/utils/common-functions.util.js";
const logger = createLogger(LOGGER_NAMES.REPORT_SERVICE);

/**
 * Generic helper to build an operations metric with current, previous month,
 * and year-to-date values and percentage change vs previous month.
 *
 * @param {Object} params
 * @param {string} params.schemaName
 * @param {Date} params.startDate
 * @param {number} params.month
 * @param {string} params.tableName
 * @param {string} params.id
 * @param {string} params.title
 * @param {string} params.format
 * @param {(rows: any[]) => number} params.currentReducer
 * @param {(rows: any[]) => number} params.previousReducer
 * @param {(rows: any[]) => number} params.yearToDateReducer
 * @returns {Promise<Object>}
 */
const buildOperationsMetric = async ({
  schemaName,
  startDate,
  month,
  tableName,
  id,
  title,
  format,
  currentReducer,
  previousReducer,
  yearToDateReducer,
}) => {
  const currentRows = await getRecordsByDateRange(
    schemaName,
    tableName,
    startDate
  );

  const previousMonthStartDate = getPreviousMonthStartDate(startDate);
  const previousMonthRows = await getRecordsByDateRange(
    schemaName,
    tableName,
    previousMonthStartDate
  );

  const yearToDateRows = await getRecordsByDateRange(
    schemaName,
    tableName,
    startDate,
    month
  );

  const value = currentReducer(currentRows) || 0;
  const previousMonthValue = previousReducer(previousMonthRows) || 0;
  const yearToDate = yearToDateReducer(yearToDateRows) || 0;

  return {
    id,
    title,
    format,
    value: Math.round(value),
    previousMonth: Math.round(previousMonthValue),
    previousMonthChange: calculatePercentageChange(value, previousMonthValue),
    yearToDate: Math.round(yearToDate),
  };
};

/**
 * Get total collections for organization
 * @param {string} schemaName - Schema name
 * @param {Date} startDate - Start date
 * @param {number} month - Month (1-12)
 * @returns {Promise<Object>} Total collections data
 */
const getTotalCollections = async (schemaName, startDate, month) => {
  const multiplierReducer = (rows) =>
    rows.reduce((sum, item) => {
      const cleaned = cleanCurrency(item.value);
      const num = Number(cleaned) || 0;
      return sum + num;
    }, 0);

  return buildOperationsMetric({
    schemaName,
    startDate,
    month,
    tableName: "sikka_gross_collections",
    id: "totalCollections",
    title: "Total Collections",
    format: "currency",
    currentReducer: multiplierReducer,
    previousReducer: multiplierReducer,
    yearToDateReducer: multiplierReducer,
  });
};

/**
 * Get new patients for organization
 * @param {string} schemaName - Schema name
 * @param {Date} startDate - Start date
 * @param {number} month - Month (1-12)
 * @returns {Promise<Object>} New patients data
 */
const getNewPatients = async (schemaName, startDate, month) => {
  return buildOperationsMetric({
    schemaName,
    startDate,
    month,
    tableName: "sikka_new_patients",
    id: "newPatients",
    title: "New Patients",
    format: "number",
    currentReducer: (rows) => Number(rows[0]?.new_patients || 0),
    previousReducer: (rows) => Number(rows[0]?.new_patients || 0),
    yearToDateReducer: (rows) =>
      rows
        .map((item) => item.new_patients)
        .reduce((sum, item) => sum + Number(item), 0),
  });
};

/**
 * Get patients scheduled for organization
 * @param {string} schemaName - Schema name
 * @param {Date} startDate - Start date
 * @param {number} month - Month (1-12)
 * @returns {Promise<Object>} Patients scheduled data
 */
const getPatientsScheduled = async (schemaName, startDate, month) => {
  const validStatuses = new Set(["Proposed", "Accepted"]);

  const scheduledReducer = (rows) =>
    rows.reduce((sum, row) => {
      if (validStatuses.has(row.treatment_plan_status)) {
        return sum + Number(row.value || 0);
      }
      return sum;
    }, 0);

  return buildOperationsMetric({
    schemaName,
    startDate,
    month,
    tableName: "sikka_treatment_plan_analysis",
    id: "patientsScheduled",
    title: "Patients Scheduled",
    format: "number",
    currentReducer: scheduledReducer,
    previousReducer: scheduledReducer,
    yearToDateReducer: scheduledReducer,
  });
};

/**
 * Get patients cancelled for organization
 * @param {string} schemaName - Schema name
 * @param {Date} startDate - Start date
 * @param {number} month - Month (1-12)
 * @returns {Promise<Object>} Patients cancelled data
 */
const getPatientsCancelled = async (schemaName, startDate, month) => {
  const status = "Rejected";
  const cancelledReducer = (rows) =>
    rows.reduce((sum, row) => {
      if (row.treatment_plan_status === status) {
        return sum + Number(row.value || 0);
      }
      return sum;
    }, 0);

  return buildOperationsMetric({
    schemaName,
    startDate,
    month,
    tableName: "sikka_treatment_plan_analysis",
    id: "cancellations",
    title: "Cancellations",
    format: "number",
    currentReducer: cancelledReducer,
    previousReducer: cancelledReducer,
    yearToDateReducer: cancelledReducer,
  });
};

/**
 * Get patients no-show for organization
 * @param {string} schemaName - Schema name
 * @param {Date} startDate - Start date
 * @param {number} month - Month (1-12)
 * @returns {Promise<Object>} Patients no-show data
 */
const getPatientsNoShow = async (schemaName, startDate, month) => {
  return buildOperationsMetric({
    schemaName,
    startDate,
    month,
    tableName: "sikka_no_show_appointment",
    id: "noShows",
    title: "No-Shows",
    format: "number",
    currentReducer: (rows) => Number(rows[0]?.value || 0),
    previousReducer: (rows) => Number(rows[0]?.value || 0),
    yearToDateReducer: (rows) =>
      rows
        .map((item) => item.value)
        .reduce((sum, item) => sum + Number(item), 0),
  });
};

/**
 * Get operations overview data for organization
 * @param {Object} params - Query parameters
 * @param {string} params.organization_id - Organization ID
 * @param {number} params.month - Month (1-12)
 * @param {number} params.year - Year
 * @returns {Promise<Object>} Operations overview data
 */
const getOperationsOverview = async ({ organization_id, month, year }) => {
  try {
    logger.info(
      `Fetching operations overview data for org: ${organization_id}, month: ${month}, year: ${year}`
    );

    const schemaName = await getOrganizationSchemaName(organization_id);
    const startDate = getStartDate(month, year);

    const [
      totalCollections,
      newPatients,
      patientsScheduled,
      patientsCancelled,
      patientsNoShow,
    ] = await Promise.all([
      getTotalCollections(schemaName, startDate, month),
      getNewPatients(schemaName, startDate, month),
      getPatientsScheduled(schemaName, startDate, month),
      getPatientsCancelled(schemaName, startDate, month),
      getPatientsNoShow(schemaName, startDate, month),
    ]);

    const responseData = [
      totalCollections,
      newPatients,
      patientsScheduled,
      patientsCancelled,
      patientsNoShow,
    ];

    logger.info("Operations overview data fetched successfully");
    return responseData;
  } catch (error) {
    logger.error("Error in OperationsService.getOperationsOverview:", error);
    throw error;
  }
};

export default {
  getOperationsOverview,
};
