import { validationRules } from "@/utils/methods/validation";

export const usersData = [
  {
    id: 1,
    name: "<PERSON>",
    email: "<EMAIL>",
    role: "Admin",
    status: "Active",
    tenant: "Acme Corporation",
    lastLogin: "2024-01-20",
    createdAt: "2024-01-15",
    phone: "******-0101",
    department: "IT",
    position: "System Administrator",
    avatar: null,
  },
  {
    id: 2,
    name: "<PERSON>",
    email: "<EMAIL>",
    role: "User",
    status: "Active",
    tenant: "TechStart Inc",
    lastLogin: "2024-01-19",
    createdAt: "2024-02-20",
    phone: "******-0102",
    department: "Marketing",
    position: "Marketing Specialist",
    avatar: null,
  },
  {
    id: 3,
    name: "<PERSON>",
    email: "<EMAIL>",
    role: "Manager",
    status: "Inactive",
    tenant: "Global Solutions",
    lastLogin: "2024-01-10",
    createdAt: "2024-01-10",
    phone: "******-0103",
    department: "Operations",
    position: "Operations Manager",
    avatar: null,
  },
  {
    id: 4,
    name: "<PERSON>",
    email: "<EMAIL>",
    role: "User",
    status: "Active",
    tenant: "Acme Corporation",
    lastLogin: "2024-01-21",
    createdAt: "2024-01-18",
    phone: "******-0104",
    department: "Finance",
    position: "Financial Analyst",
    avatar: null,
  },
  {
    id: 5,
    name: "Mike Davis",
    email: "<EMAIL>",
    role: "Super Admin",
    status: "Active",
    tenant: "TechStart Inc",
    lastLogin: "2024-01-22",
    createdAt: "2024-01-12",
    phone: "******-0105",
    department: "IT",
    position: "CTO",
    avatar: null,
  },
];

export const userFields = [
  {
    name: "full_name",
    label: "Full Name",
    type: "text",
    placeholder: "Enter full name",
    validation: { required: "Full name is required" },
  },
  {
    name: "password",
    label: "Password",
    type: "password",
    placeholder: "Enter password",
    autoComplete: "new-password",
    validation: {
      required: "Password is required",
      minLength: {
        value: 8,
        message: "Password must be at least 8 characters long",
      },
      pattern: {
        value:
          /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
        message:
          "Password must contain at least 1 uppercase letter, 1 lowercase letter, 1 number, and 1 special character",
      },
    },
  },
  {
    name: "organization_id",
    label: "Organization",
    type: "select",
    placeholder: "Select organization",
    options: [], // Will be populated dynamically from API
    validation: { required: "Organization is required" },
    isDynamic: true, // Flag to indicate this field needs dynamic options
  },
  {
    name: "email",
    label: "Email",
    type: "email",
    placeholder: "<EMAIL>",
    autoComplete: "off",
    validation: {
      required: validationRules.email.required,
      pattern: {
        value: validationRules.email.pattern,
        message: validationRules.email.message,
      },
    },
  },
  {
    name: "phone_number",
    label: "Phone Number",
    type: "tel",
    placeholder: "******-555-0198",
    validation: {
      required: "Phone number is required",
      pattern: {
        value: /^\+?[\d\s\-\(\)]{10,}$/,
        message: "Please enter a valid phone number",
      },
    },
  },
];

export const initialValues = {
  organization_id: "",
  email: "",
  password: "",
  full_name: "",
  phone_number: "",
  role: "user",
};
