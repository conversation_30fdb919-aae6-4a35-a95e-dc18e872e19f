// app/utils/normalizers.util.js
import { CATEGORY_VARIATIONS } from "./constants/file.constants.js";
import logger from "../../config/logger.config.js";

/**
 * Normalize category name to standardized format
 * @param {string} category - Category name to normalize
 * @returns {string} Normalized category name
 */
export const normalizeCategory = (category) => {
  try {
    // Guard clause: return early if invalid input
    if (!category || typeof category !== "string") {
      return category;
    }

    const categoryLower = category.toLowerCase();

    // Use mapping pattern: check if category exists in variations map
    const normalizedCategory = CATEGORY_VARIATIONS[categoryLower];
    if (normalizedCategory) {
      return normalizedCategory;
    }

    // Fallback: capitalize first letter if not found in map
    return category.charAt(0).toUpperCase() + category.slice(1).toLowerCase();
  } catch (error) {
    logger.error(`Error normalizing category: ${error.message}`, { category, error });
    // Return original category if normalization fails
    return category;
  }
};

/**
 * Remove quotes from string if present and trim whitespace
 * @param {string} value - String value to sanitize
 * @returns {string} Sanitized string
 */
export const sanitizeString = (value) => {
  if (!value) return value;
  const trimmed = value.trim();
  if (
    (trimmed.startsWith('"') && trimmed.endsWith('"')) ||
    (trimmed.startsWith("'") && trimmed.endsWith("'"))
  ) {
    return trimmed.slice(1, -1);
  }
  return trimmed;
};

