﻿# SERVER CONFIGURATION
NODE_ENV=development
REPORT_SERVICE_PORT=5004
SERVER_URL=http://localhost:5004
HOST=localhost
PROTOCOL=http

# DB CONFIGURATION
DB_NAME=perfino-local
DB_USER=postgres
DB_PASS=NehalK2214!
DB_HOST=localhost
DB_PORT=5433
DB_DIALECT=postgres
DB_SSL=false

# LOGGING CONFIGURATION
LOG_LEVEL=info
LOG_FORMAT=YYYY-MM-DD HH:mm:ss
LOG_FILE_PATH=logs/report-service.log
LOG_MAX_SIZE=20m
LOG_MAX_FILES=14
LOG_ENABLE_CONSOLE=true

# SERVICE CONFIGURATION
SERVICE_NAME=Report Service
SERVICE_VERSION=1.0.0
