"use client";

import { useState } from "react";
import PageHeader from "./PageHeader";
import StatsGrid from "./StatsGrid";
import DataTable from "./DataTable";
import PageLoader from "./PageLoader";
import NoData from "./NoData";

export default function PageWrapper({
  // Page configuration
  title,
  subtitle,

  // Header configuration
  searchPlaceholder = "Search...",
  addButtonText,
  addButtonPath,
  showAddButton = true,
  showSearch = true,
  showFilters = true,

  // Stats configuration
  stats = [],
  showStats = true,

  // Table configuration
  data = [],
  columns = [],
  itemsPerPage = 10,
  onRowClick,
  showPagination = true,
  sortable = true,
  hoverable = true,

  // Filter configuration
  filters = [],

  // Loading and error states
  loading = false,
  loadingMessage = "Loading...",
  error = null,

  // Custom content
  children,
  customContent,

  // Layout options
  className = "",
}) {
  const [searchTerm, setSearchTerm] = useState("");
  const [filterValues, setFilterValues] = useState(
    filters.reduce((acc, filter) => {
      acc[filter.key] =
        filter.defaultValue || (filter.options && filter.options[0]) || "";
      return acc;
    }, {})
  );

  // Prepare filters for PageHeader
  const headerFilters = filters.map((filter) => ({
    value: filterValues[filter.key],
    onChange: (value) =>
      setFilterValues((prev) => ({ ...prev, [filter.key]: value })),
    options: filter.options || [],
  }));

  // Show loader
  if (loading) {
    return (
      <div className={`space-y-6 ${className}`}>
        {/* Keep the header visible during loading */}
        <PageHeader
          title={title}
          subtitle={subtitle}
          searchTerm={searchTerm}
          setSearchTerm={setSearchTerm}
          searchPlaceholder={searchPlaceholder}
          filters={headerFilters}
          addButtonText={addButtonText}
          addButtonPath={addButtonPath}
          showAddButton={showAddButton}
          showSearch={showSearch}
          showFilters={showFilters}
        />
        <PageLoader message={loadingMessage} />
      </div>
    );
  }

  // Show error
  if (error) {
    return (
      <div className={`space-y-6 ${className}`}>
        <PageHeader
          title={title}
          subtitle={subtitle}
          searchTerm={searchTerm}
          setSearchTerm={setSearchTerm}
          searchPlaceholder={searchPlaceholder}
          filters={headerFilters}
          addButtonText={addButtonText}
          addButtonPath={addButtonPath}
          showAddButton={showAddButton}
          showSearch={showSearch}
          showFilters={showFilters}
        />
        <div className="bg-red-50 border border-red-200 rounded-xl p-6">
          <div className="flex items-center justify-center">
            <svg
              className="w-6 h-6 text-red-600 mr-3"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
            <p className="text-red-700 font-medium">
              {typeof error === "string"
                ? error
                : "An error occurred while loading the data."}
            </p>
          </div>
        </div>
      </div>
    );
  }

  // Hide search and filters when there's no data
  const hasData = data.length > 0;

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Page Header with Search and Filters */}
      <PageHeader
        title={title}
        subtitle={subtitle}
        searchTerm={searchTerm}
        setSearchTerm={setSearchTerm}
        searchPlaceholder={searchPlaceholder}
        filters={headerFilters}
        addButtonText={addButtonText}
        addButtonPath={addButtonPath}
        showAddButton={showAddButton}
        showSearch={showSearch && hasData}
        showFilters={showFilters && hasData}
      />

      {/* Stats Grid */}
      {/* {showStats && stats.length > 0 && (
        <StatsGrid stats={stats} />
      )} */}

      {/* Custom Content */}
      {customContent && <div className="mb-6">{customContent}</div>}

      {/* Data Table or No Data */}
      {data.length > 0 && columns.length > 0 ? (
        <DataTable
          data={data}
          columns={columns}
          searchTerm={searchTerm}
          filters={filterValues}
          itemsPerPage={itemsPerPage}
          onRowClick={onRowClick}
          showPagination={showPagination}
          sortable={sortable}
          hoverable={hoverable}
        />
      ) : columns.length > 0 ? (
        <NoData
          hasFilters={
            searchTerm !== "" ||
            Object.values(filterValues).some(
              (value) => value && value !== "All" && !value.includes("All")
            )
          }
        />
      ) : null}

      {/* Children for additional custom content */}
      {children}
    </div>
  );
}
