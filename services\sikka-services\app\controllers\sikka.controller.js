import { createLogger } from "../utils/logger.util.js";
import {
  STATUS_CODE_BAD_REQUEST,
  STATUS_CODE_INTERNAL_SERVER_ERROR,
  STATUS_CODE_SUCCESS,
} from "../utils/status_code.util.js";
import {
  LOGGER_NAMES,
  LOG_ACTIONS,
  CONTROLLER_MESSAGES,
  createKpiControllersConfig,
  BATCH_PROCESSING,
  LOG_MESSAGES,
  METHOD_TYPES,
} from "../utils/constants.util.js";
import * as sikkaService from "../services/sikka.service.js";
import { errorResponse, successResponse } from "../utils/response.util.js";
import * as kpisController from "./kpis.controller.js";
import { processAllKpiControllers } from "../utils/kpis.util.js";
import { apiRequest } from "../../../../shared/utils/axios.util.js";
import { sendJsonFile } from "../../../../shared/utils/file.util.js";
import FormData from "form-data";
import fs from "fs";
import path from "path";
import axios from "axios";

const logger = createLogger(LOGGER_NAMES.SIKKA_CONTROLLER);
const report_url = process.env.REPORT_SERVICE_URL;
const file_service_url = process.env.FILE_SERVICE_URL;

/**
 * Check connection to Sikka Account
 * @route POST /api/sikka/check-connection
 * @access Public
 */
export const checkConnection = async (req, res) => {
  try {
    logger.info(CONTROLLER_MESSAGES.CHECKING_CONNECTION);
    const { office_id } = req.body;

    const response = await sikkaService.checkOfficeIdAssociationService(
      office_id
    );
    if (response.is_associated) {
      return res
        .status(STATUS_CODE_BAD_REQUEST)
        .json(
          errorResponse(`Office ID is not associated with ${response.org_name}`)
        );
    }

    // Get authorized practices
    await sikkaService.getAuthorizedPractices(office_id);

    logger.info(CONTROLLER_MESSAGES.CONNECTION_VERIFIED);
    return res
      .status(STATUS_CODE_SUCCESS)
      .json(successResponse(CONTROLLER_MESSAGES.CONNECTION_VERIFIED));
  } catch (error) {
    logger.error(CONTROLLER_MESSAGES.CONNECTION_FAILED, {
      error: error.message,
    });
    return res
      .status(STATUS_CODE_INTERNAL_SERVER_ERROR)
      .json(errorResponse(error.message));
  }
};

/**
 * Generate Request key from Sikka
 * @route POST /api/sikka/request-key
 * @access Public
 */
export const requestKey = async (req, res) => {
  try {
    logger.info(LOG_ACTIONS.REQUESTING_KEY);
    const { office_id, schema_name } = req.body;

    const request_key_record = await sikkaService.generateRequestKey(
      office_id,
      schema_name
    );

    logger.info(LOG_ACTIONS.REQUEST_KEY_SUCCESS);
    return res
      .status(STATUS_CODE_SUCCESS)
      .json(
        successResponse(
          CONTROLLER_MESSAGES.REQUEST_KEY_GENERATED,
          request_key_record
        )
      );
  } catch (error) {
    logger.error(LOG_ACTIONS.REQUEST_KEY_FAILED, { error: error.message });
    return res
      .status(STATUS_CODE_INTERNAL_SERVER_ERROR)
      .json(errorResponse(error.message));
  }
};

/**
 * Get information about all Sequelize models
 * @route GET /api/sikka/tables
 * @access Public
 */
export const getTables = async (req, res) => {
  try {
    logger.info(LOG_ACTIONS.GETTING_MODELS_INFO);

    const models_info = await sikkaService.fetchAllModelsInfo();

    logger.info(
      `${LOG_MESSAGES.SUCCESSFULLY_RETRIEVED_INFORMATION} ${models_info.totalModels} ${LOG_MESSAGES.MODELS}`
    );

    return res.status(STATUS_CODE_SUCCESS).json(
      successResponse(CONTROLLER_MESSAGES.MODELS_INFO_FETCHED, {
        modelsInfo: models_info,
      })
    );
  } catch (error) {
    logger.error(LOG_ACTIONS.ERROR_GETTING_MODELS_INFO, {
      error: error.message,
    });
    return res
      .status(STATUS_CODE_INTERNAL_SERVER_ERROR)
      .json(errorResponse(error.message));
  }
};

/**
 * Run all KPI endpoints and report their status
 * @route POST /api/sikka/all-kpi-reports
 * @access Public
 */
export const syncAllKpiReports = async (req, res) => {
  try {
    const { office_id, schema_name, end_date, organization_id } = req.body;

    // Ensure request key is available and valid
    await sikkaService.ensureValidRequestKey(office_id, schema_name);

    // List of KPIs with their corresponding controller functions
    const kpi_controllers = createKpiControllersConfig(kpisController);

    // Process all KPI controllers
    const results = await processAllKpiControllers(
      kpi_controllers,
      req,
      logger
    );

    // Process results and send notifications
    const { success_count, fail_count, failed_apis } =
      await sikkaService.processKpiBatchSyncResults(results);

    if (success_count === 0) {
      return res
        .status(STATUS_CODE_SUCCESS)
        .json(
          errorResponse(
            CONTROLLER_MESSAGES.BATCH_KPI_RUN_FAILED,
            failed_apis[0].error
          )
        );
    }

    // If at least one KPI succeeded, update organization sync status
    let reportServiceResponse = null;
    if (success_count > 0 && end_date) {
      await sikkaService.syncOrganizationStatus(end_date, organization_id);

      // Trigger operations KPI calculation in report-service
      try {
        const endDateObj = new Date(end_date);
        const month = endDateObj.getUTCMonth() + 1;
        const year = endDateObj.getUTCFullYear();
        console.log(report_url);

        if (report_url && organization_id && month && year) {
          const response = await apiRequest(
            METHOD_TYPES.GET,
            `${report_url}/report/calculate/operations`,
            {
              organization_id,
              month,
              year,
            }
          );
          console.log(
            "[Sikka Controller] Response from report-service",
            response
          );
          reportServiceResponse = response.data || response;

          // Create JSON file from the response data
          const jsonFilePath = sendJsonFile(
            reportServiceResponse,
            `operational-${month}-${year}.json`
          );

          logger.info(
            "[Sikka Controller] Created JSON file for operations KPIs",
            { filePath: jsonFilePath }
          );

          // Upload JSON file to file-service
          try {
            const formData = new FormData();
            const fileStream = fs.createReadStream(jsonFilePath);
            const fileName = path.basename(jsonFilePath); // Get filename from path

            formData.append("json", fileStream, fileName);
            formData.append("orgId", organization_id);

            const uploadResponse = await axios.post(
              `${file_service_url}/onboarding/json`,
              formData,
              {
                headers: {
                  ...formData.getHeaders(),
                },
              }
            );

            logger.info(
              "[Sikka Controller] Successfully uploaded JSON file to file-service",
              {
                filePath: jsonFilePath,
                uploadResponse: uploadResponse.data,
              }
            );
          } catch (uploadError) {
            console.log(
              "[Sikka Controller] Error uploading JSON file to file-service",
              uploadError
            );
            logger.error(
              "[Sikka Controller] Error uploading JSON file to file-service",
              {
                error: uploadError.message,
                filePath: jsonFilePath,
              }
            );
            // Don't throw - continue execution even if upload fails
          }
        } else {
          logger.warn(
            "Skipping report-service /calculate/operations call due to missing configuration or data",
            { report_url, organization_id, end_date }
          );
        }
      } catch (reportError) {
        console.log(
          "[Sikka Controller] Error calling report-service /calculate/operations",
          reportError
        );
        logger.error("Error calling report-service /calculate/operations", {
          error: reportError.message,
        });
      }
    }

    // Trigger PDF generation for operations reports after successful sync
    if (success_count > 0 && end_date && organization_id) {
      try {
        const endDateObj = new Date(end_date);
        const month = endDateObj.getUTCMonth() + 1;
        const year = endDateObj.getUTCFullYear();

        // Get organization name if available
        let organization_name = null;
        try {
          const orgResponse = await axios.get(
            `${process.env.USER_SERVICE_URL}/api/organization/${organization_id}`
          );
          organization_name = orgResponse.data?.data?.organization_name || null;
        } catch (orgError) {
          logger.warn("Could not fetch organization name for PDF generation", {
            error: orgError.message,
          });
        }

        const pdfParams = {
          organization_id,
          organization_name,
          month,
          year,
          store_to_blob: "true",
        };

        // Generate Operations PDF (Sikka = operations only)
        try {
          logger.info(
            `Triggering operations PDF generation for organization: ${organization_id}, month: ${month}, year: ${year}`
          );

          await axios.get(`${report_url}/report/operations/pdf`, {
            params: pdfParams,
            timeout: 60000, // 60 second timeout
          });

          logger.info(
            `✅ Operations PDF generated and stored successfully for organization: ${organization_id}`
          );
        } catch (operationsPdfError) {
          logger.error(
            `Failed to generate operations PDF for organization ${organization_id}:`,
            {
              error: operationsPdfError.message,
              stack: operationsPdfError.stack,
            }
          );
          // Don't throw - PDF generation failure should not block sync response
        }
      } catch (pdfGenerationError) {
        logger.error(
          `Error in PDF generation process for organization ${organization_id}:`,
          {
            error: pdfGenerationError.message,
            stack: pdfGenerationError.stack,
          }
        );
        // Don't throw - PDF generation failure should not block sync response
      }
    }



    return res.status(STATUS_CODE_SUCCESS).json(
      successResponse(CONTROLLER_MESSAGES.BATCH_KPI_RUN_COMPLETE, {
        [BATCH_PROCESSING.FIELD_TOTAL]: results.length,
        [BATCH_PROCESSING.FIELD_SUCCESS]: success_count,
        [BATCH_PROCESSING.FIELD_FAIL]: fail_count,
        [BATCH_PROCESSING.FIELD_FAILED]: failed_apis,
        [BATCH_PROCESSING.FIELD_SUMMARY]: results.map((result) => ({
          [BATCH_PROCESSING.FIELD_NAME]: result.name,
          [BATCH_PROCESSING.FIELD_STATUS]: result.status,
        })),
        operationsKpis: reportServiceResponse,
      })
    );
  } catch (error) {
    console.log("[Sikka Controller] Error in syncAllKpiReports", error);
    logger.error("Error in syncAllKpiReports", { error: error.message });
    return res
      .status(STATUS_CODE_INTERNAL_SERVER_ERROR)
      .json(errorResponse(error.message));
  }
};
