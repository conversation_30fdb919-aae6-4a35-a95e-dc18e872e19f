// app/models/audit.model.js
import { DataTypes } from "sequelize";
import { sequelize } from "../utils/db.util.js";

const AuditLog = sequelize.define(
  "AuditLog",
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    serviceName: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    organizationId: DataTypes.STRING,
    module: DataTypes.STRING,
    action: {
      type: DataTypes.STRING,
      defaultValue: "SYNC",
    },
    syncStartTime: DataTypes.DATE,
    syncEndTime: DataTypes.DATE,
    executionStatus: {
      type: DataTypes.STRING,
      defaultValue: "Pending",
    },
    recordsProcessed: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
    },
    errorDetails: {
      type: DataTypes.JSONB,
      allowNull: true,
    },
  },
  {
    tableName: "audit_logs",
    timestamps: true,
  }
);

export default AuditLog;
