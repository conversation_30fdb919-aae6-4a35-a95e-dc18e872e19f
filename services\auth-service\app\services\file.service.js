import FormData from "form-data";
import { createUploadValue } from "../utils/file.util.js";
import { ERROR_MESSAGES } from "../utils/constants.util.js";
import { apiRequest } from "../../../../shared/utils/axios.util.js";
import { API_METHODS } from "../utils/constants/server.constants.js";

const FILE_SERVICE_URL = process.env.FILE_SERVICE_URL;
const FILE_SERVICE_API_KEY = process.env.FILE_SERVICE_API_KEY;

export const uploadOrganizationLogo = async (orgId, logo) => {
  if (!orgId || !logo || !FILE_SERVICE_URL) {
    return null;
  }

  const uploadValue = createUploadValue(logo);
  if (!uploadValue) {
    return {
      success: false,
      message: "Unsupported logo format",
    };
  }

  const formData = new FormData();
  formData.append("orgId", String(orgId));
  formData.append("logo", uploadValue.data, uploadValue.options);

  const headers = {
    ...formData.getHeaders(),
  };
  if (FILE_SERVICE_API_KEY) {
    headers["x-api-key"] = FILE_SERVICE_API_KEY;
  }

  try {
    const responseData = await apiRequest(
      API_METHODS.POST,
      `${FILE_SERVICE_URL}/onboarding/logo`,
      formData,
      headers,
      {}
    );

    return {
      success: true,
      data: responseData,
    };
  } catch (error) {
    const errorMessage = error.response?.data || error.message;
    throw new Error(
      typeof errorMessage === "string"
        ? errorMessage
        : ERROR_MESSAGES.FAILED_TO_UPLOAD_ORGANIZATION_LOGO
    );
  }
};
