import PayrollKpiService from "../services/payroll_kpi.service.js";
import { LOGGER_NAMES } from "../utils/constants/log.constants.js";
import { createLogger } from "../utils/logger.utils.js";
import { validateRequiredParams, handleControllerError, sendSuccessResponse } from "../utils/controller.utils.js";

const logger = createLogger(LOGGER_NAMES.REPORT_CONTROLLER);

/**
 * Get payroll KPI data for organization by month and year
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getPayrollKpis = async (req, res) => {
  try {
    const { organization_id, month, year } = req.query;

    logger.info(
      `Fetching payroll KPI data for org: ${organization_id}, month: ${month}, year: ${year}`
    );

    // Validate required parameters
    const validationError = validateRequiredParams(req.query, ['organization_id', 'month', 'year']);
    if (validationError) {
      return res.status(400).json(validationError);
    }

    // Fetch payroll KPI data
    const payrollKpiData = await PayrollKpiService.getPayrollKpiData({
      organization_id,
      month,
      year,
    });

    // Return successful response
    sendSuccessResponse(res, "Payroll KPIs fetched successfully", payrollKpiData);
  } catch (error) {
    logger.error("Error fetching payroll KPIs:", error);
    handleControllerError(error, res, "Error fetching payroll KPI data");
  }
};

export default {
  getPayrollKpis,
};
