import express from "express";
import auditRoutes from "./audit.route.js";

const router = express.Router();

// API version configuration

// Route mappings for better organization

const ROUTE_MAPPINGS = {
  adp: { path: "/audit", router: auditRoutes },
};

// Apply route mappings

Object.values(ROUTE_MAPPINGS).forEach(({ path, router: routeRouter }) => {
  router.use(path, routeRouter);
});

export default router;
