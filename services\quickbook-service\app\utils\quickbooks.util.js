import { sendEmail } from "../../../../shared/utils/email.util.js";
import { QUICKBOOKS_SERVICE_LOGS } from "./constants/log.constants.js";
import { createLogger } from "./logger.utils.js";
import { getSyncFailureEmailContent } from "./methods.util.js";

const logger = createLogger("QUICKBOOKS_EMAIL_UTIL");

/**
 * Send failure email notification for QuickBooks reports sync
 * @param {Object} params - Email parameters
 * @param {string[]} params.failedReportNames - Array of failed report display names
 * @param {string} [params.recipientEmail] - Recipient email address
 * @param {Error} [params.error] - Error associated with the failure
 * @returns {Promise<void>}
 */
export async function sendQuickBooksReportsSyncFailureEmail({
  error,
  organizationId,
}) {
  try {
    const {
      ADMIN_EMAIL: toEmail,
      SENDGRID_FROM_EMAIL: fromEmail,
      SYNC_FAILURE_TEMPLATE_ID: templateId,
    } = process.env;

    const content = getSyncFailureEmailContent(error, organizationId);

    await sendEmail({
      to: toEmail,
      from: fromEmail,
      templateId: templateId,
      data: content,
    });

    logger.info(QUICKBOOKS_SERVICE_LOGS.FAILURE_EMAIL_NOTIFICATION_SENT, {
      toEmail,
      organizationId,
    });
  } catch (emailError) {
    logger.warn(
      QUICKBOOKS_SERVICE_LOGS.FAILED_TO_SEND_FAILURE_EMAIL_NOTIFICATION,
      {
        error: emailError.message,
      }
    );
  }
}
