/* Dashboard Summary Popup Styles */

/* Ultra-Compact Table Styling */
.compact-tables table {
  font-size: 7px;
  border-collapse: collapse;
  width: 100%;
  margin: 0.15rem 0;
  border-radius: 3px;
  overflow: hidden;
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.03);
}

.compact-tables th {
  background: linear-gradient(135deg, #5a4bff 0%, #7c3aed 100%);
  color: white;
  font-weight: 600;
  padding: 1px 2px;
  text-align: left;
  border: 0.5px solid #e5e7eb;
  font-size: 7px;
  line-height: 1;
  height: 16px;
}

.compact-tables td {
  padding: 1px 2px;
  border: 0.5px solid #e5e7eb;
  background: white;
  font-size: 7px;
  line-height: 1;
  height: 14px;
}

.compact-tables tr:nth-child(even) td {
  background: #fafafc;
}

.compact-tables tr:hover td {
  background: #f3f4f6;
}

/* Heading Styles - Synchronized with Table */
.compact-tables h1,
.compact-tables h2,
.compact-tables h3 {
  font-size: 7px;
  font-weight: 600;
  color: #4338ca;
  margin-top: 0.15rem;
  margin-bottom: 0.05rem;
  line-height: 1;
  letter-spacing: 0.3px;
  padding: 0;
}

/* Reduce space between headings and tables */
.dashboard-summary-content h2 + div[class*="overflow-x-auto"],
.dashboard-summary-content h2 + table,
.dashboard-summary-content h3 + div[class*="overflow-x-auto"],
.dashboard-summary-content h3 + table {
  margin-top: 0.5rem !important;
}

.dashboard-summary-content h2 {
  margin-bottom: 0.5rem !important;
}

.dashboard-summary-content h3 {
  margin-bottom: 0.5rem !important;
}

.compact-tables h1:first-child,
.compact-tables h2:first-child,
.compact-tables h3:first-child {
  margin-top: 0;
}

/* Paragraph Styles - Synchronized with Table */
.compact-tables p {
  margin: 0.1rem 0;
  line-height: 1;
  color: #374151;
  font-size: 7px;
  letter-spacing: 0.2px;
  padding: 0;
}

/* List Styles - Synchronized with Table */
.compact-tables ul,
.compact-tables ol {
  margin: 0.1rem 0;
  padding-left: 0.8rem;
}

.compact-tables li {
  margin: 0.05rem 0;
  color: #374151;
  font-size: 7px;
  line-height: 1;
  letter-spacing: 0.2px;
}

/* Strong/Bold Text */
.compact-tables strong {
  font-weight: 600;
  color: #1f2937;
}

/* Alignment Synchronization */
.compact-tables {
  display: flex;
  flex-direction: column;
  gap: 0.05rem;
}

.compact-tables table,
.compact-tables h1,
.compact-tables h2,
.compact-tables h3,
.compact-tables p,
.compact-tables ul,
.compact-tables ol {
  margin-left: 0;
  margin-right: 0;
  width: 100%;
}

/* Custom Scrollbar Styling */
.dashboard-summary-scroll::-webkit-scrollbar {
  width: 5px;
}

.dashboard-summary-scroll::-webkit-scrollbar-track {
  background: #f3f4f6;
  border-radius: 2px;
}

.dashboard-summary-scroll::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #5a4bff 0%, #7c3aed 100%);
  border-radius: 2px;
}

.dashboard-summary-scroll::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #4338ca 0%, #6d28d9 100%);
}

/* Modal Container */
.dashboard-summary-modal {
  background: white;
  border-radius: 0.5rem;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(229, 231, 235, 0.5);
}

/* Header Styles */
.dashboard-summary-header {
  background: linear-gradient(
    to right,
    rgba(229, 231, 235, 0.2),
    rgba(168, 85, 247, 0.1),
    rgba(236, 72, 153, 0.1)
  );
  border-bottom: 1px solid rgba(229, 231, 235, 0.7);
  padding: 0.625rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.dashboard-summary-header-icon {
  width: 2rem;
  height: 2rem;
  background: linear-gradient(to right, #6366f1, #a855f7);
  border-radius: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.dashboard-summary-header-icon svg {
  width: 1rem;
  height: 1rem;
  color: white;
}

.dashboard-summary-header-title {
  font-size: 0.875rem;
  font-weight: 700;
  background: linear-gradient(to right, #4f46e5, #7c3aed);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.dashboard-summary-header-subtitle {
  font-size: 0.5625rem;
  color: #6b7280;
  margin-top: 0.125rem;
  font-weight: 500;
}

/* Label Section */
.dashboard-summary-label {
  background: white;
  border-bottom: 1px solid #f3f4f6;
  padding: 0.375rem 1rem;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.dashboard-summary-label-text {
  font-size: 0.75rem;
  font-weight: 600;
  color: #4f46e5;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.dashboard-summary-label-badge {
  font-size: 0.5rem;
  font-weight: 400;
  color: #4f46e5;
  background: #eef2ff;
  padding: 0.125rem 0.375rem;
  border-radius: 9999px;
}

/* Content Area */
.dashboard-summary-content {
  flex: 1;
  overflow-y: auto;
  background: white;
  padding: 0.75rem 1rem;
}

/* Footer Styles */
.dashboard-summary-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.5rem 1rem;
  border-top: 1px solid rgba(229, 231, 235, 0.25);
  background: linear-gradient(
    to right,
    rgba(249, 250, 251, 0.6),
    rgba(238, 242, 255, 0.4)
  );
}

.dashboard-summary-footer-tip {
  font-size: 0.5625rem;
  color: #6b7280;
}

.dashboard-summary-footer-button {
  border: 1px solid #c7d2fe;
  color: #4f46e5;
  padding: 0.25rem 0.75rem;
  font-size: 0.75rem;
  border-radius: 0.5rem;
  font-weight: 500;
  transition: all 0.2s;
  cursor: pointer;
  background: white;
}

.dashboard-summary-footer-button:hover {
  background: #eef2ff;
  border-color: #a5b4fc;
  transform: scale(1.05);
}

/* Close Button */
.dashboard-summary-close-btn {
  padding: 0.25rem;
  background: transparent;
  border: none;
  cursor: pointer;
  border-radius: 0.5rem;
  transition: all 0.3s;
}

.dashboard-summary-close-btn:hover {
  background: rgba(255, 255, 255, 0.6);
}

.dashboard-summary-close-btn svg {
  width: 1rem;
  height: 1rem;
  color: #4b5563;
}

/* Responsive Design */
@media (max-width: 768px) {
  .compact-tables table {
    font-size: 6px;
  }

  .compact-tables th,
  .compact-tables td {
    padding: 0.5px 1px;
    font-size: 6px;
  }

  .dashboard-summary-header {
    padding: 0.5rem;
  }

  .dashboard-summary-content {
    padding: 0.5rem;
  }
}

@media (max-width: 480px) {
  .compact-tables table {
    font-size: 5px;
  }

  .compact-tables th,
  .compact-tables td {
    padding: 0.5px 1px;
    font-size: 5px;
  }

  .dashboard-summary-header-title {
    font-size: 0.75rem;
  }
}
