import { configureStore } from "@reduxjs/toolkit";
import authReducer from "@/redux/Slice/Authentication";
import organizationReducer from "@/redux/Slice/organization";
import quickbookAccountReducer from "@/redux/Slice/QuickbookAccount";
import sageAccountReducer from "@/redux/Slice/SageAccount";
import netsuiteAccountReducer from "@/redux/Slice/NetsuiteAccount";
import fileOperationsReducer from "@/redux/Slice/fileOperations";
import clientOnlyReducer from "@/redux/Slice/clientOnly";
import bookkeepingReducer from "@/redux/Slice/Bookkeeping";
import userReducer from "@/redux/Slice/userSlice";
import chatReducer from "@/redux/Slice/chat";
import reportFoldersReducer from "@/redux/Slice/reportFolders";
// import powerBiWorkflowReducer from "@/redux/Slice/powerBiWorkflow";
import folderAvailabilityReducer from "@/redux/Slice/folderAvailability";
import reportReducer from "@/redux/Slice/reports";
import operationsReducer from "@/redux/Slice/operationsSlice";

export const store = configureStore({
  reducer: {
    auth: authReducer,
    organizations: organizationReducer,
    quickbooksAccount: quickbookAccountReducer,
    sageAccount: sageAccountReducer,
    netsuiteAccount: netsuiteAccountReducer,
    bookkeeping: bookkeepingReducer,
    chat: chatReducer,
    fileOperations: fileOperationsReducer,
    clientOnly: clientOnlyReducer,
    users: userReducer,
    reportFolders: reportFoldersReducer,
    // powerBiWorkflow: powerBiWorkflowReducer,
    folderAvailability: folderAvailabilityReducer,
    reports: reportReducer,
    operations: operationsReducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ["persist/PERSIST"],
        ignoredPaths: ["fileOperations.uploadedFiles"],
      },
    }),
});

export default store;
