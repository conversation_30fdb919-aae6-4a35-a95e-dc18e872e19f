// app/middleware/bodySanitizer.middleware.js
/**
 * Body Sanitization Middleware
 * Sanitizes request body to ensure all string values are properly formatted
 * and safe for JSON processing, especially handling apostrophes and special characters
 */
import logger from "../../config/logger.config.js";

/**
 * Recursively sanitize string values in an object or array
 * @param {any} value - Value to sanitize (can be object, array, string, or primitive)
 * @returns {any} Sanitized value
 */
const sanitizeValue = (value) => {
  // Handle null or undefined
  if (value === null || value === undefined) {
    return value;
  }

  // Handle strings - trim and ensure proper encoding
  if (typeof value === "string") {
    // Trim whitespace
    let sanitized = value.trim();
    
    // Remove any leading/trailing quotes that might have been incorrectly added
    if (
      (sanitized.startsWith('"') && sanitized.endsWith('"')) ||
      (sanitized.startsWith("'") && sanitized.endsWith("'"))
    ) {
      sanitized = sanitized.slice(1, -1);
    }
    
    // Return the sanitized string (apostrophes and special chars are fine in JSON strings)
    return sanitized;
  }

  // Handle arrays - recursively sanitize each element
  if (Array.isArray(value)) {
    return value.map((item) => sanitizeValue(item));
  }

  // Handle objects - recursively sanitize each property
  if (typeof value === "object") {
    const sanitized = {};
    for (const [key, val] of Object.entries(value)) {
      // Sanitize the key as well
      const sanitizedKey = typeof key === "string" ? key.trim() : key;
      sanitized[sanitizedKey] = sanitizeValue(val);
    }
    return sanitized;
  }

  // Return primitives as-is (numbers, booleans, etc.)
  return value;
};

/**
 * Middleware to sanitize request body
 * Ensures all string values are properly formatted and safe
 */
export const bodySanitizer = (req, res, next) => {
  try {
    // Only sanitize if body exists
    if (req.body && typeof req.body === "object") {
      // Sanitize the entire body recursively
      req.body = sanitizeValue(req.body);
      
      logger.debug("Request body sanitized successfully", {
        bodyKeys: Object.keys(req.body),
      });
    }
    
    next();
  } catch (error) {
    logger.error(`Error sanitizing request body: ${error.message}`, { error });
    // Continue with original body if sanitization fails
    next();
  }
};

