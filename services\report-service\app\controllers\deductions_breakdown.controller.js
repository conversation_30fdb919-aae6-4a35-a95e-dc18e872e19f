import DeductionsBreakdownService from "../services/deductions_breakdown.service.js";
import { LOGGER_NAMES } from "../utils/constants/log.constants.js";
import { createLogger } from "../utils/logger.utils.js";
import { validateRequiredParams, handleControllerError, sendSuccessResponse } from "../utils/controller.utils.js";

const logger = createLogger(LOGGER_NAMES.REPORT_CONTROLLER);

/**
 * Get deductions breakdown data for organization by month and year
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getDeductionsBreakdown = async (req, res) => {
  try {
    const { organization_id, month, year } = req.query;

    logger.info(
      `Fetching deductions breakdown for org: ${organization_id}, month: ${month}, year: ${year}`
    );

    // Validate required parameters
    const validationError = validateRequiredParams(req.query, ['organization_id', 'month', 'year']);
    if (validationError) {
      return res.status(400).json(validationError);
    }

    // Fetch deductions breakdown data
    const deductionsBreakdownData = await DeductionsBreakdownService.getDeductionsBreakdownData({
      organization_id,
      month,
      year,
    });

    // Return successful response
    sendSuccessResponse(res, "Deductions breakdown fetched successfully", deductionsBreakdownData);
  } catch (error) {
    logger.error("Error fetching deductions breakdown:", error);
    handleControllerError(error, res, "Error fetching deductions breakdown data");
  }
};

export default {
  getDeductionsBreakdown,
};
