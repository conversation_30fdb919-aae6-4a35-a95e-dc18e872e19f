/**
 * Convert JSON summaryData to HTML format
 * @param {Object} summaryData - JSON object with title and sections
 * @returns {string} HTML string
 */
export const formatSummaryDataToHTML = (summaryData) => {
  if (!summaryData || typeof summaryData !== "object") return "";
  const { title, sections } = summaryData;
  if (!sections || !Array.isArray(sections)) return "";

  let html = title ? `<h2 class="summary-title">${title}</h2>` : "";

  sections.forEach((section) => {
    if (!section.heading || !section.type) return;
    const { heading, type, data } = section;

    if (type === "table" && Array.isArray(data?.headers) && Array.isArray(data?.rows)) {
      const headers = data.headers;
      const rows = data.rows.map((row) =>
        typeof row === "object" && !Array.isArray(row)
          ? headers.map((header) => row[header] ?? null)
          : row
      );

      const validColumns = headers
        .map((_, colIndex) => (rows.some((row) => row[colIndex] != null && row[colIndex] !== "") ? colIndex : null))
        .filter((idx) => idx !== null);

      if (validColumns.length === 0) return;

      html += `<h3 class="summary-section-heading">${heading}</h3>`;
      html += `<div class="summary-table-wrapper"><table class="summary-table"><thead><tr>`;
      validColumns.forEach((colIndex) => {
        html += `<th class="summary-table-header">${headers[colIndex]}</th>`;
      });
      html += `</tr></thead><tbody>`;
      rows.forEach((row) => {
        if (!Array.isArray(row)) return;
        html += `<tr class="summary-table-row">`;
        validColumns.forEach((colIndex, cellIndex) => {
          const cell = row[colIndex];
          const isNumeric = cellIndex > 0 && typeof cell === "string" && /^[\$-\d]/.test(cell?.trim() || "");
          const alignClass = isNumeric ? "summary-table-cell-right" : "summary-table-cell-left";
          const boldClass = cellIndex === 0 ? "summary-table-cell-bold" : "";
          html += `<td class="summary-table-cell ${alignClass} ${boldClass}">${cell || ""}</td>`;
        });
        html += `</tr>`;
      });
      html += `</tbody></table></div>`;
    } else if (type === "list" && Array.isArray(data)) {
      const nonEmptyItems = data.filter((item) => item && typeof item === "string" && item.trim() !== "");
      if (nonEmptyItems.length === 0) return;
      html += `<h3 class="summary-section-heading">${heading}</h3><ul class="summary-list">`;
      nonEmptyItems.forEach((item) => {
        html += `<li class="summary-list-item">${item}</li>`;
      });
      html += `</ul>`;
    } else if (type === "paragraph" && typeof data === "string" && data.trim() !== "") {
      html += `<h3 class="summary-section-heading">${heading}</h3><p class="summary-paragraph">${data}</p>`;
    }
  });

  return html;
};

