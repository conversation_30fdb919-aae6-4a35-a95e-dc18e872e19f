"use client";

/**
 * InfoCard is a generic card shell for charts/sections.
 * Props:
 * - title: string
 * - subtitle?: string
 * - children: ReactNode
 */
export default function InfoCard({ title, subtitle, children }) {
  return (
    <div className="rounded-xl bg-white shadow-sm ring-1 ring-slate-100 p-5 lg:p-6 h-full">
      <div className="flex flex-col gap-1 mb-4 text-center items-center">
        <h3 className="text-xl font-semibold text-slate-900">{title}</h3>
      </div>
      {children}
    </div>
  );
}
