import { validationResult } from "express-validator";
import { errorResponse } from "../utils/response.util.js";
import { STATUS_CODE_BAD_REQUEST } from "../utils/status_code.utils.js";

export const validateRequest = (validators) => async (req, res, next) => {
  await Promise.all(validators.map((validator) => validator.run(req)));

  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res
      .status(STATUS_CODE_BAD_REQUEST)
      .json(
        errorResponse(
          "Validation failed",
          errors
            .array()
            .map((err) => err.msg)
            .join(", ")
        )
      );
  }

  next();
};

