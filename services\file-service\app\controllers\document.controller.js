// app/controllers/document.controller.js
import logger from "../../config/logger.config.js";
import {
  DOCUMENT_MESSAGES,
  DOCUMENT_LOG_MESSAGES,
} from "../utils/constants/document.constants.js";
import {
  storeDocumentService,
  getDocumentsService,
  getDocumentByIdService,
  updateDocumentByIdService,
  storePdfToBlob,
} from "../services/document.service.js";
import blobStorageService from "../services/blobStorage.service.js";
import {
  handleServiceResponse,
  handleControllerError,
  validateRequiredFields,
} from "../utils/controllerHandler.util.js";
import { errorResponse, successResponse } from "../utils/response.util.js";
import {
  STATUS_CODE_BAD_REQUEST,
  STATUS_CODE_OK,
} from "../utils/status_code.utils.js";

/**
 * Extract organization name from blob storage path
 * Path format: {orgId}/{orgName}/Power BI-Reports/{service}/{year}/{month}/
 */
const extractOrgNameFromPath = (blobPath) => {
  if (!blobPath) return null;
  const segments = blobPath.split("/").filter(Boolean);
  // Path structure: [orgId, orgName, Power BI-Reports, service, year, month]
  if (segments.length >= 2 && segments[1] !== "Power BI-Reports") {
    return segments[1];
  }
  return null;
};

/**
 * Normalize payload - support both camelCase and snake_case
 */
const normalizePayload = (body) => {
  // Support both formats: camelCase (new) and snake_case (old)
  const orgId = body.orgId || body.organization_id;
  const orgName =
    body.orgName || body.organizationName || body.organization_name;
  const blobPath = body.blobStoragePath || body.blob_storage_path;
  const fileName = body.fileName || body.file_name;
  const fileSize = body.fileSize || body.file_size;
  const mimeType = body.mimeType || body.mime_type;

  // Extract orgName from blob path if not provided
  const resolvedOrgName = orgName || extractOrgNameFromPath(blobPath);

  return {
    organization_id: orgId,
    organization_name: resolvedOrgName,
    blob_storage_path: blobPath,
    service: body.service,
    month: body.month,
    year: body.year,
    file_name: fileName,
    file_size: fileSize,
    mime_type: mimeType,
    metadata: body.metadata,
  };
};

export const storeDocument = async (req, res) => {
  const requestId = `DOC-${Date.now()}-${Math.random()
    .toString(36)
    .substr(2, 9)}`;
  logger.info(
    `[${requestId}] [DOCUMENT_API] ===== START: POST /api/document =====`
  );
  logger.info(`[${requestId}] [DOCUMENT_API] Request payload:`, {
    body: req.body,
    headers: {
      "content-type": req.headers["content-type"],
      "x-api-key": req.headers["x-api-key"] ? "***" : undefined,
    },
  });

  try {
    // Normalize payload (supports both camelCase and snake_case)
    const normalizedPayload = normalizePayload(req.body);
    const {
      organization_id,
      blob_storage_path,
      file_name,
      organization_name,
      service,
      month,
      year,
    } = normalizedPayload;

    logger.info(`[${requestId}] [DOCUMENT_API] Normalized payload:`, {
      organization_id,
      organization_name,
      blob_storage_path,
      file_name,
      service,
      month,
      year,
    });

    const validation = validateRequiredFields(
      { organization_id, blob_storage_path, file_name },
      ["organization_id", "blob_storage_path", "file_name"]
    );

    if (!validation.isValid) {
      logger.warn(
        `[${requestId}] [DOCUMENT_API] Validation failed:`,
        validation.errors
      );
      return res
        .status(STATUS_CODE_BAD_REQUEST)
        .json(errorResponse(DOCUMENT_MESSAGES.MISSING_REQUIRED_FIELDS));
    }

    // Extract refresh token from cookies
    let refreshToken = null;
    if (req.headers.cookie) {
      const cookies = req.headers.cookie.split(";").reduce((acc, cookie) => {
        const [key, value] = cookie.trim().split("=");
        acc[key] = value;
        return acc;
      }, {});
      refreshToken = cookies.refresh_token || null;
      logger.info(
        `[${requestId}] [DOCUMENT_API] Refresh token extracted: ${
          refreshToken ? "YES" : "NO"
        }`
      );
    }

    logger.info(
      `[${requestId}] [DOCUMENT_API] Calling storeDocumentService...`
    );
    // Pass refreshToken to service for summary generation
    const serviceResponse = await storeDocumentService(
      {
        ...normalizedPayload,
        refreshToken,
      },
      requestId
    );

    logger.info(`[${requestId}] [DOCUMENT_API] Service response received:`, {
      success: serviceResponse.success,
      status: serviceResponse.status,
      documentId: serviceResponse.data?.id,
      message: serviceResponse.message,
    });

    logger.info(
      `[${requestId}] [DOCUMENT_API] ===== END: POST /api/document =====`
    );
    return handleServiceResponse(
      serviceResponse,
      res,
      `${DOCUMENT_LOG_MESSAGES.CONTROLLER_STORAGE_SUCCESS}: ${serviceResponse.data?.id}`,
      `${DOCUMENT_LOG_MESSAGES.CONTROLLER_STORAGE_FAILED}: ${serviceResponse.message}`
    );
  } catch (error) {
    logger.error(`[${requestId}] [DOCUMENT_API] Unexpected error:`, {
      error: error.message,
      stack: error.stack,
    });
    return handleControllerError(
      error,
      res,
      DOCUMENT_LOG_MESSAGES.CONTROLLER_STORAGE_UNEXPECTED_ERROR,
      DOCUMENT_MESSAGES.STORAGE_FAILED
    );
  }
};

export const getDocuments = async (req, res) => {
  logger.info(DOCUMENT_LOG_MESSAGES.CONTROLLER_GET_REQUEST);

  try {
    const { organization_id, service, month, year, blob_storage_path } = req.query;

    if (!organization_id) {
      return res
        .status(STATUS_CODE_BAD_REQUEST)
        .json(errorResponse(DOCUMENT_MESSAGES.MISSING_ORGANIZATION_ID));
    }

    const filters = {};
    if (service) filters.service = service;
    if (month) filters.month = parseInt(month);
    if (year) filters.year = parseInt(year);
    if (blob_storage_path) filters.blob_storage_path = blob_storage_path;

    const serviceResponse = await getDocumentsService(organization_id, filters);
    return handleServiceResponse(serviceResponse, res);
  } catch (error) {
    return handleControllerError(
      error,
      res,
      DOCUMENT_LOG_MESSAGES.CONTROLLER_GET_ERROR,
      DOCUMENT_MESSAGES.FETCH_FAILED
    );
  }
};

/**
 * GET /api/document/:id - Get document by ID (for status checking)
 */
export const getDocumentById = async (req, res) => {
  const requestId = `GET-${Date.now()}-${Math.random()
    .toString(36)
    .substr(2, 9)}`;
  logger.info(
    `[${requestId}] [DOCUMENT_API] ===== START: GET /api/document/:id =====`
  );
  logger.info(`[${requestId}] [DOCUMENT_API] Document ID: ${req.params.id}`);

  try {
    const { id } = req.params;

    if (!id) {
      logger.warn(`[${requestId}] [DOCUMENT_API] Missing document ID`);
      return res
        .status(STATUS_CODE_BAD_REQUEST)
        .json(errorResponse("Document ID is required"));
    }

    logger.info(
      `[${requestId}] [DOCUMENT_API] Calling getDocumentByIdService...`
    );
    const serviceResponse = await getDocumentByIdService(id);

    logger.info(`[${requestId}] [DOCUMENT_API] Service response received:`, {
      success: serviceResponse.success,
      status: serviceResponse.status,
      hasData: !!serviceResponse.data,
    });

    logger.info(
      `[${requestId}] [DOCUMENT_API] ===== END: GET /api/document/:id =====`
    );
    return handleServiceResponse(
      serviceResponse,
      res,
      DOCUMENT_LOG_MESSAGES.CONTROLLER_RETRIEVAL_SUCCESS,
      DOCUMENT_LOG_MESSAGES.CONTROLLER_RETRIEVAL_FAILED
    );
  } catch (error) {
    logger.error(`[${requestId}] [DOCUMENT_API] Unexpected error:`, {
      error: error.message,
      stack: error.stack,
    });
    return handleControllerError(
      error,
      res,
      DOCUMENT_LOG_MESSAGES.CONTROLLER_RETRIEVAL_UNEXPECTED_ERROR,
      DOCUMENT_MESSAGES.RETRIEVAL_FAILED
    );
  }
};

export const updateDocumentById = async (req, res) => {
  logger.info(
    `${DOCUMENT_LOG_MESSAGES.CONTROLLER_START_STORAGE}: Updating document ${req.params.id}`
  );

  try {
    const { id } = req.params;

    if (!id) {
      return res
        .status(STATUS_CODE_BAD_REQUEST)
        .json(errorResponse(DOCUMENT_MESSAGES.MISSING_REQUIRED_FIELDS));
    }

    const serviceResponse = await updateDocumentByIdService(id, req.body || {});

    return handleServiceResponse(
      serviceResponse,
      res,
      `${DOCUMENT_LOG_MESSAGES.CONTROLLER_STORAGE_SUCCESS}: ${id}`,
      `${DOCUMENT_LOG_MESSAGES.CONTROLLER_STORAGE_FAILED}: ${serviceResponse?.message}`
    );
  } catch (error) {
    return handleControllerError(
      error,
      res,
      DOCUMENT_LOG_MESSAGES.CONTROLLER_STORAGE_UNEXPECTED_ERROR,
      DOCUMENT_MESSAGES.STORAGE_FAILED
    );
  }
};

/**
 * POST /api/document/store-pdf - Store PDF buffer to Azure Blob Storage
 * Accepts PDF buffer and metadata, stores to blob storage
 */
export const storePdfDocument = async (req, res) => {
  const requestId = `PDF-${Date.now()}-${Math.random()
    .toString(36)
    .substr(2, 9)}`;
  logger.info(
    `[${requestId}] ${DOCUMENT_LOG_MESSAGES.CONTROLLER_PDF_STORAGE_START}`
  );
  logger.info(`[${requestId}] [PDF_STORAGE] Request details:`, {
    hasBody: !!req.body,
    hasFile: !!req.file,
    contentType: req.headers["content-type"],
    bodyKeys: req.body ? Object.keys(req.body) : [],
  });

  try {
    // Support both multipart/form-data (with file) and JSON with base64 buffer
    let pdfBuffer;
    let organization_id, organization_name, service, month, year, fileName;

    if (req.file) {
      // Multipart form data with file upload
      pdfBuffer = req.file.buffer;
      organization_id = req.body.organization_id;
      organization_name = req.body.organization_name;
      service = req.body.service;
      month = req.body.month;
      year = req.body.year;
      fileName = req.body.file_name || req.file.originalname;
    } else if (req.body.pdfBuffer) {
      // JSON with base64 encoded buffer
      pdfBuffer = Buffer.from(req.body.pdfBuffer, "base64");
      organization_id = req.body.organization_id;
      organization_name = req.body.organization_name;
      service = req.body.service;
      month = req.body.month;
      year = req.body.year;
      fileName = req.body.file_name;
    } else {
      logger.warn(`[${requestId}] [PDF_STORAGE] No PDF buffer provided`);
      return res
        .status(STATUS_CODE_BAD_REQUEST)
        .json(errorResponse(DOCUMENT_MESSAGES.PDF_BUFFER_REQUIRED));
    }

    logger.info(`[${requestId}] [PDF_STORAGE] Parsed request:`, {
      organization_id,
      organization_name,
      service,
      month,
      year,
      fileName,
      bufferSize: pdfBuffer?.length,
    });

    // Validate required fields
    const validation = validateRequiredFields(
      { organization_id, service, month, year },
      ["organization_id", "service", "month", "year"]
    );

    if (!validation.isValid) {
      logger.warn(
        `[${requestId}] [PDF_STORAGE] Validation failed:`,
        validation.errors
      );
      return res
        .status(STATUS_CODE_BAD_REQUEST)
        .json(errorResponse(DOCUMENT_MESSAGES.MISSING_PDF_PARAMS));
    }

    logger.info(
      `[${requestId}] [PDF_STORAGE] Calling storePdfToBlob service...`
    );
    const serviceResponse = await storePdfToBlob({
      organization_id,
      organization_name,
      service,
      month: Number(month),
      year: Number(year),
      pdfBuffer,
      fileName,
    });

    logger.info(
      `[${requestId}] ${DOCUMENT_LOG_MESSAGES.CONTROLLER_PDF_STORAGE_SUCCESS}`,
      {
        success: serviceResponse.success,
        status: serviceResponse.status,
        blobPath: serviceResponse.data?.blobPath,
      }
    );

    return handleServiceResponse(
      serviceResponse,
      res,
      `${DOCUMENT_LOG_MESSAGES.CONTROLLER_PDF_STORAGE_SUCCESS}: ${serviceResponse.data?.blobPath}`,
      `${DOCUMENT_LOG_MESSAGES.CONTROLLER_PDF_STORAGE_FAILED}: ${serviceResponse.message}`
    );
  } catch (error) {
    logger.error(
      `[${requestId}] ${DOCUMENT_LOG_MESSAGES.CONTROLLER_PDF_STORAGE_FAILED}`,
      {
        error: error.message,
        stack: error.stack,
      }
    );
    return handleControllerError(
      error,
      res,
      DOCUMENT_LOG_MESSAGES.CONTROLLER_PDF_STORAGE_FAILED,
      DOCUMENT_MESSAGES.PDF_STORAGE_FAILED
    );
  }
};

/**
 * GET /api/document/sas-url - Get SAS URL for blob download
 * Query: blob_path (required) - The blob storage path
 */
export const getSasUrl = async (req, res) => {
  const requestId = `SAS-${Date.now()}-${Math.random()
    .toString(36)
    .substr(2, 9)}`;
  logger.info(
    `[${requestId}] [DOCUMENT_API] ===== START: GET /api/document/sas-url =====`
  );

  try {
    const { blob_path } = req.query;

    if (!blob_path) {
      logger.warn(`[${requestId}] [DOCUMENT_API] Missing blob_path parameter`);
      return res
        .status(STATUS_CODE_BAD_REQUEST)
        .json(errorResponse("blob_path query parameter is required"));
    }

    logger.info(`[${requestId}] [DOCUMENT_API] Generating SAS URL for:`, {
      blob_path,
    });

    // Generate SAS URL using blob storage service
    const sasUrl = await blobStorageService.getBlobSasUrl(blob_path, 1); // 1 hour expiry

    logger.info(`[${requestId}] [DOCUMENT_API] SAS URL generated successfully`);

    logger.info(
      `[${requestId}] [DOCUMENT_API] ===== END: GET /api/document/sas-url =====`
    );

    return res.status(STATUS_CODE_OK).json(
      successResponse("SAS URL generated successfully", {
        sasUrl,
        expiresIn: "1 hour",
      })
    );
  } catch (error) {
    logger.error(`[${requestId}] [DOCUMENT_API] Error generating SAS URL:`, {
      error: error.message,
      stack: error.stack,
    });

    // Handle specific error cases
    if (
      error.message?.includes("not found") ||
      error.message?.includes("NOT_FOUND")
    ) {
      return res
        .status(404)
        .json(errorResponse("Blob file not found in storage"));
    }

    return res
      .status(500)
      .json(errorResponse(`Failed to generate SAS URL: ${error.message}`));
  }
};
