// config/shared.config.js
import dotenv from 'dotenv';
dotenv.config();
import { createLogger } from '../app/utils/logger.utils.js';
import { CONFIG_DEFAULTS, CONFIG_ENV_TYPES } from '../app/utils/constants/config.constants.js';

const logger = createLogger('SHARED_CONFIG');

/**

/**
 * Parse environment variable value based on type
 * @param {string} value - Environment variable value
 * @param {string} type - Expected type (string, number, boolean, array, json)
 * @param {any} defaultValue - Default value if parsing fails
 * @returns {any} Parsed value
 */
export const parseEnvValue = (value, type = CONFIG_ENV_TYPES.STRING, defaultValue = null) => {
  if (!value) return defaultValue;
  
  switch (type) {
    case CONFIG_ENV_TYPES.NUMBER:
      const num = parseInt(value, 10);
      return isNaN(num) ? defaultValue : num;
    case CONFIG_ENV_TYPES.BOOLEAN:
      return value.toLowerCase() === 'true';
    case CONFIG_ENV_TYPES.ARRAY:
      return value.split(',').map(item => item.trim());
    case CONFIG_ENV_TYPES.JSON:
      try {
        return JSON.parse(value);
      } catch {
        return defaultValue;
      }
    default:
      return value;
  }
};

/**
 * Validate required environment variables
 * @param {Array<string>} requiredVars - Array of required environment variable names
 * @param {string} moduleName - Name of the module for error context
 * @throws {Error} If any required variables are missing
 */
export const validateRequiredEnvVars = (requiredVars, moduleName = 'Configuration') => {
  const missingVars = requiredVars.filter(varName => !process.env[varName]);
  if (missingVars.length > 0) {
    const error = `Missing required environment variables for ${moduleName}: ${missingVars.join(', ')}`;
    logger.error(error);
    throw new Error(error);
  }
  logger.info(`✅ Environment variables validated for ${moduleName}`);
};

/**
 * Get database configuration based on environment
 * @returns {Object} Database configuration object
 */
export const getDatabaseConfig = () => {
  // Allow explicit control over SSL via DB_SSL env var.
  // If DB_SSL is not provided, default to true for production, false for development.
  const defaultSslEnabled = process.env.NODE_ENV === 'production';
  const dbSslEnabled = process.env.DB_SSL
    ? parseEnvValue(process.env.DB_SSL, CONFIG_ENV_TYPES.BOOLEAN, defaultSslEnabled)
    : defaultSslEnabled;
  
  return {
    database: process.env.DB_NAME,
    username: process.env.DB_USER,
    password: process.env.DB_PASS,
    host: process.env.DB_HOST,
    port: parseEnvValue(process.env.DB_PORT, CONFIG_ENV_TYPES.NUMBER, 5433),
    dialect: 'postgres',
    logging: process.env.NODE_ENV === 'development' ? console.log : false,
    pool: {
      max: process.env.NODE_ENV === 'production' ? 10 : 5,
      min: 0,
      acquire: 30000,
      idle: 10000,
    },
    // Provide dialectOptions only when SSL is enabled to avoid connecting with SSL
    // to servers which do not support it (error: "The server does not support SSL connections").
    dialectOptions: dbSslEnabled ? {
      ssl: {
        require: true,
        rejectUnauthorized: false,
      },
    } : {},
  };
};

/**
 * Get logging configuration
 * @returns {Object} Logging configuration object
 */
export const getLoggingConfig = () => {
  return {
    level: process.env.LOG_LEVEL || CONFIG_DEFAULTS.LOG_INFO_LEVEL,
    format: process.env.LOG_FORMAT || 'combined',
    filePath: process.env.LOG_FILE_PATH || CONFIG_DEFAULTS.LOG_DIRECTORY,
    maxSize: process.env.LOG_MAX_SIZE || CONFIG_DEFAULTS.LOG_MAX_SIZE,
    maxFiles: parseEnvValue(process.env.LOG_MAX_FILES, CONFIG_ENV_TYPES.NUMBER, CONFIG_DEFAULTS.LOG_MAX_FILES),
    enableConsole: parseEnvValue(process.env.LOG_ENABLE_CONSOLE, CONFIG_ENV_TYPES.BOOLEAN, true)
  };
};

export default {
  parseEnvValue,
  validateRequiredEnvVars,
  getDatabaseConfig,
  getLoggingConfig,
};
