/* Listing Component Styles */

/*Client Avatar Styles*/
.listing-client-avatar {
  width: 3rem; /* w-12 */
  height: 3rem; /* h-12 */
  background: linear-gradient(
    to bottom right,
    #60a5fa,
    #9333ea
  ); /* from-blue-400 to-purple-600 */
  border-radius: 9999px; /* rounded-full */
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.listing-client-avatar:hover {
  transform: scale(1.1);
}

.listing-client-avatar-text {
  color: white;
  font-size: 0.875rem; /* text-sm */
  font-weight: 700; /* font-bold */
}

/*Client Table Styles*/
.listing-table {
  width: 100%;
  table-layout: fixed;
  vertical-align: middle;
  background-color: #ffffff;
}

/* Table column widths */
.listing-table-col-name {
  width: 35%;
}

.listing-table-col-status {
  width: 13%;
}

.listing-table-col-sync {
  width: 30%;
}

.listing-table-col-actions {
  width: 22%;
}

.listing-table-row {
  vertical-align: middle;
  transition: all 0.2s ease;
  background-color: #ffffff;
  border-bottom: 1px solid #e5e7eb;
}

.listing-table-row:hover {
  background-color: rgba(79, 70, 229, 0.05); /* primary/5 */
}

.listing-table-row-last {
  border-bottom: 0;
}

.listing-table-cell {
  padding: 1.5rem 1.25rem; /* py-6 px-5 */
  min-width: 0;
  vertical-align: middle;
}

.listing-table-cell-center {
  padding: 1.5rem 1rem; /* py-6 px-4 */
  text-align: center;
  vertical-align: middle;
}

.listing-table-cell-right {
  padding: 1.5rem 1.25rem; /* py-6 px-5 */
  text-align: right;
  vertical-align: middle;
}

.listing-client-info {
  display: flex;
  align-items: center;
  gap: 0.75rem; /* gap-3 */
  min-width: 0;
}

.listing-client-details {
  min-width: 0;
  flex: 1;
}

.listing-client-name {
  font-size: 1rem; /* text-base */
  font-weight: 700; /* font-bold */
  color: #111827; /* text-heading */
  transition: color 0.2s ease;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.listing-client-email {
  font-size: 0.75rem; /* text-xs */
  color: #6b7280; /* text-body */
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-top: 0.125rem; /* mt-0.5 */
}

/*Table Header Styles*/
.listing-table-header {
  border-bottom: 2px solid #e5e7eb;
  background-color: #f8fafc; /* background-page */
}

.listing-table-header-cell {
  padding: 1rem 1.25rem; /* py-4 px-5 */
  text-align: left;
  font-size: 0.75rem; /* text-xs */
  text-transform: uppercase;
  color: #6b7280; /* text-body */
  font-weight: 700; /* font-bold */
  letter-spacing: 0.05em; /* tracking-wider */
  vertical-align: middle;
  transition: color 0.2s ease;
  cursor: pointer;
}

.listing-table-header-cell:hover {
  color: #4f46e5; /* primary */
}

.listing-table-header-cell-center {
  text-align: center;
  padding: 1rem 1rem; /* py-4 px-4 */
}

.listing-table-header-cell-right {
  text-align: right;
  padding: 1rem 1.25rem; /* py-4 px-5 */
}

.listing-table-header-sort-icon {
  width: 1rem; /* w-4 */
  height: 1rem; /* h-4 */
  margin-left: 0.375rem; /* ml-1 */
  color: #9ca3af; /* text-gray-400 */
}

.listing-table-header-sort-icon-active {
  color: #4f46e5; /* primary */
}

.listing-table-header-sort-container {
  display: flex;
  align-items: center;
  gap: 0.375rem; /* gap-1.5 */
}

/*Sync Badges Styles - Modern, icon-based status indicators*/

/* Container for all sync badges - Vertical layout, centered */
.listing-sync-badges-container {
  display: flex;
  flex-direction: column;
  gap: 0.375rem; /* gap-1.5 - slightly spaced vertically */
  align-items: center;
  justify-content: center;
  min-width: 140px;
  width: 100%;
}

/* Individual sync badge chip - Rounded full style */
.listing-sync-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.375rem; /* 1.5 * 0.25rem = 6px */
  padding: 3px 0.75rem; /* py-[3px] px-3 */
  border-radius: 9999px; /* rounded-full */
  border-width: 1px;
  box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05); /* shadow-sm */
  transition: all 0.2s ease-in-out;
  cursor: pointer;
  width: fit-content;
  font-size: 0.75rem; /* text-xs */
}

.listing-sync-badge:hover {
  box-shadow:
    0 4px 6px -1px rgb(0 0 0 / 0.1),
    0 2px 4px -2px rgb(0 0 0 / 0.1); /* shadow-md */
  transform: scale(1.02);
}

.listing-sync-badge:active {
  transform: scale(0.98);
}

/* Status-specific background colors - Using new semantic palette */

/* Operational - Warning (Amber/Gold) */
.listing-sync-badge-operational {
  background-color: rgba(245, 158, 11, 0.1); /* #F59E0B with 10% opacity */
  border-color: rgba(245, 158, 11, 0.3); /* #F59E0B with 30% opacity */
}

/* Payroll - Info (Blue) */
.listing-sync-badge-payroll {
  background-color: rgba(59, 130, 246, 0.1); /* #3B82F6 with 10% opacity */
  border-color: rgba(59, 130, 246, 0.3); /* #3B82F6 with 30% opacity */
}

/* Financial - Primary (Purple/Indigo) */
.listing-sync-badge-financial {
  background-color: rgba(79, 70, 229, 0.1); /* #4F46E5 with 10% opacity */
  border-color: rgba(79, 70, 229, 0.3); /* #4F46E5 with 30% opacity */
}

/* Synced - Success (Green) */
.listing-sync-badge-synced {
  background-color: rgba(16, 185, 129, 0.1); /* #10B981 with 10% opacity */
  border-color: rgba(16, 185, 129, 0.3); /* #10B981 with 30% opacity */
}

.listing-sync-badge-syncing {
  background-color: rgba(245, 158, 11, 0.1); /* #F59E0B with 10% opacity */
  border-color: rgba(245, 158, 11, 0.3); /* #F59E0B with 30% opacity */
}

.listing-sync-badge-not-synced {
  background-color: rgba(239, 68, 68, 0.1); /* #EF4444 with 10% opacity */
  border-color: rgba(239, 68, 68, 0.3); /* #EF4444 with 30% opacity */
}

/* Status icon */
.listing-sync-icon {
  width: 1rem; /* h-4 */
  height: 1rem; /* w-4 */
  flex-shrink: 0;
  transition: transform 0.2s ease-in-out;
}

.listing-sync-badge:hover .listing-sync-icon {
  transform: scale(1.1);
}

.listing-sync-icon-spinning {
  animation: listing-spin 1s linear infinite;
}

@keyframes listing-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Icon color variants - Using new semantic colors */
.listing-icon-synced {
  color: #10b981; /* semantic-success */
}

.listing-icon-syncing {
  color: #f59e0b; /* semantic-warning */
}

.listing-icon-not-synced {
  color: #ef4444; /* semantic-danger */
}

/* Category-specific icon colors */
.listing-icon-operational {
  color: #f59e0b; /* semantic-warning */
}

.listing-icon-payroll {
  color: #3b82f6; /* semantic-info */
}

.listing-icon-financial {
  color: #4f46e5; /* primary */
}

/* Badge content container */
.listing-badge-content {
  display: flex;
  align-items: center;
  gap: 0.375rem; /* gap-1.5 */
  flex-wrap: wrap;
  flex-direction: row;
}

/* Category name */
.listing-category-name {
  font-size: 0.75rem; /* text-xs */
  font-weight: 600; /* font-semibold */
  line-height: 1.25; /* leading-tight */
  white-space: nowrap;
}

/* Separator colon */
.listing-separator {
  font-size: 0.75rem; /* text-xs */
  opacity: 0.8;
  margin: 0 0.25rem; /* mx-1 */
  font-weight: 500;
}

/* Status/Time text */
.listing-status-text {
  font-size: 0.75rem; /* text-xs */
  font-weight: 500; /* font-medium */
  line-height: 1.25; /* leading-tight */
  white-space: nowrap;
}

/* Text color variants - Using new semantic colors */
.listing-text-synced {
  color: #10b981; /* semantic-success */
}

.listing-text-syncing {
  color: #f59e0b; /* semantic-warning */
}

.listing-text-not-synced {
  color: #ef4444; /* semantic-danger */
}

/* Category-specific text colors */
.listing-text-operational {
  color: #f59e0b; /* semantic-warning */
}

.listing-text-payroll {
  color: #3b82f6; /* semantic-info */
}

.listing-text-financial {
  color: #4f46e5; /* primary */
}

/* Critical status styles - for oldest sync or not synced */
.listing-sync-icon-critical {
  transform: scale(1.15);
}

.listing-category-name-critical {
  font-weight: 700 !important; /* font-bold */
}

.listing-status-text-critical {
  font-weight: 600 !important; /* font-semibold */
}

/* ============================================
   Table Actions Styles
   ============================================ */
.listing-table-actions-container {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 0.5rem; /* gap-2 */
}

.listing-action-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
  border-radius: 0.5rem; /* rounded-lg */
  transition: all 0.2s ease;
  font-size: 0.75rem; /* text-xs */
}

.listing-action-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.listing-action-button-dashboard {
  padding: 0.375rem 0.75rem; /* py-1.5 px-3 */
  background-color: #EDE9FE; /* bg-purple-100 */
  color: #6D28D9; /* text-purple-700 */
  font-weight: 500; /* font-medium */
}

.listing-action-button-dashboard:hover:not(:disabled) {
  background-color: #DDD6FE; /* hover:bg-purple-200 */
}

.listing-action-button-bookclosure {
  padding: 0.375rem 1rem; /* py-1.5 px-4 */
  background-color: #4F46E5; /* bg-primary */
  color: #ffffff; /* text-white */
  font-weight: 600; /* font-semibold */
  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1); /* shadow-md */
  height: 2rem; /* h-8 */
  width: 100%;
  min-width: 150px;
}

.listing-action-button-bookclosure:hover:not(:disabled) {
  background-color: #4338CA; /* hover:bg-primary-dark */
  box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1); /* shadow-lg */
  transform: scale(1.02);
}

.listing-action-button-outline {
  padding: 0.375rem 0.75rem; /* py-1.5 px-3 */
  border: 1px solid #4F46E5; /* border-primary */
  color: #4F46E5; /* text-primary */
  background-color: #FFFFFF; /* bg-background-card */
  font-weight: 500; /* font-medium */
  box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05); /* shadow-sm */
}

.listing-action-button-outline:hover:not(:disabled) {
  background-color: rgba(79, 70, 229, 0.05); /* hover:bg-primary/5 */
  border-color: #4338CA; /* hover:border-primary-dark */
}

.listing-action-button-default {
  padding: 0.375rem 0.75rem; /* py-1.5 px-3 */
  background-color: rgba(156, 163, 175, 0.1); /* bg-text-subtle/10 */
  color: #6B7280; /* text-text-body */
  font-weight: 500; /* font-medium */
}

.listing-action-button-default:hover:not(:disabled) {
  background-color: rgba(156, 163, 175, 0.2); /* hover:bg-text-subtle/20 */
}

.listing-action-button-small {
  height: 1.75rem; /* h-7 */
  width: 100%;
  min-width: 90px;
}

.listing-action-button-small:hover:not(:disabled) {
  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1); /* hover:shadow-md */
  transform: translateY(-0.125rem); /* hover:-translate-y-0.5 */
}

.listing-action-button-icon {
  margin-right: 0.25rem; /* mr-1 */
  flex-shrink: 0;
  width: 0.875rem; /* h-3.5 */
  height: 0.875rem; /* w-3.5 */
}

.listing-action-button-text {
  white-space: nowrap;
}
