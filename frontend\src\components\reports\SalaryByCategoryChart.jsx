'use client';

import { formatCompactCurrency } from '../../utils/methods/formatters';
import { EChartWrapper } from './EChartWrapper';

export function SalaryByCategoryChart({ data }) {
  const categories = data.categories.map((item) => item.name);
  const colors = ['#34d399', '#2f7ed8', '#ffc542', '#ff8c5a'];
  const values = data.categories.map((item, index) => ({
    value: item.value,
    itemStyle: {
      color: colors[index % colors.length],
    },
  }));

  const option = {
    color: colors,
    tooltip: {
      trigger: 'axis',
      axisPointer: { type: 'shadow' },
      formatter: (params) => {
        const param = Array.isArray(params) ? params[0] : params;
        return `${param.name}: ${formatCompactCurrency(param.value)}`;
      },
    },
    grid: { left: '2%', right: '6%', bottom: '2%', top: '4%', containLabel: true },
    xAxis: {
      type: 'value',
      axisLabel: { formatter: (value) => formatCompactCurrency(value), color: '#475569' },
      splitLine: { lineStyle: { color: '#e2e8f0' } },
    },
    yAxis: {
      type: 'category',
      data: categories,
      axisLabel: { color: '#1e293b', fontWeight: 600 },
    },
    series: [
      {
        type: 'bar',
        data: values,
        barWidth: 42,
        itemStyle: { borderRadius: 0 },
        label: {
          show: true,
          position: 'right',
          formatter: ({ value }) => formatCompactCurrency(value),
        },
      },
    ],
  };

  return <EChartWrapper option={option} />;
}
