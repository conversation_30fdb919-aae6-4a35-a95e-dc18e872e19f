import CashFlowRepository from "../repository/cash_flow.repository.js";
import { LOGGER_NAMES } from "../utils/constants/log.constants.js";
import { createLogger } from "../utils/logger.utils.js";
import { isValidMonth, isValidYear, parseNumericValue } from "../utils/format.utils.js";

const logger = createLogger(LOGGER_NAMES.REPORT_SERVICE);

/**
 * Get cash flow details by group for organization
 * @param {Object} params - Query parameters
 * @param {string} params.organization_id - Organization ID
 * @param {number} params.month - Month (1-12)
 * @param {number} params.year - Year
 * @returns {Promise<Object>} Cash flow data grouped by operating, investing, financing, net_cash_flow
 */
const getCashFlowByGroup = async ({ organization_id, month, year }) => {
  try {
    logger.info(
      `Fetching cash flow data for org: ${organization_id}, month: ${month}, year: ${year}`
    );

    // Validate parameters
    if (!organization_id) {
      throw new Error("Organization ID is required");
    }

    if (!month || !isValidMonth(month)) {
      throw new Error("Valid month (1-12) is required");
    }

    if (!year || !isValidYear(year)) {
      throw new Error("Valid year is required");
    }

    // Convert to numbers
    const monthNum = parseInt(month, 10);
    const yearNum = parseInt(year, 10);

    // Get organization schema name
    const schemaName = await CashFlowRepository.getOrganizationSchemaName(
      organization_id
    );

    if (!schemaName) {
      logger.warn(`Schema not found for organization: ${organization_id}`);
      throw new Error("Organization not found or schema not configured");
    }

    // Get cash flow report ID for the month
    const reportId = await CashFlowRepository.getCashFlowReportId(
      schemaName,
      monthNum,
      yearNum
    );

    if (!reportId) {
      logger.info(
        `No Cash Flow report found for org: ${organization_id}, month: ${month}, year: ${year}`
      );
      return {
        organization_id,
        month: monthNum,
        year: yearNum,
        cash_flow: {
          operating: 0,
          investing: 0,
          financing: 0,
          net_cash_flow: 0,
        },
        message: "No cash flow report found for the specified period",
      };
    }

    // Get cash flow totals
    const cashFlowTotals = await CashFlowRepository.getCashFlowTotals(
      schemaName,
      reportId
    );

    if (!cashFlowTotals) {
      logger.info(
        `No cash flow totals found for report ID: ${reportId}`
      );
      return {
        organization_id,
        month: monthNum,
        year: yearNum,
        cash_flow: {
          operating: 0,
          investing: 0,
          financing: 0,
          net_cash_flow: 0,
        },
        message: "No cash flow totals found for the specified period",
      };
    }

    // Parse and round the values
    const operating = Math.round(parseNumericValue(cashFlowTotals.operating));
    const investing = Math.round(parseNumericValue(cashFlowTotals.investing));
    const financing = Math.round(parseNumericValue(cashFlowTotals.financing));
    const netCashFlow = Math.round(parseNumericValue(cashFlowTotals.net_cash_flow));

    logger.info(
      `Cash Flow data - Operating: ${operating}, Investing: ${investing}, Financing: ${financing}, Net: ${netCashFlow}`
    );

    return {
      organization_id,
      month: monthNum,
      year: yearNum,
      cash_flow: {
        operating,
        investing,
        financing,
        net_cash_flow: netCashFlow,
      },
    };
  } catch (error) {
    logger.error("Error in CashFlowService.getCashFlowByGroup:", error);
    throw error;
  }
};

export default {
  getCashFlowByGroup,
};
