// app/controllers/file.controller.js
import {
  getReportFoldersOnly,
  getReportFilesForFolder,
  getReportSummaryForFile,
} from "../services/report.service.js";
import logger from "../../config/logger.config.js";
import {
  STATUS_CODE_BAD_REQUEST,
} from "../utils/status_code.utils.js";
import { errorResponse } from "../utils/response.util.js";
import {
  handleServiceResponse,
  handleControllerError,
} from "../utils/controllerHandler.util.js";
import {
  FILE_MESSAGES,
  FILE_LOG_MESSAGES,
} from "../utils/constants/file.constants.js";
import { MONTH_NAME_TO_ABBR } from "../utils/constants/report.constants.js";
import {
  getOrganizationId,
  getOrganizationName,
} from "../utils/queryParams.util.js";

// Validation helpers
const validateOrganizationId = (organizationId, res) => {
  if (!organizationId) {
    res.status(STATUS_CODE_BAD_REQUEST).json(
      errorResponse(FILE_MESSAGES.ORGANIZATION_ID_REQUIRED)
    );
    return false;
  }
  return true;
};

const validateFolder = (folder, res) => {
  if (!folder) {
    res.status(STATUS_CODE_BAD_REQUEST).json(
      errorResponse(FILE_MESSAGES.FOLDER_REQUIRED)
    );
    return false;
  }
  return true;
};

const validateFileName = (fileName, res) => {
  if (!fileName) {
    res.status(STATUS_CODE_BAD_REQUEST).json(
      errorResponse(FILE_MESSAGES.FILE_NAME_REQUIRED)
    );
    return false;
  }
  return true;
};

export const getReportFolders = async (req, res) => {
  try {
    const organizationId = getOrganizationId(req.query);
    const organizationName = getOrganizationName(req.query);

    if (!validateOrganizationId(organizationId, res)) return;

    const result = await getReportFoldersOnly(organizationId, organizationName);
    return handleServiceResponse(
      result,
      res,
      FILE_LOG_MESSAGES.REPORT_FOLDERS_COMPLETE
    );
  } catch (error) {
    return handleControllerError(
      error,
      res,
      FILE_LOG_MESSAGES.REPORT_FOLDERS_ERROR,
      FILE_MESSAGES.REPORT_FOLDERS_FAILED
    );
  }
};

export const getReportFiles = async (req, res) => {
  try {
    const organizationId = getOrganizationId(req.query);
    const organizationName = getOrganizationName(req.query);
    const folder = req.query.folder || req.query.category || req.query.type;
    const monthsParam = req.query.months || req.query.month || null;
    const filename = req.query.filename || req.query.fileName || null;

    if (!validateOrganizationId(organizationId, res)) return;

    // If filename is provided, parse it to extract folder and month
    if (filename && !folder) {
      try {
        const filenameWithoutExt = filename.replace(/\.pdf$/i, "").trim();
        
        // Parse filename: "Finance August-2025.pdf" or similar patterns
        const monthYearMatch = filenameWithoutExt.match(/(\w+)\s+(\w+)[-\s](\d{4})/i);
        const monthMatch = filenameWithoutExt.match(/(\w+)\s+(\w+)/i);
        
        let parsedFolder = null;
        let parsedMonth = null;
        
        if (monthYearMatch) {
          // Format: "Finance August-2025"
          parsedFolder = monthYearMatch[1];
          const month = monthYearMatch[2];
          const year = monthYearMatch[3];
          
          // Convert month name to abbreviation (e.g., "August" -> "Aug")
          const monthLower = month.toLowerCase();
          const monthAbbr = MONTH_NAME_TO_ABBR[monthLower] || month;
          const yearShort = year.slice(-2);
          parsedMonth = `${monthAbbr}-${yearShort}`;
        } else if (monthMatch) {
          // Format: "Finance August" - use current year
          parsedFolder = monthMatch[1];
          const month = monthMatch[2];
          const currentYear = new Date().getFullYear();
          const yearShort = currentYear.toString().slice(-2);
          
          const monthLower = month.toLowerCase();
          const monthAbbr = MONTH_NAME_TO_ABBR[monthLower] || month;
          parsedMonth = `${monthAbbr}-${yearShort}`;
        }
        
        // Use parsed values if available
        if (parsedFolder) {
          const finalFolder = folder || parsedFolder;
          const finalMonths = monthsParam || parsedMonth;
          
          const result = await getReportFilesForFolder(
            organizationId,
            organizationName,
            finalFolder,
            finalMonths,
            filename
          );

          return handleServiceResponse(
            result,
            res,
            FILE_LOG_MESSAGES.REPORT_FILES_COMPLETE,
            FILE_LOG_MESSAGES.REPORT_FILES_ERROR
          );
        }
      } catch (parseError) {
        logger.warn(`Error parsing filename: ${parseError.message}`, {
          filename,
        });
        // Fall through to normal flow
      }
    }

    // Normal flow: folder is required
    if (!validateFolder(folder, res)) return;

    const result = await getReportFilesForFolder(
      organizationId,
      organizationName,
      folder,
      monthsParam,
      filename
    );

    return handleServiceResponse(
      result,
      res,
      FILE_LOG_MESSAGES.REPORT_FILES_COMPLETE,
      FILE_LOG_MESSAGES.REPORT_FILES_ERROR
    );
  } catch (error) {
    return handleControllerError(
      error,
      res,
      FILE_LOG_MESSAGES.REPORT_FILES_ERROR,
      FILE_MESSAGES.REPORT_FILES_FAILED
    );
  }
};

export const getReportSummary = async (req, res) => {
  try {
    const organizationId = getOrganizationId(req.query);
    const organizationName = getOrganizationName(req.query);
    const folder = req.query.folder || req.query.category || req.query.type;
    const fileName = req.query.fileName || req.query.filename;

    if (!validateOrganizationId(organizationId, res)) return;
    if (!validateFolder(folder, res)) return;
    if (!validateFileName(fileName, res)) return;

    const result = await getReportSummaryForFile(
      organizationId,
      organizationName,
      folder,
      fileName
    );

    return handleServiceResponse(
      result,
      res,
      FILE_LOG_MESSAGES.REPORT_SUMMARY_COMPLETE,
      FILE_LOG_MESSAGES.REPORT_SUMMARY_ERROR
    );
  } catch (error) {
    return handleControllerError(
      error,
      res,
      FILE_LOG_MESSAGES.REPORT_SUMMARY_ERROR,
      FILE_MESSAGES.REPORT_SUMMARY_FAILED
    );
  }
};
