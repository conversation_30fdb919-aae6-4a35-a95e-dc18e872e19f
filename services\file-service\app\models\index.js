// app/models/index.js
import { Sequelize } from "sequelize";
import documentModel from "./document.model.js";
import dbConfig from "../../config/database.config.js";
import logger from "../../config/logger.config.js";

// Initialize Sequelize connection with error handling
let sequelize;
try {
  sequelize = new Sequelize(dbConfig.database, dbConfig.username, dbConfig.password, {
    host: dbConfig.host,
    port: dbConfig.port,
    dialect: dbConfig.dialect,
    logging: dbConfig.logging,
    pool: dbConfig.pool,
    dialectOptions: dbConfig.dialectOptions,
    define: {
      timestamps: true,
      underscored: true,
      freezeTableName: true,
    },
  });
} catch (error) {
  logger.error(`Error initializing Sequelize: ${error.message}`, { error });
  throw error;
}

const db = {};

// Initialize all models
try {
  db.Document = documentModel(sequelize);
} catch (error) {
  logger.error(`Error initializing Document model: ${error.message}`, { error });
  throw error;
}

// Initialize associations if needed using early return pattern
try {
  Object.keys(db).forEach((modelName) => {
    // Guard clause: check if model has associate method
    if (db[modelName] && typeof db[modelName].associate === 'function') {
      db[modelName].associate(db);
    }
  });
} catch (error) {
  logger.error(`Error initializing model associations: ${error.message}`, { error });
  // Continue without associations if initialization fails
}

db.sequelize = sequelize;
db.Sequelize = Sequelize;

// Export models and sequelize instance
export const Document = db.Document;
export { sequelize };
export default db;

