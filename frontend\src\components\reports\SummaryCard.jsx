"use client";

import clsx from "clsx";
import { Card } from "antd";
import { formatCardValue, formatPercent } from "../../utils/methods/formatters";

export function SummaryCard({ card }) {
  const isPositive = card.previousMonthChange >= 0;
  const pmChange = `${isPositive ? "+" : ""}${formatPercent(card.previousMonthChange)}`;

  return (
    <Card
      className="card-surface border-0"
      styles={{ body: { padding: 20, textAlign: "center" } }}
    >
      <p className="text-sm font-medium text-slate-500 text-center">
        {card.title}
      </p>
      <div className="mt-2 text-2xl font-semibold text-slate-900 text-center">
        {formatCardValue(card.value, card.format)}
      </div>
      <div className="mt-3 text-xs text-slate-500 text-center">
        <p className="text-center">
          PM: {formatCardValue(card.previousMonth, card.format)}{" "}
          <span
            className={clsx(
              "font-semibold",
              isPositive ? "text-emerald-500" : "text-rose-500"
            )}
          >
            ({pmChange})
          </span>
        </p>
        <p className="mt-1 text-center">
          YTD: {formatCardValue(card.yearToDate, card.format)}
        </p>
      </div>
    </Card>
  );
}
