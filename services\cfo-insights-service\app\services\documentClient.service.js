import { httpGet, httpPatch } from "../../../../shared/utils/axios.util.js";
import { ERROR_MESSAGES } from "../utils/constants/error.constants.js";
import { TIMING_CONSTANTS } from "../utils/constants/timing.constants.js";
import { CHAT_CONSTANTS } from "../utils/constants/chat.constants.js";
import { LOG_MESSAGES } from "../utils/constants/logMessages.constants.js";
import logger from "../config/logger.config.js";

const SUMMARY_STATUS = {
  PROCESSING: "PROCESSING",
  COMPLETED: "COMPLETED",
  FAILED: "FAILED",
};

export const getSummaryStatuses = () => SUMMARY_STATUS;

const getBaseUrl = () => process.env.FILE_SERVICE_URL;

const buildHeaders = () => {
  const headers = {
    "Content-Type": "application/json",
  };

  if (process.env.FILE_SERVICE_API_KEY) {
    headers["x-api-key"] = process.env.FILE_SERVICE_API_KEY;
  }

  return headers;
};

export const resolveDocumentRecord = async ({
  documentId,
  filename,
  organizationId,
  organizationName,
  month,
  year,
  folder,
  months,
  service,
} = {}) => {
  const baseUrl = getBaseUrl();
  if (!baseUrl) {
    return { error: `${ERROR_MESSAGES.GENERAL.FILE_SERVICE_URL_MISSING}. Cannot resolve file: ${filename || "unknown"}` };
  }

  // Use /api/files/report-files endpoint instead of /api/files/resolve
  const params = new URLSearchParams();

  if (!organizationId || typeof organizationId !== "string" || !organizationId.trim()) {
    return { error: `${ERROR_MESSAGES.GENERAL.DOCUMENT_IDENTIFIER_REQUIRED}. ${ERROR_MESSAGES.GENERAL.ORGANIZATION_ID_REQUIRED_RESOLVE}: ${filename || "unknown"}` };
  }

  if (!filename || typeof filename !== "string" || !filename.trim()) {
    return { error: `${ERROR_MESSAGES.GENERAL.FILENAME_REQUIRED_RESOLVE}. Provided: ${filename}` };
  }

  if (!organizationName || typeof organizationName !== "string" || !organizationName.trim()) {
    return { error: `${ERROR_MESSAGES.GENERAL.ORGANIZATION_NAME_REQUIRED_RESOLVE}: ${filename}` };
  }

  if (month === undefined || month === null || Number.isNaN(Number.parseInt(month, 10))) {
    return { error: `${ERROR_MESSAGES.GENERAL.MONTH_REQUIRED_RESOLVE}: ${filename}` };
  }

  if (year === undefined || year === null || Number.isNaN(Number.parseInt(year, 10))) {
    return { error: `${ERROR_MESSAGES.GENERAL.YEAR_REQUIRED_RESOLVE}: ${filename}` };
  }

  // Extract folder/service from parameters or filename if not provided
  // Priority: folder > service > extract from filename > error
  // Filename format: "Finance July-2025.pdf" or "Operations August-2025.pdf"
  let resolvedFolder = folder || service;
  if (!resolvedFolder && filename) {
    const filenameTrimmed = filename.trim();
    for (const validFolder of CHAT_CONSTANTS.VALID_FOLDERS) {
      if (filenameTrimmed.startsWith(validFolder)) {
        resolvedFolder = validFolder;
        break;
      }
    }
  }

  if (!resolvedFolder || typeof resolvedFolder !== "string" || !resolvedFolder.trim()) {
    return { error: `${ERROR_MESSAGES.VALIDATION.FOLDER_REQUIRED}: ${filename}. ${ERROR_MESSAGES.VALIDATION.FOLDER_INVALID}` };
  }

  // Map organizationId to orgId (as expected by report-files endpoint)
  params.append("orgId", organizationId.trim());

  // Map organizationName to orgname (as expected by report-files endpoint)
  params.append("orgname", organizationName.trim());

  // Add filename parameter
  params.append("filename", filename.trim());

  // Add folder parameter (required)
  params.append("folder", resolvedFolder.trim());

  if (months) {
    params.append("months", months);
  } else if (month && year) {
    // Convert month and year to months format (e.g., "Aug-25")
    // Handle various month formats: "August", "Aug", "8", etc.
    let monthAbbr = month;
    const monthLower = month.toString().toLowerCase();
    
    // Map full month names and numbers to abbreviations
    const monthMap = {
      january: "Jan", jan: "Jan", "1": "Jan", "01": "Jan",
      february: "Feb", feb: "Feb", "2": "Feb", "02": "Feb",
      march: "Mar", mar: "Mar", "3": "Mar", "03": "Mar",
      april: "Apr", apr: "Apr", "4": "Apr", "04": "Apr",
      may: "May", "5": "May", "05": "May",
      june: "Jun", jun: "Jun", "6": "Jun", "06": "Jun",
      july: "Jul", jul: "Jul", "7": "Jul", "07": "Jul",
      august: "Aug", aug: "Aug", "8": "Aug", "08": "Aug",
      september: "Sep", sep: "Sep", "9": "Sep", "09": "Sep",
      october: "Oct", oct: "Oct", "10": "Oct",
      november: "Nov", nov: "Nov", "11": "Nov",
      december: "Dec", dec: "Dec", "12": "Dec",
    };
    
    monthAbbr = monthMap[monthLower] || (month.length > 3 ? month.substring(0, 3) : month);
    const yearShort = year.toString().slice(-2);
    params.append("months", `${monthAbbr}-${yearShort}`);
  }

  const url = `${baseUrl}/files/report-files?${params.toString()}`;

  try {
    const response = await httpGet(url, {
      headers: buildHeaders(),
      timeout: TIMING_CONSTANTS.HTTP_CLIENT_REQUEST_MS, // Use longer timeout (30s instead of 5s)
    });

    if (response.data?.success && response.data?.data?.files?.length > 0) {
      // Extract first file from the files array
      const fileData = response.data.data.files[0];
      
      // Map response to expected format
      // blobStoragePath contains the full blob path (e.g., "orgId/orgName/Reports/Finance/2025/August/file.pdf")
      const blobName = fileData.blobStoragePath || null;
      const sasUrl = fileData.url || null;
      
      if (!blobName || !sasUrl) {
        const monthNum = Number.parseInt(month, 10);
        const yearNum = Number.parseInt(year, 10);
        const monthName = CHAT_CONSTANTS.MONTH_NAMES[monthNum - 1] || monthNum;
        const filePath = `${organizationId.trim()}/${organizationName.trim()}/Reports/${resolvedFolder.trim()}/${yearNum}/${monthName}/`;
        
        return { 
          error: `File "${filename.trim()}" found but ${ERROR_MESSAGES.GENERAL.FILE_MISSING_BLOB_METADATA} (blobName: ${!!blobName}, sasUrl: ${!!sasUrl}) at path: ${filePath}`
        };
      }

      return {
        data: {
          blobName,
          sasUrl,
          fileName: fileData.name ? fileData.name : filename.trim(),
          organizationId: response.data.data.orgId ? response.data.data.orgId : organizationId.trim(),
          organizationName: response.data.data.org ? response.data.data.org : organizationName.trim(),
        },
      };
    }

    // Build detailed error message with file path
    const monthNum = Number.parseInt(month, 10);
    const yearNum = Number.parseInt(year, 10);
    const monthName = CHAT_CONSTANTS.MONTH_NAMES[monthNum - 1] || monthNum;
    const filePath = `${organizationId.trim()}/${organizationName.trim()}/Reports/${resolvedFolder.trim()}/${yearNum}/${monthName}/`;
    
    return { 
      error: `${ERROR_MESSAGES.GENERAL.DOCUMENT_RESOLVE_FAILED}. File "${filename.trim()}" not found at path: ${filePath}. ${ERROR_MESSAGES.GENERAL.FILE_RESOLUTION_NO_FILES}`
    };
  } catch (error) {
    const monthNum = Number.parseInt(month, 10);
    const yearNum = Number.parseInt(year, 10);
    const monthName = CHAT_CONSTANTS.MONTH_NAMES[monthNum - 1] || monthNum;
    const filePath = `${organizationId.trim()}/${organizationName.trim()}/Reports/${resolvedFolder.trim()}/${yearNum}/${monthName}/`;
    
    if (error.code === "ECONNABORTED" || error.message?.includes("timeout")) {
      logger.error(LOG_MESSAGES.CHAT.FILE_RESOLUTION_TIMEOUT, { 
        filename: filename.trim(), 
        filePath, 
        error: error.message,
        errorCode: error.code 
      });
      return { 
        error: `${ERROR_MESSAGES.GENERAL.REQUEST_TIMEOUT} "${filename.trim()}" at path: ${filePath}. ${ERROR_MESSAGES.GENERAL.FILE_SERVICE_TIMEOUT}`
      };
    }
    
    if (error.response?.status === 404) {
      logger.warn(LOG_MESSAGES.CHAT.FILE_RESOLUTION_NOT_FOUND, { 
        filename: filename.trim(), 
        filePath, 
        status: 404 
      });
      return { 
        error: `${ERROR_MESSAGES.GENERAL.DOCUMENT_NOT_FOUND}. File "${filename.trim()}" not found at path: ${filePath}. ${ERROR_MESSAGES.GENERAL.FILE_RESOLUTION_HTTP_404}`
      };
    }

    if (error.response?.status === 400) {
      logger.warn(LOG_MESSAGES.CHAT.FILE_RESOLUTION_BAD_REQUEST, { 
        filename: filename.trim(), 
        filePath, 
        status: 400,
        error: error.response?.data?.message || error.message 
      });
      return { 
        error: `${ERROR_MESSAGES.GENERAL.FILE_RESOLUTION_BAD_REQUEST}. File "${filename.trim()}" at path: ${filePath}. ${error.response?.data?.message || error.message}`
      };
    }

    logger.error(LOG_MESSAGES.CHAT.FILE_RESOLUTION_ERROR, { 
      filename: filename.trim(), 
      filePath, 
      error: error.message,
      status: error.response?.status 
    });
    return { 
      error: `${ERROR_MESSAGES.GENERAL.INTERNAL_ERROR} ${ERROR_MESSAGES.GENERAL.FILE_SERVICE_ERROR} "${filename.trim()}" at path: ${filePath}. ${error.message}`
    };
  }
};

/**
 * Update document record in File Service
 * @param {string} documentId
 * @param {Object} payload
 * @returns {Promise<void>}
 */
export const updateDocumentRecord = async (documentId, payload = {}) => {
  if (!documentId) {
    logger.warn(LOG_MESSAGES.CHAT.DOCUMENT_UPDATE_VALIDATION_ERROR, { 
      error: ERROR_MESSAGES.GENERAL.DOCUMENT_IDENTIFIER_REQUIRED,
      documentId: null 
    });
    return { error: ERROR_MESSAGES.GENERAL.DOCUMENT_IDENTIFIER_REQUIRED };
  }

  const baseUrl = getBaseUrl();
  if (!baseUrl) {
    logger.error(LOG_MESSAGES.CHAT.DOCUMENT_UPDATE_FAILED, { 
      error: ERROR_MESSAGES.GENERAL.FILE_SERVICE_URL_MISSING,
      documentId 
    });
    return { error: ERROR_MESSAGES.GENERAL.FILE_SERVICE_URL_MISSING };
  }

  const url = `${baseUrl}/document/${documentId}`;

  try {
    logger.info(LOG_MESSAGES.INFO.DOCUMENT_UPDATE_STARTED, { documentId });
    await httpPatch(url, payload, {
      headers: buildHeaders(),
      timeout: TIMING_CONSTANTS.DOCUMENT_FETCH_MS,
    });
    logger.info(LOG_MESSAGES.SUCCESS.DOCUMENT_UPDATED, { documentId });
    return { success: true };
  } catch (error) {
    logger.error(LOG_MESSAGES.CHAT.DOCUMENT_UPDATE_FAILED, { 
      error: ERROR_MESSAGES.GENERAL.DOCUMENT_UPDATE_FAILED,
      documentId,
      httpError: error.message,
      status: error.response?.status 
    });
    return { error: ERROR_MESSAGES.GENERAL.DOCUMENT_UPDATE_FAILED };
  }
};


