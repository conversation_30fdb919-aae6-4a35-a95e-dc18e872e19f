"use client";

import { memo, useMemo } from "react";
import { Button } from "../ui/button";
import { X, FileText } from "lucide-react";
import "@/styles/cfo-insights.css";

export const CFOHeader = memo(function CFOHeader({
  effectiveMonth,
  organizationName,
  isOperationsSelected,
  isFinancialSelected,
  onEndChat,
  onViewSummary,
  sidebarOpen = false,
}) {

  return (
    <div className={`cfo-insights-header ${sidebarOpen ? "sidebar-open" : ""}`}>
      <div className="cfo-insights-header-content">
        <div className="cfo-insights-header-row">
          <div className="cfo-insights-header-left">
            <div>
              <h1 className="cfo-insights-title">
                Financial Summary for {organizationName || "Organization"}
              </h1>
            </div>
          </div>
          <div className="flex items-center gap-1.5 sm:gap-2">
            {onViewSummary && (
            <Button
              onClick={onViewSummary}
              variant="ghost"
              size="sm"
              className="text-[#4F46E5] hover:bg-[#4F46E5]/10 transition-all duration-200 rounded-lg px-2.5 py-1.5 sm:px-3"
            >
              <FileText className="w-3.5 h-3.5 sm:w-4 sm:h-4 mr-1 sm:mr-1.5" />
              <span className="hidden sm:inline text-xs sm:text-sm">View Summary</span>
            </Button>
            )}
            <Button
              onClick={onEndChat}
              variant="ghost"
              size="sm"
              className="text-[#4F46E5] hover:bg-[#4F46E5]/10 transition-all duration-200 rounded-lg px-2.5 py-1.5 sm:px-3"
            >
              <X className="w-3.5 h-3.5 sm:w-4 sm:h-4 mr-1 sm:mr-1.5" />
              <span className="hidden sm:inline text-xs sm:text-sm">End Chat</span>
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
});
