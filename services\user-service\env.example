DB_HOST=your_db_host
DB_PORT=your_db_port
DB_NAME=your_db_name
DB_PASS=your_db_password
DB_USER=your_db_user
DB_DIALECT=postgres

# ENVIRONMENT CONFIGURATION
NODE_ENV=development

# SERVER CONFIGURATION
PORT=3003

# EMAIL
NOTIFICATION_EMAIL=your_notification_email

# SENDGRID TEMPLATE ID
FORGOT_PASSWORD_TEMPLATE_ID=your_forgot_password_template

# URLS
FRONTEND_URL=your_frontend_url
AUTH_SERVICE_URL=your_auth_service_url


# DAYS to expire password
DAYS_TO_EXPIRE_PASSWORD=your_days_to_expire_password

# LOGGING CONFIGURATION
LOG_LEVEL=info
LOG_TO_FILE=logs/user-service.log

# Comma-separated list of frontend origins (no trailing slash)
ALLOWED_ORIGINS=your_allowed_origins
