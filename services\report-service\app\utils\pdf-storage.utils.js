// app/utils/pdf-storage.utils.js
/**
 * Utility for storing generated PDFs to file-service blob storage
 */
import { httpPost } from "../../../../shared/utils/axios.util.js";
import { createLogger } from "./logger.utils.js";
import { LOGGER_NAMES } from "./constants/log.constants.js";

const logger = createLogger(LOGGER_NAMES.REPORT_CONTROLLER);

/**
 * Get file service URL from environment
 * @returns {string} File service base URL
 */
const getFileServiceUrl = () => {
  const url = process.env.FILE_SERVICE_API_URL;
  if (!url) {
    logger.warn(
      "[PDF_STORAGE] FILE_SERVICE_API_URL not configured, using default"
    );
    return "http://localhost:3005";
  }
  return url;
};

/**
 * Get file service API key from environment
 * @returns {string|null} File service API key
 */
const getFileServiceApiKey = () => {
  return process.env.FILE_SERVICE_API_KEY || null;
};

/**
 * Store PDF buffer to file-service blob storage
 * @param {Object} params - Storage parameters
 * @param {Buffer} params.pdfBuffer - PDF file buffer
 * @param {string} params.organization_id - Organization ID
 * @param {string} params.organization_name - Organization name (optional)
 * @param {string} params.service - Service type (finance, payroll, operations)
 * @param {number} params.month - Month (1-12)
 * @param {number} params.year - Year
 * @param {string} params.fileName - File name (optional)
 * @param {string} params.refreshToken - Refresh token for authentication
 * @returns {Promise<{success: boolean, data?: Object, error?: string}>}
 */
export const storePdfToFileService = async ({
  pdfBuffer,
  organization_id,
  organization_name,
  service,
  month,
  year,
  fileName,
  refreshToken,
}) => {
  const requestId = `PDF-STORE-${Date.now()}`;

  try {
    if (!pdfBuffer) {
      logger.warn(`[${requestId}] No PDF buffer provided`);
      return { success: false, error: "PDF buffer is required" };
    }

    const fileServiceUrl = getFileServiceUrl();
    const url = `${fileServiceUrl}/api/document/store-pdf`;

    logger.info(`[${requestId}] Storing PDF to file-service`, {
      url,
      organization_id,
      service,
      month,
      year,
      bufferSize: pdfBuffer.length,
    });

    // Convert buffer to base64 for JSON transport
    const base64Buffer = pdfBuffer.toString("base64");

    const payload = {
      pdfBuffer: base64Buffer,
      organization_id,
      organization_name,
      service,
      month,
      year,
      file_name: fileName,
    };

    const headers = {
      "Content-Type": "application/json",
    };

    // Add API key for file-service authentication
    const apiKey = getFileServiceApiKey();
    if (apiKey) {
      headers["x-api-key"] = apiKey;
    } else {
      logger.warn(`[${requestId}] FILE_SERVICE_API_KEY not configured`);
    }

    // Add refresh token cookie if available
    if (refreshToken) {
      headers.Cookie = `refresh_token=${refreshToken}`;
    }

    const response = await httpPost(url, payload, {
      headers,
      timeout: 60000, // 60 second timeout for large PDFs
    });

    if (response.data?.success) {
      logger.info(`[${requestId}] PDF stored successfully`, {
        blobPath: response.data?.data?.blobPath,
        blobUrl: response.data?.data?.blobUrl,
      });
      return {
        success: true,
        data: response.data?.data,
      };
    }

    logger.warn(`[${requestId}] PDF storage failed`, {
      message: response.data?.message,
    });
    return {
      success: false,
      error: response.data?.message || "Unknown error",
    };
  } catch (error) {
    logger.error(`[${requestId}] Error storing PDF to file-service`, {
      error: error.message,
      response: error.response?.data,
    });
    return {
      success: false,
      error: error.response?.data?.message || error.message,
    };
  }
};

export default {
  storePdfToFileService,
};
