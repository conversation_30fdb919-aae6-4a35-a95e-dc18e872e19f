/**
 * Format number to K notation (e.g., 125000 -> "125K")
 * @param {number} value - The number to format
 * @returns {string} Formatted string in K notation
 */
export const formatToK = (value) => {
  if (value === null || value === undefined || isNaN(value)) {
    return "0K";
  }

  const numValue = parseFloat(value);

  if (numValue === 0) {
    return "0K";
  }

  // Convert to K (thousands)
  const kValue = numValue / 1000;

  // Round to 1 decimal place if not a whole number
  if (kValue % 1 === 0) {
    return `${kValue}K`;
  }

  return `${kValue.toFixed(1)}K`;
};

/**
 * Parse numeric value safely
 * @param {*} value - Value to parse
 * @returns {number} Parsed number or 0
 */
export const parseNumericValue = (value) => {
  if (value === null || value === undefined) {
    return 0;
  }

  const parsed = parseFloat(value);
  return isNaN(parsed) ? 0 : parsed;
};

/**
 * Validate month parameter
 * @param {number|string} month - Month value (1-12)
 * @returns {boolean} True if valid
 */
export const isValidMonth = (month) => {
  const monthNum = parseInt(month, 10);
  return !isNaN(monthNum) && monthNum >= 1 && monthNum <= 12;
};

/**
 * Validate year parameter
 * @param {number|string} year - Year value
 * @returns {boolean} True if valid
 */
export const isValidYear = (year) => {
  const yearNum = parseInt(year, 10);
  return !isNaN(yearNum) && yearNum >= 2000 && yearNum <= 2100;
};

/**
 * Format number to K notation with two decimal places (e.g., 393183.45 -> "393.18K")
 * @param {number} value - The number to format
 * @returns {string} Formatted string in K notation with two decimal places
 */
export const formatToKWithDecimals = (value) => {
  if (value === null || value === undefined || isNaN(value)) {
    return "0.00K";
  }

  const numValue = parseFloat(value);

  if (numValue === 0) {
    return "0.00K";
  }

  // Convert to K (thousands)
  const kValue = numValue / 1000;

  // Format with two decimal places
  return `${kValue.toFixed(2)}K`;
};

export default {
  formatToK,
  formatToKWithDecimals,
  parseNumericValue,
  isValidMonth,
  isValidYear,
};
