import winston from "winston";
import DailyRotateFile from "winston-daily-rotate-file";
import path from "path";
import { fileURLToPath } from "url";
import { LOG_CONSTANTS } from "../utils/constants/logMessages.constants.js";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Create logs directory if it doesn't exist
const logsDir = path.join(__dirname, LOG_CONSTANTS.LOG_DIRECTORY_RELATIVE_PATH);

const logFormat = winston.format.combine(
  winston.format.timestamp({
    format: LOG_CONSTANTS.TIMESTAMP_FORMAT,
  }),
  winston.format.errors({ stack: true }),
  winston.format.json()
);

const consoleFormat = winston.format.combine(
  winston.format.colorize(),
  winston.format.timestamp({
    format: LOG_CONSTANTS.TIMESTAMP_FORMAT,
  }),
  winston.format.printf(({ timestamp, level, message, ...meta }) => {
    let log = `${timestamp} [${level}] ${message}`;
    if (Object.keys(meta).length > 0) {
      log += ` ${JSON.stringify(meta)}`;
    }
    return log;
  })
);

const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || LOG_CONSTANTS.DEFAULT_LOG_LEVEL,
  format: logFormat,
  defaultMeta: { service: LOG_CONSTANTS.SERVICE_NAME },
  transports: [
    // Console transport
    new winston.transports.Console({
      format: consoleFormat,
    }),
    
    // File transport for all logs
    new DailyRotateFile({
      filename: path.join(logsDir, LOG_CONSTANTS.GENERAL_LOG_FILENAME),
      datePattern: "YYYY-MM-DD",
      zippedArchive: true,
      maxSize: LOG_CONSTANTS.GENERAL_LOG_MAX_SIZE,
      maxFiles: LOG_CONSTANTS.GENERAL_LOG_MAX_FILES,
    }),
    
    // File transport for error logs
    new DailyRotateFile({
      filename: path.join(logsDir, LOG_CONSTANTS.ERROR_LOG_FILENAME),
      datePattern: "YYYY-MM-DD",
      zippedArchive: true,
      maxSize: LOG_CONSTANTS.ERROR_LOG_MAX_SIZE,
      maxFiles: LOG_CONSTANTS.ERROR_LOG_MAX_FILES,
      level: "error",
    }),
  ],
});

// Create logs directory
import fs from "fs";
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
}

export default logger;
