/**
 * Sidebar utility functions and constants
 */

export const MONTH_NAMES = {
  Jan: "January",
  Feb: "February",
  Mar: "March",
  Apr: "April",
  May: "May",
  Jun: "June",
  Jul: "July",
  Aug: "August",
  Sep: "September",
  Oct: "October",
  Nov: "November",
  Dec: "December",
};

export const CATEGORY_KEYS = {
  FINANCE: "Finance",
  OPERATIONS: "Operations",
  PAYROLL: "Payroll",
};

/**
 * Format month label from abbreviation to full name
 * @param {string} monthLabel - Month label in format "Jan-24" or "Jan-2024"
 * @returns {string} Formatted month label like "January 2024"
 */
export const formatMonthLabel = (monthLabel) => {
  if (!monthLabel || typeof monthLabel !== "string") return monthLabel;

  const [abbr, year] = monthLabel.split("-");
  if (!abbr || !year) return monthLabel;

  const normalizedYear = year.length === 2 ? `20${year}` : year;
  return `${MONTH_NAMES[abbr] || abbr} ${normalizedYear}`;
};

/**
 * Create a map of folder keys to their month values
 * @param {Array} reportFolders - Array of folder objects with months
 * @returns {Object} Map of folder key to array of month values
 */
export const createFolderMonthsMap = (reportFolders) => {
  return reportFolders.reduce((acc, folder) => {
    if (folder?.key) {
      const months = Array.isArray(folder.months)
        ? folder.months
            .map((month) => month?.value)
            .filter((value) => typeof value === "string")
        : [];
      acc[folder.key] = months;
    }
    return acc;
  }, {});
};

/**
 * Normalize months array from folder data
 * @param {Array} rawMonths - Raw months array from folder
 * @param {Function} formatMonthLabelFn - Function to format month labels
 * @returns {Array} Normalized months array with value and label
 */
export const normalizeMonths = (rawMonths, formatMonthLabelFn) => {
  if (!Array.isArray(rawMonths)) return [];

  return rawMonths
    .map((month) => {
      const value =
        typeof month === "string"
          ? month
          : month?.value || month?.month || null;
      if (!value) return null;
      return {
        value,
        label: month?.label || formatMonthLabelFn(value),
      };
    })
    .filter(Boolean);
};

/**
 * Parse month value string to Date object
 * Handles formats like "Jan-24", "Dec-2024", or "January 2024"
 * @param {string} monthValue - Month value string to parse
 * @param {Object} monthAbbreviationToIndex - Map of month abbreviations to indices (0-11)
 * @returns {Date} Parsed Date object, or new Date(0) if parsing fails
 */
export const parseMonthValue = (monthValue, monthAbbreviationToIndex) => {
  if (!monthValue) return new Date(0);

  // Handle format like "Jan-24" or "Dec-2024"
  if (monthValue.includes("-")) {
    const [abbr, year] = monthValue.split("-");
    const yearFull = year.length === 2 ? `20${year}` : year;
    const monthIndex = monthAbbreviationToIndex[abbr] ?? 0;
    return new Date(parseInt(yearFull), monthIndex);
  }

  // Handle format like "January 2024"
  const parsed = new Date(monthValue);
  return isNaN(parsed.getTime()) ? new Date(0) : parsed;
};
