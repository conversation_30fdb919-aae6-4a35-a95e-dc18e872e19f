"use client";

import { formatCompactCurrency } from "../../utils/methods/formatters";
import { EChartWrapper } from "./EChartWrapper";

export function AgingReceivablesChart({ data }) {
  const categories = data.periods.map((item) => item.name);
  const colors = ["#34d399", "#2f7ed8", "#ffc542", "#ff8c5a"];
  const values = data.periods.map((item, index) => ({
    value: item.value,
    itemStyle: {
      color: colors[index % colors.length],
    },
  }));

  const option = {
    color: colors,
    tooltip: {
      trigger: "axis",
      axisPointer: { type: "shadow" },
      formatter: (params) => {
        const param = Array.isArray(params) ? params[0] : params;
        return `${param.name}: ${formatCompactCurrency(param.value)}`;
      },
    },
    grid: { left: "3%", right: "4%", bottom: "3%", containLabel: true },
    xAxis: {
      type: "category",
      data: categories,
      axisLabel: { color: "#1e293b", fontWeight: 600, interval: 0 },
    },
    yAxis: {
      type: "value",
      axisLabel: { formatter: (value) => formatCompactCurrency(value), color: "#475569" },
      splitLine: { lineStyle: { color: "#e2e8f0" } },
    },
    series: [
      {
        type: "bar",
        data: values,
        barWidth: 50,
        itemStyle: { borderRadius: [0, 0, 0, 0] },
        label: {
          show: true,
          position: "top",
          formatter: ({ value }) => formatCompactCurrency(value),
        },
      },
    ],
  };

  return <EChartWrapper option={option} />;
}
