import { httpPost, httpGet } from "../../../../shared/utils/axios.util.js";
import {
  HTTP_HEADERS,
  HTTP_HEADER_VALUES,
  COOKIE_NAMES,
} from "./constants/http.constants.js";

const buildHeaders = ({ headers = {}, refreshToken = null } = {}) => {
  const finalHeaders = {
    [HTTP_HEADERS.CONTENT_TYPE]: HTTP_HEADER_VALUES.APPLICATION_JSON,
    ...headers,
  };

  if (refreshToken) {
    finalHeaders[HTTP_HEADERS.COOKIE] = `${COOKIE_NAMES.REFRESH_TOKEN}=${refreshToken}`;
  }

  return finalHeaders;
};

const buildConfig = ({ config = {}, refreshToken = null } = {}) => {
  const { headers = {}, ...rest } = config;

  return {
    headers: buildHeaders({ headers, refreshToken }),
    ...rest,
  };
};

export const postWithRefreshToken = (
  url,
  data,
  refreshToken,
  config = {}
) => httpPost(url, data, buildConfig({ config, refreshToken }));

export const getWithRefreshToken = (
  url,
  refreshToken,
  config = {}
) => httpGet(url, buildConfig({ config, refreshToken }));

export const buildJsonRequestConfig = (refreshToken = null, config = {}) =>
  buildConfig({ config, refreshToken });

