import bcrypt from "bcryptjs";
import jwt from "jsonwebtoken";
import { v4 as uuidv4 } from "uuid";
import { createLogger } from "../utils/logger.utils.js";
import {
  LOGGER_NAMES,
  LOGGER_MESSAGES,
  AUTH_LOG_ACTIONS,
} from "../utils/constants/log.constants.js";
import { HARDCODED_STRINGS } from "../utils/constants/strings.constants.js";
import { AUTH_MESSAGES } from "../utils/constants/auth.constants.js";
import { authRepository } from "../repository/auth.repository.js";
import { tokenRepository } from "../repository/token.repository.js";
import { organizationRepository } from "../repository/organization.repository.js";
// Using authRepository instead of userRepository for auth operations
import {
  createAccessToken,
  createRefreshToken,
  revokeAllUserTokens,
} from "./token.service.js";
import {
  sendEmailVerification,
} from "../utils/sendgrid-email.utils.js";
import * as status from "../utils/status_code.utils.js";

const logger = createLogger(LOGGER_NAMES.AUTH_SERVICE);

// HELPER FUNCTIONS

/**
 * Create standardized service response
 */
const createServiceResponse = (
  success,
  statusCode,
  message,
  data = null,
  error = null
) => ({
  success,
  statusCode,
  message,
  data,
  error,
});

/**
 * Handle service errors consistently
 */
const handleServiceError = (error, operation) => {
  logger.error(`Service error in ${operation}:`, { error: error.message });
  return createServiceResponse(
    false,
    status.STATUS_CODE_INTERNAL_SERVER_ERROR,
    HARDCODED_STRINGS.SERVICE_MESSAGES.INTERNAL_ERROR_OCCURRED,
    null,
    error.message
  );
};

/**
 * Validate password strength
 */
const validatePassword = async (plainPassword, hashedPassword) => {
  try {
    return await bcrypt.compare(plainPassword, hashedPassword);
  } catch (error) {
    logger.error(
      HARDCODED_STRINGS.SERVICE_MESSAGES.PASSWORD_VALIDATION_ERROR,
      error
    );
    return false;
  }
};

// AUTHENTICATION SERVICES

/**
 * Get Organization by ID Service - Fetch organization details
 */
export const getOrganizationById = async (organizationId) => {
  try {
    logger.info("Getting organization by ID", { organizationId });

    if (!organizationId) {
      logger.warn("Organization ID is required");
      return createServiceResponse(
        false,
        status.STATUS_CODE_BAD_REQUEST,
        "Organization ID is required"
      );
    }

    const organization = await organizationRepository.findOrganizationById(
      organizationId
    );

    if (!organization) {
      logger.warn("Organization not found", { organizationId });
      return createServiceResponse(
        false,
        status.STATUS_CODE_NOT_FOUND,
        "Organization not found"
      );
    }

    logger.info("Organization retrieved successfully", { organizationId });
    return createServiceResponse(
      true,
      status.STATUS_CODE_SUCCESS,
      "Organization retrieved successfully",
      organization
    );
  } catch (error) {
    return handleServiceError(error, "getOrganizationById");
  }
};

/**
 * Login Service - Complete authentication flow with MFA
 */
export const loginService = async (loginData) => {
  try {
    const { email, password, mfaCode } = loginData;

    logger.info(AUTH_LOG_ACTIONS.LOGIN_ATTEMPT, { email });

    // Check if user exists in auth-service database
    const user = await authRepository.findUserByEmail(email);
    if (!user) {
      logger.warn(AUTH_MESSAGES.USER_NOT_FOUND, { email });
      return createServiceResponse(
        false,
        status.STATUS_CODE_BAD_REQUEST,
        AUTH_MESSAGES.USER_NOT_FOUND
      );
    }

    // Debug logging to check user data
    logger.info(HARDCODED_STRINGS.SERVICE_MESSAGES.USER_FOUND_FOR_LOGIN, {
      email: user.email,
      hasPassword: !!user.password_hash,
      isActive: user.is_active,
    });

    // Check if user is active
    if (!user.is_active) {
      logger.warn(ACCOUNT_INACTIVE, { email });
      return createServiceResponse(
        false,
        status.STATUS_CODE_BAD_REQUEST,
        AUTH_MESSAGES.ACCOUNT_INACTIVE
      );
    }
    // Validate password
    const isPasswordValid = await validatePassword(
      password,
      user.password_hash
    );
    if (!isPasswordValid) {
      logger.warn(AUTH_MESSAGES.INVALID_CREDENTIALS, { email });
      return createServiceResponse(
        false,
        status.STATUS_CODE_BAD_REQUEST,
        AUTH_MESSAGES.INVALID_CREDENTIALS
      );
    }

    // Generate tokens

    // Update last login
    await authRepository.updateLastLogin(user.id);

    logger.info(AUTH_MESSAGES.LOGIN_SUCCESS, {
      userId: user.id,
      email: user.email,
    });

    let accessToken;
    let refreshToken;
    try {
      accessToken = await createAccessToken({
        userId: user.id,
        email: user.email,
        role: user.role,
      });

      refreshToken = await createRefreshToken({
        userId: user.id,
        email: user.email,
        roles: user.role,
      });
    } catch (tokenError) {
      logger.error("Failed to generate tokens", {
        error: tokenError.message,
        userId: user.id,
      });
      return createServiceResponse(
        false,
        status.STATUS_CODE_INTERNAL_SERVER_ERROR,
        "Failed to generate authentication tokens"
      );
    }

    // Store tokens in database
    try {
      // Store both access and refresh tokens in a single row
      await tokenRepository.create({
        user_id: user.id,
        access_token: accessToken,
        refresh_token: refreshToken,
        expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days (refresh token expiry)
        created_by: user.id,
      });

      logger.info("Tokens stored successfully", {
        userId: user.id,
      });
    } catch (tokenError) {
      logger.error("Failed to store tokens in database", {
        error: tokenError.message,
        userId: user.id,
      });
      // Continue with login even if token storage fails
    }

    return createServiceResponse(
      true,
      status.STATUS_CODE_SUCCESS,
      AUTH_MESSAGES.LOGIN_SUCCESS,
      {
        user: {
          id: user.id,
          email: user.email,
          name: user.full_name,
          role: user.role,
          roles: user.roles || [],
          is_active: user.is_active,
          created_at: user.created_at,
          updated_at: user.updated_at,
        },
        tokens: {
          access_token: accessToken,
          refresh_token: refreshToken,
        },
      }
    );
  } catch (error) {
    return handleServiceError(error, "loginService");
  }
};

/**
 * Logout Service - Secure logout with token revocation
 */
export const logoutService = async (userId) => {
  try {
    logger.info(AUTH_LOG_ACTIONS.LOGOUT_ATTEMPT, { userId });

    // Revoke all tokens for user
    await revokeAllUserTokens(userId);

    logger.info(AUTH_LOG_ACTIONS.LOGOUT_SUCCESS, { userId });

    return createServiceResponse(
      true,
      status.STATUS_CODE_SUCCESS,
      AUTH_MESSAGES.LOGOUT_SUCCESS
    );
  } catch (error) {
    return handleServiceError(error, "logoutService");
  }
};

/**
 * Get User Profile Service - Fetch user profile
 */
export const getUserProfileService = async (userId) => {
  try {
    const user = await authRepository.findUserById(userId);

    if (!user) {
      return createServiceResponse(
        false,
        status.STATUS_CODE_NOT_FOUND,
        USER_NOT_FOUND
      );
    }

    const { password, mfa_secret, ...userProfile } = user.toJSON();

    return createServiceResponse(
      true,
      status.STATUS_CODE_SUCCESS,
      HARDCODED_STRINGS.USER_PROFILE_RETRIEVED_SUCCESS,
      {
        user: {
          ...userProfile,
          role: {
            id: user.role?.id,
            name: user.role?.name,
            description: user.role?.description,
          },
        },
      }
    );
  } catch (error) {
    return handleServiceError(error, "getUserProfileService");
  }
};

/**
 * Update User Profile Service - Profile update business logic
 */
export const updateUserProfileService = async (updateData) => {
  try {
    const { userId, updates, requesterId } = updateData;

    // Authorization check
    if (userId !== requesterId) {
      return createServiceResponse(
        false,
        status.STATUS_CODE_FORBIDDEN,
        HARDCODED_STRINGS.YOU_CAN_ONLY_UPDATE_YOUR_OWN_PROFILE
      );
    }

    // Remove sensitive fields
    const sanitizedUpdates = { ...updates };
    delete sanitizedUpdates.password;
    delete sanitizedUpdates.mfa_secret;
    delete sanitizedUpdates.email_verified;
    delete sanitizedUpdates.role_id; // Prevent role escalation

    await authRepository.updateUser(userId, sanitizedUpdates);

    return createServiceResponse(
      true,
      status.STATUS_CODE_SUCCESS,
      HARDCODED_STRINGS.PROFILE_UPDATE_SUCCESS
    );
  } catch (error) {
    return handleServiceError(error, "updateUserProfileService");
  }
};

// TOKEN HELPER FUNCTIONS

const createEmailVerificationToken = async (userId) => {
  const emailVerificationToken =
    uuidv4() + HARDCODED_STRINGS.STRING_OPS_EXTENDED.DASH + Date.now();
  const expiresAt = new Date(
    Date.now() + HARDCODED_STRINGS.TIME_VALUES.TWENTY_FOUR_HOURS_MS
  ); // 24 hours

  const tokenData = await tokenRepository.create({
    userId,
    emailVerificationToken,
    emailVerificationTokenExpiresAt: expiresAt,
    tokenType: HARDCODED_STRINGS.TOKEN_TYPES_EXTENDED.EMAIL_VERIFICATION,
  });

  logger.info(
    HARDCODED_STRINGS.SERVICE_MESSAGES.EMAIL_VERIFICATION_TOKEN_CREATED,
    {
      userId,
      tokenId: tokenData.id,
      expiresAt,
    }
  );

  return emailVerificationToken;
};

const _validateResetToken = async (resetToken) => {
  try {
    const tokenData = await tokenRepository.find({
      resetToken,
      revoked: false,
      resetTokenExpiresAt: { $gt: new Date() },
    });

    if (!tokenData) {
      logger.warn(
        HARDCODED_STRINGS.SERVICE_MESSAGES.INVALID_OR_EXPIRED_RESET_TOKEN
      );
      return null;
    }

    return {
      tokenId: tokenData.id,
      userId: tokenData.userId,
    };
  } catch (error) {
    logger.error(
      HARDCODED_STRINGS.SERVICE_MESSAGES.ERROR_VALIDATING_RESET_TOKEN,
      { error: error.message }
    );
    return null;
  }
};
