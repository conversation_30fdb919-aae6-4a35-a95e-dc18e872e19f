import OperationsOverviewService from "./operations.overview.service.js";
import OperationsSummaryService from "./operations.summary.service.js";
import OperationsTrendsService from "./operations.trends.service.js";
import { generateOperationsHTML, generatePDF } from "../utils/pdf.utils.js";
import { createLogger } from "../utils/logger.utils.js";
import { LOGGER_NAMES } from "../utils/constants/log.constants.js";

const logger = createLogger(LOGGER_NAMES.REPORT_SERVICE);

const generateOperationsReportPDF = async ({
  organization_id,
  organization_name,
  month,
  year,
}) => {
  try {
    logger.info(
      `Fetching operations data: org=${organization_id}, month=${month}, year=${year}`
    );

    const [overview, summary, trends] = await Promise.allSettled([
      OperationsOverviewService.getOperationsOverview({
        organization_id,
        month,
        year,
      }),
      OperationsSummaryService.getOperationsSummary({
        organization_id,
        month,
        year,
      }),
      OperationsTrendsService.getOperationsTrends({
        organization_id,
        month,
        year,
      }),
    ]);

    const getData = (res) => (res.status === "fulfilled" ? res.value : null);

    const reportData = {
      organization: organization_name || "Organization",
      month,
      year,
      overview: getData(overview) || [],
      summary: getData(summary) || {},
      trends: getData(trends) || {},
    };

    logger.info("Generating PDF from aggregated operations data");
    return await generatePDF(generateOperationsHTML(reportData));
  } catch (error) {
    logger.error("Error generating operations PDF:", error);
    throw error;
  }
};

export default { generateOperationsReportPDF };
