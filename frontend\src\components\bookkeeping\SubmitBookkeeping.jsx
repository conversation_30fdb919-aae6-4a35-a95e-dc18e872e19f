"use client";

import { useState, useMemo, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useDispatch, useSelector } from "react-redux";
import Heading from "@/components/bookkeeping/layout/Heading";
import MonthSelector from "@/components/bookkeeping/layout/MonthSelector";
import ClientInfo from "@/components/bookkeeping/layout/ClientInfo";
import UploadSection from "@/components/bookkeeping/upload/UploadSection";
import ActionButtons from "@/components/bookkeeping/core/Actions";
import { useToast } from "@/components/ui/toast";
import { BOOKCLOSURE_CONSTANTS } from "@/utils/constants/bookclosure";
import {
  syncFinancialData,
  syncOperationalData,
} from "@/redux/Thunks/bookkeeping";
import { fetchOrganizations } from "@/redux/Thunks/organization";
import { getBookkeepingMonths } from "@/utils/methods/bookkeeping";
import "@/styles/bookkeeping.css";

export default function SubmitBookkeeping({ clientId }) {
  const router = useRouter();
  const dispatch = useDispatch();
  const { addToast } = useToast();

  // Redux state
  const { financial, operational } = useSelector((state) => state.bookkeeping);

  // Generate dynamic array of completed months for the current year
  const bookkeepingMonths = useMemo(() => getBookkeepingMonths(), []);

  const [selectedMonth, setSelectedMonth] = useState(
    bookkeepingMonths[0] || ""
  );
  const [uploadStates, setUploadStates] = useState({
    financial: false,
    operational: false,
    payroll: false,
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isDrafting, setIsDrafting] = useState(false);
  const [organizationName, setOrganizationName] = useState(null);
  const [clientInfo, setClientInfo] = useState(null);

  // Fetch organization data
  useEffect(() => {
    const fetchOrgData = async () => {
      try {
        const result = await dispatch(fetchOrganizations()).unwrap();
        const targetOrg = result.find(
          (org) => String(org.id) === String(clientId)
        );
        if (targetOrg) {
          const orgName = targetOrg.name ?? targetOrg.organization_name ?? null;
          setOrganizationName(orgName);
          setClientInfo({
            name: orgName,
            company: orgName,
            status: targetOrg.status || "Active",
          });
        }
      } catch (error) {
        // Error fetching organizations
      }
    };

    if (clientId) {
      fetchOrgData();
    }
  }, [dispatch, clientId]);

  // Handlers
  const handleMonthChange = (month) => setSelectedMonth(month);

  // Separate method for ADP/Payroll sync
  const handleAdpSync = (files) => {
    // Simulate loading state for UI feedback
    setUploadStates((prev) => ({ ...prev, payroll: true }));
    setTimeout(() => {
      setUploadStates((prev) => ({ ...prev, payroll: false }));
      addToast(BOOKCLOSURE_CONSTANTS.TOAST_MESSAGES.SYNC_SUCCESS, "success");
    }, 1000);
  };

  const handleSync = async (type, files) => {
    setUploadStates((prev) => ({ ...prev, [type]: true }));

    try {
      if (type === "financial") {
        await dispatch(
          syncFinancialData({
            clientId,
            month: selectedMonth,
            files: files || [],
          })
        ).unwrap();
        addToast(BOOKCLOSURE_CONSTANTS.TOAST_MESSAGES.SYNC_SUCCESS, "success");
      } else if (type === "operational") {
        await dispatch(
          syncOperationalData({
            clientId,
            month: selectedMonth,
            files: files || [],
          })
        ).unwrap();
        addToast(BOOKCLOSURE_CONSTANTS.TOAST_MESSAGES.SYNC_SUCCESS, "success");
      } else if (type === "payroll") {
        // Use the separate ADP method
        handleAdpSync(files);
        return; // Early return to avoid the finally block
      }
    } catch (error) {
      addToast(
        `${BOOKCLOSURE_CONSTANTS.TOAST_MESSAGES.SYNC_ERROR} ${error.message || ""}`,
        "error"
      );
    } finally {
      // Only reset loading state for financial and operational
      if (type !== "payroll") {
        setUploadStates((prev) => ({ ...prev, [type]: false }));
      }
    }
  };

  const handleSaveAsDraft = () => {
    setIsDrafting(true);
    setTimeout(() => {
      setIsDrafting(false);
      addToast(BOOKCLOSURE_CONSTANTS.TOAST_MESSAGES.DRAFT_SAVED, "success");
    }, 1500);
  };

  const handleCancel = () => {
    router.push("/listing");
  };

  const handleSubmitData = () => {
    setIsSubmitting(true);
    // Simulate submit
    setTimeout(() => {
      setIsSubmitting(false);
      addToast(BOOKCLOSURE_CONSTANTS.TOAST_MESSAGES.DATA_SUBMITTED, "success");
      router.push("/listing");
    }, 1500);
  };

  return (
    <div className="bookkeeping-container">
      <div className="bookkeeping-wrapper">
        <Heading onBack={() => router.push("/listing")} />
        <div className="bookkeeping-controls-row">
          <MonthSelector
            selectedMonth={selectedMonth}
            onChange={handleMonthChange}
            months={bookkeepingMonths}
          />
          <ClientInfo
            organizationName={organizationName}
            client={clientInfo}
          />
        </div>
        <UploadSection
          uploadStates={{
            ...uploadStates,
            financial: financial.loading,
            operational: operational.loading,
          }}
          id={clientId}
          selectedMonth={selectedMonth}
          onSync={handleSync}
        />
      </div>
    </div>
  );
}
