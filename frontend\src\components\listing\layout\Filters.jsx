import React from "react";
import PageTitle from "../../common/PageTitle";
import FiltersBar from "../../common/FiltersBar";
import { LISTING_CONSTANTS } from "@/utils/constants/listing";
import { useAuthContext } from "@/redux/Providers/AuthProvider";
import { ROLE_CONSTANTS } from "@/utils/constants";

export default function Filters({
  organizationsLength,
  searchTerm,
  setSearchTerm,
  statusFilter,
  setStatusFilter,
  title = LISTING_CONSTANTS.PAGE_TITLE,
  subtitle = LISTING_CONSTANTS.PAGE_SUBTITLE,
  addButtonText = LISTING_CONSTANTS.ADD_BUTTON_TEXT,
  addButtonPath,
  onAddClick,
  searchPlaceholder = LISTING_CONSTANTS.SEARCH_PLACEHOLDER,
  statusOptions = Object.values(LISTING_CONSTANTS.STATUS_FILTER),
}) {
  const filters = [
    {
      key: "status",
      value: statusFilter,
      onChange: setStatusFilter,
      options: statusOptions,
    },
  ];

  const { isAdmin } = useAuthContext();

  return (
    <PageTitle
      title={title}
      subtitle={subtitle}
      addButtonText={addButtonText}
      addButtonPath={addButtonPath}
      onAddClick={onAddClick}
      showAddButton={isAdmin}
    >
      {isAdmin && organizationsLength > 0 && (
        <FiltersBar
          searchTerm={searchTerm}
          onSearchChange={setSearchTerm}
          searchPlaceholder={searchPlaceholder}
          filters={filters}
        />
      )}
    </PageTitle>
  );
}
