import axios from "axios";
import tokenStorage from "@/lib/tokenStorage";

// Global cache for failed user IDs to prevent unnecessary requests
const failedUserIdsCache = new Set();
const MAX_CACHE_SIZE = 100; // Prevent memory leaks

// Factory to create an Axios API client with base URL and interceptors
export const api = (baseURL) => {
  const instance = axios.create({
    baseURL: baseURL,
    withCredentials: true,
    headers: {
      "Content-Type": "application/json",
    },
  });

  // Request interceptor
  instance.interceptors.request.use(
    (config) => {
      const accessToken = tokenStorage.getAccessToken();
      if (accessToken && !tokenStorage.isTokenExpired(accessToken)) {
        config.headers.Authorization = `Bearer ${accessToken}`;
      }

      // Prevent requests for known failed user IDs - reject before network call
      if (config.url?.includes("/users/") && config.method === "get") {
        const userId = config.url
          .split("/users/")[1]
          ?.split("?")[0]
          ?.split("/")[0];
        if (userId && failedUserIdsCache.has(userId)) {
          // Create a cancelled error that won't trigger network request
          const cancelledError = new Error(
            "Request cancelled - user not found"
          );
          cancelledError.isCancelled = true;
          cancelledError.config = config;
          cancelledError.response = {
            status: 404,
            data: { message: "User not found" },
          };
          return Promise.reject(cancelledError);
        }
      }

      return config;
    },
    (error) => Promise.reject(error)
  );

  // Response interceptor for handling errors
  instance.interceptors.response.use(
    (response) => response,
    (error) => {
      if (error.isCancelled) {
        return Promise.reject(error);
      }

      if (error.response?.status === 401 || error.response?.status === 403) {
        tokenStorage.clearAuthData();
        if (typeof window !== "undefined") {
          window.location.href = "/login";
        }
      }

      // Cache failed user IDs for 404s
      if (
        error.response?.status === 404 &&
        error.config?.url?.includes("/users/")
      ) {
        const userId = error.config.url.split("/users/")[1]?.split("?")[0];
        if (userId) {
          // Prevent cache from growing too large
          if (failedUserIdsCache.size >= MAX_CACHE_SIZE) {
            const firstId = failedUserIdsCache.values().next().value;
            failedUserIdsCache.delete(firstId);
          }
          failedUserIdsCache.add(userId);
        }
      }

      return Promise.reject(error);
    }
  );

  return instance;
};

// Export function to check if user ID is in failed cache
export const isFailedUserId = (userId) => {
  return userId ? failedUserIdsCache.has(userId) : false;
};

// Export function to add failed user IDs to cache
export const addFailedUserId = (userId) => {
  if (userId) {
    if (failedUserIdsCache.size >= MAX_CACHE_SIZE) {
      const firstId = failedUserIdsCache.values().next().value;
      failedUserIdsCache.delete(firstId);
    }
    failedUserIdsCache.add(userId);
  }
};

// Export function to clear failed user IDs cache
export const clearFailedUserIdsCache = () => {
  failedUserIdsCache.clear();
};

// Export function to remove a specific user ID from cache (useful when user is created/updated)
export const removeFailedUserId = (userId) => {
  if (userId) failedUserIdsCache.delete(userId);
};

export default api;
