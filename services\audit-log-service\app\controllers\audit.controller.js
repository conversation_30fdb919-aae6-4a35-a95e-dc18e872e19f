import { successResponse, errorResponse } from "../utils/response.util.js";
import { createLog } from "../services/audit.service.js";

/**
 * API Controller to create an audit log
 *
 * @param {*} req
 * @param {*} res
 */
export const createAuditLogController = async (req, res) => {
  try {
    const newLog = await createLog(req.body);
    res
      .status(201)
      .json(successResponse("Audit log created successfully", newLog));
  } catch (err) {
    res.status(500).json(errorResponse(err.message));
  }
};
