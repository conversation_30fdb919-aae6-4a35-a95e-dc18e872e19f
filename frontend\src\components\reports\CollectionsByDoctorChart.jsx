"use client";

import { formatCompactCurrency } from "../../utils/methods/formatters";
import { EChartWrapper } from "./EChartWrapper";

/**
 * Truncate long text with ellipsis
 * @param {string} text - Text to truncate
 * @param {number} maxLength - Maximum character length
 * @returns {string} Truncated text
 */
const truncateText = (text, maxLength = 12) => {
  if (!text || text.length <= maxLength) return text;
  return text.substring(0, maxLength - 1) + "…";
};

export function CollectionsByDoctorChart({ data }) {
  const categories = data.doctors.map((item) => item.name);
  const colors = ["#34d399", "#2f7ed8", "#ffc542", "#ff8c5a"];
  const values = data.doctors.map((item, index) => ({
    value: item.value,
    itemStyle: {
      color: colors[index % colors.length],
    },
  }));

  const option = {
    color: colors,
    tooltip: {
      trigger: "axis",
      axisPointer: { type: "shadow" },
      formatter: (params) => {
        const param = Array.isArray(params) ? params[0] : params;
        // Show full name in tooltip
        const fullName = categories[param.dataIndex] || param.name;
        return `${fullName}: ${formatCompactCurrency(param.value)}`;
      },
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "25%", // Increased bottom margin for rotated labels
      top: "15%",
      containLabel: true,
    },
    xAxis: {
      type: "category",
      data: categories,
      axisLabel: {
        color: "#1e293b",
        fontWeight: 600,
        interval: 0,
        fontSize: 11,
        rotate: 30, // Rotate labels 30 degrees to prevent overlap
        formatter: function (value) {
          // Truncate long names and show full name in tooltip
          return truncateText(value, 14);
        },
      },
      axisTick: {
        alignWithLabel: true,
      },
    },
    yAxis: {
      type: "value",
      axisLabel: {
        formatter: (value) => formatCompactCurrency(value),
        color: "#475569",
      },
      splitLine: { lineStyle: { color: "#e2e8f0" } },
    },
    series: [
      {
        type: "bar",
        data: values,
        barMaxWidth: 50,
        itemStyle: { borderRadius: [0, 0, 0, 0] },
        label: {
          show: true,
          position: "top",
          formatter: ({ value }) => formatCompactCurrency(value),
          fontSize: 11,
          color: "#475569",
        },
        labelLayout: {
          hideOverlap: true,
          moveOverlap: "shiftY",
        },
      },
    ],
  };

  return <EChartWrapper option={option} />;
}
