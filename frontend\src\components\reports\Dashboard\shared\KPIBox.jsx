"use client";

/**
 * KPIBox renders a compact metrics card with primary and secondary values.
 * Props:
 * - title: string
 * - amount: string | number
 * - pmValue: string (previous month)
 * - ytdValue: string (year to date)
 * - change: string (optional change indicator, e.g., "+5.2%")
 * - isPositive: boolean (optional, for styling change indicator color)
 * - color: hex string used for accent dot/border
 */
export default function KPIBox({
  title,
  amount,
  pmValue,
  ytdValue,
  change,
  isPositive,
  color = "#5B5BD6",
}) {
  return (
    <div className="h-full flex flex-col items-center justify-center text-center rounded-xl bg-white shadow-sm ring-1 ring-slate-100 p-5 lg:p-6">
      <div className="flex items-center gap-2 text-xl font-semibold text-slate-900">
        {title}
      </div>
      <div className="mt-4 text-3xl font-semibold text-slate-900 leading-tight">
        {amount}
      </div>
      <div className="-mb-2 flex flex-col text-base text-slate-700 gap-0.5">
        {pmValue !== undefined && (
          <span>
            PM: <span className="font-medium text-slate-700">{pmValue}</span>
            {change && (
              <span
                className={`ml-1 font-semibold ${
                  isPositive ? "text-green-600" : "text-red-600"
                }`}
              >
                ({change})
              </span>
            )}
          </span>
        )}
        {ytdValue !== undefined && (
          <span>
            YTD: <span className="font-medium text-slate-700">{ytdValue}</span>
          </span>
        )}
      </div>
    </div>
  );
}
