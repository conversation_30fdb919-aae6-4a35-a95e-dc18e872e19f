"use client";

import { usePathname } from "next/navigation";
import AppSidebar from "@/components/sidebar/AppSidebar";
import ProtectedRoute from "@/components/providers/ProtectedRoute";
import AdminRoute from "@/components/providers/AdminRoute";
import { ROUTES } from "@/utils/const";

export default function DashboardLayout({ children }) {
  const pathname = usePathname();
  const isCfoInsightsRoute = pathname.startsWith("/cfo-insights");

  // Don't show sidebar on login and dashboard pages
  const shouldShowSidebar =
    pathname !== ROUTES.AUTH.LOGIN &&
    pathname !== ROUTES.DASHBOARD &&
    !isCfoInsightsRoute;

  // Check if this is an admin-only route
  const isAdminRoute = pathname.startsWith(ROUTES.MASTERS.BASE);

  // Layout content
  const layoutContent = (
    <div className="min-h-screen bg-gray-50">
      {shouldShowSidebar ? (
        <div className="flex h-screen">
          <div className="h-full">
            <AppSidebar />
          </div>
          <div className="flex-1 h-full overflow-auto bg-gray-100">
            <main className="p-8">{children}</main>
          </div>
        </div>
      ) : (
        children
      )}
    </div>
  );

  // Wrap with appropriate protection based on route type
  if (isAdminRoute) {
    return <AdminRoute>{layoutContent}</AdminRoute>;
  }

  // For regular dashboard routes, just require authentication
  return <ProtectedRoute>{layoutContent}</ProtectedRoute>;
}
