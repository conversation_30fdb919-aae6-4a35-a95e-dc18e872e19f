"use client";

import ReactEcharts from "echarts-for-react";
import { formatCompactCurrency } from "@/utils/methods/formatters";

const DEFAULT_COLORS = ["#34d399", "#2f7ed8", "#ffc542", "#ff8c5a"];

/**
 * Don<PERSON><PERSON><PERSON> wraps echarts pie/donut chart.
 * Props:
 * - data: [{ name: string, value: number }]
 * - colors: string[]
 * - centerText?: string
 */
export default function DonutChart({ data = [], colors = [], centerText }) {
  const option = {
    color: colors.length ? colors : DEFAULT_COLORS,
    tooltip: {
      trigger: "item",
      formatter: (params) =>
        `${params.marker} ${params.name}: ${formatCompactCurrency(params.value)} (${params.percent}%)`,
    },
    legend: {
      show: false,
    },
    series: [
      {
        name: "Breakdown",
        type: "pie",
        center: ["50%", "50%"], // centered since legend is hidden
        radius: ["40%", "60%"], // smaller radius so outside labels fit without clipping
        avoidLabelOverlap: true,
        labelLayout: {
          hideOverlap: true,
          moveOverlap: "shiftY",
          alignTo: "edge",
          edgeDistance: 14,
        },
        itemStyle: {
          borderColor: "#fff",
          borderWidth: 0,
        },
        label: {
          show: true,
          position: "outside",
          minMargin: 12,
          formatter: (params) =>
            `${params.name}\n${formatCompactCurrency(params.value)} (${params.percent}%)`,
          overflow: "break",
        },
        labelLine: {
          show: true,
          length: 10,
          length2: 8,
          smooth: 0.2,
        },
        data,
      },
    ],
    graphic: centerText
      ? [
          {
            type: "text",
            left: "center",
            top: "center",
            style: {
              text: centerText,
              textAlign: "center",
              fill: "#0f172a",
              fontSize: 16,
              fontWeight: 600,
            },
          },
        ]
      : [],
  };

  return (
    <div className="h-72">
      <ReactEcharts style={{ height: "100%", width: "100%" }} option={option} />
    </div>
  );
}
