"use client";

import PropTypes from "prop-types";
import ProtectedRoute from "./ProtectedRoute";
import { ROLE_CONSTANTS } from "@/utils/constants";

/**
 * AdminRoute component - wrapper for admin-only routes
 * Automatically sets requiredRole to "admin"
 */
export default function AdminRoute({
  children,
  fallbackComponent = null,
  redirectTo = null, // Let ProtectedRoute handle the redirect logic
}) {
  return (
    <ProtectedRoute
      requiredRole={ROLE_CONSTANTS.ROLE_TYPES.ADMIN}
      fallbackComponent={fallbackComponent}
      redirectTo={redirectTo}
    >
      {children}
    </ProtectedRoute>
  );
}

// PropTypes validation
AdminRoute.propTypes = {
  children: PropTypes.node.isRequired,
  fallbackComponent: PropTypes.node,
  redirectTo: PropTypes.string,
};
