import { createLogger } from "../utils/logger.util.js";
import { LOGGER_NAMES } from "../utils/constants.util.js";
import { fetchAndStoreKpi } from "../utils/methods.util.js";
import { kpiRepository } from "../repositories/kpi.repository.js";
import { getKpiConfig } from "../utils/kpis.util.js";

const logger = createLogger(LOGGER_NAMES.KPIS_SERVICE);

/**
 * Generic KPI fetch and store method
 * @param {string} kpi_key - KPI key (e.g., 'account_receivables')
 * @param {string} office_id - Office ID
 * @param {string|null} start_date - Start date (optional for some KPIs)
 * @param {string|null} end_date - End date (optional for some KPIs)
 * @param {string|null} schema_name - Optional schema name for schema-aware storage
 * @returns {Promise<Array>} Array of stored records
 */
export const fetchKpi = async (
  kpi_key,
  office_id,
  start_date,
  end_date,
  schema_name = null
) => {
  const config = getKpiConfig(kpi_key);

  // Create repository function wrapper - always pass schemaName (can be null)
  const repositoryFunction = async (data) =>
    await kpiRepository.storeKpi(kpi_key, data, schema_name);

  return await fetchAndStoreKpi(
    office_id,
    start_date,
    end_date,
    config.endpoint,
    repositoryFunction,
    config.logAction.success,
    config.logAction.failed,
    logger,
    schema_name
  );
};
