import { createSelector } from "@reduxjs/toolkit";

const selectReportFoldersState = (state) => state.reportFolders || {};

export const selectReportFolders = createSelector(
  [selectReportFoldersState],
  (reportFoldersState) => ({
    folders: reportFoldersState.folders || [],
    folderMap: reportFoldersState.folderMap || {},
    monthsByFolder: reportFoldersState.monthsByFolder || {},
    filesByFolder: reportFoldersState.filesByFolder || {},
    filesLoading: reportFoldersState.filesLoading || {},
    filesError: reportFoldersState.filesError || {},
    summaries: reportFoldersState.summaries || {},
    summaryLoading: reportFoldersState.summaryLoading || {},
    summaryError: reportFoldersState.summaryError || {},
    loadingFolders: reportFoldersState.loadingFolders || false,
    foldersError: reportFoldersState.foldersError || null,
    organizationId: reportFoldersState.organizationId || null,
    organizationName: reportFoldersState.organizationName || null,
  })
);

export const selectFolders = createSelector(
  [selectReportFoldersState],
  (state) => state.folders || []
);

export const selectFolderMap = createSelector(
  [selectReportFoldersState],
  (state) => state.folderMap || {}
);

export const selectMonthsByFolder = createSelector(
  [selectReportFoldersState],
  (state) => state.monthsByFolder || {}
);

export const selectFilesByFolder = createSelector(
  [selectReportFoldersState],
  (state) => state.filesByFolder || {}
);

export const selectFilesLoading = createSelector(
  [selectReportFoldersState],
  (state) => state.filesLoading || {}
);

export const selectFilesError = createSelector(
  [selectReportFoldersState],
  (state) => state.filesError || {}
);

export const selectSummaries = createSelector(
  [selectReportFoldersState],
  (state) => state.summaries || {}
);

export const selectSummaryLoading = createSelector(
  [selectReportFoldersState],
  (state) => state.summaryLoading || {}
);

export const selectSummaryError = createSelector(
  [selectReportFoldersState],
  (state) => state.summaryError || {}
);

export const selectLoadingFolders = createSelector(
  [selectReportFoldersState],
  (state) => state.loadingFolders || false
);

export const selectFoldersError = createSelector(
  [selectReportFoldersState],
  (state) => state.foldersError || null
);

export const selectOrganizationId = createSelector(
  [selectReportFoldersState],
  (state) => state.organizationId || null
);

export const selectOrganizationName = createSelector(
  [selectReportFoldersState],
  (state) => state.organizationName || null
);
// REFACTORED: Memoized selectors for reportFolders to prevent unnecessary re-renders
