/**
 * JSON cleaning and parsing utilities
 * Handles markdown code blocks, smart quotes, and safe JSON parsing
 */

/**
 * Clean JSON string by removing markdown code blocks and fixing smart quotes
 */
export function cleanJsonString(jsonString) {
  if (!jsonString || typeof jsonString !== "string") {
    return jsonString;
  }
  return jsonString
    .replaceAll(/```json\n?/g, "")
    .replaceAll(/```\n?/g, "")
    .trim()
    .replaceAll(/[""]/g, '"')
    .replaceAll(/['']/g, "'")
    .replaceAll(/[\u2018\u2019]/g, "'")
    .replaceAll(/[\u201C\u201D]/g, '"');
}

/**
 * Safely parse JSON string with automatic cleaning and extraction
 */
export function parseJsonSafely(jsonString) {
  if (!jsonString || typeof jsonString !== "string") {
    throw new Error("Invalid JSON string: must be a non-empty string");
  }
  const cleaned = cleanJsonString(jsonString);
  try {
    return JSON.parse(cleaned);
  } catch (parseError) {
    const jsonRegex = /\{[\s\S]*\}/;
    const jsonMatch = jsonRegex.exec(cleaned);
    if (jsonMatch && jsonMatch[0]) {
      try {
        const extracted = cleanJsonString(jsonMatch[0]);
        return JSON.parse(extracted);
      } catch (extractError) {
        throw new Error(`Failed to parse JSON: ${parseError.message}`);
      }
    }
    throw new Error(`Failed to parse JSON: ${parseError.message}`);
  }
}

