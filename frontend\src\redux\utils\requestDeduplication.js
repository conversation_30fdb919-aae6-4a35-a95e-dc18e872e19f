// redux/utils/requestDeduplication.js
/**
 * Request deduplication utility for Redux
 * REFACTORED: Converted from React hook to Redux utility (no hooks)
 * Prevents the same request from being made multiple times within a time window
 */

// Cache for pending requests
const requestCache = new Map();
const timeouts = new Map();

/**
 * Create a deduplicated request
 * @param {string} key - Unique key for the request
 * @param {Function} requestFn - Async function that makes the request
 * @param {number} timeWindow - Time window in milliseconds (default: 1000ms)
 * @returns {Promise} Request promise (cached if already in progress)
 */
export const makeDeduplicatedRequest = async (
  key,
  requestFn,
  timeWindow = 1000
) => {
  // Check if request is already in progress
  if (requestCache.has(key)) {
    return requestCache.get(key);
  }

  // Create the request promise
  const requestPromise = (async () => {
    try {
      const result = await requestFn();
      return result;
    } finally {
      // Clear from cache after time window
      const timeout = setTimeout(() => {
        requestCache.delete(key);
        timeouts.delete(key);
      }, timeWindow);

      timeouts.set(key, timeout);
    }
  })();

  // Store in cache
  requestCache.set(key, requestPromise);

  return requestPromise;
};

/**
 * Clear cached request(s)
 * @param {string} key - Key to clear (optional, clears all if not provided)
 */
export const clearRequestCache = (key) => {
  if (key) {
    requestCache.delete(key);
    const timeout = timeouts.get(key);
    if (timeout) {
      clearTimeout(timeout);
      timeouts.delete(key);
    }
  } else {
    // Clear all
    requestCache.clear();
    timeouts.forEach((timeout) => clearTimeout(timeout));
    timeouts.clear();
  }
};

/**
 * Get current cache size
 * @returns {number} Number of pending requests in cache
 */
export const getCacheSize = () => requestCache.size;
