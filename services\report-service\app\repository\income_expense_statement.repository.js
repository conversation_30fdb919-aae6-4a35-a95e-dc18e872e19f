import { sequelize } from "../models/index.js";
import models from "../models/index.js";
import { LOGGER_NAMES } from "../utils/constants/log.constants.js";
import { createLogger } from "../utils/logger.utils.js";
import {
  getOrganizationSchemaName,
  getReportId,
  getMonthDateRange,
} from "../utils/repository.utils.js";

const logger = createLogger(LOGGER_NAMES.REPORT_REPOSITORY);

/**
 * Get P&L report ID for a specific month and year
 * @param {string} schemaName - Organization schema name
 * @param {number} month - Month (1-12)
 * @param {number} year - Year
 * @returns {Promise<string|null>} Report ID or null
 */
const getPnlReportId = async (schemaName, month, year) => {
  return await getReportId(schemaName, "qb_pnl_reports", month, year, "P&L");
};

/**
 * Get P&L report raw field for a specific month and year
 * @param {string} schemaName - Organization schema name
 * @param {number} month - Month (1-12)
 * @param {number} year - Year
 * @returns {Promise<Object|null>} Report raw field or null
 */
const getPNLReport = async (schemaName, month, year) => {
  try {
    logger.info(
      `Fetching P&L report raw field from schema: ${schemaName} for month: ${month}, year: ${year}`
    );

    const { startDate, endDate } = getMonthDateRange(month, year);

    const query = `
      SELECT raw
      FROM "${schemaName}".qb_pnl_reports
      WHERE start_date >= :startDate AND end_date <= :endDate
      LIMIT 1
    `;

    const results = await sequelize.query(query, {
      replacements: { startDate, endDate },
      type: sequelize.QueryTypes.SELECT,
    });

    if (results.length === 0) {
      logger.info(
        `No P&L report found for schema: ${schemaName}, month: ${month}, year: ${year}`
      );
      return null;
    }

    logger.info(`Found P&L report raw field for schema: ${schemaName}`);
    return results[0].raw;
  } catch (error) {
    logger.error(`Error getting P&L report raw field:`, error);
    throw error;
  }
};
/**
 * Get P&L report raw field for a specific month and year
 * @param {string} schemaName - Organization schema name
 * @param {number} month - Month (1-12)
 * @param {number} year - Year
 * @returns {Promise<Object|null>} Report raw field or null
 */
const getBalanceSheetReport = async (schemaName, month, year) => {
  try {
    logger.info(
      `Fetching P&L report raw field from schema: ${schemaName} for month: ${month}, year: ${year}`
    );

    const { startDate, endDate } = getMonthDateRange(month, year);

    const query = `
      SELECT raw
      FROM "${schemaName}".qb_balance_sheet_reports
      WHERE start_date >= :startDate AND end_date <= :endDate
      LIMIT 1
    `;

    const results = await sequelize.query(query, {
      replacements: { startDate, endDate },
      type: sequelize.QueryTypes.SELECT,
    });

    if (results.length === 0) {
      logger.info(
        `No P&L report found for schema: ${schemaName}, month: ${month}, year: ${year}`
      );
      return null;
    }

    logger.info(`Found P&L report raw field for schema: ${schemaName}`);
    return results[0].raw;
  } catch (error) {
    logger.error(`Error getting P&L report raw field:`, error);
    throw error;
  }
};

/**
 * Get income and expense line items for a report
 * @param {string} schemaName - Organization schema name
 * @param {string} reportId - Report ID
 * @returns {Promise<Array>} P&L line items with path and amount details
 */
const getIncomeExpenseLineItems = async (schemaName, reportId) => {
  try {
    logger.info(
      `Fetching income and expense line items from schema: ${schemaName} for report ID: ${reportId}`
    );

    const query = `
      SELECT
        id,
        path,
        amount,
        account_name
      FROM "${schemaName}".qb_pnl_lines
      WHERE report_id = :reportId
      ORDER BY path
    `;

    const results = await sequelize.query(query, {
      replacements: { reportId },
      type: sequelize.QueryTypes.SELECT,
    });

    logger.info(
      `Retrieved ${results.length} income and expense line items from schema: ${schemaName}`
    );

    return results;
  } catch (error) {
    logger.error(
      `Error in IncomeExpenseStatementRepository.getIncomeExpenseLineItems:`,
      error
    );
    throw error;
  }
};

export default {
  getOrganizationSchemaName,
  getPnlReportId,
  getPNLReport,
  getIncomeExpenseLineItems,
  getBalanceSheetReport,
};
