import KpiService from "../services/kpi.service.js";
import { LOGGER_NAMES } from "../utils/constants/log.constants.js";
import { createLogger } from "../utils/logger.utils.js";
import { validateRequiredParams, handleControllerError, sendSuccessResponse } from "../utils/controller.utils.js";

const logger = createLogger(LOGGER_NAMES.REPORT_CONTROLLER);

/**
 * Get KPI data for organization by month and year
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getKpis = async (req, res) => {
  try {
    const { organization_id, month, year } = req.query;

    logger.info(
      `Fetching KPI data for org: ${organization_id}, month: ${month}, year: ${year}`
    );

    // Validate required parameters
    const validationError = validateRequiredParams(req.query, ['organization_id', 'month', 'year']);
    if (validationError) {
      return res.status(400).json(validationError);
    }

    // Fetch KPI data
    const kpiData = await KpiService.getKpiData({
      organization_id,
      month,
      year,
    });

    // Return successful response
    sendSuccessResponse(res, "KPIs fetched successfully", kpiData);
  } catch (error) {
    logger.error("Error fetching KPIs:", error);
    handleControllerError(error, res, "Error fetching KPI data");
  }
};

export default {
  getKpis,
};
