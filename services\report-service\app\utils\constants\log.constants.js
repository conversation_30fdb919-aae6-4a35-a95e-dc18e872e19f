// LOG CONSTANTS - Logger Messages and Names for Report Service

// Log Levels
export const LOG_LEVELS = {
  ERROR: "error",
  WARN: "warn",
  INFO: "info",
  HTTP: "http",
  VERBOSE: "verbose",
  DEBUG: "debug",
  SILLY: "silly",
};

// Log Formats
export const LOG_FORMATS = {
  COMBINED: "combined",
  COMMON: "common",
  DEV: "dev",
  SHORT: "short",
  TINY: "tiny",
  JSON: "json",
};

// Logger Names Constants
export const LOGGER_NAMES = {
  // Controllers
  REPORT_CONTROLLER: "ReportController",

  // Services
  REPORT_SERVICE: "ReportService",

  // Middleware
  AUTH_MIDDLEWARE: "AuthMiddleware",
  VALIDATION_MIDDLEWARE: "ValidationMiddleware",
  RATELIMIT_MIDDLEWARE: "RateLimitMiddleware",

  // Repositories
  REPORT_REPOSITORY: "ReportRepository",

  // Configuration
  POSTGRES_CONFIG: "PostgresConfig",
  APP_CONFIG: "AppConfig",

  // System
  SERVER: "Server",
  DATABASE: "Database",

  // Log File Types
  LOG_FILE_TYPES: {
    APPLICATION: "application",
    ERROR: "error",
    EXCEPTIONS: "exceptions",
    REJECTIONS: "rejections",
  },

  // Service Names
  SERVICE_NAMES: {
    REPORT_SERVICE: "report-service",
  },
};

// REPORT SERVICE LOGGER MESSAGES
export const LOGGER_MESSAGES = {
  // Server Messages
  SERVER: {
    START: "Report service server started on port: %s",
    STOP: "Report service server stopped",
    ERROR: "Server error: %s",
  },

  // Database Messages
  DATABASE: {
    CONNECT_SUCCESS: "Successfully connected to database",
    CONNECT_ERROR: "Database connection error: %s",
    QUERY_ERROR: "Database query error: %s",
    DISCONNECT: "Database disconnected",
  },

  // Error Messages
  ERROR: {
    INTERNAL: "Internal server error: %s",
    VALIDATION: "Validation error: %s",
    NOT_FOUND: "Resource not found: %s",

    // Server Errors
    SERVER: {
      REPORT_SERVICE_ERROR: "Report Service Error: %s",
      DATABASE_INITIALIZATION_ERROR: "Database initialization error for %s:",
      SHUTDOWN_ERROR: "Error during shutdown for %s:",
    },
  },

  // Common Messages
  COMMON: {
    MISSING_ENV_VARS: "Missing required environment variables:",
    ENV_VARS_VALIDATED: "Environment variables validated successfully",
    CONFIG_INITIALIZED: "Configuration initialized successfully",
    CONFIG_INIT_FAILED: "Configuration initialization failed:",
  },
};

export default {
  LOG_LEVELS,
  LOG_FORMATS,
  LOGGER_NAMES,
  LOGGER_MESSAGES,
};
