import { LOGGER_NAMES } from "../utils/constants/log.constants.js";
import { createLogger } from "../utils/logger.utils.js";
import {
  validateRequiredParams,
  handleControllerError,
  sendSuccessResponse,
} from "../utils/controller.utils.js";
import OperationsOverviewService from "../services/operations.overview.service.js";
import OperationsSummaryService from "../services/operations.summary.service.js";
import OperationsTrendsService from "../services/operations.trends.service.js";

const logger = createLogger(LOGGER_NAMES.REPORT_CONTROLLER);

/**
 * Get operations overview data for organization by month and year
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getOperationsOverview = async (req, res) => {
  try {
    const { organization_id, month, year } = req.query;

    logger.info(
      `Fetching operations overview data for org: ${organization_id}, month: ${month}, year: ${year}`
    );

    // Validate required parameters
    const validationError = validateRequiredParams(req.query, [
      "organization_id",
      "month",
      "year",
    ]);
    if (validationError) {
      return res.status(400).json(validationError);
    }

    // Fetch operations overview data
    const overviewData = await OperationsOverviewService.getOperationsOverview({
      organization_id,
      month,
      year,
    });

    // Return successful response
    sendSuccessResponse(
      res,
      "Operations overview fetched successfully",
      overviewData
    );
  } catch (error) {
    logger.error("Error fetching operations overview:", error);
    handleControllerError(
      error,
      res,
      "Error fetching operations overview data"
    );
  }
};

/**
 * Get operations summary data for organization by month and year
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getOperationsSummary = async (req, res) => {
  try {
    const { organization_id, month, year } = req.query;

    logger.info(
      `Fetching operations summary data for org: ${organization_id}, month: ${month}, year: ${year}`
    );

    // Validate required parameters
    const validationError = validateRequiredParams(req.query, [
      "organization_id",
      "month",
      "year",
    ]);
    if (validationError) {
      return res.status(400).json(validationError);
    }

    // Fetch operations summary data
    const summaryData = await OperationsSummaryService.getOperationsSummary({
      organization_id,
      month,
      year,
    });

    // Return successful response
    sendSuccessResponse(
      res,
      "Operations summary fetched successfully",
      summaryData
    );
  } catch (error) {
    logger.error("Error fetching operations summary:", error);
    handleControllerError(error, res, "Error fetching operations summary data");
  }
};

/**
 * Get operations trends data for organization by month and year
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getOperationsTrends = async (req, res) => {
  try {
    const { organization_id, month, year } = req.query;

    logger.info(
      `Fetching operations trends data for org: ${organization_id}, month: ${month}, year: ${year}`
    );

    // Validate required parameters
    const validationError = validateRequiredParams(req.query, [
      "organization_id",
      "month",
      "year",
    ]);
    if (validationError) {
      return res.status(400).json(validationError);
    }

    // Fetch operations trends data
    const trendsData = await OperationsTrendsService.getOperationsTrends({
      organization_id,
      month,
      year,
    });

    // Return successful response
    sendSuccessResponse(
      res,
      "Operations trends fetched successfully",
      trendsData
    );
  } catch (error) {
    logger.error("Error fetching operations trends:", error);
    handleControllerError(error, res, "Error fetching operations trends data");
  }
};

/**
 * Calculate combined operations KPIs by aggregating overview, summary and trends
 * using the same query parameters as the other operations endpoints.
 *
 * Expected query params: organization_id, month, year
 *
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const calculateKpis = async (req, res) => {
  try {
    const { organization_id, month, year } = req.query;

    logger.info(
      `Calculating operations KPIs for org: ${organization_id}, month: ${month}, year: ${year}`
    );

    // Validate required parameters on the query object
    const validationError = validateRequiredParams(req.query, [
      "organization_id",
      "month",
      "year",
    ]);
    if (validationError) {
      return res.status(400).json(validationError);
    }

    const payload = { organization_id, month, year };

    // Call all three underlying services in parallel with the same payload
    const [overviewData, summaryData, trendsData] = await Promise.all([
      OperationsOverviewService.getOperationsOverview(payload),
      OperationsSummaryService.getOperationsSummary(payload),
      OperationsTrendsService.getOperationsTrends(payload),
    ]);

    const combinedData = {
      overview: overviewData,
      summary: summaryData,
      trends: trendsData,
    };

    sendSuccessResponse(
      res,
      "Operations KPIs calculated successfully",
      combinedData
    );
  } catch (error) {
    logger.error("Error calculating operations KPIs:", error);
    handleControllerError(error, res, "Error calculating operations KPIs");
  }
};

export default {
  getOperationsOverview,
  getOperationsSummary,
  getOperationsTrends,
  calculateKpis,
};
