# Server Configuration
NODE_ENV=development
ADP_SERVICE_PORT=3006
LOG_LEVEL=info

# Database Configuration
DB_HOST=your_db_host
DB_PORT=your_db_port
DB_NAME=your_db_name
DB_USERNAME=your_db_username
DB_PASS=your_db_password
DB_USER=your_db_user
DB_DIALECT=postgres

# CORS Configuration
ALLOWED_ORIGINS=your_allowed_origins

SENDGRID_FROM_EMAIL=your_sendgrid_email

# ADP API Configuration (for future use)
ADP_CLIENT_ID=your_adp_client_id
ADP_CLIENT_SECRET=your_adp_client_secret
ADP_API_BASE_URL=your_adp_api_base_url
ADP_API_VERSION=v1

# Logging Configuration
LOG_TO_FILE=true
LOG_TO_CONSOLE=true

# Common
NODE_ENV=production
