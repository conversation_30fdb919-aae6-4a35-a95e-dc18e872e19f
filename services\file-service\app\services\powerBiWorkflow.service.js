import https from "node:https";
import logger from "../../config/logger.config.js";
import { STATUS_CODE_OK } from "../utils/status_code.utils.js";
import {
  createSuccessServiceResponse,
  createInternalServerErrorResponse,
} from "../utils/serviceResponse.util.js";
import { getCachedEnv } from "../utils/env.util.js";
import { sanitizeString } from "../utils/normalizers.util.js";
import {
  POWERBI_MESSAGES,
  POWERBI_LOG_MESSAGES,
  POWERBI_CONFIG,
  SERVICE_WORKFLOW_URL_MAP,
  TIMEOUT_DETECTION,
} from "../utils/constants/powerBiWorkflow.constants.js";
import { getSystemApiKey } from "../utils/env.util.js";
import { executePowerBiRequest } from "../utils/powerBiRequest.util.js";

// TLS configuration: allow disabling certificate verification via env for testing only
const ALLOW_INSECURE_TLS = process.env.POWER_BI_ALLOW_INSECURE_TLS === "true";
// Reusable HTTPS agent; by default we verify certificates
const httpsAgent = new https.Agent({
  rejectUnauthorized: !ALLOW_INSECURE_TLS,
});

const buildWorkflowUrlFromEnv = (service) => {
  const workflowUrlKey = SERVICE_WORKFLOW_URL_MAP[service];
  if (!workflowUrlKey) return null;

  try {
    const url = sanitizeString(getCachedEnv(workflowUrlKey));
    return url || null;
  } catch (_error) {
    return null;
  }
};

const getWorkflowApiKey = () => {
  const apiKey = sanitizeString(getSystemApiKey());

  if (!apiKey) {
    throw new Error(
      POWERBI_MESSAGES.WORKFLOW_TRIGGER_FAILED(
        "Power BI workflow API key is not configured"
      )
    );
  }

  return apiKey;
};

/**
 * Trigger Power BI workflow
 * @param {Object} payload - Workflow payload
 * @param {string} payload.service - Service name (Finance, Operations, or Payroll)
 * @param {string} payload.organization_id - Organization ID
 * @param {string} payload.organization_name - Organization name
 * @param {number} payload.month - Month number (1-12)
 * @param {number} payload.year - Year
 * @param {string} payload.monthYear - Month and year format (e.g., "Aug-2025")
 * @param {string} payload.file_name - File name
 * @param {number} payload.file_size - File size in bytes
 * @param {string} payload.mime_type - MIME type
 * @returns {Object} Service response
 */
export const triggerPowerBiWorkflow = async (payload) => {
  try {
    const serviceWorkflowUrl = buildWorkflowUrlFromEnv(payload.service);

    let fullUrl = sanitizeString(serviceWorkflowUrl);

    if (!fullUrl) {
      throw new Error(POWERBI_MESSAGES.WORKFLOW_TRIGGER_FAILED("Missing workflow configuration"));
    }

    const sanitizedApiKey = getWorkflowApiKey();

    logger.info(POWERBI_LOG_MESSAGES.TRIGGERING_WORKFLOW(payload.service), {
      url: fullUrl,
      hasApiKey: !!sanitizedApiKey,
      payload,
    });

    // Log equivalent curl command for easier debugging (gate output with debug flag & mask in prod)
    try {
      // Properly escape JSON for curl command - use double quotes for JSON data to handle apostrophes safely
      const jsonData = JSON.stringify(payload);
      // Escape double quotes and backslashes for shell safety
      const escapedData = jsonData.replace(/\\/g, '\\\\').replace(/"/g, '\\"');
      const shouldMask =
        process.env.NODE_ENV === "production" ||
        process.env.MASK_POWERBI_APIKEY === "true";
      const maskApiKey = (key) =>
        key && shouldMask ? key.replace(/.(?=.{4})/g, "*") : key;
      const loggedApiKey = maskApiKey(sanitizedApiKey);
      // Use double quotes for JSON data to safely handle apostrophes and special characters
      const curlCmd = `curl -X POST '${fullUrl}' -H 'Content-Type: application/json' -H 'x-api-key: ${loggedApiKey}' -d "${escapedData}"`;
      logger.info(`Equivalent curl command for Power BI workflow: ${curlCmd}`);

        console.log("[PowerBI] Equivalent curl command:", curlCmd);
    
    } catch (e) {
      logger.warn("Failed to build curl debug string", { error: e.message });
    }

    // Small retry wrapper with exponential backoff to handle transient errors
    const maxRetries = parseInt(process.env.POWERBI_MAX_RETRIES || "2", 10);
    const baseDelayMs = parseInt(
      process.env.POWERBI_RETRY_BASE_DELAY_MS || "300",
      10
    );

    const response = await executePowerBiRequest({
      url: fullUrl,
      payload,
      apiKey: sanitizedApiKey,
      timeout: POWERBI_CONFIG.TIMEOUT,
      httpsAgent,
      maxRetries,
      baseDelayMs,
      onRetry: async ({ attempt, maxRetries: totalRetries, delay, error }) => {
        logger.warn(
          `Power BI request failed, retrying attempt ${attempt}/${totalRetries} after ${delay}ms`,
          { error: error.message, url: fullUrl }
        );
        await new Promise((resolve) => setTimeout(resolve, delay));
      },
    });

    const { status, data: responseData } = response;

    logger.info(
      POWERBI_LOG_MESSAGES.WORKFLOW_TRIGGERED_SUCCESS(payload.service),
      {
        status,
        responseData,
      }
    );

    if (POWERBI_CONFIG.ACCEPTED_STATUS_CODES.includes(status)) {
      return createSuccessServiceResponse(
        STATUS_CODE_OK,
        POWERBI_MESSAGES.WORKFLOW_TRIGGERED_SUCCESS(payload.service),
        responseData
      );
    }

    logger.warn(POWERBI_LOG_MESSAGES.UNEXPECTED_STATUS_CODE(status), {
      responseData,
    });
    return createSuccessServiceResponse(
      status,
      POWERBI_MESSAGES.WORKFLOW_TRIGGERED_WITH_STATUS(payload.service, status),
      responseData
    );
  } catch (error) {
    let fullUrl = null;
    try {
      const svcKey = payload.service ? payload.service.toUpperCase() : "";
      fullUrl = buildWorkflowUrlFromEnv(payload.service);

    } catch {
      fullUrl = null;
    }

    // Detect timeout errors
    const errorCode = error.code?.toUpperCase();
    const errorMessage = error.message?.toLowerCase() || "";
    
    const isTimeout =
      TIMEOUT_DETECTION.ERROR_CODES.includes(errorCode) ||
      TIMEOUT_DETECTION.KEYWORDS.some((keyword) =>
        errorMessage.includes(keyword.toLowerCase())
      ) ||
      (error.config?.timeout && !error.response && error.request);

    if (isTimeout) {
      // Log timeout internally for debugging
      logger.warn(POWERBI_LOG_MESSAGES.TIMEOUT_DETECTED, {
        errorMessage: error.message,
        errorCode: error.code,
        url: fullUrl,
        payload,
        timeout: error.config?.timeout,
      });

      // Return success response with processing message for timeout
      return createSuccessServiceResponse(
        STATUS_CODE_OK,
        POWERBI_MESSAGES.TIMEOUT_SUCCESS,
        null
      );
    }

    if (error.response) {
      const { status, data } = error.response;
      const errorMessage =
        data?.message || data?.error?.message || error.message;

      logger.error(POWERBI_LOG_MESSAGES.API_ERROR(status), {
        status,
        responseData: data,
        errorMessage,
        url: fullUrl,
        payload,
      });

      return createInternalServerErrorResponse(
        POWERBI_MESSAGES.INTERNAL_ERROR,
        {
          status,
          responseData: data,
          errorMessage,
        }
      );
    }

    if (error.request) {
      logger.error(POWERBI_LOG_MESSAGES.REQUEST_FAILED, {
        errorMessage: error.message,
        url: fullUrl,
        payload,
      });

      return createInternalServerErrorResponse(
        POWERBI_MESSAGES.INTERNAL_ERROR,
        {
          errorMessage: error.message,
          url: fullUrl,
        }
      );
    }

    logger.error(POWERBI_LOG_MESSAGES.SETUP_ERROR, {
      errorMessage: error.message,
      url: fullUrl,
      payload,
    });

    return createInternalServerErrorResponse(
      POWERBI_MESSAGES.INTERNAL_ERROR,
      {
        errorMessage: error.message,
      }
    );
  }
};
