"use client";

import { Suspense } from "react";
import Dashboard from "@/components/dashboard/Dashboard";
import { SidebarProvider } from "@/contexts/SidebarContext";

export default function DashboardPage() {
  return (
    <SidebarProvider>
      <div className="min-h-screen bg-gray-100">
        <Suspense
          fallback={
            <div className="flex items-center justify-center h-screen">
              <div className="text-center">
                <div className="animate-spin rounded-full h-16 w-16 border-4 border-blue-500 border-t-transparent mx-auto mb-4"></div>
                <h3 className="text-lg font-semibold text-gray-800 mb-2">
                  Loading Dashboard
                </h3>
                <p className="text-gray-500">
                  Please wait while we load your dashboard...
                </p>
              </div>
            </div>
          }
        >
          <Dashboard />
        </Suspense>
      </div>
    </SidebarProvider>
  );
}
