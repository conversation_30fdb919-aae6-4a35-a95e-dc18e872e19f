import { cookies } from "next/headers";

/**
 * Server-side authentication utilities for Next.js App Router
 * These functions can be used in Server Components, API routes, and middleware
 */

/**
 * Decode JWT token payload (server-side safe)
 * @param {string} token - JWT token
 * @returns {Object|null} Decoded payload or null
 */
export function decodeTokenServer(token) {
  if (!token || typeof token !== "string") return null;

  try {
    const parts = token.split(".");
    if (parts.length !== 3) {
      return null;
    }

    // Decode the payload (middle part)
    const payload = JSON.parse(Buffer.from(parts[1], "base64").toString());
    return payload;
  } catch (_error) {
    return null;
  }
}

/**
 * Check if token is expired (server-side)
 * @param {Object} payload - Decoded token payload
 * @returns {boolean} True if token is expired
 */
export function isTokenExpiredServer(payload) {
  if (!payload || typeof payload.exp !== "number") return true;

  const currentTime = Math.floor(Date.now() / 1000);
  return payload.exp < currentTime;
}

/**
 * Get access token from server-side cookies
 * @returns {string|null} Access token or null
 */
export function getAccessTokenServer() {
  try {
    const cookieStore = cookies();
    const accessToken = cookieStore.get("accessToken");
    return accessToken?.value || null;
  } catch (_error) {
    return null;
  }
}

/**
 * Get refresh token from server-side cookies
 * @returns {string|null} Refresh token or null
 */
export function getRefreshTokenServer() {
  try {
    const cookieStore = cookies();
    const refreshToken = cookieStore.get("refreshToken");
    return refreshToken?.value || null;
  } catch (_error) {
    return null;
  }
}

/**
 * Validate access token and return user data (server-side)
 * @returns {Object} Authentication status and user data
 */
export function validateAuthServer() {
  const accessToken = getAccessTokenServer();

  if (!accessToken) {
    return {
      isAuthenticated: false,
      user: null,
      error: "No access token found",
    };
  }

  const payload = decodeTokenServer(accessToken);

  if (!payload) {
    return {
      isAuthenticated: false,
      user: null,
      error: "Invalid token format",
    };
  }

  if (isTokenExpiredServer(payload)) {
    return {
      isAuthenticated: false,
      user: null,
      error: "Token expired",
    };
  }

  // Extract user data from token payload
  const user = {
    id: payload.userId,
    email: payload.email,
    role: payload.roles && payload.roles.length > 0 ? payload.roles[0] : "user",
    roles: payload.roles || [],
    organization_id: payload.organization_id,
    schema_name: payload.schema_name,
    iat: payload.iat,
    exp: payload.exp,
  };

  return {
    isAuthenticated: true,
    user,
    error: null,
  };
}

/**
 * Check if user has required role (server-side)
 * @param {string} requiredRole - Required role
 * @returns {Object} Authorization status
 */
export function checkRoleServer(requiredRole) {
  const authResult = validateAuthServer();

  if (!authResult.isAuthenticated) {
    return {
      hasAccess: false,
      user: null,
      error: authResult.error,
    };
  }

  const userRole = authResult.user.role;

  if (requiredRole && userRole !== requiredRole) {
    return {
      hasAccess: false,
      user: authResult.user,
      error: `Required role: ${requiredRole}, user role: ${userRole}`,
    };
  }

  return {
    hasAccess: true,
    user: authResult.user,
    error: null,
  };
}

/**
 * Check if user has any of the required roles (server-side)
 * @param {string[]} requiredRoles - Array of required roles
 * @returns {Object} Authorization status
 */
export function checkRolesServer(requiredRoles) {
  const authResult = validateAuthServer();

  if (!authResult.isAuthenticated) {
    return {
      hasAccess: false,
      user: null,
      error: authResult.error,
    };
  }

  const userRoles = authResult.user.roles || [authResult.user.role];
  const hasRequiredRole = requiredRoles.some((role) =>
    userRoles.includes(role)
  );

  if (!hasRequiredRole) {
    return {
      hasAccess: false,
      user: authResult.user,
      error: `Required roles: ${requiredRoles.join(", ")}, user roles: ${userRoles.join(", ")}`,
    };
  }

  return {
    hasAccess: true,
    user: authResult.user,
    error: null,
  };
}

/**
 * Get redirect URL based on user role (server-side)
 * @param {Object} user - User object
 * @returns {string} Redirect URL
 */
export function getRedirectUrlByRoleServer(user) {
  if (!user) return "/login";

  switch (user.role) {
    case "admin":
      return "/listing";
    case "user":
    default:
      return "/dashboard";
  }
}

/**
 * Server-side authentication guard for pages
 * Use this in Server Components to protect pages
 * @param {string} requiredRole - Optional required role
 * @returns {Object} Authentication result with redirect info
 */
export function authGuardServer(requiredRole = null) {
  const authResult = validateAuthServer();

  if (!authResult.isAuthenticated) {
    return {
      isAuthenticated: false,
      user: null,
      shouldRedirect: true,
      redirectTo: "/login",
      error: authResult.error,
    };
  }

  // Check role if required
  if (requiredRole) {
    const roleCheck = checkRoleServer(requiredRole);
    if (!roleCheck.hasAccess) {
      const redirectUrl = getRedirectUrlByRoleServer(authResult.user);
      return {
        isAuthenticated: true,
        user: authResult.user,
        shouldRedirect: true,
        redirectTo: redirectUrl,
        error: roleCheck.error,
      };
    }
  }

  return {
    isAuthenticated: true,
    user: authResult.user,
    shouldRedirect: false,
    redirectTo: null,
    error: null,
  };
}

/**
 * Extract user data from request headers (set by middleware)
 * @param {Headers} headers - Request headers
 * @returns {Object|null} User data or null
 */
export function getUserFromHeaders(headers) {
  try {
    const userId = headers.get("x-user-id");
    const userRole = headers.get("x-user-role");
    const userEmail = headers.get("x-user-email");

    if (!userId || !userRole || !userEmail) {
      return null;
    }

    return {
      id: userId,
      role: userRole,
      email: userEmail,
    };
  } catch (_error) {
    return null;
  }
}
