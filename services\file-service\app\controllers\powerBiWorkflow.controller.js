// app/controllers/powerBiWorkflow.controller.js
import { triggerPowerBiWorkflow } from "../services/powerBiWorkflow.service.js";
import {
  handleServiceResponse,
  handleControllerError,
  validateRequiredFields,
} from "../utils/controllerHandler.util.js";
import {
  STATUS_CODE_BAD_REQUEST,
  STATUS_CODE_INTERNAL_SERVER_ERROR,
} from "../utils/status_code.utils.js";
import { errorResponse } from "../utils/response.util.js";
import { getCachedEnv } from "../utils/env.util.js";
import { sanitizeString } from "../utils/normalizers.util.js";
import {
  VALID_SERVICES,
  REQUIRED_FIELDS,
  POWERBI_MESSAGES,
  POWERBI_LOG_MESSAGES,
  SERVICE_WORKFLOW_URL_MAP,
} from "../utils/constants/powerBiWorkflow.constants.js";

/**
 * Trigger Power BI Workflow
 * POST /api/powerbi/trigger
 *
 * @param {Object} req - Express request object
 * @param {Object} req.body - Request body
 * @param {string} req.body.organization_id - Organization ID (required)
 * @param {string} req.body.organization_name - Organization name (required)
 * @param {string} req.body.service - Service name: Finance, Operations, or Payroll (required)
 * @param {number} req.body.month - Month number (1-12) (required)
 * @param {number} req.body.year - Year (required)
 * @param {string} req.body.monthYear - Month and year format (e.g., "Aug-2025") (required)
 * @param {string} req.body.file_name - File name (required)
 * @param {number} req.body.file_size - File size in bytes (required)
 * @param {string} req.body.mime_type - MIME type (e.g., "application/pdf") (required)
 * @param {Object} res - Express response object
 */
export const triggerWorkflow = async (req, res) => {
  try {
    const {
      organization_id,
      organization_name,
      service,
      month,
      year,
      monthYear,
      file_name,
      file_size,
      mime_type,
    } = req.body;

    // Validate required fields
    const validation = validateRequiredFields(req.body, REQUIRED_FIELDS);
    if (!validation.isValid) {
      return res
        .status(STATUS_CODE_BAD_REQUEST)
        .json(
          errorResponse(
            validation.message,
            POWERBI_MESSAGES.MISSING_FIELDS(validation.missingFields)
          )
        );
    }

    // Validate service value
    if (!VALID_SERVICES.includes(service)) {
      return res
        .status(STATUS_CODE_BAD_REQUEST)
        .json(
          errorResponse(
            POWERBI_MESSAGES.SERVICE_REQUIRED,
            POWERBI_MESSAGES.INVALID_SERVICE(VALID_SERVICES)
          )
        );
    }

    // Validate workflow environment configuration for the requested service
    try {
      const workflowUrlKey = SERVICE_WORKFLOW_URL_MAP[service];
      if (!workflowUrlKey) {
        return res
          .status(STATUS_CODE_INTERNAL_SERVER_ERROR)
          .json(
            errorResponse(
              POWERBI_MESSAGES.WORKFLOW_CONFIGURATION_MISSING(service),
              POWERBI_MESSAGES.WORKFLOW_TRIGGER_FAILED_FOR_SERVICE(service)
            )
          );
      }

      const workflowUrl = sanitizeString(getCachedEnv(workflowUrlKey));

      if (!workflowUrl) {
        return res
          .status(STATUS_CODE_INTERNAL_SERVER_ERROR)
          .json(
            errorResponse(
              POWERBI_MESSAGES.WORKFLOW_URL_NOT_CONFIGURED(service, workflowUrlKey),
              POWERBI_MESSAGES.WORKFLOW_TRIGGER_FAILED_FOR_SERVICE(service)
            )
          );
      }
    } catch (err) {
      return res
        .status(STATUS_CODE_INTERNAL_SERVER_ERROR)
        .json(
          errorResponse(
            POWERBI_MESSAGES.WORKFLOW_ENV_CONFIG_ERROR(service, err.message),
            POWERBI_MESSAGES.WORKFLOW_TRIGGER_FAILED_FOR_SERVICE(service)
          )
        );
    }

    // Build payload with sanitized string fields
    // Note: bodySanitizer middleware already sanitizes req.body, but we sanitize again here for extra safety
    const payload = {
      organization_id: sanitizeString(organization_id),
      organization_name: sanitizeString(organization_name),
      service: sanitizeString(service),
      month: Number.parseInt(month, 10),
      year: Number.parseInt(year, 10),
      monthYear: sanitizeString(monthYear),
      file_name: sanitizeString(file_name),
      file_size: Number.parseInt(file_size, 10),
      mime_type: sanitizeString(mime_type),
    };

    // Call service
    const serviceResponse = await triggerPowerBiWorkflow(payload);

    // Handle service response
    return handleServiceResponse(
      serviceResponse,
      res,
      POWERBI_MESSAGES.WORKFLOW_TRIGGERED_SUCCESS(service),
      POWERBI_MESSAGES.WORKFLOW_TRIGGER_FAILED_FOR_SERVICE(service)
    );
  } catch (error) {
    return handleControllerError(
      error,
      res,
      POWERBI_LOG_MESSAGES.CONTROLLER_ERROR,
      POWERBI_LOG_MESSAGES.UNEXPECTED_ERROR
    );
  }
};
