export function mapOperationsKpiData(overviewData) {
  if (!overviewData) {
    return null;
  }

  return overviewData.map((card) => {
    const change = card.previousMonthChange || 0;
    const isPositive = change >= 0;
    const changeSign = isPositive ? "+" : "";
    return {
      id: card.title,
      title: card.title,
      value: card.value,
      previousMonth: card.previousMonth,
      yearToDate: card.yearToDate,
      format: card.format,
      change: `${changeSign}${change.toFixed(2)}%`,
      isPositive,
    };
  });
}

export function mapOperationsCollectionsComparisonData(
  summaryData,
  chartPeriod
) {
  if (!summaryData || !summaryData.collectionsComparison) {
    return null;
  }

  return {
    ...summaryData.collectionsComparison,
    comparison: summaryData.collectionsComparison.comparison.map((item) => ({
      ...item,
      period: chartPeriod || item.period,
    })),
  };
}

export function mapOperationsCollectionsByDoctorData(summaryData) {
  if (!summaryData || !summaryData.collectionsByDoctor) {
    return null;
  }

  return summaryData.collectionsByDoctor;
}

export function mapOperationsCollectionsByPayerData(summaryData) {
  if (!summaryData || !summaryData.collectionsByPayerType) {
    return null;
  }

  return summaryData.collectionsByPayerType;
}

export function mapOperationsTreatmentPlanStatusData(trendsData) {
  if (!trendsData || !trendsData.treatmentPlanStatus) {
    return null;
  }

  return trendsData.treatmentPlanStatus;
}

export function mapOperationsAgingReceivablesData(trendsData) {
  if (!trendsData || !trendsData.agingAccountReceivables) {
    return null;
  }

  return trendsData.agingAccountReceivables;
}
