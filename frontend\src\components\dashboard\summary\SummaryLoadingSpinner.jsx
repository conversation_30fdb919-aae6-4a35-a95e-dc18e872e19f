"use client";

import { motion } from "framer-motion";
import "@/styles/summary-popup.css";

export const SummaryLoadingSpinner = () => (
  <div className="summary-popup-loading">
    <div className="summary-popup-loading-spinner">
      <motion.div
        className="summary-popup-loading-ring summary-popup-loading-ring-outer"
        animate={{ rotate: 360 }}
        transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
      />
      <motion.div
        className="summary-popup-loading-ring summary-popup-loading-ring-middle"
        animate={{ rotate: -360 }}
        transition={{ duration: 1.5, repeat: Infinity, ease: "linear" }}
      />
      <motion.div
        className="summary-popup-loading-ring summary-popup-loading-ring-inner"
        animate={{ rotate: 360 }}
        transition={{ duration: 0.8, repeat: Infinity, ease: "linear" }}
      />
      <motion.div
        className="summary-popup-loading-dot"
        animate={{ scale: [1, 1.5, 1], opacity: [1, 0.5, 1] }}
        transition={{ duration: 1.5, repeat: Infinity, ease: "easeInOut" }}
      />
    </div>
    <motion.p
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 0.1 }}
      className="summary-popup-loading-text"
    >
      Generating summary...
    </motion.p>
  </div>
);
