"use client";

import { memo } from "react";
import { Building2 } from "lucide-react";
import { BOOKCLOSURE_CONSTANTS } from "@/utils/constants/bookclosure";

const ClientInfoCard = memo(function ClientInfoCard({
  organizationName,
  client,
}) {
  const displayName =
    organizationName || client?.name || client?.company || "Organization";
  const status = client?.status || "Active";

  return (
    <div className="bookkeeping-client-info">
      <div className="bookkeeping-client-info-content">
        <div className="bookkeeping-client-info-icon">
          <Building2 className="w-5 h-5" />
        </div>
        <div className="bookkeeping-client-info-details">
          <div className="bookkeeping-client-info-label">Client:</div>
          <h3 className="bookkeeping-client-info-name">{displayName}</h3>
          <div className="bookkeeping-client-info-meta">
            <span className="bookkeeping-client-info-status">
              <span className="bookkeeping-client-info-status-dot"></span>
              {BOOKCLOSURE_CONSTANTS.CLIENT_INFO.STATUS_LABEL} {status}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
});

ClientInfoCard.displayName = "ClientInfoCard";

export default ClientInfoCard;
