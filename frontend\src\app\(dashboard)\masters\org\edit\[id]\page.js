"use client";

import { useEffect } from "react";
import { useRouter, useParams } from "next/navigation";
import { useDispatch, useSelector } from "react-redux";
import AddForm from "@/components/common/AddForm";
import { organizationFields } from "@/utils/data/organizations";
import { ORGANIZATION_CONSTANTS } from "@/utils/constants/organization";
import {
  getOrganizationById,
  updateOrganization,
} from "@/redux/Thunks/organization.js";
import CustomSpinner from "@/components/common/CustomSpinner";
import { useToast } from "@/components/ui/toast";
import { prepareLogoPayload, getDefaultLogoAsBase64 } from "@/utils/methods/file";

export default function EditOrganizationPage() {
  const router = useRouter();
  const params = useParams();
  const dispatch = useDispatch();
  const { addToast } = useToast();
  const orgId = params.id; // Keep as string since it's a UUID

  // Get organization data from Redux store
  const { organization, loading, error } = useSelector((state) => {
    // Try different possible data structures
    const orgData =
      state.organizations?.currentOrganization?.data ||
      state.organizations?.currentOrganization ||
      state.organizations?.data;

    return {
      organization: orgData,
      loading: state.organizations?.loading,
      error: state.organizations?.error,
    };
  });

  // Fetch organization data when component mounts
  useEffect(() => {
    if (orgId) {
      dispatch(getOrganizationById(orgId));
    }
  }, [dispatch, orgId]);

  // Loading state
  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <CustomSpinner tip="Loading organization details..." />
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-red-600">
            Error Loading Organization
          </h2>
          <p className="text-gray-600 mt-2">
            {error?.message ||
              "An error occurred while loading the organization"}
          </p>
          <button
            onClick={() => router.push("/masters/org")}
            className="mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
          >
            Back to Organizations
          </button>
        </div>
      </div>
    );
  }

  // Organization not found
  if (!organization) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900">
            {ORGANIZATION_CONSTANTS.ERROR_MESSAGES.NOT_FOUND_TITLE}
          </h2>
          <p className="text-gray-600 mt-2">
            {ORGANIZATION_CONSTANTS.ERROR_MESSAGES.NOT_FOUND_MESSAGE}
          </p>
          <button
            onClick={() => router.push("/masters/org")}
            className="mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
          >
            Back to Organizations
          </button>
        </div>
      </div>
    );
  }

  const handleSubmit = async (values) => {
    try {
      const payload = { ...values };
      // If logo is a File instance (new upload), process it
      if (values.logo instanceof File) {
        payload.logo = await prepareLogoPayload(values.logo);
      } else if (!values.logo) {
        // If no logo is provided and it's not already set, use default logo-dark.png
        payload.logo = await getDefaultLogoAsBase64();
      }
      // If logo is already a string (existing logo), keep it as is

      await dispatch(updateOrganization({ id: orgId, data: payload })).unwrap();
      router.push("/masters/org");
    } catch (error) {
      addToast("Failed to update organization. Please try again.", "error");
    }
  };

  return (
    <AddForm
      heading={ORGANIZATION_CONSTANTS.EDIT_PAGE.HEADING}
      subTitle={ORGANIZATION_CONSTANTS.EDIT_PAGE.SUBTITLE}
      onBack={() => router.push("/masters/org")}
      backLabel={ORGANIZATION_CONSTANTS.EDIT_PAGE.BACK_LABEL}
      title={ORGANIZATION_CONSTANTS.EDIT_PAGE.TITLE}
      fields={organizationFields}
      initialValues={organization}
      onSubmit={handleSubmit}
      mode="edit"
      submitLabel={ORGANIZATION_CONSTANTS.EDIT_PAGE.SUBMIT_LABEL}
    />
  );
}
