"use client";

import { formatCompactCurrency } from "../../utils/methods/formatters";
import { EChartWrapper } from "./EChartWrapper";

export function DeductionsDonutChart({ data }) {
  const option = {
    color: ["#2f7ed8", "#ff8c5a", "#ffc542", "#34d399"],
    tooltip: {
      trigger: "item",
      formatter: (params) => {
        return `${params.marker} ${params.name}: ${formatCompactCurrency(params.value)} (${params.percent}%)`;
      },
    },
    legend: {
      bottom: 0,
      left: "center",
    },
    series: [
      {
        name: data.title,
        type: "pie",
        radius: ["55%", "80%"],
        avoidLabelOverlap: false,
        itemStyle: { borderColor: "#fff", borderWidth: 0 },
        label: {
          show: true,
          formatter: (params) =>
            `${params.name}\n${formatCompactCurrency(params.value)} (${params.percent}%)`,
        },
        labelLine: { show: true },
        data: data.categories.map((item) => ({
          value: item.value,
          name: item.name,
        })),
      },
    ],
  };

  return <EChartWrapper option={option} />;
}
