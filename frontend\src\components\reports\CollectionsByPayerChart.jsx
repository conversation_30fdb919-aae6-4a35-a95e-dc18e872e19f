"use client";

import { formatCompactCurrency } from "../../utils/methods/formatters";
import { EChartWrapper } from "./EChartWrapper";

export function CollectionsByPayerChart({ data }) {
  const option = {
    color: ["#2f7ed8", "#ff8c5a", "#ffc542", "#34d399", "#a78bfa", "#f87171"],
    tooltip: {
      trigger: "item",
      formatter: (params) =>
        `${params.marker} ${params.name}: ${formatCompactCurrency(params.value)} (${params.percent}%)`,
    },
    legend: {
      bottom: 0,
      left: "center",
      icon: "circle",
      itemWidth: 10,
      itemHeight: 10,
      itemGap: 15,
      textStyle: {
        color: "#475569",
        fontSize: 11,
      },
      formatter: (name) => {
        const item = data.categories.find((cat) => cat.name === name);
        if (item) {
          const shortName = name.split(" - ")[0];
          return `${shortName}`;
        }
        return name;
      },
    },
    series: [
      {
        name: data.title,
        type: "pie",
        center: ["50%", "48%"],
        radius: ["40%", "60%"],
        avoidLabelOverlap: true,
        labelLayout: {
          hideOverlap: true,
          moveOverlap: "shiftY",
          alignTo: "edge",
          edgeDistance: 14,
        },
        itemStyle: {
          borderColor: "#fff",
          borderWidth: 0,
        },
        label: {
          show: true,
          position: "outside",
          minMargin: 12,
          formatter: (params) => {
            const shortName = params.name.split(" - ")[0];
            return `${formatCompactCurrency(params.value)} (${params.percent}%)`;
          },
          overflow: "break",
        },
        labelLine: {
          show: true,
          length: 10,
          length2: 8,
          smooth: 0.2,
        },
        data: data.categories.map((item) => ({
          value: item.value,
          name: item.name,
        })),
      },
    ],
  };

  return <EChartWrapper option={option} />;
}
