/* Dashboard Header Styles */
.dashboard-header {
  background-color: #ffffff;
  border-bottom: 1px solid #E5E7EB;
  padding: 0.75rem 1.5rem;
  width: 100%;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  position: sticky;
  top: 0;
  z-index: 40;
}

.dashboard-header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 1rem;
  max-width: 100%;
}

.dashboard-header-left {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  flex: 1;
  min-width: 0;
}

.dashboard-header-back-icon {
  flex-shrink: 0;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  color: #6B7280;
  background: none;
  border: none;
  cursor: pointer;
  border-radius: 0.375rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.dashboard-header-back-icon:hover {
  color: #4F46E5;
  background-color: rgba(79, 70, 229, 0.1);
  transform: translateX(-2px);
  box-shadow: 0 2px 4px rgba(79, 70, 229, 0.15);
}

.dashboard-header-back-icon:active {
  transform: translateX(-1px) scale(0.95);
}

.dashboard-header-title-section {
  flex: 1;
  min-width: 0;
}

.dashboard-header-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
  margin: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  transition: all 0.3s ease;
  line-height: 1.4;
}

@media (min-width: 640px) {
  .dashboard-header-title {
    font-size: 1.25rem;
  }
}

.dashboard-header-date-filter {
  flex-shrink: 0;
}

/* Animated Select Component Styles */
.animated-select-wrapper {
  position: relative;
  min-width: 160px;
}

.animated-select-trigger {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 0.4375rem 0.625rem;
  padding-right: 1.75rem;
  border: 1.5px solid #E5E7EB;
  border-radius: 0.5rem;
  background-color: #ffffff;
  color: #111827;
  font-size: 0.8125rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  outline: none;
  position: relative;
}

.animated-select-trigger:hover {
  border-color: #4F46E5;
  background-color: #FAFAFA;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(79, 70, 229, 0.1);
}

.animated-select-trigger:focus,
.animated-select-trigger--open {
  border-color: #4F46E5;
  background-color: #ffffff;
  box-shadow: 
    0 0 0 3px rgba(79, 70, 229, 0.1),
    0 4px 6px -1px rgba(79, 70, 229, 0.15);
  transform: translateY(-1px);
}

.animated-select-value {
  flex: 1;
  text-align: left;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.animated-select-chevron {
  position: absolute;
  right: 0.75rem;
  width: 1rem;
  height: 1rem;
  color: #6B7280;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: center;
}

.animated-select-chevron--open {
  transform: rotate(180deg);
  color: #4F46E5;
}

.animated-select-dropdown {
  position: absolute;
  top: calc(100% + 0.5rem);
  left: 0;
  right: 0;
  z-index: 50;
  background-color: #ffffff;
  border: 1.5px solid #E5E7EB;
  border-radius: 0.5rem;
  box-shadow: 
    0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  max-height: 240px;
  overflow-y: auto;
  animation: dropdownFadeIn 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: top;
}

@keyframes dropdownFadeIn {
  from {
    opacity: 0;
    transform: translateY(-8px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.animated-select-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.625rem 0.75rem;
  font-size: 0.875rem;
  color: #111827;
  cursor: pointer;
  transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  border-left: 3px solid transparent;
}

.animated-select-option:hover,
.animated-select-option--highlighted {
  background-color: rgba(79, 70, 229, 0.08);
  border-left-color: #4F46E5;
  padding-left: 0.875rem;
}

.animated-select-option--selected {
  background-color: rgba(79, 70, 229, 0.1);
  color: #4F46E5;
  font-weight: 600;
  border-left-color: #4F46E5;
}

.animated-select-option--selected:hover,
.animated-select-option--selected.animated-select-option--highlighted {
  background-color: rgba(79, 70, 229, 0.15);
}

.animated-select-option-label {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.animated-select-check-icon {
  width: 1rem;
  height: 1rem;
  color: #4F46E5;
  margin-left: 0.5rem;
  flex-shrink: 0;
  animation: checkFadeIn 0.2s ease;
}

@keyframes checkFadeIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Custom Scrollbar for Dropdown */
.animated-select-dropdown::-webkit-scrollbar {
  width: 6px;
}

.animated-select-dropdown::-webkit-scrollbar-track {
  background: #F9FAFB;
}

.animated-select-dropdown::-webkit-scrollbar-thumb {
  background: #D1D5DB;
  border-radius: 3px;
}

.animated-select-dropdown::-webkit-scrollbar-thumb:hover {
  background: #9CA3AF;
}

.dashboard-header-right {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-shrink: 0;
}

.dashboard-header-view-insights-btn {
  background-color: #4F46E5;
  color: #ffffff;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  font-weight: 600;
  font-size: 0.875rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2px 4px rgba(79, 70, 229, 0.2);
  border: none;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.dashboard-header-view-insights-btn:hover {
  background-color: #4338CA;
  transform: translateY(-2px);
  box-shadow: 0 8px 16px -4px rgba(79, 70, 229, 0.4);
}

.dashboard-header-view-insights-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(79, 70, 229, 0.2);
}

.dashboard-header-export-btn {
  background-color: #ffffff;
  color: #4F46E5;
  border: 1.5px solid #4F46E5;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  font-weight: 500;
  font-size: 0.875rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  box-shadow: 0 1px 2px rgba(79, 70, 229, 0.1);
  cursor: pointer;
}

.dashboard-header-export-btn:hover {
  background-color: rgba(79, 70, 229, 0.05);
  border-color: #4338CA;
  color: #4338CA;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(79, 70, 229, 0.2);
}

.dashboard-header-export-btn:active {
  transform: translateY(0);
}

.dashboard-header-export-btn svg {
  flex-shrink: 0;
}

/* Floating Action Button (FAB) Styles */
.dashboard-fab {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  width: 3.5rem;
  height: 3.5rem;
  border-radius: 50%;
  background-color: #4F46E5;
  color: #ffffff;
  border: none;
  box-shadow: 
    0 10px 15px -3px rgba(79, 70, 229, 0.3),
    0 4px 6px -2px rgba(79, 70, 229, 0.2);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 1000;
  animation: fadeInScale 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.dashboard-fab:hover {
  background-color: #4338CA;
  transform: scale(1.1) translateY(-2px);
  box-shadow: 
    0 20px 25px -5px rgba(79, 70, 229, 0.4),
    0 10px 10px -5px rgba(79, 70, 229, 0.2);
}

.dashboard-fab:active {
  transform: scale(1.05) translateY(0);
}

.dashboard-fab-icon {
  width: 1.5rem;
  height: 1.5rem;
  color: #ffffff;
}

@media (max-width: 768px) {
  .dashboard-fab {
    bottom: 1.5rem;
    right: 1.5rem;
    width: 3rem;
    height: 3rem;
  }

  .dashboard-fab-icon {
    width: 1.25rem;
    height: 1.25rem;
  }
}

/* Category Tabs Styles - Embossed/3D Effect */
.dashboard-category-tabs-container {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 0;
  padding: 0.375rem;
  background-color: #F8FAFC;
  border-radius: 0.5rem;
  transition: all 0.3s ease;
  position: relative;
  z-index: 20;
  box-shadow: none;
  margin-bottom: 0.25rem;
  margin-top: 0.25rem;
}

.dashboard-category-tabs {
  display: flex;
  gap: 0.25rem;
  flex-wrap: wrap;
  border-bottom: none;
}

.dashboard-category-tab {
  padding: 0.5rem 1.25rem;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  border: none;
  background-color: transparent;
  color: #6B7280; /* Theme body text color */
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.dashboard-category-tab:hover {
  color: #4F46E5; /* Primary purple */
  background-color: rgba(79, 70, 229, 0.05); /* Very light purple tint */
}

.dashboard-category-tab--active {
  background-color: rgba(79, 70, 229, 0.1); /* Light purple matching theme */
  color: #4F46E5; /* Primary purple */
  font-weight: 600;
  /* Embossed/3D effect with inset shadow - subtle and theme-aligned */
  box-shadow: 
    inset 0 1px 2px rgba(255, 255, 255, 0.9),
    inset 0 -1px 2px rgba(79, 70, 229, 0.15),
    0 1px 2px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(79, 70, 229, 0.2);
}

.dashboard-category-tab--active:hover {
  color: #4F46E5;
  background-color: rgba(79, 70, 229, 0.12); /* Slightly darker on hover */
}

.dashboard-category-tab:active {
  transform: scale(0.98);
}

.dashboard-cfo-insights-btn {
  flex-shrink: 0;
}

@media (max-width: 768px) {
  .dashboard-category-tabs-container {
    flex-direction: column;
    align-items: stretch;
  }

  .dashboard-category-tabs {
    width: 100%;
    justify-content: stretch;
  }

  .dashboard-category-tab {
    flex: 1;
    min-width: 0;
  }

  .dashboard-cfo-insights-btn {
    width: 100%;
  }
}

@media (max-width: 768px) {
  .dashboard-header-content {
    flex-direction: column;
    align-items: stretch;
  }

  .dashboard-header-left {
    flex-direction: column;
    align-items: stretch;
    gap: 0.75rem;
  }

  .dashboard-header-right {
    width: 100%;
    justify-content: stretch;
  }

  .dashboard-header-view-insights-btn,
  .dashboard-header-export-btn {
    flex: 1;
  }
}

/* Dashboard Component Styles */

.dashboard-container {
  flex: 1;
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  display: flex;
  flex-direction: column;
  background-color: #F4F4F5;
  min-width: 0;
  padding: 0.75rem 1rem;
  transition: all 0.3s ease;
  scroll-behavior: smooth;
}

.dashboard-container::-webkit-scrollbar {
  width: 8px;
}

.dashboard-container::-webkit-scrollbar-track {
  background: transparent;
}

.dashboard-container::-webkit-scrollbar-thumb {
  background-color: rgba(203, 213, 225, 0.5);
  border-radius: 4px;
  transition: background-color 0.3s ease;
}

.dashboard-container::-webkit-scrollbar-thumb:hover {
  background-color: rgba(148, 163, 184, 0.8);
}

@media (min-width: 640px) {
  .dashboard-container {
    padding: 1rem 1.25rem;
  }
}

@media (min-width: 1024px) {
  .dashboard-container {
    padding: 1.25rem 1.5rem;
  }
}

.dashboard-content {
  width: 100%;
  max-width: 90rem;
  margin-left: auto;
  margin-right: auto;
  animation: fadeIn 0.4s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
}
  to {
    opacity: 1;
  transform: translateY(0);
  }
}

.dashboard-pdf-container {
  flex: 1;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  min-height: 0;
  width: 100%;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  padding-top: 0.25rem;
  margin-top: 0;
}

/* PDF Report Frame - Professional paper-like appearance */
.dashboard-pdf-frame {
  background-color: #F9FAFB !important;
  box-shadow: 
    0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04) !important;
  border: 1px solid #E5E7EB !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  animation: slideUp 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* PDF Content Areas - Pure white for charts and tables */
.dashboard-pdf-frame canvas {
  background-color: #FFFFFF !important;
}

.dashboard-selector-container {
  flex: 1;
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgb(249 250 251);
  min-width: 0;
  padding: 0.5rem;
}

@media (min-width: 640px) {
  .dashboard-selector-container {
    padding: 1rem;
  }
}

.dashboard-selector-content {
  width: 100%;
  max-width: 56rem;
}

.dashboard-selector-card {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow:
    0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05);
  padding: 2rem;
}

.dashboard-selector-header {
  text-align: center;
  margin-bottom: 2rem;
}

.dashboard-selector-title {
  font-size: 1.875rem;
  font-weight: 700;
  color: rgb(31 41 55);
  margin-bottom: 0.5rem;
}

.dashboard-selector-subtitle {
  color: rgb(75 85 99);
}

.dashboard-selector-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
}

@media (min-width: 768px) {
  .dashboard-selector-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

.dashboard-selector-button {
  position: relative;
  overflow: hidden;
  border-radius: 0.75rem;
  padding: 2rem;
  color: white;
  transition: all 0.3s ease;
}

.dashboard-selector-button:hover {
  transform: scale(1.05);
  box-shadow:
    0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.dashboard-selector-button-content {
  position: relative;
  z-index: 10;
}

.dashboard-selector-icon-container {
  width: 4rem;
  height: 4rem;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: auto;
  margin-right: auto;
  margin-bottom: 1rem;
}

.dashboard-selector-icon {
  width: 2rem;
  height: 2rem;
  fill: currentColor;
}

.dashboard-selector-name {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.dashboard-selector-shine {
  position: absolute;
  inset: 0;
  background: linear-gradient(
    to right,
    transparent,
    rgba(255, 255, 255, 0.1),
    transparent
  );
  transform: skewX(-12deg) translateX(-100%);
  transition: transform 0.7s ease;
}

.dashboard-selector-button:hover .dashboard-selector-shine {
  transform: skewX(-12deg) translateX(100%);
}
