import { But<PERSON> } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";

export default function FormHeader({ title, subtitle, onBack, backLabel }) {
  return (
    <div className="flex items-center justify-between mb-4">
      <div className="flex flex-col">
        <h1 className="text-2xl font-semibold text-gray-800 mb-1">{title}</h1>
        {subtitle && <p className="text-sm text-gray-500 mt-1">{subtitle}</p>}
      </div>
      <Button
        variant="outline"
        onClick={onBack}
        className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-300 shadow-sm hover:shadow-md transition-all duration-200"
      >
        <ArrowLeft className="h-4 w-4 mr-2" />
        {backLabel || "Back"}
      </Button>
    </div>
  );
}
