// app/services/chat.service.js
import logger from "../../config/logger.config.js";
import {
  getChatServiceApiUrl,
  getAuthServiceBaseUrl,
} from "../utils/env.util.js";
import { postWithRefreshToken, getWithRefreshToken } from "../utils/request.util.js";
import { ERROR_MESSAGES } from "../utils/constants/error.constants.js";
import { CHAT_LOG_MESSAGES } from "../utils/constants/logMessages.constants.js";
import { startChatSessionRequest } from "../../../../shared/utils/chatSession.util.js";

/**
 * Start a chat session for a document
 * @param {string} filename - Document filename
 * @param {string} organizationId - Organization ID
 * @param {string} organizationName - Organization name
 * @param {number} year - Year
 * @param {number} month - Month (1-12)
 * @param {string} refreshToken - Refresh token for authentication
 * @param {string} requestId - Request ID for logging
 * @param {string} service - Service name (Finance, Operations, Payroll)
 * @returns {Promise<Object>} Chat session response with sessionId
 */
export const startChatSession = async (
  filename,
  organizationId,
  organizationName,
  year,
  month,
  refreshToken,
  requestId = null,
  service = null
) => {
  const logPrefix = requestId ? `[${requestId}]` : '';
  try {
    const CHAT_SERVICE_API_URL = getChatServiceApiUrl();
    logger.info(`${logPrefix} ${CHAT_LOG_MESSAGES.START_CHAT_SESSION}`, {
      filename,
      organizationId,
      organizationName,
      year,
      month,
      service,
    });

    const { data, message, status, response } = await startChatSessionRequest({
      baseUrl: CHAT_SERVICE_API_URL,
      filename,
      orgId: organizationId,
      orgName: organizationName,
      year,
      month,
      service,
      refreshToken,
      requestId,
      useBlobPath: true, // Use Power BI-Reports blob path structure for PDFs
    });

    const { sessionId, filename: resolvedFilename } = data ?? {};
    if (sessionId) {
      logger.info(`${logPrefix} ${CHAT_LOG_MESSAGES.SESSION_STARTED_WITH_ID}: ${sessionId}`);
      return {
        success: true,
        sessionId,
        filename: resolvedFilename,
      };
    }

    logger.error(`${logPrefix} ${CHAT_LOG_MESSAGES.START_CHAT_SESSION_INVALID_RESPONSE}`);
    throw new Error(
      `${ERROR_MESSAGES.CHAT_SESSION_RESPONSE_INVALID}: ${
        message || "sessionId"
      }`
    );
  } catch (error) {
    // Enhance error with more details
    const enhancedError = new Error(error.message);
    enhancedError.response = error.response;
    enhancedError.config = error.config;
    enhancedError.request = error.request;
    
    logger.error(`${logPrefix} ${CHAT_LOG_MESSAGES.START_CHAT_SESSION_ERROR}:`, {
      error: error.message,
      status: error.response?.status,
      statusText: error.response?.statusText,
      responseData: error.response?.data,
      url: error.config?.url,
      method: error.config?.method,
      stack: error.stack,
    });
    throw enhancedError;
  }
};

/**
 * Get summary from chat service
 * @param {string} sessionId - Chat session ID
 * @param {string} refreshToken - Refresh token for authentication
 * @returns {Promise<Object>} Summary response with summaryData (JSON object)
 */
export const getChatSummary = async (sessionId, refreshToken, requestId = null, message = null) => {
  const logPrefix = requestId ? `[${requestId}]` : '';
  try {
    const CHAT_SERVICE_API_URL = getChatServiceApiUrl();
    const url = `${CHAT_SERVICE_API_URL}/chat/summary`;
    
    // Default message if not provided
    const defaultMessage = "Provide the summary of this dhashborad";
    const summaryMessage = message || defaultMessage;
    
    const payload = { 
      sessionId,
      message: summaryMessage
    };

    // Log curl-equivalent command before making the request
    const curlCommand = `curl -X POST "${url}" ${refreshToken ? `-H "Cookie: refresh_token=***"` : ''} -H "Content-Type: application/json" -d '${JSON.stringify(payload)}'`;
    logger.info(`${logPrefix} ${CHAT_LOG_MESSAGES.GET_CHAT_SUMMARY_START}: ${curlCommand}`);
    logger.info(`${logPrefix} ${CHAT_LOG_MESSAGES.GET_CHAT_SUMMARY_REQUEST}:`, {
      sessionId,
      message: summaryMessage,
      url,
    });

    // New API format: sessionId and message (organization retrieved from session)
    // Increased timeout to 5 minutes (300000ms) for summary generation which can take longer
    const response = await postWithRefreshToken(
      url,
      payload,
      refreshToken,
      {
        timeout: 300000, // 5 minutes - summary generation can take time
      }
    );

    logger.info(`${logPrefix} ${CHAT_LOG_MESSAGES.GET_CHAT_SUMMARY_RESPONSE}:`, {
      status: response?.status,
      hasData: !!response?.data,
    });

    // Extract data from response - handle nested structure
    // Response structure: { status: "success", data: { summaryData, filename, orgId, ... } }
    const responseBody = response?.data ?? {};
    const responseData = responseBody?.data ?? responseBody; // Handle both nested and flat structures
    
    const { summaryData, filename, orgId, orgName, year, month } = responseData ?? {};
    
    if (summaryData) {
      logger.info(`${logPrefix} ${CHAT_LOG_MESSAGES.GET_CHAT_SUMMARY_SUCCESS}:`, {
        hasTitle: !!summaryData.title,
        sectionsCount: summaryData.sections?.length,
        filename,
        orgId,
        orgName,
        year,
        month,
      });
      return {
        success: true,
        summaryData, // JSON object with title and sections
        filename,
        orgId,
        orgName,
        year,
        month,
      };
    }

    logger.error(`${logPrefix} ${CHAT_LOG_MESSAGES.GET_CHAT_SUMMARY_INVALID_RESPONSE}`);
    throw new Error(
      `${ERROR_MESSAGES.CHAT_SUMMARY_RESPONSE_INVALID}: summaryData`
    );
  } catch (error) {
    // Enhance error with more details
    const enhancedError = new Error(error.message);
    enhancedError.response = error.response;
    enhancedError.config = error.config;
    enhancedError.request = error.request;
    
    logger.error(`${logPrefix} ${CHAT_LOG_MESSAGES.GET_CHAT_SUMMARY_ERROR}:`, {
      error: error.message,
      status: error.response?.status,
      statusText: error.response?.statusText,
      responseData: error.response?.data,
      url: error.config?.url,
      method: error.config?.method,
      sessionId,
      stack: error.stack,
    });
    throw enhancedError;
  }
};

/**
 * Fetch organization name from auth service
 * @param {string} organizationId - Organization ID
 * @param {string} refreshToken - Refresh token for authentication (optional)
 * @returns {Promise<string|null>} Organization name or null
 */
export const fetchOrganizationName = async (
  organizationId,
  refreshToken = null
) => {
  try {
    const AUTH_SERVICE_BASE_URL = getAuthServiceBaseUrl();
    const url = `${AUTH_SERVICE_BASE_URL}/organization/${organizationId}`;

    logger.info(
      `${CHAT_LOG_MESSAGES.FETCH_ORGANIZATION}: ${organizationId}`
    );

    const { data } = await getWithRefreshToken(url, refreshToken);

    const organizationName = data?.data?.name ?? data?.name ?? null;

    if (organizationName) {
      logger.info(
        `${CHAT_LOG_MESSAGES.ORGANIZATION_FETCH_SUCCESS}: ${organizationName}`
      );
      return { success: true, organizationName };
    }

    logger.warn(CHAT_LOG_MESSAGES.FILENAME_LOOKUP_FALLBACK);
    logger.warn(
      `${CHAT_LOG_MESSAGES.ORGANIZATION_NOT_FOUND}: ${organizationId}`
    );
    return { error: ERROR_MESSAGES.ORGANIZATION_NOT_FOUND };
  } catch (error) {
    logger.warn(
      `${CHAT_LOG_MESSAGES.ORGANIZATION_LOOKUP_ERROR}: ${error.message}`,
      {
        error: error.response?.data || error.message,
        status: error.response?.status,
      }
    );
    return { error: ERROR_MESSAGES.ORGANIZATION_LOOKUP_FAILED };
  }
};

/**
 * Extract organization name from filename
 * Example: "CHP Finance Dashboard - August 2025.pdf" -> "CHP"
 * @param {string} filename - Document filename
 * @returns {string|null} Extracted organization name or null
 */
export const extractOrganizationFromFilename = (filename) => {
  if (!filename) return null;

  try {
    // Extract organization name from filename (assumes format: "ORG_NAME ...")
    const match = filename.match(
      /^([A-Za-z0-9\s]+?)(?:\s+(?:Finance|Operational|Payroll|PMS|Dashboard))/i
    );
    if (match && match[1]) {
      return match[1].trim();
    }

    // Fallback: try to extract first word before any dash or space
    const firstPart = filename.split(/[\s-]/)[0];
    if (firstPart && firstPart.length > 0) {
      return firstPart.trim();
    }

    return null;
  } catch (error) {
    logger.warn(
      `${CHAT_LOG_MESSAGES.EXTRACT_ORGANIZATION_ERROR}: ${filename}`,
      {
        error: error.message,
      }
    );
    return null;
  }
};
