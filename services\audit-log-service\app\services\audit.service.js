import AuditLog from "../models/audit.model.js";

/**
 * Create an audit log entry
 *
 * @param {*} data
 * @param {*} schemaName
 * @returns
 */
export const createLog = async (data, schemaName = "chp_solutions") => {
  try {
    const AuditLogSchema = AuditLog.schema(schemaName);

    const log = await AuditLogSchema.create({
      serviceName: data.serviceName || "",
      organizationId: data.organizationId || 0,
      syncStartTime: data.syncStartTime || new Date(),
      syncendTime: data.syncEndTime || new Date(),
      executionStatus: data.executionStatus || "Pending",
      recordsProcessed: data.recordsProcessed || 0,
    });

    return {
      status: true,
      message: "Audit log created successfully",
      data: log,
    };
  } catch (error) {
    console.error(
      `❌ Error creating audit log in schema [${schemaName}]:`,
      error
    );
    return {
      status: false,
      message: "Failed to create audit log",
      error: error.message,
    };
  }
};
