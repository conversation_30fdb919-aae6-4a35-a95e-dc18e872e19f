import IncomeExpenseStatementService from "../services/income_expense_statement.service.js";
import { LOGGER_NAMES } from "../utils/constants/log.constants.js";
import { createLogger } from "../utils/logger.utils.js";

const logger = createLogger(LOGGER_NAMES.REPORT_CONTROLLER);

/**
 * Get income and expense statement for organization by month and year
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getIncomeExpenseStatement = async (req, res) => {
  try {
    const { organization_id, month, year } = req.query;

    logger.info(
      `Fetching income and expense statement for org: ${organization_id}, month: ${month}, year: ${year}`
    );

    // Validate required parameters
    if (!organization_id) {
      return res.status(400).json({
        success: false,
        message: "Organization ID (organization_id) is required",
        error: "Missing required parameter: organization_id",
      });
    }

    if (!month) {
      return res.status(400).json({
        success: false,
        message: "Month is required",
        error: "Missing required parameter: month",
      });
    }

    if (!year) {
      return res.status(400).json({
        success: false,
        message: "Year is required",
        error: "Missing required parameter: year",
      });
    }

    // Fetch income and expense statement data
    const statementData =
      await IncomeExpenseStatementService.getIncomeExpenseStatement({
        organization_id,
        month,
        year,
      });

    // // Check if data exists
    // if (!statementData.data || statementData.data.length === 0) {
    //   return res.status(200).json({
    //     success: true,
    //     message:
    //       statementData.message ||
    //       "No income and expense data found for the specified period",
    //     data: statementData,
    //   });
    // }

    // Return successful response
    res.status(200).json({
      success: true,
      message: "Income and expense statement fetched successfully",
      data: statementData,
    });
  } catch (error) {
    logger.error("Error fetching income and expense statement:", error);

    // Handle validation errors with 400 status
    if (
      error.message.includes("required") ||
      error.message.includes("Valid") ||
      error.message.includes("Invalid")
    ) {
      return res.status(400).json({
        success: false,
        message: error.message,
        error: error.message,
      });
    }

    // Handle not found errors with 404 status
    if (error.message.includes("not found")) {
      return res.status(404).json({
        success: false,
        message: error.message,
        error: error.message,
      });
    }

    // Handle all other errors with 500 status
    res.status(500).json({
      success: false,
      message: "Error fetching income and expense statement",
      error: error.message,
    });
  }
};

export default {
  getIncomeExpenseStatement,
};
