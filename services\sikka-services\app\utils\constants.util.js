// SIKKA API CONFIGURATION
export const SIKKA_API = {
  BASE_URL: "https://api.sikkasoft.com",
  VERSION: "v4",
  ENDPOINTS: {
    AUTHORIZED_PRACTICES: "/authorized_practices",
    REQUEST_KEY: "/request_key",
    ACCOUNT_RECEIVABLES: "accounts_receivable",
    TREATMENT_PLAN_ANALYSIS: "treatment_plan_analysis",
    TOTAL_PRODUCTION_PER_DAY: "total_production_per_day",
    TOTAL_PRODUCTION_BY_DENTIST: "total_production_by_dentist",
    TOTAL_PRODUCTION_BY_HYGIENIST: "total_production_by_hygienist",
    AVG_DAILY_PRODUCTION: "average_daily_scheduled_production",
    NO_SHOW_APPOINTMENTS: "no_show_appointments",
    NEW_PATIENTS: "new_patients",
    HYGIENE_REAPPOINTMENT: "hygiene_reappointment",
    DIRECT_RESTORATIONS: "total_direct_restorations_by_provider",
    GROSS_COLLECTION: "gross_collection",
  },
  GRANT_TYPES: {
    REQUEST_KEY: "request_key",
  },
};

// METHOD TYPES
export const METHOD_TYPES = {
  GET: "get",
  POST: "post",
  PUT: "put",
  DELETE: "delete",
  PATCH: "patch",
};

// SIKKA MESSAGES
export const SIKKA_MESSAGES = {
  REQUEST_KEY_SUCCESS: "Request key generated successfully",
  REQUEST_KEY_FAILED: "Failed to generate request key",
  AUTHORIZED_PRACTICES_SUCCESS: "Authorized practices fetched successfully",
  NO_AUTHORIZED_PRACTICES: "No authorized practices found",
  MISSING_PRACTICE_CREDENTIALS:
    "Missing office_id or secret_key in practice data",
  INVALID_REQUEST_KEY_RESPONSE: "Invalid response from request key API",
  NETWORK_ERROR: "Network error occurred while calling Sikka API",
  INVALID_CREDENTIALS: "Invalid credentials",
  API_CALL_FAILED: "API call failed with status",
  SIKKA_API_ERROR: "Sikka API Error",
  NETWORK_ERROR_OCCURRED: "Network error occurred while calling Sikka API",
  ACCOUNT_RECEIVABLES_SUCCESS: "Account receivables fetched successfully",
  ACCOUNT_RECEIVABLES_FAILED: "Failed to fetch account receivables",
  NO_DATA_RECEIVED: "No data received from API",
  MODELS_INFO_FETCHED_SUCCESSFULLY: "Models information fetched successfully",
  FAILED_TO_LOAD_MODELS: "Failed to load models information",
  MODELS_DIRECTORY_NOT_FOUND: "Models directory not found",
  COULD_NOT_FIND_REQUEST_KEY:
    "Could not find existing RequestKey, will create new one:",
  REQUEST_KEY_TABLE_NOT_EXIST: "RequestKey table does not exist in schema",
  WILL_GENERATE_NEW_KEY: "will generate new key",
  ERROR_PROCESSING_MODEL: "Error processing model",
  FAILED_TO_LOAD_MODEL: "Failed to load model:",
};

// MODEL FIELDS
export const MODEL_FIELDS = {
  APP_ID: "app_id",
  APP_KEY: "app_key",
  OFFICE_ID: "office_id",
  SECRET_KEY: "secret_key",
  REQUEST_KEY: "request_key",
  GRANT_TYPE: "grant_type",
  START_TIME: "start_time",
  END_TIME: "end_time",
  EXPIRES_IN: "expires_in",
  EMAIL: "email",
  ITEMS: "items",
  DATA: "data",
  ISSUED_TO: "issued_to",
  SCHEMA_NAME: "schema_name",
  TABLE_NAME: "tableName",
  MODEL_NAME: "modelName",
  COLUMNS: "columns",
  ASSOCIATIONS: "associations",
  TIMESTAMP: "timestamp",
  TOTAL_MODELS: "totalModels",
  MODELS: "models",
};

// VALIDATION RULES
export const VALIDATION_RULES = {
  APP_ID_MIN_LENGTH: 1,
  APP_ID_MAX_LENGTH: 100,
  APP_KEY_MIN_LENGTH: 1,
  APP_KEY_MAX_LENGTH: 255,
};

// VALIDATION MESSAGES
export const VALIDATION_MESSAGES = {
  APP_ID_REQUIRED: "App ID is required",
  APP_ID_LENGTH: "App ID must be between 1 and 100 characters",
  APP_KEY_REQUIRED: "App Key is required",
  APP_KEY_LENGTH: "App Key must be between 1 and 255 characters",
  VALIDATION_FAILED: "Validation failed",
  VALIDATION_PROCESSING_ERROR: "Error processing validation",
  DATA_MUST_BE_ARRAY: "Data must be an array",
  INVALID_DATA_TYPE: "Invalid data type",
  MISSING_REQUIRED_FIELDS: "Missing required fields",
  INVALID_OBJECT: "Invalid object",
};

// LOGGER CONFIGURATION
export const LOGGER_NAMES = {
  SIKKA_CONTROLLER: "sikka-controller",
  SIKKA_SERVICE: "sikka-service",
  VALIDATION_MIDDLEWARE: "validation-middleware",
  KPIS_SERVICE: "kpis-service",
  KPIS_CONTROLLER: "kpis-controller",
};

// LOG ACTIONS
export const LOG_ACTIONS = {
  REQUESTING_KEY: "Requesting API key from Sikka",
  REQUEST_KEY_SUCCESS: "Request key generated successfully",
  REQUEST_KEY_FAILED: "Failed to generate request key",
  FETCHING_PRACTICES: "Fetching authorized practices from Sikka",
  PRACTICES_FETCHED: "Authorized practices fetched successfully",
  PRACTICES_FETCH_FAILED: "Failed to fetch authorized practices",
  REQUEST_KEY_API_FAILED: "Request key API call failed",
  FETCHING_ACCOUNT_RECEIVABLES: "Fetching account receivables from Sikka",
  FETCHING_TREATMENT_ANALYSIS: "Fetching treatment analysis from Sikka",
  FETCHING_NO_SHOW_APPOINTMENTS: "Fetching no show appointments from Sikka",
  FETCHING_AVG_DAILY_PRODUCTION: "Fetching avg daily production from Sikka",
  FETCHING_NEW_PATIENTS: "Fetching new patients from Sikka",
  FETCHING_HYGIENE_REAPPOINTMENT: "Fetching hygiene reappointment from Sikka",
  FETCHING_DIRECT_RESTORATIONS: "Fetching direct restorations from Sikka",
  ACCOUNT_RECEIVABLES_SUCCESS: "Account receivables fetched successfully",
  ACCOUNT_RECEIVABLES_FAILED: "Failed to fetch account receivables",
  TREATMENT_ANALYSIS_SUCCESS: "Treatment analysis fetched successfully",
  TREATMENT_ANALYSIS_FAILED: "Failed to fetch treatment analysis",
  NO_SHOW_APPOINTMENTS_SUCCESS: "No show appointments fetched successfully",
  NO_SHOW_APPOINTMENTS_FAILED: "Failed to fetch no show appointments",
  AVG_DAILY_PRODUCTION_SUCCESS: "Avg daily production fetched successfully",
  AVG_DAILY_PRODUCTION_FAILED: "Failed to fetch avg daily production",
  NEW_PATIENTS_SUCCESS: "New patients fetched successfully",
  NEW_PATIENTS_FAILED: "Failed to fetch new patients",
  HYGIENE_REAPPOINTMENT_SUCCESS: "Hygiene reappointment fetched successfully",
  HYGIENE_REAPPOINTMENT_FAILED: "Failed to fetch hygiene reappointment",
  DIRECT_RESTORATIONS_SUCCESS: "Direct restorations fetched successfully",
  DIRECT_RESTORATIONS_FAILED: "Failed to fetch direct restorations",
  GETTING_MODELS_INFO: "Getting models information",
  ERROR_GETTING_MODELS_INFO: "Error getting models information",
  CALLING_API: "Calling",
  API_SUCCESSFUL: "successful",
  FETCHING_GROSS_COLLECTION: "Fetching GROSS COLLECTION from Sikka",
  GROSS_COLLECTION_SUCCESS: "GROSS_COLLECTION fetched successfully",
  GROSS_COLLECTION_FAILED: "Failed to fetch GROSS_COLLECTION",
};

// MODULES AND OPERATIONS
export const MODULES = {
  SIKKA: "sikka",
};

export const OPERATIONS = {
  REQUEST_KEY: "request_key",
  AUTHORIZED_PRACTICES: "authorized_practices",
};

// CONFIGURATION DEFAULTS
export const CONFIG_DEFAULTS = {
  SIKKA_API_TIMEOUT: 30000, // 30 seconds
  REQUEST_KEY_EXPIRY_HOURS: 24, // 24 hours
  EXPIRY_BUFFER_MS: 5 * 60 * 1000, // 5 minutes in milliseconds
};

// VALIDATION DEFAULTS
export const VALIDATION_DEFAULTS = {
  DEFAULT_SOURCE: "body",
  LOG_MESSAGE_FAILED: "Validation failed",
  LOG_MESSAGE_MIDDLEWARE_ERROR: "Validation middleware error",
  LOG_MESSAGE_MULTI_FAILED: "Multi-source validation failed",
  LOG_MESSAGE_MULTI_ERROR: "Multi-source validation middleware error",
};

// BUSINESS LOGIC CONSTANTS
export const BUSINESS_CONSTANTS = {
  HTTP_SUCCESS_STATUS: 200,
  CREDENTIAL_SEPARATOR: ", ",
  STRING_TYPE: "string",
};

// HTTP HEADERS
export const HTTP_HEADERS = {
  APP_ID: "App-Id",
  APP_KEY: "App-Key",
  CONTENT_TYPE: "Content-Type",
  APPLICATION_JSON: "application/json",
};

// ERROR MESSAGES FOR VALIDATION
export const ERROR_MESSAGES = {
  APP_ID_REQUIRED_STRING: "App ID is required and must be a string",
  APP_KEY_REQUIRED_STRING: "App Key is required and must be a string",
  API_CALL_FAILED_FOR: "API call failed for authorized practices",
  REQUEST_KEY_API_FAILED: "Request key API failed",
  ACCOUNT_RECEIVABLES_FAILED: "Failed to fetch account receivables",
  TREATMENT_ANALYSIS_FAILED: "Failed to fetch treatment analysis",
  INVALID_KPI_KEY: "Invalid KPI key:",
  APP_ID_LENGTH_ERROR: "App ID must be between",
  APP_KEY_LENGTH_ERROR: "App Key must be between",
  AND_CHARACTERS: "characters",
  UNKNOWN_ERROR: "Unknown error",
  HTTP_ERROR: "HTTP",
  NO_RESPONSE_RECEIVED: "No response received",
  NO_RESPONSE_DATA_OR_STATUS: "No response data or status for",
  FAILED_TO_PROCESS: "Failed to process",
  ERROR_IN: "Error in",
};

// SERVER CONSTANTS
export const SERVER_CONSTANTS = {
  SIKKA_SERVER_LABEL: "sikka-server",
  DEFAULT_ALLOWED_ORIGIN: "http://localhost:3000",
  HEALTH_ENDPOINT: "/health",
  API_ENDPOINT: "/api",
  CATCH_ALL_ROUTE: "*",
  ENVIRONMENT_DEVELOPMENT: "development",
};

// SERVER MESSAGES
export const SERVER_MESSAGES = {
  SIKKA_SERVICE_HEALTHY: "Sikka service is healthy",
  ROUTE_NOT_FOUND: "Route not found",
  INTERNAL_SERVER_ERROR: "Internal server error",
  SIKKA_SERVICE_STARTED: "Sikka service started on port",
  HEALTH_CHECK_URL: "Health check:",
  API_ENDPOINT_URL: "API endpoint:",
  ENVIRONMENT_INFO: "Environment:",
  SIGTERM_RECEIVED: "SIGTERM received, shutting down gracefully",
  SIGINT_RECEIVED: "SIGINT received, shutting down gracefully",
  PROCESS_TERMINATED: "Process terminated",
  UNHANDLED_ERROR: "Unhandled error:",
};

// REQUEST CONFIGURATION
export const REQUEST_CONFIG = {
  JSON_LIMIT: "10mb",
  URL_ENCODED_LIMIT: "10mb",
};

export const LOG_DATABASE = {
  INITIALIZE_DATABASE: "Initializing database connection...",
  INITIALIZE_DATABASE_SUCCESS: "Database connection initialized successfully",
  INITIALIZE_DATABASE_FAILED: "Database connection initialization failed",
  CONNECTION_FAILED: "Database connection failed",
  CONNECTED_TO_DATABASE: "Connected to database successfully",
  DATABASE_SYNCHRONIZED: "Database synchronized successfully",
  DATABASE_ERROR: "Database connection error occurred",
  DB_RECONNECT: "Attempting to reconnect to database",
  QUERY_TRYING: "Retrying database query",
  NO_ACCOUNTS_FOUND: "No accounts found in API response",
};

export const PAGINATION = {
  DEFAULT_PAGE: 1,
  DEFAULT_LIMIT: 10,
  MAX_LIMIT: 100,
  MIN_LIMIT: 1,
};

export const LOG_ERRORS = {
  CREATING: "Error creating user",
  ACCESS_DENIED: "Access denied. User does not have permission.",
  FETCHING_ALL: "Error fetching users",
  FETCHING_BY_ID: "Error fetching user by ID",
  FETCHING_BY_EMAIL: "Error fetching user by email",
  FETCHING_BY_PHONE: "Error fetching user by phone number",
  UPDATING: "Error updating user",
  FETCHING_ROLE_BY_NAME: "Error fetching role by name",
  FETCHING_ROLE_BY_ID: "Error fetching role by ID",
  DELETING: "Error deleting user",
  UPDATING_LAST_LOGIN: "Error updating user last login",
  UPDATING_PASSWORD: "Error updating user password",
  FETCHING_BY_RESET_PASSWORD_TOKEN:
    "Error fetching user by reset password token",
  VALIDATING_PASSWORD: "Error validating password",
  GENERATING_RESET_PASSWORD_TOKEN: "Error generating reset password token",
  VERIFYING_RESET_PASSWORD_TOKEN: "Error verifying reset password token",
};

// SIKKA SPECIFIC CONSTANTS
export const SIKKA_CONSTANTS = {
  REQUEST_KEY_EXPIRED_MESSAGE: "Request key has expired, generating new one",
  REQUEST_KEY_VALID_MESSAGE: "Using existing valid request key",
};

// CONTROLLER MESSAGES
export const CONTROLLER_MESSAGES = {
  REQUEST_KEY_GENERATED: "Request key generated and stored successfully",
  ACCOUNT_RECEIVABLES_FETCHED:
    "Account receivables fetched and stored successfully",
  OPERATION_FAILED: "Operation failed",
  TREATMENT_ANALYSIS_FETCHED:
    "Treatment analysis fetched and stored successfully",
  NO_SHOW_APPOINTMENTS_FETCHED:
    "No show appointments fetched and stored successfully",
  AVG_DAILY_PRODUCTION_FETCHED:
    "Avg daily production fetched and stored successfully",
  NEW_PATIENTS_FETCHED: "New patients fetched and stored successfully",
  MODELS_INFO_FETCHED: "Models information fetched successfully",
  CHECKING_CONNECTION: "Checking connection to Sikka Account",
  CONNECTION_VERIFIED: "Connection to Sikka Account verified successfully",
  CONNECTION_FAILED: "Failed to check connection to Sikka Account",
  BATCH_KPI_RUN_COMPLETE: "Batch KPI run complete",
  MODELS_RETRIEVED: "Successfully retrieved information for",
  MODELS: "models",
  CHECKING_REQUEST_KEY_AVAILABILITY: "Checking request key availability",
  REQUEST_KEY_GENERATED_SUCCESSFULLY: "Request key generated successfully",
  VALID_REQUEST_KEY_FOUND: "Valid request key found",
  ERROR_UPDATING_ORGANIZATION_SYNC_STATUS:
    "Error updating organization sync status",
  BATCH_KPI_RUN_FAILED: "All KPI sync got failed, please try again later",
};

// LOGGER NAMES EXTENSION
export const LOGGER_NAMES_EXTENDED = {
  SIKKA_REPOSITORY: "sikka-repository",
};

// SERVICE LAYER CONSTANTS
export const SERVICE_CONSTANTS = {
  REQUEST_KEY_OPERATION: "request_key_operation",
  ACCOUNT_RECEIVABLES_OPERATION: "account_receivables_operation",
  AUTHORIZED_PRACTICES_OPERATION: "authorized_practices_operation",
};

// API RESPONSE FIELD MAPPINGS
export const API_RESPONSE_FIELDS = {
  REQUEST_KEY: "request_key",
  ISSUED_TO: "issued_to",
  START_TIME: "start_time",
  END_TIME: "end_time",
  EXPIRES_IN: "expires_in",
  ITEMS: "items",
  TREATMENT_ANALYSIS: "treatment_analysis",
  NO_SHOW_APPOINTMENTS: "no_show_appointments",
  AVG_DAILY_PRODUCTION: "avg_daily_production",
  NEW_PATIENTS: "new_patients",
};

// NUMERIC CONSTANTS
export const NUMERIC_CONSTANTS = {
  PRACTICE_ID_DEFAULT: 1,
  RADIX_DECIMAL: 10,
};

// HTTP HEADER CONSTANTS
export const HTTP_HEADER_CONSTANTS = {
  REQUEST_KEY_HEADER: "Request-Key",
};

// KPI KEYS - Single source of truth for all KPI identifiers
export const KPI_KEYS = {
  ACCOUNT_RECEIVABLES: "account_receivables",
  TREATMENT_ANALYSIS: "treatment_analysis",
  HYGIENE_REAPPOINTMENT: "hygiene_reappointment",
  AVG_DAILY_PRODUCTION: "avg_daily_production",
  NEW_PATIENTS: "new_patients",
  NO_SHOW_APPOINTMENTS: "no_show_appointments",
  DIRECT_RESTORATIONS: "direct_restorations",
  TOTAL_PRODUCTION_PER_DAY: "total_production_per_day",
  TOTAL_PRODUCTION_BY_DENTIST: "total_production_by_dentist",
  TOTAL_PRODUCTION_BY_HYGIENIST: "total_production_by_hygienist",
  GROSS_COLLECTION: "gross_collection",
};

// KPI MESSAGES
export const KPI_MESSAGES = {
  ACCOUNT_RECEIVABLES_FETCHED:
    "Account receivables fetched and stored successfully",
  TREATMENT_ANALYSIS_FETCHED:
    "Treatment analysis fetched and stored successfully",
  HYGIENE_REAPPOINTMENT_FETCHED:
    "Hygiene reappointment fetched and stored successfully",
  AVG_DAILY_PRODUCTION_FETCHED:
    "Avg daily production fetched and stored successfully",
  NEW_PATIENTS_FETCHED: "New patients fetched and stored successfully",
  NO_SHOW_APPOINTMENTS_FETCHED:
    "No show appointments fetched and stored successfully",
  DIRECT_RESTORATIONS_FETCHED:
    "Direct restorations fetched and stored successfully",
  TOTAL_PRODUCTION_PER_DAY_FETCHED:
    "Total production per day fetched and stored successfully",
  TOTAL_PRODUCTION_BY_DENTIST_FETCHED:
    "Total production by dentist fetched and stored successfully",
  TOTAL_PRODUCTION_BY_HYGIENIST_FETCHED:
    "Total production by hygienist fetched and stored successfully",
  GROSS_COLLECTION_FETCHED:
    "Gross collections fetched and stored successfully",
};

// BATCH PROCESSING CONSTANTS
export const BATCH_PROCESSING = {
  FIELD_REPORT_RESPONSE: "report_response",
  STATUS_SUCCESS: "success",
  STATUS_FAIL: "fail",
  FIELD_NAME: "name",
  FIELD_STATUS: "status",
  FIELD_TOTAL: "total",
  FIELD_SUCCESS: "success",
  FIELD_FAIL: "fail",
  FIELD_FAILED: "failed",
  FIELD_SUMMARY: "summary",
  FIELD_ERROR: "error",
  FIELD_MESSAGE: "message",
  FIELD_ERRORS: "errors",
  FIELD_HAS_DATA: "hasData",
  FIELD_IS_SUCCESS: "isSuccess",
  RESPONSE_FOR: "Response for",
  NO_RESPONSE_DATA_OR_STATUS: "No response data or status for",
  FAILED_TO_PROCESS: "Failed to process",
  ERROR_IN: "Error in",
};

// DATABASE ERROR MESSAGES
export const DATABASE_ERRORS = {
  RELATION: "relation",
  DOES_NOT_EXIST: "does not exist",
};

// FILE SYSTEM CONSTANTS
export const FILE_SYSTEM = {
  MODEL_JS_EXTENSION: ".model.js",
  JS_EXTENSION: ".js",
  TABLES_DIRECTORY: "../models/tables",
};

// API URL CONSTANTS
export const API_URLS = {
  KPI_BASE: "/kpis/",
  STARTDATE_PARAM: "startdate",
  ENDDATE_PARAM: "enddate",
  PRACTICE_ID_PARAM: "practice_id",
  ORGANIZATION_SYNC_BASE: "/organization/sync/",
  ORGANIZATION_CHECK_OFFICE_ID_ASSOCIATION: "/organization/check-office-id-association/",
};

// SYNC CONSTANTS
export const SYNC_CONSTANTS = {
  TYPE_SIKKA: "sikka",
};

// LOG MESSAGES
export const LOG_MESSAGES = {
  FETCHING_ORGANIZATION_DETAILS: "Fetching organization details for office_id:",
  RETRIEVED_SCHEMA_NAME: "Retrieved schema name:",
  FOR_OFFICE_ID: "for office_id:",
  FAILED_TO_RETRIEVE_ORGANIZATION:
    "Failed to retrieve organization details for office_id:",
  ERROR_FETCHING_ORGANIZATION:
    "Error fetching organization details for office_id",
  SUCCESSFULLY_STORED_RECORDS: "Successfully stored",
  RECORDS: "records",
  IN_SCHEMA_FOR_ORGANIZATION: "in schema for organization:",
  SUCCESSFULLY_RETRIEVED_INFORMATION: "Successfully retrieved information for",
  MODELS: "models",
  ORGANIZATION_SYNC_STATUS_UPDATED:
    "Organization sync status updated for org_id:",
  WITH_LAST_SYNCED_AT: "with lastSyncedAt:",
  COULD_NOT_FETCH_ORGANIZATION_ID:
    "Could not fetch organization ID for office_id:",
};

// EMAIL CONSTANTS
export const EMAIL_CONSTANTS = {
  SIKKA_KPI_REPORTS_SYNC_FAILED_SUBJECT:
    "Sikka KPI Reports Sync - Some Reports Failed",
  SIKKA_KPI_REPORTS_SYNC_FAILED_TEXT:
    "Your organization's Sikka KPI sync encountered failures.",
  SIKKA_KPI_REPORTS_FAILED_TO_SYNC: "The following KPI reports failed to sync:",
  SIKKA_KPI_REPORTS_SYNC_CHECK_LOGS:
    "Please check the system logs for more details.",
  SIKKA_KPI_REPORTS_SYNC_CONTACT_SUPPORT:
    "Please contact the support team if you need assistance.",
  MISSING_RECIPIENT_OR_SENDER_EMAIL:
    "Cannot send email: missing recipient or sender email",
  SIKKA_SYNC_FAILED_EMAIL_SENT:
    "Sikka KPI reports sync failure email notification sent",
  SIKKA_SYNC_FAILED_EMAIL_FAILED:
    "Failed to send Sikka KPI reports sync failure email notification",
  SIKKA_KPI_REPORTS_SYNC_FAILED_HTML_TEMPLATE: (
    failedApis
  ) => `<h2>Sikka KPI Reports Sync - Some Reports Failed</h2>
             <p>Your organization's Sikka KPI sync encountered failures.</p>
             <p><strong>The following KPI reports failed to sync:</strong></p>
             <ul>
               ${failedApis
                 .map((api) => `<li>${api.name}: ${api.error}</li>`)
                 .join("")}
             </ul>
             <p>Please contact the support team if you need assistance.</p>`,
  SIKKA_KPI_REPORTS_SYNC_FAILED_TEXT_TEMPLATE: (failedApis) =>
    `Your organization's Sikka KPI sync encountered failures.\n\nThe following KPI reports failed to sync:\n${failedApis
      .map((api) => `- ${api.name}: ${api.error}`)
      .join(
        "\n"
      )}\n\nPlease check the system logs for more details.\n\nPlease contact the support team if you need assistance.`,
};

// VALIDATION FIELD MESSAGES
export const VALIDATION_FIELD_MESSAGES = {
  MISSING_REQUIRED_FIELDS: "Missing required fields:",
};

/**
 * KPI Controllers Configuration Factory
 * Creates the list of KPIs with their corresponding controller functions
 * @param {Object} kpisController - Object containing all KPI controller functions
 * @returns {Array} Array of KPI controller configurations
 */
export const createKpiControllersConfig = (kpisController) => {
  return [
    {
      name: KPI_KEYS.ACCOUNT_RECEIVABLES,
      controller: kpisController.accountReceivables,
    },
    {
      name: KPI_KEYS.TREATMENT_ANALYSIS,
      controller: kpisController.treatmentAnalysis,
    },
    {
      name: KPI_KEYS.AVG_DAILY_PRODUCTION,
      controller: kpisController.avgDailyProduction,
    },
    {
      name: KPI_KEYS.NO_SHOW_APPOINTMENTS,
      controller: kpisController.noShowAppointments,
    },
    {
      name: KPI_KEYS.NEW_PATIENTS,
      controller: kpisController.newPatients,
    },
    {
      name: KPI_KEYS.TOTAL_PRODUCTION_PER_DAY,
      controller: kpisController.totalProductionPerDay,
    },
    {
      name: KPI_KEYS.TOTAL_PRODUCTION_BY_DENTIST,
      controller: kpisController.totalProductionByDentist,
    },
    {
      name: KPI_KEYS.DIRECT_RESTORATIONS,
      controller: kpisController.directRestorations,
    },
    {
      name: KPI_KEYS.HYGIENE_REAPPOINTMENT,
      controller: kpisController.hygieneReappointment,
    },
    {
      name: KPI_KEYS.TOTAL_PRODUCTION_BY_HYGIENIST,
      controller: kpisController.totalProductionByHygienist,
    },
     {
      name: KPI_KEYS.GROSS_COLLECTION,
      controller: kpisController.grossCollection,
    },
  ];
};
