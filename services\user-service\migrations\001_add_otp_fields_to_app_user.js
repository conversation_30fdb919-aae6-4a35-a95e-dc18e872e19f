"use strict";

const SCHEMA_NAME = "Authentication";
const TABLE_NAME = "app_user";
const TABLE_REFERENCE = { tableName: TABLE_NAME, schema: SCHEMA_NAME };

export const up = async (queryInterface, Sequelize) => {
  const tableDescription = await queryInterface.describeTable(
    TABLE_REFERENCE
  );

  // Add otp column if it doesn't exist
  if (!tableDescription.otp) {
    await queryInterface.addColumn(
      TABLE_REFERENCE,
      "otp",
      {
        type: Sequelize.TEXT,
        allowNull: true,
      }
    );
  }

  // Add otp_expiry column if it doesn't exist
  if (!tableDescription.otp_expiry) {
    await queryInterface.addColumn(
      TABLE_REFERENCE,
      "otp_expiry",
      {
        type: Sequelize.DATE,
        allowNull: true,
      }
    );
  }
};

export const down = async (queryInterface) => {
  const tableDescription = await queryInterface.describeTable(
    TABLE_REFERENCE
  );

  // Remove otp_expiry column if it exists
  if (tableDescription.otp_expiry) {
    await queryInterface.removeColumn(
      TABLE_REFERENCE,
      "otp_expiry"
    );
  }

  // Remove otp column if it exists
  if (tableDescription.otp) {
    await queryInterface.removeColumn(
      TABLE_REFERENCE,
      "otp"
    );
  }
};

