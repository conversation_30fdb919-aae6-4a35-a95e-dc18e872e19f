import { httpPost } from "../../../../shared/utils/axios.util.js";

/**
 * Execute the Power BI workflow request with retry logic.
 *
 * @param {Object} options
 * @param {string} options.url - Workflow endpoint URL
 * @param {Object} options.payload - Payload to send to workflow
 * @param {string} options.apiKey - API key for authentication
 * @param {number} options.timeout - Request timeout in milliseconds
 * @param {import("node:https").Agent} options.httpsAgent - HTTPS agent configuration
 * @param {number} options.maxRetries - Maximum number of retries
 * @param {number} options.baseDelayMs - Base delay (ms) for exponential backoff
 * @param {Function} [options.onRetry] - Optional callback invoked before each retry attempt
 * @returns {Promise<import("axios").AxiosResponse>} Axios response object
 */
export const executePowerBiRequest = async ({
  url,
  payload,
  apiKey,
  timeout,
  httpsAgent,
  maxRetries,
  baseDelayMs,
  onRetry,
}) => {
  let attempt = 0;

  while (attempt <= maxRetries) {
    try {
      return await httpPost(url, payload, {
        headers: {
          "Content-Type": "application/json",
          "x-api-key": apiKey,
        },
        timeout,
        httpsAgent,
        maxRedirects: 0,
      });
    } catch (error) {
      attempt += 1;
      const status = error?.response?.status;

      // Do not retry on most 4xx errors (except 429)
      if (status && status >= 400 && status < 500 && status !== 429) {
        throw error;
      }

      if (attempt > maxRetries) {
        throw error;
      }

      const delay = baseDelayMs * Math.pow(2, attempt - 1);

      if (typeof onRetry === "function") {
        await onRetry({ attempt, maxRetries, delay, error, url });
      } else {
        await new Promise((resolve) => setTimeout(resolve, delay));
      }
    }
  }

  throw new Error("Power BI request failed unexpectedly without response");
};

export default {
  executePowerBiRequest,
};
