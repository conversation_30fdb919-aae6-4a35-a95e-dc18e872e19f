// Utility functions for formatting data
import { MESSAGES } from "../constants/messages";

/**
 * Format date strings into human-readable format
 * @param {string|Date} dateString - Date to format
 * @param {string} format - Format type: 'short', 'long', 'relative'
 * @returns {string} Formatted date string
 */
export const formatDate = (dateString, format = "short") => {
  if (!dateString) return "-";

  const date = new Date(dateString);

  if (format === "short") {
    return date.toLocaleDateString();
  } else if (format === "long") {
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  } else if (format === "relative") {
    const now = new Date();
    const diffTime = Math.abs(now - date);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 0) return MESSAGES.TIME.JUST_NOW;
    if (diffDays === 1) return "Yesterday";
    if (diffDays < 7) return `${diffDays} days ago`;
    if (diffDays < 30) return `${Math.ceil(diffDays / 7)} weeks ago`;
    return date.toLocaleDateString();
  }

  return date.toLocaleDateString();
};

/**
 * Format numbers with various options
 * @param {number} number - Number to format
 * @param {string} format - Format type: 'default', 'currency', 'percentage', 'compact'
 * @returns {string} Formatted number string
 */
export const formatNumber = (number, format = "default") => {
  if (number === null || number === undefined) return "-";

  if (format === "currency") {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(number);
  } else if (format === "percentage") {
    return `${number}%`;
  } else if (format === "compact") {
    return new Intl.NumberFormat("en-US", {
      notation: "compact",
      maximumFractionDigits: 1,
    }).format(number);
  }

  return new Intl.NumberFormat("en-US").format(number);
};

/**
 * Format file size in human-readable format
 * @param {number} bytes - File size in bytes
 * @returns {string} Formatted file size
 */
export const formatFileSize = (bytes) => {
  if (bytes === 0) return "0 Bytes";

  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB", "TB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
};

/**
 * Format phone numbers
 * @param {string} phoneNumber - Phone number to format
 * @returns {string} Formatted phone number
 */
export const formatPhoneNumber = (phoneNumber) => {
  if (!phoneNumber) return "-";

  // Remove all non-digit characters
  const cleaned = phoneNumber.replace(/\D/g, "");

  // Format as (XXX) XXX-XXXX for US numbers
  if (cleaned.length === 10) {
    return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(
      6
    )}`;
  }

  // Return original if not a standard US number
  return phoneNumber;
};

/**
 * Truncate text with ellipsis
 * @param {string} text - Text to truncate
 * @param {number} maxLength - Maximum length before truncation
 * @returns {string} Truncated text
 */
export const truncateText = (text, maxLength = 50) => {
  if (!text) return "";
  if (text.length <= maxLength) return text;
  return text.slice(0, maxLength) + "...";
};

/**
 * Get initials from a name
 * @param {string} name - Full name
 * @returns {string} Initials
 */
export const getInitials = (name) => {
  if (!name) return "";
  return name
    .split(" ")
    .map((n) => n[0])
    .join("")
    .toUpperCase();
};

/**
 * Format address into a single line
 * @param {Object} address - Address object with street, city, state, zip
 * @returns {string} Formatted address
 */
export const formatAddress = (address) => {
  if (!address) return "-";

  const parts = [
    address.street,
    address.city,
    address.state,
    address.zip,
  ].filter(Boolean);

  return parts.join(", ");
};

/**
 * Format time duration in human-readable format
 * @param {number} seconds - Duration in seconds
 * @returns {string} Formatted duration
 */
export const formatDuration = (seconds) => {
  if (!seconds) return "0s";

  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const remainingSeconds = seconds % 60;

  if (hours > 0) {
    return `${hours}h ${minutes}m ${remainingSeconds}s`;
  } else if (minutes > 0) {
    return `${minutes}m ${remainingSeconds}s`;
  } else {
    return `${remainingSeconds}s`;
  }
};

/**
 * Format percentage with proper rounding
 * @param {number} value - Value to convert to percentage
 * @param {number} total - Total value for percentage calculation
 * @param {number} decimals - Number of decimal places
 * @returns {string} Formatted percentage
 */
export const formatPercentage = (value, total, decimals = 1) => {
  if (!value || !total) return "0%";
  const percentage = (value / total) * 100;
  return `${percentage.toFixed(decimals)}%`;
};

/**
 * Format status text with proper capitalization
 * @param {string} status - Status string
 * @returns {string} Formatted status
 */
export const formatStatus = (status) => {
  if (!status) return "";
  return status.charAt(0).toUpperCase() + status.slice(1).toLowerCase();
};

/**
 * Format date to DD/MM/YYYY format
 * @param {string|Date} dateString - Date to format
 * @returns {string} Formatted date string in DD/MM/YYYY format
 */
export const formatDateToMMDDYYYY = (dateString) => {
  if (!dateString) return "-";

  const date = new Date(dateString);

  // Check if date is valid
  if (Number.isNaN(date.getTime())) return "-";

  const month = String(date.getMonth() + 1).padStart(2, "0");
  const day = String(date.getDate()).padStart(2, "0");
  const year = date.getFullYear();

  return `${month}/${day}/${year}`;
};

/**
 * Given a month string like 'January 2025', return start/end ISO date strings (YYYY-MM-DD, local time).
 * @param {string} monthYearStr - Month and year string (e.g., 'January 2025')
 * @returns {{ startDate: string, endDate: string }}
 */
export function getMonthDateRange(monthYearStr) {
  if (!monthYearStr || typeof monthYearStr !== "string")
    return { startDate: "", endDate: "" };
  const parsedDate = new Date(monthYearStr);
  if (Number.isNaN(parsedDate.getTime())) return { startDate: "", endDate: "" };

  const year = parsedDate.getFullYear();
  const month = parsedDate.getMonth(); // 0-based

  // Start: first day of the month (local)
  const startDate = new Date(year, month, 1);
  // End: last day of the month (local)
  const endDate = new Date(year, month + 1, 0);

  // Pad helper
  const pad = (num) => String(num).padStart(2, "0");
  const startStr = `${startDate.getFullYear()}-${pad(
    startDate.getMonth() + 1
  )}-${pad(startDate.getDate())}`;
  const endStr = `${endDate.getFullYear()}-${pad(endDate.getMonth() + 1)}-${pad(
    endDate.getDate()
  )}`;

  return {
    startDate: startStr,
    endDate: endStr,
  };
}

const currencyFormatter = new Intl.NumberFormat("en-US", {
  style: "currency",
  currency: "USD",
  maximumFractionDigits: 0,
});

const compactCurrencyFormatter = new Intl.NumberFormat("en-US", {
  style: "currency",
  currency: "USD",
  notation: "compact",
  maximumFractionDigits: 2,
});

const percentFormatter = new Intl.NumberFormat("en-US", {
  style: "percent",
  maximumFractionDigits: 2,
});

export function formatCardValue(value, format = "currency" | "percent") {
  if (format === "currency") {
    return currencyFormatter.format(value);
  }
  return `${value.toFixed(2)}%`;
}

export function formatCompactCurrency(value) {
  if (value >= 1000) {
    return compactCurrencyFormatter.format(value);
  }
  return currencyFormatter.format(value);
}

export function formatPercent(value) {
  return percentFormatter.format(value / 100);
}

// export function formatNumber(value) {
//   return new Intl.NumberFormat("en-US", { maximumFractionDigits: 0 }).format(
//     value
//   );
// }
