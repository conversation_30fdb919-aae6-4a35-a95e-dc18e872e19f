"use client";

import { formatCompactCurrency } from "../../utils/methods/formatters";
import { EChartWrapper } from "./EChartWrapper";

export function CollectionsComparisonChart({ data }) {
  const categories = data.comparison.map((item) => item.period);
  const patientSeries = data.comparison.map((item) => item.patientCollection);
  const insuranceSeries = data.comparison.map((item) => item.insuranceCollection);

  const option = {
    color: ["#2f7ed8", "#fab349ff"],
    tooltip: {
      trigger: "axis",
      axisPointer: { type: "shadow" },
      formatter: (params) => {
        let result = `${params[0].axisValue}<br/>`;
        params.forEach((param) => {
          result += `${param.marker} ${param.seriesName}: ${formatCompactCurrency(param.value)}<br/>`;
        });
        return result;
      },
    },
    legend: {
      data: ["Patient Collection", "Insurance Collection"],
      bottom: 0,
      left: "center",
    },
    grid: { left: "3%", right: "4%", bottom: "15%", top: "10%", containLabel: true },
    xAxis: {
      type: "category",
      data: categories,
      axisLabel: { color: "#1e293b", fontWeight: 600 },
    },
    yAxis: {
      type: "value",
      axisLabel: { formatter: (value) => formatCompactCurrency(value), color: "#475569" },
      splitLine: { lineStyle: { color: "#e2e8f0" } },
    },
    series: [
      {
        name: "Patient Collection",
        type: "bar",
        data: patientSeries,
        barWidth: 36,
        barGap: "30%",
        itemStyle: { borderRadius: [0, 0, 0, 0] },
        label: {
          show: true,
          position: "top",
          formatter: ({ value }) => formatCompactCurrency(value),
        },
      },
      {
        name: "Insurance Collection",
        type: "bar",
        data: insuranceSeries,
        barWidth: 36,
        barGap: "30%",
        itemStyle: { borderRadius: [0, 0, 0, 0] },
        label: {
          show: true,
          position: "top",
          formatter: ({ value }) => formatCompactCurrency(value),
        },
      },
    ],
  };

  return <EChartWrapper option={option} />;
}
