"use client";

import { memo } from "react";
import { Button } from "../ui/button";
import { Textarea } from "../ui/textarea";
import { Send } from "lucide-react";
import { CHAT_MESSAGES } from "@/utils/constants/chat";
import "@/styles/cfo-insights.css";

export const CFOInputArea = memo(function CFOInputArea({
  question,
  isLoading,
  onChange,
  onSubmit,
}) {
  const handleKeyDown = (e) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      onSubmit(e);
    }
  };

  return (
    <div className="cfo-insights-input-card">
      <div className="cfo-insights-input-content">
        <div className="cfo-insights-char-count">
          {`${question.length}/2000`}
        </div>
        <form onSubmit={onSubmit} className="cfo-insights-input-form">
          <div className="cfo-insights-input-wrapper">
            <Textarea
              value={question}
              onChange={onChange}
              placeholder={CHAT_MESSAGES.PLACEHOLDER}
              className="cfo-insights-textarea"
              disabled={isLoading}
              rows={1}
              maxLength={2000}
              onKeyDown={handleKeyDown}
            />
          </div>
            <Button
              type="submit"
              disabled={!question.trim() || isLoading}
              size="sm"
              className="cfo-insights-send-button"
            >
              <Send className="cfo-insights-send-icon" />
            </Button>
        </form>
      </div>
    </div>
  );
});
