/**
 * Format chat response from JSON format (responseType, narrative, comparisonData)
 * @param {Object} response - Chat response object with responseType, narrative, comparisonData
 * @returns {string} Formatted HTML string
 */
export const formatChatResponse = (response) => {
  if (!response || typeof response !== "object") {
    return "";
  }

  let html = "";

  // Extract narrative (description)
  const narrative = response.narrative || "";
  if (narrative) {
    html += `<div class="chat-narrative">${narrative}</div>`;
  }

  // Extract comparisonData and render table if available
  const comparisonData = response.comparisonData;
  if (comparisonData && comparisonData.type !== "NONE") {
    const { type, tableHeaders, tableRows, qualitativeSummary, sourceUrls } = comparisonData;

    // Render table if headers and rows are available
    if (
      tableHeaders &&
      Array.isArray(tableHeaders) &&
      tableHeaders.length > 0 &&
      tableRows &&
      Array.isArray(tableRows) &&
      tableRows.length > 0
    ) {
      html += `<div class="chat-comparison-table mt-4">`;
      html += `<div class="overflow-x-auto mt-2 mb-6 rounded-xl border border-gray-300 shadow-lg bg-white">`;
      html += `<table class="w-full border-collapse min-w-full">`;
      
      // Table header
      html += `<thead>`;
      html += `<tr class="bg-gradient-to-r from-indigo-600 to-indigo-700">`;
      tableHeaders.forEach((header) => {
        html += `<th class="text-white font-bold text-sm px-4 py-3 text-left border-b-2 border-indigo-800 uppercase tracking-wide">${header}</th>`;
      });
      html += `</tr>`;
      html += `</thead>`;

      // Table body
      html += `<tbody>`;
      tableRows.forEach((row, index) => {
        html += `<tr class="hover:bg-indigo-50/50 transition-colors duration-150 even:bg-gray-50/30">`;
        row.forEach((cell) => {
          // Check if this is a URL (last column might be source URLs)
          const isUrl = typeof cell === "string" && (cell.startsWith("http://") || cell.startsWith("https://"));
          if (isUrl) {
            html += `<td class="px-4 py-3 border-b border-gray-200 text-gray-700 text-sm bg-white"><a href="${cell}" target="_blank" rel="noopener noreferrer" class="text-indigo-600 hover:text-indigo-800 underline">Source</a></td>`;
          } else {
            html += `<td class="px-4 py-3 border-b border-gray-200 text-gray-700 text-sm bg-white">${cell || "N/A"}</td>`;
          }
        });
        html += `</tr>`;
      });
      html += `</tbody>`;
      html += `</table>`;
      html += `</div>`;
      html += `</div>`;
    }

    // Render qualitative summary if available and no table data
    if (qualitativeSummary && (!tableRows || tableRows.length === 0)) {
      html += `<div class="chat-qualitative-summary mt-4 p-4 bg-blue-50 rounded-lg border border-blue-200">`;
      html += `<p class="text-gray-700 text-sm leading-relaxed">${qualitativeSummary}</p>`;
      html += `</div>`;
    }

    // Render source URLs if available
    if (sourceUrls && Array.isArray(sourceUrls) && sourceUrls.length > 0) {
      html += `<div class="chat-sources mt-3">`;
      html += `<p class="text-xs text-gray-500 mb-2">Sources:</p>`;
      html += `<ul class="list-disc list-inside space-y-1">`;
      sourceUrls.forEach((url) => {
        html += `<li><a href="${url}" target="_blank" rel="noopener noreferrer" class="text-indigo-600 hover:text-indigo-800 underline text-xs">${url}</a></li>`;
      });
      html += `</ul>`;
      html += `</div>`;
    }
  }

  return html;
};

/**
 * Check if response is a JSON chat response format
 * @param {any} response - Response to check
 * @returns {boolean} True if response has JSON chat format
 */
export const isJsonChatResponse = (response) => {
  return (
    response &&
    typeof response === "object" &&
    (response.responseType || response.narrative || response.comparisonData)
  );
};

