"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useDispatch, useSelector } from "react-redux";
import PageWrapper from "@/components/common/PageWrapper";
import {
  renderStatusBadge,
  renderActionButtons,
  renderRoleBadge,
} from "@/components/common/TableRenderers";
import DeleteModal from "@/components/common/DeleteModal";
import { USER_CONSTANTS } from "@/utils/constants/user";
import { fetchUsers, deleteUser } from "@/redux/Thunks/userThunks.js";
import { fetchOrganizations } from "@/redux/Thunks/organization.js";
import { useToast } from "@/components/ui/toast";

export default function UserPage() {
  const router = useRouter();
  const dispatch = useDispatch();
  const { addToast } = useToast();
  const [deleteModal, setDeleteModal] = useState({ isOpen: false, item: null });
  const [isDeleting, setIsDeleting] = useState(false);

  // Redux state
  const { users, loading, error } = useSelector((state) => state.users);
  const { organizations } = useSelector((state) => state.organizations);

  // Fetch users and organizations on component mount
  useEffect(() => {
    dispatch(fetchUsers());
    dispatch(fetchOrganizations());
  }, [dispatch]);

  // Action handlers
  const handleView = (item) => {
    router.push(`/masters/user/view/${item.id}`);
  };

  const handleEdit = (item) => {
    router.push(`/masters/user/edit/${item.id}`);
  };

  const handleDelete = (item) => {
    setDeleteModal({ isOpen: true, item });
  };

  const confirmDelete = async () => {
    if (!deleteModal.item) return;

    setIsDeleting(true);
    try {
      await dispatch(deleteUser(deleteModal.item.id));
      setDeleteModal({ isOpen: false, item: null });
      // Refresh the users list
      dispatch(fetchUsers());
    } catch (error) {
      addToast("Failed to delete user. Please try again.", "error");
    } finally {
      setIsDeleting(false);
    }
  };
  // Ensure users is always an array
  const usersArray = Array.isArray(users) ? users : [];
  const organizationsArray = Array.isArray(organizations) ? organizations : [];

  // Function to get organization name by ID
  const getOrganizationName = (organizationId) => {
    const organization = organizationsArray.find(
      (org) => org.id === organizationId
    );
    return organization ? organization.name : "-";
  };

  // Stats configuration
  const stats = [
    {
      title: USER_CONSTANTS.STATS.TOTAL_USERS.TITLE,
      value: usersArray.length.toString(),
      icon: USER_CONSTANTS.STATS.TOTAL_USERS.ICON,
      bgColor: USER_CONSTANTS.STATS.TOTAL_USERS.BG_COLOR,
      iconColor: USER_CONSTANTS.STATS.TOTAL_USERS.ICON_COLOR,
    },
    {
      title: USER_CONSTANTS.STATS.ACTIVE_USERS.TITLE,
      value: usersArray.filter((u) => u.is_active === true).length.toString(),
      icon: USER_CONSTANTS.STATS.ACTIVE_USERS.ICON,
      bgColor: USER_CONSTANTS.STATS.ACTIVE_USERS.BG_COLOR,
      iconColor: USER_CONSTANTS.STATS.ACTIVE_USERS.ICON_COLOR,
    },
    {
      title: USER_CONSTANTS.STATS.INACTIVE_USERS.TITLE,
      value: usersArray.filter((u) => u.is_active === false).length.toString(),
      icon: USER_CONSTANTS.STATS.INACTIVE_USERS.ICON,
      bgColor: USER_CONSTANTS.STATS.INACTIVE_USERS.BG_COLOR,
      iconColor: USER_CONSTANTS.STATS.INACTIVE_USERS.ICON_COLOR,
    },
    {
      title: USER_CONSTANTS.STATS.ADMINS.TITLE,
      value: usersArray
        .filter((u) => u.role === USER_CONSTANTS.ROLES.ADMIN)
        .length.toString(),
      icon: USER_CONSTANTS.STATS.ADMINS.ICON,
      bgColor: USER_CONSTANTS.STATS.ADMINS.BG_COLOR,
      iconColor: USER_CONSTANTS.STATS.ADMINS.ICON_COLOR,
    },
  ];

  // Table columns configuration
  const columns = [
    {
      key: "full_name",
      title: USER_CONSTANTS.TABLE_HEADERS.NAME,
      width: "20%",
    },
    {
      key: "email",
      title: USER_CONSTANTS.TABLE_HEADERS.EMAIL,
      width: "20%",
    },
    {
      key: "role",
      title: USER_CONSTANTS.TABLE_HEADERS.ROLE,
      width: "15%",
      render: (value) => renderRoleBadge(value),
    },
    {
      key: "is_active",
      title: USER_CONSTANTS.TABLE_HEADERS.STATUS,
      width: "10%",
      render: (value) => renderStatusBadge(value ? "active" : "inactive"),
    },
    {
      key: "organization_id",
      title: "Organization",
      width: "15%",
      render: (value) => getOrganizationName(value),
    },
    // {
    //   key: "last_login",
    //   title: USER_CONSTANTS.TABLE_HEADERS.LAST_LOGIN,
    //   width: "10%",
    //   render: (value) =>
    //     value ? new Date(value).toLocaleDateString() : "Never",
    // },
    // {
    //   key: "actions",
    //   title: USER_CONSTANTS.TABLE_HEADERS.ACTIONS,
    //   width: "15%",
    //   sortable: false,
    //   render: (_, item) =>
    //     renderActionButtons(item, handleView, handleEdit, handleDelete),
    // },
  ];

  // Filters configuration
  const filters = [
    {
      key: "status",
      options: [
        USER_CONSTANTS.FILTERS.STATUS.ALL,
        USER_CONSTANTS.FILTERS.STATUS.ACTIVE,
        USER_CONSTANTS.FILTERS.STATUS.INACTIVE,
      ],
      defaultValue: USER_CONSTANTS.FILTERS.STATUS.ALL,
    },
    {
      key: "role",
      options: [
        USER_CONSTANTS.FILTERS.ROLE.ALL,
        USER_CONSTANTS.FILTERS.ROLE.ADMIN,
        USER_CONSTANTS.FILTERS.ROLE.MANAGER,
        USER_CONSTANTS.FILTERS.ROLE.USER,
      ],
      defaultValue: USER_CONSTANTS.FILTERS.ROLE.ALL,
    },
  ];

  return (
    <>
      <PageWrapper
        title={USER_CONSTANTS.PAGE_TITLE}
        subtitle={USER_CONSTANTS.PAGE_SUBTITLE}
        searchPlaceholder={USER_CONSTANTS.SEARCH_PLACEHOLDER}
        addButtonText={USER_CONSTANTS.ADD_BUTTON_TEXT}
        addButtonPath={USER_CONSTANTS.ADD_BUTTON_PATH}
        stats={stats}
        data={usersArray}
        columns={columns}
        filters={filters}
        itemsPerPage={10}
        loading={loading}
        loadingMessage="Loading users..."
        error={error}
      />

      <DeleteModal
        isOpen={deleteModal.isOpen}
        onClose={() => setDeleteModal({ isOpen: false, item: null })}
        onConfirm={confirmDelete}
        title={USER_CONSTANTS.DELETE_MODAL.TITLE}
        description={USER_CONSTANTS.DELETE_MODAL.DESCRIPTION}
        itemName={deleteModal.item?.name}
        isLoading={isDeleting}
      />
    </>
  );
}
