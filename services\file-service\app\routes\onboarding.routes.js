import express from "express";
import { uploadFile } from "../controllers/onboarding.controller.js";
import { authMiddleware } from "../middleware/auth.middleware.js";
import { parseFormDataField, handleUnifiedUpload } from "../middleware/upload.middleware.js";
import { validateRequest } from "../middleware/validation.middleware.js";
import { uploadFileValidator } from "../validators/onboarding.validator.js";

const router = express.Router();

/**
 * Unified file upload endpoint - Single route for all uploads
 * 
 * 1. Logo: POST /file-upload
 *    Body: { orgId, type: "logo" }
 *    - Stores at: {orgId}/org-logos/logo_{timestamp}.{ext}
 * 
 * 2. Reports: POST /file-upload
 *    Body: { orgId, orgName, type: "report", service, year, month }
 *    - Stores at: {orgId}/{orgName}/Reports/{service}/{year}/{month}/{fileName}
 */
router.post(
  "/file-upload",
  authMiddleware,
  parseFormDataField,
  handleUnifiedUpload,
  validateRequest(uploadFileValidator),
  uploadFile
);

export default router;
