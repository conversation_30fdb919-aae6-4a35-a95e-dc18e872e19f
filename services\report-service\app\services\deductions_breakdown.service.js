import DeductionsBreakdownRepository from "../repository/deductions_breakdown.repository.js";
import { LOGGER_NAMES } from "../utils/constants/log.constants.js";
import { createLogger } from "../utils/logger.utils.js";
import {
  isValidMonth,
  isValidYear,
  parseNumericValue,
  formatToKWithDecimals,
} from "../utils/format.utils.js";
import { getMonthDateRange } from "../utils/repository.utils.js";

const logger = createLogger(LOGGER_NAMES.REPORT_SERVICE);

/**
 * Format deduction name to proper display format
 * @param {string} fieldName - Field name to format
 * @returns {string} Formatted deduction name
 */
const formatDeductionName = (fieldName) => {
  const nameMap = {
    retirement_401k: "401K Retirement",
    retirement_401k_percentage: "Retirement 401K Percentage",
    blue_cross_pre_tax: "Blue Cross Pre Tax",
  };
  return nameMap[fieldName] || fieldName;
};

/**
 * Get deductions breakdown data for organization
 * @param {Object} params - Query parameters
 * @param {string} params.organization_id - Organization ID
 * @param {number} params.month - Month (1-12)
 * @param {number} params.year - Year
 * @returns {Promise<Object>} Deductions breakdown data
 */
const getDeductionsBreakdownData = async ({ organization_id, month, year }) => {
  try {
    logger.info(
      `Fetching deductions breakdown for org: ${organization_id}, month: ${month}, year: ${year}`
    );

    // Validate parameters
    if (!organization_id) {
      throw new Error("Organization ID is required");
    }

    if (!month || !isValidMonth(month)) {
      throw new Error("Valid month (1-12) is required");
    }

    if (!year || !isValidYear(year)) {
      throw new Error("Valid year is required");
    }

    // Convert to numbers
    const monthNum = parseInt(month, 10);
    const yearNum = parseInt(year, 10);

    // Get organization schema name
    const schemaName =
      await DeductionsBreakdownRepository.getOrganizationSchemaName(
        organization_id
      );

    if (!schemaName) {
      logger.warn(`Schema not found for organization: ${organization_id}`);
      throw new Error("Organization not found or schema not configured");
    }

    // Calculate date range from month and year
    const { startDate, endDate } = getMonthDateRange(monthNum, yearNum);

    logger.info(
      `Calculated date range for month ${monthNum}, year ${yearNum}: ${startDate} to ${endDate}`
    );

    // Fetch deductions breakdown
    const deductionsData =
      await DeductionsBreakdownRepository.getDeductionsBreakdown(
        schemaName,
        startDate,
        endDate
      );

    // Parse and format values - default to 0 if field not available
    const retirement401k = parseNumericValue(
      deductionsData?.retirement_401k ?? 0
    );
    const retirement401kPercentage = parseNumericValue(
      deductionsData?.retirement_401k_percentage ?? 0
    );
    const blueCrossPreTax = parseNumericValue(
      deductionsData?.blue_cross_pre_tax ?? 0
    );
    const deductionTotal = parseNumericValue(
      deductionsData?.deduction_total ?? 0
    );

    logger.info(
      `Deductions breakdown data parsed - 401K: ${retirement401k}, 401K%: ${retirement401kPercentage}, Blue Cross: ${blueCrossPreTax}, Total: ${deductionTotal}`
    );

    // Build deductions array with name, amount, and percentage
    const deductions = [
      {
        deduction_name: formatDeductionName("retirement_401k"),
        deductions_amount: formatToKWithDecimals(retirement401k),
        deductions_percentage:
          deductionTotal > 0
            ? ((retirement401k / deductionTotal) * 100).toFixed(2)
            : "0.00",
      },
      {
        deduction_name: formatDeductionName("retirement_401k_percentage"),
        deductions_amount: formatToKWithDecimals(retirement401kPercentage),
        deductions_percentage:
          deductionTotal > 0
            ? ((retirement401kPercentage / deductionTotal) * 100).toFixed(2)
            : "0.00",
      },
      {
        deduction_name: formatDeductionName("blue_cross_pre_tax"),
        deductions_amount: formatToKWithDecimals(blueCrossPreTax),
        deductions_percentage:
          deductionTotal > 0
            ? ((blueCrossPreTax / deductionTotal) * 100).toFixed(2)
            : "0.00",
      },
    ];

    logger.info(
      `Deductions breakdown formatted successfully with ${deductions.length} deduction types`
    );

    // Return formatted response
    return {
      deductions_breakdown: deductions,
    };
  } catch (error) {
    logger.error(
      "Error in DeductionsBreakdownService.getDeductionsBreakdownData:",
      error
    );
    throw error;
  }
};

export default {
  getDeductionsBreakdownData,
};
