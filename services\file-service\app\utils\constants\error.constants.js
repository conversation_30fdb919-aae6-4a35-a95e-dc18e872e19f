// ERROR CONSTANTS
// Static messages for Error Handling

export const ERROR_MESSAGES = {
  // Generic Errors
  INTERNAL_SERVER_ERROR: "Internal server error",
  UNEXPECTED_ERROR: "An unexpected error occurred",
  REQUEST_PROCESSING_ERROR: "An error occurred processing your request",
  GENERIC_ERROR: "An unexpected error occurred.",
  INTERNAL_ERROR: "Internal service error.",

  // Not Found Errors
  ROUTE_NOT_FOUND: "Not Found",
  ROUTE_NOT_FOUND_MESSAGE: "Route not found",

  // File Upload Errors
  FILE_UPLOAD_ERROR: "File upload error",
  MULTER_ERROR: "MulterError",
  ONLY_PDF_ALLOWED: "Only PDF files are allowed",

  // JSON Parsing Errors
  JSON_PARSE_ERROR: "Invalid JSON format",
  JSON_SYNTAX_ERROR: "SyntaxError",

  // Database Errors
  DATABASE_ERROR: "Database error occurred",
  SEQUELIZE_VALIDATION_ERROR: "SequelizeValidationError",
  SEQUELIZE_UNIQUE_CONSTRAINT_ERROR: "SequelizeUniqueConstraintError",
  SEQUELIZE_DATABASE_ERROR: "SequelizeDatabaseError",

  // Chat / Summary Errors
  CHAT_SESSION_RESPONSE_INVALID: "Invalid response from chat service",
  CHAT_SUMMARY_RESPONSE_INVALID: "Invalid response from chat summary service",
  ORGANIZATION_LOOKUP_FAILED: "Unable to fetch organization details",
  ORGANIZATION_NOT_FOUND: "Organization not found",

  // Environment & Configuration
  ENVIRONMENT_VARIABLE_MISSING: "Environment variable is not configured",
  STORAGE_CONNECTION_STRING_MISSING: "Azure storage connection string is not configured",
  STORAGE_CONTAINER_NAME_MISSING: "Azure blob container name is not configured",
};

export const ERROR_LOG_MESSAGES = {
  ERROR_OCCURRED: "Error occurred",
  UNEXPECTED_ERROR: "An unexpected error occurred",
  DATABASE_ERROR: "Database error occurred",
};

export default {
  ERROR_MESSAGES,
  ERROR_LOG_MESSAGES,
};

