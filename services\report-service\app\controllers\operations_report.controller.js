import OperationsReportService from "../services/operations_report.service.js";
import { createLogger } from "../utils/logger.utils.js";
import { LOGGER_NAMES } from "../utils/constants/log.constants.js";
import {
  validateRequiredParams,
  handleControllerError,
} from "../utils/controller.utils.js";
import { storePdfToFileService } from "../utils/pdf-storage.utils.js";

const logger = createLogger(LOGGER_NAMES.REPORT_CONTROLLER);
const MONTHS = [
  "January",
  "February",
  "March",
  "April",
  "May",
  "June",
  "July",
  "August",
  "September",
  "October",
  "November",
  "December",
];

const generateOperationsPDF = async (req, res) => {
  try {
    const { organization_id, organization_name, month, year, store_to_blob } =
      req.query;

    const validationError = validateRequiredParams(req.query, [
      "organization_id",
      "month",
      "year",
    ]);
    if (validationError) return res.status(400).json(validationError);

    logger.info(
      `Generating operations PDF: org=${organization_id}, month=${month}, year=${year}`
    );

    const pdf = await OperationsReportService.generateOperationsReportPDF({
      organization_id,
      organization_name,
      month,
      year,
    });

    const filename = `Operations_Report_${
      MONTHS[parseInt(month) - 1]
    }_${year}.pdf`;

    // Store PDF to blob storage if requested
    if (store_to_blob === "true") {
      logger.info(
        `Storing operations PDF to blob storage: org=${organization_id}`
      );
      const storageResult = await storePdfToFileService({
        pdfBuffer: pdf,
        organization_id,
        organization_name,
        service: "operations",
        month: parseInt(month),
        year: parseInt(year),
        fileName: filename,
        refreshToken: req.cookies?.refresh_token,
      });

      if (storageResult.success) {
        logger.info(
          `Operations PDF stored successfully: ${storageResult.data?.blobPath}`
        );
      } else {
        logger.warn(
          `Failed to store operations PDF to blob: ${storageResult.error}`
        );
      }
    }

    res.setHeader("Content-Type", "application/pdf");
    res.setHeader("Content-Disposition", `attachment; filename="${filename}"`);
    res.send(pdf);
  } catch (error) {
    logger.error("Error generating operations PDF:", error);
    handleControllerError(error, res, "Error generating operations report PDF");
  }
};

export default { generateOperationsPDF };
