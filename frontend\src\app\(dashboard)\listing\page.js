"use client";

import Filters from "@/components/listing/layout/Filters";
import StatsCards from "@/components/listing/layout/StatsCards";
import { useState, useEffect, useMemo } from "react";
import ClientTable from "@/components/listing/ClientTable";
import { LISTING_CONSTANTS } from "@/utils/constants";
import { useDispatch, useSelector } from "react-redux";
import {
  fetchOrganizations,
  getOrganizationById,
} from "@/redux/Thunks/organization.js";
import { useAuthContext } from "@/redux/Providers/AuthProvider";
import { ROLE_CONSTANTS } from "@/utils/constants/role";

export default function ListingPage() {
  const [searchTerm, setSearchTerm] = useState(LISTING_CONSTANTS.SEARCH.ALL);
  const [statusFilter, setStatusFilter] = useState(
    LISTING_CONSTANTS.STATUS_FILTER.ALL
  );

  const dispatch = useDispatch();
  const { userRole, user, isAdmin } = useAuthContext();
  const { organizations, currentOrganization, loading, error } = useSelector(
    (state) => state.organizations
  );

  // Convert single organization to array for consistency
  const organizationsList = useMemo(() => {
    if (userRole === ROLE_CONSTANTS.ROLE_TYPES.USER && currentOrganization) {
      return [currentOrganization];
    }
    return organizations || [];
  }, [userRole, currentOrganization, organizations]);

  useEffect(() => {
    if (!userRole || !user) return;

    const fetchData = async () => {
      if (userRole === ROLE_CONSTANTS.ROLE_TYPES.USER) {
        if (user?.organization_id) {
          await dispatch(getOrganizationById(user.organization_id));
        }
      } else {
        await dispatch(fetchOrganizations());
      }
    };

    fetchData();
  }, [dispatch, userRole, user, user?.organization_id]);

  const title = isAdmin
    ? LISTING_CONSTANTS.PAGE_TITLE
    : LISTING_CONSTANTS.PAGE_TITLE_USER;
  const subtitle = isAdmin
    ? LISTING_CONSTANTS.PAGE_SUBTITLE
    : LISTING_CONSTANTS.PAGE_SUBTITLE_USER;

  return (
    <div className="min-h-screen bg-background-page max-w-7xl mx-auto px-4 py-6">
      {/* Page Header */}
      <div className="mb-6">
        <Filters
          title={title}
          subtitle={subtitle}
          searchTerm={searchTerm}
          setSearchTerm={setSearchTerm}
          statusFilter={statusFilter}
          organizationsLength={organizationsList.length}
          setStatusFilter={setStatusFilter}
          addButtonText={LISTING_CONSTANTS.ADD_BUTTON_TEXT}
          addButtonPath={LISTING_CONSTANTS.ADD_BUTTON_PATH}
        />
      </div>

      {/* Stats Cards */}
      {isAdmin && (
        <div className="mb-6">
          <StatsCards organizations={organizationsList} loading={loading} />
        </div>
      )}

      {/* Client Table */}
        <ClientTable
          searchTerm={searchTerm}
          statusFilter={statusFilter}
          organizations={organizationsList}
          loading={loading}
          error={error}
        />
    </div>
  );
}
