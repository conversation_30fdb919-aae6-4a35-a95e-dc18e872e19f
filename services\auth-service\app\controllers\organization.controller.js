import { createLogger } from "../utils/logger.utils.js";
import {
  addOrganizationService,
  getAllOrganizationsService,
  getOrganizationService,
  getOrganizationByOfficeIdService,
  getOrganizationByRealmIdService,
  updateSyncFieldService,
  updateRealmIdService,
  checkRealmIdExistsService,
  checkRealmIdAssociationService,
  checkOfficeIdAssociationService,
  updateQbConnectionService,
  getOrganizationServicesService,
} from "../services/organization.service.js";
import { LOGGER_NAMES } from "../utils/constants/log.constants.js";
import { ORGANIZATION_MESSAGES } from "../utils/constants/organization.constants.js";
import { VALIDATION_MESSAGES } from "../utils/constants/validation.constants.js";
import { errorResponse, successResponse } from "../utils/response.util.js";
import { errorHandler } from "../utils/error.utils.js";
import { validationResult } from "express-validator";
import * as status from "../utils/status_code.utils.js";
import { LOG_ACTIONS } from "../utils/constants.util.js";

const logger = createLogger(LOGGER_NAMES.ORGANIZATION_CONTROLLER);

// HELPER FUNCTIONS
/**
 * Check validation errors from express-validator
 */
const checkValidation = (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res
      .status(status.STATUS_CODE_BAD_REQUEST)
      .json(
        errorResponse(VALIDATION_MESSAGES.VALIDATION_FAILED, errors.array())
      );
  }
  return null;
};

// CONTROLLER FUNCTIONS
/**
 * Add a new organization
 * @route POST /api/v1/organization/add-org
 * @access Public (or Protected - depending on requirements)
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const addOrganization = async (req, res) => {
  logger.info(
    "organizationController.addOrganization - Starting organization creation request"
  );
  try {
    // Check for validation errors
    const validationError = checkValidation(req, res);
    if (validationError) {
      logger.warn("organizationController.addOrganization - Validation failed");
      return validationError;
    }
    const {
      name,
      email,
      phone,
      website,
      description,
      services,
      realm_id,
      office_id,
      logo,
    } = req.body;
    // Get user ID from token if available (for audit purposes)
    const createdBy = req.user?.id || null;
    logger.info(
      `organizationController.addOrganization - Creating organization with email: ${email}`
    );
    // Call service to add organization
    const serviceResponse = await addOrganizationService(
      {
        name,
        email,
        phone,
        website,
        description,
        services,
        realm_id,
        office_id,
        logo,
      },
      createdBy
    );
    // Handle service response
    if (serviceResponse.success) {
      logger.info(
        `organizationController.addOrganization - Organization created successfully: ${serviceResponse.data.id}`
      );
      return res
        .status(serviceResponse.statusCode)
        .json(successResponse(serviceResponse.message, serviceResponse.data));
    } else {
      logger.warn(
        `organizationController.addOrganization - Organization creation failed: ${serviceResponse.message}`
      );
      return res
        .status(serviceResponse.statusCode)
        .json(errorResponse(serviceResponse.message, serviceResponse.error));
    }
  } catch (error) {
    logger.error(
      `organizationController.addOrganization - Unexpected error: ${error.message}`
    );
    // Use error handler utility
    // const handledError = errorHandler(error);
    return res
      .status(500)
      .json(
        errorResponse(
          error.message || ORGANIZATION_MESSAGES.ORGANIZATION_CREATION_FAILED
        )
      );
  }
};
/**
 * Get all organizations (paginated)
 * @route GET /api/v1/organization/
 * @access Protected
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const getAllOrganizations = async (req, res) => {
  logger.info(
    "organizationController.getAllOrganizations - Get all organizations request"
  );
  try {
    // Handle query params (pagination, filters)
    const { page = 1, limit = 100, ...filters } = req.query;
    const options = {
      page: Number.parseInt(page),
      limit: Number.parseInt(limit),
      filters,
    };
    const serviceResponse = await getAllOrganizationsService(options);
    if (serviceResponse.success) {
      return res
        .status(200)
        .json(successResponse(serviceResponse.message, serviceResponse.data));
    } else {
      return res
        .status(500)
        .json(errorResponse(serviceResponse.message, serviceResponse.error));
    }
  } catch (error) {
    logger.error(
      `organizationController.getAllOrganizations - Error: ${error.message}`
    );
    return res
      .status(500)
      .json(errorResponse("Failed to fetch organizations", error.message));
  }
};
/**
 * Get organization by ID
 * @route GET /api/v1/organization/:id
 * @access Protected
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const getOrganization = async (req, res) => {
  logger.info(
    "organizationController.getOrganization - Get organization request"
  );
  try {
    const { id } = req.params;

    // Validate organization ID parameter
    if (!id) {
      logger.warn(
        "organizationController.getOrganization - Missing organization ID parameter"
      );
      return res
        .status(status.STATUS_CODE_BAD_REQUEST)
        .json(
          errorResponse(
            VALIDATION_MESSAGES.MISSING_REQUIRED_PARAMETER ||
              "Organization ID is required",
            "Organization ID parameter is missing"
          )
        );
    }

    logger.info(
      `organizationController.getOrganization - Fetching organization with ID: ${id}`
    );

    // Call service to get organization
    const serviceResponse = await getOrganizationService(id);

    // Handle service response
    if (serviceResponse.success) {
      logger.info(
        `organizationController.getOrganization - Organization found successfully: ${serviceResponse.data.name}`
      );
      return res
        .status(serviceResponse.statusCode || 200)
        .json(successResponse(serviceResponse.message, serviceResponse.data));
    } else {
      logger.warn(
        `organizationController.getOrganization - Organization not found or error: ${serviceResponse.message}`
      );
      return res
        .status(serviceResponse.statusCode)
        .json(errorResponse(serviceResponse.message, serviceResponse.error));
    }
  } catch (error) {
    logger.error(
      `organizationController.getOrganization - Unexpected error: ${error.message}`
    );
    // Use error handler utility
    return res
      .status(500)
      .json(errorResponse("Failed to fetch organizations", error.message));
  }
};
/**
 * Update organization (placeholder for future implementation)
 * @route PUT /api/v1/organization/:id
 * @access Protected
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const updateOrganization = async (req, res) => {
  logger.info(
    "organizationController.updateOrganization - Update organization request"
  );
  try {
    // TODO: Implement update organization logic
    return res
      .status(status.STATUS_CODE_NOT_IMPLEMENTED)
      .json(errorResponse("Update organization endpoint not implemented yet"));
  } catch (error) {
    logger.error(
      `organizationController.updateOrganization - Error: ${error.message}`
    );
    const handledError = errorHandler(error);
    return res
      .status(
        handledError.statusCode || status.STATUS_CODE_INTERNAL_SERVER_ERROR
      )
      .json(
        errorResponse(
          handledError.message || "Failed to update organization",
          handledError.details || error.message
        )
      );
  }
};
/**
 * Delete organization (placeholder for future implementation)
 * @route DELETE /api/v1/organization/:id
 * @access Protected
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const deleteOrganization = async (req, res) => {
  logger.info(
    "organizationController.deleteOrganization - Delete organization request"
  );
  try {
    // TODO: Implement delete organization logic
    return res
      .status(status.STATUS_CODE_NOT_IMPLEMENTED)
      .json(errorResponse("Delete organization endpoint not implemented yet"));
  } catch (error) {
    logger.error(
      `organizationController.deleteOrganization - Error: ${error.message}`
    );
    const handledError = errorHandler(error);
    return res
      .status(
        handledError.statusCode || status.STATUS_CODE_INTERNAL_SERVER_ERROR
      )
      .json(
        errorResponse(
          handledError.message || "Failed to delete organization",
          handledError.details || error.message
        )
      );
  }
};

/**
 * Get organization by office_id
 * @route GET /api/v1/organization/office/:office_id
 * @access Protected
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const getOrganizationByOfficeId = async (req, res) => {
  logger.info(
    "organizationController.getOrganizationByOfficeId - Get organization by office_id request"
  );
  try {
    const { office_id } = req.params;

    // Validate office_id parameter
    if (!office_id) {
      logger.warn(
        "organizationController.getOrganizationByOfficeId - Missing office_id parameter"
      );
      return res
        .status(status.STATUS_CODE_BAD_REQUEST)
        .json(
          errorResponse(
            VALIDATION_MESSAGES.MISSING_REQUIRED_PARAMETER ||
              "Office ID is required",
            "Office ID parameter is missing"
          )
        );
    }

    logger.info(
      `organizationController.getOrganizationByOfficeId - Fetching organization with office_id: ${office_id}`
    );

    // Call service to get organization by office_id
    const serviceResponse = await getOrganizationByOfficeIdService(office_id);

    // Handle service response
    if (serviceResponse.success) {
      logger.info(
        `organizationController.getOrganizationByOfficeId - Organization found successfully: ${serviceResponse.data.name}`
      );
      return res
        .status(serviceResponse.statusCode || 200)
        .json(successResponse(serviceResponse.message, serviceResponse.data));
    } else {
      logger.warn(
        `organizationController.getOrganizationByOfficeId - Organization not found or error: ${serviceResponse.message}`
      );
      return res
        .status(serviceResponse.statusCode)
        .json(errorResponse(serviceResponse.message, serviceResponse.error));
    }
  } catch (error) {
    logger.error(
      `organizationController.getOrganizationByOfficeId - Unexpected error: ${error.message}`
    );
    // Use error handler utility
    const handledError = errorHandler(error);
    return res
      .status(
        handledError.statusCode || status.STATUS_CODE_INTERNAL_SERVER_ERROR
      )
      .json(
        errorResponse(
          handledError.message || "Failed to fetch organization",
          handledError.details || error.message
        )
      );
  }
};

/**
 * Get organization by realm_id
 * @route GET /api/v1/organization/realm/:realm_id
 * @access Protected
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const getOrganizationByRealmId = async (req, res) => {
  logger.info(
    "organizationController.getOrganizationByRealmId - Get organization by realm_id request"
  );
  try {
    const { realm_id } = req.params;

    // Validate realm_id parameter
    if (!realm_id) {
      logger.warn(
        "organizationController.getOrganizationByRealmId - Missing realm_id parameter"
      );
      return res
        .status(status.STATUS_CODE_BAD_REQUEST)
        .json(
          errorResponse(
            VALIDATION_MESSAGES.MISSING_REQUIRED_PARAMETER ||
              "Realm ID is required",
            "Realm ID parameter is missing"
          )
        );
    }

    logger.info(
      `organizationController.getOrganizationByRealmId - Fetching organization with realm_id: ${realm_id}`
    );

    // Call service to get organization by realm_id
    const serviceResponse = await getOrganizationByRealmIdService(realm_id);

    // Handle service response
    if (serviceResponse.success) {
      logger.info(
        `organizationController.getOrganizationByRealmId - Organization found successfully: ${serviceResponse.data.name}`
      );
      return res
        .status(serviceResponse.statusCode || 200)
        .json(successResponse(serviceResponse.message, serviceResponse.data));
    } else {
      logger.warn(
        `organizationController.getOrganizationByRealmId - Organization not found or error: ${serviceResponse.message}`
      );
      return res
        .status(serviceResponse.statusCode)
        .json(errorResponse(serviceResponse.message, serviceResponse.error));
    }
  } catch (error) {
    logger.error(
      `organizationController.getOrganizationByRealmId - Unexpected error: ${error.message}`
    );
    // Use error handler utility
    const handledError = errorHandler(error);
    return res
      .status(
        handledError.statusCode || status.STATUS_CODE_INTERNAL_SERVER_ERROR
      )
      .json(
        errorResponse(
          handledError.message || "Failed to fetch organization",
          handledError.details || error.message
        )
      );
  }
};

/**
 * Update sync field for organization
 * @route PUT /api/v1/organization/:id/sync
 * @access Protected
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const updateSyncField = async (req, res) => {
  logger.info(
    "organizationController.updateSyncField - Update sync field request"
  );
  try {
    // Check for validation errors
    const validationError = checkValidation(req, res);
    if (validationError) {
      logger.warn("organizationController.updateSyncField - Validation failed");
      return validationError;
    }

    const { id } = req.params;
    const { type, lastSyncedAt } = req.body;

    // Validate required parameters
    if (!id) {
      logger.warn(
        "organizationController.updateSyncField - Missing organization ID parameter"
      );
      return res
        .status(status.STATUS_CODE_BAD_REQUEST)
        .json(
          errorResponse(
            VALIDATION_MESSAGES.MISSING_REQUIRED_PARAMETER ||
              "Organization ID is required",
            "Organization ID parameter is missing"
          )
        );
    }

    if (!type) {
      logger.warn(
        "organizationController.updateSyncField - Missing sync type parameter"
      );
      return res
        .status(status.STATUS_CODE_BAD_REQUEST)
        .json(
          errorResponse(
            "Sync type is required",
            "Sync type parameter is missing"
          )
        );
    }

    logger.info(
      `organizationController.updateSyncField - Updating sync field for organization: ${id}, type: ${type}`
    );

    // Call service to update sync field
    const serviceResponse = await updateSyncFieldService(
      id,
      type,
      lastSyncedAt
    );

    // Handle service response
    if (serviceResponse.success) {
      logger.info(
        `organizationController.updateSyncField - Sync field updated successfully for organization: ${id}`
      );
      return res
        .status(serviceResponse.statusCode || 200)
        .json(successResponse(serviceResponse.message, serviceResponse.data));
    } else {
      logger.warn(
        `organizationController.updateSyncField - Sync field update failed: ${serviceResponse.message}`
      );
      return res
        .status(serviceResponse.statusCode)
        .json(errorResponse(serviceResponse.message));
    }
  } catch (error) {
    logger.error(
      `organizationController.updateSyncField - Unexpected error: ${error.message}`
    );
    // Use error handler utility
    const handledError = errorHandler(error);
    return res
      .status(
        handledError.statusCode || status.STATUS_CODE_INTERNAL_SERVER_ERROR
      )
      .json(
        errorResponse(
          handledError.message || "Failed to update sync field",
          handledError.details || error.message
        )
      );
  }
};

/**
 * Update realm_id for organization
 * @route PUT /api/v1/organization/realm/:organization_id
 * @access Protected
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const updateRealmId = async (req, res) => {
  logger.info("organizationController.updateRealmId - Update realm_id request");
  try {
    // Check for validation errors
    const validationError = checkValidation(req, res);
    if (validationError) {
      logger.warn("organizationController.updateRealmId - Validation failed");
      return validationError;
    }

    const { organization_id } = req.params;
    const { realm_id } = req.body;

    // Validate required parameters
    if (!organization_id) {
      logger.warn(
        "organizationController.updateRealmId - Missing organization ID parameter"
      );
      return res
        .status(status.STATUS_CODE_BAD_REQUEST)
        .json(
          errorResponse(
            VALIDATION_MESSAGES.MISSING_REQUIRED_PARAMETER ||
              "Organization ID is required",
            "Organization ID parameter is missing"
          )
        );
    }

    if (!realm_id) {
      logger.warn(
        "organizationController.updateRealmId - Missing realm_id parameter"
      );
      return res
        .status(status.STATUS_CODE_BAD_REQUEST)
        .json(
          errorResponse("Realm ID is required", "Realm ID parameter is missing")
        );
    }

    logger.info(
      `organizationController.updateRealmId - Updating realm_id for organization: ${organization_id}, realm_id: ${realm_id}`
    );

    // Call service to update realm_id
    const serviceResponse = await updateRealmIdService(
      organization_id,
      realm_id
    );

    // Handle service response
    if (serviceResponse.success) {
      logger.info(
        `organizationController.updateRealmId - Realm ID updated successfully for organization: ${organization_id}`
      );
      return res
        .status(serviceResponse.statusCode || 200)
        .json(successResponse(serviceResponse.message, serviceResponse.data));
    } else {
      logger.warn(
        `organizationController.updateRealmId - Realm ID update failed: ${serviceResponse.message}`
      );
      return res
        .status(serviceResponse.statusCode)
        .json(errorResponse(serviceResponse.message, serviceResponse.error));
    }
  } catch (error) {
    logger.error(
      `organizationController.updateRealmId - Unexpected error: ${error.message}`
    );
    // Use error handler utility
    const handledError = errorHandler(error);
    return res
      .status(
        handledError.statusCode || status.STATUS_CODE_INTERNAL_SERVER_ERROR
      )
      .json(
        errorResponse(
          handledError.message || "Failed to update realm_id",
          handledError.details || error.message
        )
      );
  }
};

/**
 * Check if realm_id exists for organization
 * @route GET /api/v1/organization/check-realm/:org_id
 * @access Protected
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const checkRealmIdExists = async (req, res) => {
  logger.info(
    "organizationController.checkRealmIdExists - Checking if realm_id exists for organization"
  );
  try {
    const { org_id } = req.params;

    // Validate organization ID parameter
    if (!org_id) {
      logger.warn(
        "organizationController.checkRealmIdExists - Missing organization ID parameter"
      );
      return res
        .status(status.STATUS_CODE_BAD_REQUEST)
        .json(
          errorResponse(
            VALIDATION_MESSAGES.MISSING_REQUIRED_PARAMETER ||
              "Organization ID is required",
            "Organization ID parameter is missing"
          )
        );
    }

    logger.info(
      `organizationController.checkRealmIdExists - Checking realm_id for organization with ID: ${org_id}`
    );

    // Call service to check realm_id
    const serviceResponse = await checkRealmIdExistsService(org_id);

    // Handle service response
    if (serviceResponse.success) {
      logger.info(
        `organizationController.checkRealmIdExists - Check completed successfully for organization: ${org_id}`
      );
      return res
        .status(serviceResponse.statusCode || 200)
        .json(successResponse(serviceResponse.message, serviceResponse.data));
    } else {
      logger.warn(
        `organizationController.checkRealmIdExists - Check failed: ${serviceResponse.message}`
      );
      return res
        .status(serviceResponse.statusCode)
        .json(errorResponse(serviceResponse.message, serviceResponse.error));
    }
  } catch (error) {
    logger.error(
      `organizationController.checkRealmIdExists - Unexpected error: ${error.message}`
    );
    // Use error handler utility
    const handledError = errorHandler(error);
    return res
      .status(
        handledError.statusCode || status.STATUS_CODE_INTERNAL_SERVER_ERROR
      )
      .json(
        errorResponse(
          handledError.message || "Failed to check realm_id",
          handledError.details || error.message
        )
      );
  }
};

/**
 * Check if realm_id is associated with any organization
 * @route GET /api/v1/organization/check-realm-association/:realm_id
 * @access Protected
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const checkRealmIdAssociation = async (req, res) => {
  logger.info(
    "organizationController.checkRealmIdAssociation - Checking if realm_id is associated with any organization"
  );
  try {
    const { realm_id } = req.params;

    // Validate realm_id parameter
    if (!realm_id) {
      logger.warn(
        "organizationController.checkRealmIdAssociation - Missing realm_id parameter"
      );
      return res
        .status(status.STATUS_CODE_BAD_REQUEST)
        .json(
          errorResponse(
            VALIDATION_MESSAGES.MISSING_REQUIRED_PARAMETER ||
              "Realm ID is required",
            "Realm ID parameter is missing"
          )
        );
    }

    logger.info(
      `organizationController.checkRealmIdAssociation - Checking realm_id association: ${realm_id}`
    );

    // Call service to check realm_id association
    const serviceResponse = await checkRealmIdAssociationService(realm_id);

    // Handle service response
    if (serviceResponse.success) {
      logger.info(
        `organizationController.checkRealmIdAssociation - Check completed successfully for realm_id: ${realm_id}`
      );
      return res
        .status(serviceResponse.statusCode || 200)
        .json(successResponse(serviceResponse.message, serviceResponse.data));
    } else {
      logger.warn(
        `organizationController.checkRealmIdAssociation - Check failed: ${serviceResponse.message}`
      );
      return res
        .status(serviceResponse.statusCode)
        .json(errorResponse(serviceResponse.message, serviceResponse.error));
    }
  } catch (error) {
    logger.error(
      `organizationController.checkRealmIdAssociation - Unexpected error: ${error.message}`
    );
    // Use error handler utility
    const handledError = errorHandler(error);
    return res
      .status(
        handledError.statusCode || status.STATUS_CODE_INTERNAL_SERVER_ERROR
      )
      .json(
        errorResponse(
          handledError.message || "Failed to check realm_id association",
          handledError.details || error.message
        )
      );
  }
};

/**
 * Check if office_id is associated with any organization
 * @route GET /api/v1/organization/check-office-association/:office_id
 * @access Protected
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const checkOfficeIdAssociation = async (req, res) => {
  logger.info(LOG_ACTIONS.CHECKING_OFFICE_ID_ASSOCIATION);
  try {
    const { office_id } = req.params;
    const response = await checkOfficeIdAssociationService(office_id);
    const message = response.is_associated
      ? ORGANIZATION_MESSAGES.OFFICE_ID_ASSOCIATED
      : ORGANIZATION_MESSAGES.OFFICE_ID_NOT_ASSOCIATED;

    return res
      .status(status.STATUS_CODE_SUCCESS)
      .json(successResponse(message, response));
  } catch (error) {
    return res
      .status(status.STATUS_CODE_INTERNAL_SERVER_ERROR)
      .json(
        errorResponse(
          error.message ||
            ORGANIZATION_MESSAGES.FAILED_TO_CHECK_OFFICE_ID_ASSOCIATION
        )
      );
  }
};

/**
 * Update QB connection status for organization
 * @route PUT /api/v1/organization/:org_id/qb-connect
 * @access Protected
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const updateQbConnection = async (req, res) => {
  logger.info(
    "organizationController.updateQbConnection - Update QB connection status request"
  );
  try {
    const { org_id } = req.params;

    // Validate organization ID parameter
    if (!org_id) {
      logger.warn(
        "organizationController.updateQbConnection - Missing organization ID parameter"
      );
      return res
        .status(status.STATUS_CODE_BAD_REQUEST)
        .json(
          errorResponse(
            VALIDATION_MESSAGES.MISSING_REQUIRED_PARAMETER ||
              "Organization ID is required",
            "Organization ID parameter is missing"
          )
        );
    }

    logger.info(
      `organizationController.updateQbConnection - Updating QB connection status for organization: ${org_id}`
    );

    // Call service to update QB connection status
    const serviceResponse = await updateQbConnectionService(org_id);

    // Handle service response
    if (serviceResponse.success) {
      logger.info(
        `organizationController.updateQbConnection - QB connection status updated successfully for organization: ${org_id}`
      );
      return res
        .status(serviceResponse.statusCode || 200)
        .json(successResponse(serviceResponse.message, serviceResponse.data));
    } else {
      logger.warn(
        `organizationController.updateQbConnection - QB connection status update failed: ${serviceResponse.message}`
      );
      return res
        .status(serviceResponse.statusCode)
        .json(errorResponse(serviceResponse.message, serviceResponse.error));
    }
  } catch (error) {
    logger.error(
      `organizationController.updateQbConnection - Unexpected error: ${error.message}`
    );
    // Use error handler utility
    const handledError = errorHandler(error);
    return res
      .status(
        handledError.statusCode || status.STATUS_CODE_INTERNAL_SERVER_ERROR
      )
      .json(
        errorResponse(
          handledError.message || "Failed to update QB connection status",
          handledError.details || error.message
        )
      );
  }
};

/**
 * Get services for a specific organization
 * @route GET /services/:id
 * @access Protected
 * @description Retrieves the services array for a specific organization by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const getOrganizationServices = async (req, res) => {
  logger.info(
    "organizationController.getOrganizationServices - Get organization services request"
  );
  try {
    const { id } = req.params;

    // Validate organization ID parameter
    if (!id) {
      logger.warn(
        "organizationController.getOrganizationServices - Missing organization ID parameter"
      );
      return res
        .status(status.STATUS_CODE_BAD_REQUEST)
        .json(
          errorResponse(
            VALIDATION_MESSAGES.MISSING_REQUIRED_PARAMETER ||
              "Organization ID is required",
            "Organization ID parameter is missing"
          )
        );
    }

    logger.info(
      `organizationController.getOrganizationServices - Fetching services for organization: ${id}`
    );

    // Call service to get organization services
    const serviceResponse = await getOrganizationServicesService(id);

    // Handle service response
    if (serviceResponse.success) {
      logger.info(
        `organizationController.getOrganizationServices - Services fetched successfully for organization: ${id}`
      );
      return res
        .status(serviceResponse.statusCode || 200)
        .json(successResponse(serviceResponse.message, serviceResponse.data));
    } else {
      logger.warn(
        `organizationController.getOrganizationServices - Failed to fetch services: ${serviceResponse.message}`
      );
      return res
        .status(serviceResponse.statusCode)
        .json(errorResponse(serviceResponse.message, serviceResponse.error));
    }
  } catch (error) {
    logger.error(
      `organizationController.getOrganizationServices - Unexpected error: ${error.message}`
    );
    // Use error handler utility
    const handledError = errorHandler(error);
    return res
      .status(
        handledError.statusCode || status.STATUS_CODE_INTERNAL_SERVER_ERROR
      )
      .json(
        errorResponse(
          handledError.message || "Failed to fetch organization services",
          handledError.details || error.message
        )
      );
  }
};
