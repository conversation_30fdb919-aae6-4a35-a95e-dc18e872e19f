export const HARDCODED_STRINGS = {
  QUICKBOOKS: 'QuickBooks',
  MISSING_ENV_VARS: 'Missing environment variables',
  ORGANIZATION_ID_AND_ACCOUNT_ID_REQUIRED: 'Organization ID and Account ID are required',
  UNKNOWN_USER: 'unknown',
  CODE: 'code',
  SOMETHING_WENT_WRONG: 'Something went wrong',

  REPORT_TYPES: {
    TRIAL_BALANCE: 'TrialBalance',
    PROFIT_AND_LOSS: 'ProfitAndLoss',
    BALANCE_SHEET: 'BalanceSheet',
    CASH_FLOW: 'CashFlow',
    TRIAL_BALANCE_DISPLAY: 'Trial Balance',
    PROFIT_LOSS_DISPLAY: 'Profit & Loss',
    BALANCE_SHEET_DISPLAY: 'Balance Sheet',
    CASH_FLOW_DISPLAY: 'Cash Flow'
  },

  STRING_OPS:{
    BASE64: 'base64'
  }
  ,
  BOOLEAN: {
    TRUE: 'true',
    FALSE: 'false'
  },

  DB_ORDER: {
    CREATED_AT_DESC: [['created_at', 'DESC']]
  },

  REPORT_DEFAULTS: {
    COL_TYPE: 'Money',
    REPORT_NAME_TRIAL_BALANCE: 'TrialBalance',
    REPORT_NAME_PROFIT_LOSS: 'ProfitAndLoss',
    REPORT_NAME_BALANCE_SHEET: 'BalanceSheet',
    REPORT_NAME_CASH_FLOW: 'CashFlow',
    REPORT_BASIS: 'Accrual',
    SUMMARIZE_COLUMNS_BY: 'Month',
    CURRENCY: 'USD',
    ROW_TYPE_DATA: 'Data',
    PERIOD_DATES: 'Period dates',
    FAILED_TO_REFRESH_TOKEN: 'Failed to refresh token',
    DEFAULT_COLUMN_TITLE: 'Current Period',
    DEFAULT_COLUMN_TYPE: 'Data',
  },

  EMAIL: {
    NOT_AVAILABLE: 'N/A',
    MISSING_RECIPIENT_OR_SENDER_EMAIL: 'Cannot send email: missing recipient or sender email',
    QUICKBOOKS_REPORTS_SYNC_FAILED_SUBJECT:
      'QuickBooks Reports Sync - Some Reports Failed',
    QUICKBOOKS_REPORTS_SYNC_FAILED_TEXT:
      "Your organization's QuickBooks sync encountered failures.",
    QUICKBOOKS_REPORTS_SYNC_FAILED_ERROR_SUBJECT:
      'QuickBooks Reports Sync Failed',
    QUICKBOOKS_REPORTS_SYNC_CHECK_LOGS:
      'Please check the system logs for more details.',
    QUICKBOOKS_REPORTS_SYNC_CONTACT_SUPPORT:
      'Please contact the support team if you need assistance.',
    QUICKBOOKS_REPORTS_FAILED_TO_SYNC: 'The following reports failed to sync:',
    QUICKBOOKS_SYNC_ENCOUNTERED_ERROR:
      'QuickBooks reports sync encountered an error.',
  },

  REALM_ID: {
    UPDATE_FAILED_WARNING: (organizationId, error) =>
      `Failed to update realm_id for organization ${organizationId}: ${error}`,
    UPDATE_FAILED_ERROR: (error) => `Failed to update realm_id: ${error}`,
    UPDATE_SUCCESS: (organizationId) =>
      `Successfully updated realm_id for organization: ${organizationId}`,
    UPDATE_ERROR_WARNING: (organizationId, errorMessage) =>
      `Error updating realm_id for organization ${organizationId}: ${errorMessage}`,
    REALM_ID_AVAILABLE: 'Realm ID is available for association',
  },
};

export default HARDCODED_STRINGS;
