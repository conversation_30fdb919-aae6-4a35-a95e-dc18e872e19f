import React, { useState, useEffect, useRef } from "react";
import { useDispatch } from "react-redux";
import PropTypes from "prop-types";
import UploadCard from "./UploadCard";
import { cardConfigs } from "@/utils/data/bookkeeping";
import { BOOKCLOSURE_CONSTANTS } from "@/utils/constants/bookclosure";
import { POWERBI_WORKFLOW_CONSTANTS } from "@/utils/constants/powerBiWorkflow";
import { uploadAdpFile } from "@/redux/Thunks/adp";
import { fetchOrganizations } from "@/redux/Thunks/organization";
import {
  syncFinancialData,
  syncOperationalData,
} from "@/redux/Thunks/bookkeeping";
// import {
//   triggerFinanceWorkflow,
//   triggerOperationsWorkflow,
//   triggerPayrollWorkflow,
// } from "@/redux/Thunks/powerBiWorkflow";
import {
  formatDateToMMDDYYYY,
  getMonthDateRange,
} from "@/utils/methods/formatters";
import { Upload } from "lucide-react";
import {
  isFileMonthMatching,
  parseMonthString,
  generateFileName,
  isNextMonthAfter,
} from "@/utils/methods/bookkeeping";
import { useToast } from "@/components/ui/toast";

export default function UploadSection({ uploadStates, id, selectedMonth }) {
  const { addToast } = useToast();
  const dispatch = useDispatch();
  const [localUploadStates, setLocalUploadStates] = useState({});
  const [workflowErrors, setWorkflowErrors] = useState({});
  const [schemaName, setSchemaName] = useState(null);
  const [organizationServices, setOrganizationServices] = useState([]);
  const [email, setEmail] = useState(null);
  const prevMonthRef = useRef(selectedMonth);

  // Reset sync states when month changes
  useEffect(() => {
    if (prevMonthRef.current !== selectedMonth) {
      // Reset states only for cards that were previously synced
      setLocalUploadStates((prev) => {
        const newStates = {};
        Object.keys(prev).forEach((key) => {
          if (prev[key] === "synced") {
            newStates[key] = false;
          } else {
            newStates[key] = prev[key];
          }
        });
        return newStates;
      });
      prevMonthRef.current = selectedMonth;
    }
  }, [selectedMonth]);

  const [qbLastSyncedAt, setQbLastSyncedAt] = useState(null);
  const [adpLastSyncedAt, setAdpLastSyncedAt] = useState(null);
  const [sikkaLastSyncedAt, setSikkaLastSyncedAt] = useState(null);
  const [realmId, setRealmId] = useState(null);
  const [officeId, setOfficeId] = useState(null);
  const [organizationName, setOrganizationName] = useState(null);

  useEffect(() => {
    const fetchOrgData = async () => {
      try {
        const result = await dispatch(fetchOrganizations()).unwrap();
        const targetOrg = result.find((org) => String(org.id) === String(id));
        if (targetOrg) {
          setSchemaName(targetOrg.schema_name);
          setOrganizationServices(targetOrg.services || []);
          setQbLastSyncedAt(targetOrg.qb_last_synced_at);
          setAdpLastSyncedAt(targetOrg.adp_last_synced_at);
          setSikkaLastSyncedAt(targetOrg.sikka_last_synced_at);
          setRealmId(targetOrg.realm_id);
          setOfficeId(targetOrg.office_id);
          setEmail(targetOrg.email);
          const orgName = targetOrg.name ?? targetOrg.organization_name ?? null;
          setOrganizationName(orgName);
        }
      } catch (error) {
        // Error fetching organizations
      }
    };

    if (id) {
      fetchOrgData();
    }
  }, [dispatch, id]);

  const isServiceSelected = (serviceKey) => {
    return (
      Array.isArray(organizationServices) &&
      organizationServices.includes(serviceKey)
    );
  };

  const getFilteredCards = () => {
    return cardConfigs;
  };

  // Helper function to trigger Power BI workflow - eliminates duplicate code
  // const triggerPowerBiWorkflow = async (
  //   serviceType,
  //   fileSize = null,
  //   mimeType = null
  // ) => {
  //   if (!organizationName) {
  //     throw new Error("Organization name is required for Power BI workflow");
  //   }

  //   const { month, year, monthYear, monthName } =
  //     parseMonthString(selectedMonth);

  //   // Map service type to service constant and workflow trigger function
  //   const serviceConfig = {
  //     finance: {
  //       constant: BOOKCLOSURE_CONSTANTS.SERVICES.FINANCE,
  //       trigger: triggerFinanceWorkflow,
  //       displayName: "Finance",
  //     },
  //     operations: {
  //       constant: BOOKCLOSURE_CONSTANTS.SERVICES.OPERATIONS,
  //       trigger: triggerOperationsWorkflow,
  //       displayName: "Operations",
  //     },
  //     payroll: {
  //       constant: BOOKCLOSURE_CONSTANTS.SERVICES.PAYROLL,
  //       trigger: triggerPayrollWorkflow,
  //       displayName: "Payroll",
  //     },
  //   };

  //   const normalizedService = serviceType.toLowerCase();
  //   const config = serviceConfig[normalizedService];
  //   if (!config) {
  //     throw new Error(`Unknown service type: ${serviceType}`);
  //   }

  //   const fileName = generateFileName(
  //     organizationName,
  //     config.constant,
  //     monthName,
  //     year
  //   );

  //   try {
  //     const workflowResult = await dispatch(
  //       config.trigger({
  //         organization_id: id,
  //         organization_name: organizationName,
  //         month: month,
  //         year: year,
  //         monthYear: monthYear,
  //         file_name: fileName,
  //         file_size:
  //           fileSize ?? BOOKCLOSURE_CONSTANTS.FILE_TYPES.DEFAULT_FILE_SIZE,
  //         mime_type: mimeType ?? BOOKCLOSURE_CONSTANTS.FILE_TYPES.PDF,
  //       })
  //     ).unwrap();

  //     // Handle response (success or timeout)
  //     setWorkflowErrors((prev) => ({ ...prev, [normalizedService]: null }));

  //     // Check if message indicates processing/timeout
  //     const messageLower = (workflowResult.message || "").toLowerCase();
  //     const isProcessingMessage =
  //       POWERBI_WORKFLOW_CONSTANTS.PROCESSING_KEYWORDS.some((keyword) =>
  //         messageLower.includes(keyword.toLowerCase())
  //       );

  //     if (isProcessingMessage) {
  //       // Show non-blocking info toast for processing/timeout
  //       addToast(
  //         workflowResult.message ||
  //           POWERBI_WORKFLOW_CONSTANTS.PROCESSING_MESSAGE,
  //         "info",
  //         6000
  //       );
  //     } else if (workflowResult?.success !== false) {
  //       // Show success toast for normal success (explicitly check !== false to handle undefined/null)
  //       addToast(
  //         workflowResult.message ||
  //           POWERBI_WORKFLOW_CONSTANTS.TRIGGER_SUCCESS(config.displayName),
  //         "success",
  //         6000
  //       );
  //     } else {
  //       // Handle unexpected success: false case
  //       addToast(
  //         workflowResult.message || POWERBI_WORKFLOW_CONSTANTS.INTERNAL_ERROR,
  //         "error",
  //         6000
  //       );
  //     }
  //   } catch (powerBiError) {
  //     // Handle error response (rejected promise)
  //     setWorkflowErrors((prev) => ({ ...prev, [normalizedService]: null }));

  //     // Extract error message from rejected value
  //     const errorMessage =
  //       powerBiError?.message || POWERBI_WORKFLOW_CONSTANTS.INTERNAL_ERROR;

  //     // Show error toast
  //     addToast(errorMessage, "error", 6000);
  //   }
  // };

  const handleFileUpload = async (
    cardKey,
    file,
    inputElement,
    selectedMonthArg
  ) => {
    if (!file) {
      return;
    }

    const selectedMonthToUse = selectedMonthArg ?? selectedMonth;

    const validExtension = /\.xlsx$/i;

    if (!validExtension.test(file.name)) {
      addToast("Only .xlsx files are allowed", "error");
      if (inputElement) inputElement.value = null;
      return;
    }

    const validNamePattern =
      /^[A-Za-z0-9_-]+_\d{2}[A-Z][a-z]+20\d{2}_\d{2}[A-Z][a-z]+20\d{2}\.xlsx$/;

    if (!validNamePattern.test(file.name)) {
      addToast(
        "Invalid file name format. Expected format like:- company-name_01August2025_31August2025.xlsx",
        "error"
      );
      if (inputElement) inputElement.value = null;
      return;
    }

    const fileName = file.name.replace(/\.[^/.]+$/, "");

    if (!isFileMonthMatching(selectedMonthToUse, fileName)) {
      addToast("Selected month does not match file month", "error");
      if (inputElement) {
        inputElement.value = null;
      }
      return;
    }

    const urlSegments = window.location.pathname.split("/").filter(Boolean);
    const orgId = urlSegments[urlSegments.length - 1];
    if (!orgId) {
      return;
    }

    if (cardKey === "payroll" && !schemaName) {
      return;
    }

    // Validate that the selected month is the month immediately after the last synced month
    // Map service key to its corresponding last synced timestamp
    const serviceLastSyncedMap = {
      payroll: adpLastSyncedAt,
      operational: sikkaLastSyncedAt,
      financial: qbLastSyncedAt,
    };

    const lastSyncedForService = serviceLastSyncedMap[cardKey];

    if (
      lastSyncedForService &&
      !isNextMonthAfter(lastSyncedForService, selectedMonthToUse)
    ) {
      const serviceNameMap = {
        payroll: "ADP",
        operational: "Sikka",
        financial: "QuickBooks",
      };
      const svcName = serviceNameMap[cardKey] || "service";
      const lastDate = new Date(lastSyncedForService);
      const lastMonthName =
        BOOKCLOSURE_CONSTANTS.MONTH_NAMES.FULL[lastDate.getMonth()];
      const lastYear = lastDate.getFullYear();
      addToast(
        `Selected month (${selectedMonthToUse}) must be the month after last ${svcName} sync (${lastMonthName} ${lastYear})`,
        "error"
      );
      if (inputElement) inputElement.value = null;
      return;
    }

    // Mark as loading (string) so UI can distinguish loading vs synced
    setLocalUploadStates((prev) => ({ ...prev, [cardKey]: "loading" }));

    try {
      if (cardKey === "payroll") {
        const response = await dispatch(
          uploadAdpFile({ file, schemaName, orgId, email, selectedMonth })
        )
          .unwrap()
          .catch((err) => {
            addToast(err, "error");
          });

        if (response && response.data && response.data.message) {
          addToast(
            BOOKCLOSURE_CONSTANTS.TOAST_MESSAGES.UPLOAD_SUCCESS,
            "success"
          );
        }

        const lastSyncedAt =
          response?.data?.data?.lastSyncedAt ??
          response?.data?.lastSyncedAt ??
          null;
        if (lastSyncedAt) {
          setAdpLastSyncedAt(lastSyncedAt);
        }

        // Trigger Power BI workflow for Payroll
        // await triggerPowerBiWorkflow("payroll", file.size, file.type);

        // Mark payroll as synced so UI shows "Uploaded" and disables further uploads until month change
        if (response) {
          setLocalUploadStates((prev) => ({ ...prev, [cardKey]: "synced" }));
        }
      }
    } catch (error) {
      // Upload failed
      // Clear loading state for this card if it was loading
      setLocalUploadStates((prev) => {
        if (prev[cardKey] === "loading") {
          return { ...prev, [cardKey]: false };
        }
        return prev;
      });
      if (inputElement) inputElement.value = null;
    } finally {
      // Only clear file input here (do not override synced state)
      if (inputElement) inputElement.value = null;
    }
  };

  const handleFinancialSync = async () => {
    // Reset any previous state
    setLocalUploadStates((prev) => ({ ...prev, financial: false }));

    // Validate selected month is the month immediately after last QuickBooks sync (if available)
    if (qbLastSyncedAt && !isNextMonthAfter(qbLastSyncedAt, selectedMonth)) {
      const lastDate = new Date(qbLastSyncedAt);
      const lastMonthName =
        BOOKCLOSURE_CONSTANTS.MONTH_NAMES.FULL[lastDate.getMonth()];
      const lastYear = lastDate.getFullYear();
      addToast(
        `Selected month (${selectedMonth}) must be the month after last QuickBooks sync (${lastMonthName} ${lastYear})`,
        "error"
      );
      return;
    }

    if (!realmId || !schemaName) {
      addToast("Missing required configuration for sync", "error");
      return;
    }

    // Set loading state
    setLocalUploadStates((prev) => ({ ...prev, financial: "loading" }));
    try {
      const syncResult = await dispatch(
        syncFinancialData({
          clientId: id,
          email: email,
          month: selectedMonth,
          files: [],
          realmId: realmId,
          schemaName: schemaName,
        })
      ).unwrap();

      if (syncResult?.success !== false) {
        addToast(BOOKCLOSURE_CONSTANTS.TOAST_MESSAGES.SYNC_SUCCESS, "success");

        // await triggerPowerBiWorkflow("finance");

        // Update the last synced timestamp and mark as synced
        const { endDate } = getMonthDateRange(selectedMonth);
        setQbLastSyncedAt(endDate);
        setLocalUploadStates((prev) => {
          // Keep existing states but update financial
          return {
            ...prev,
            financial: "synced",
          };
        });
      } else {
        throw new Error(syncResult?.message || "Sync failed");
      }
    } catch (error) {
      const errorMessage = error?.message || "";
      addToast(
        `${BOOKCLOSURE_CONSTANTS.TOAST_MESSAGES.SYNC_ERROR} ${errorMessage}`,
        "error"
      );
      setLocalUploadStates((prev) => {
        // Keep existing states but update financial
        return {
          ...prev,
          financial: false,
        };
      });
    }
  };

  const handleOperationalSync = async (files) => {
    // Reset any previous state
    setLocalUploadStates((prev) => ({ ...prev, operational: false }));

    // Validate selected month is the month immediately after last Sikka sync
    if (
      sikkaLastSyncedAt &&
      !isNextMonthAfter(sikkaLastSyncedAt, selectedMonth)
    ) {
      const lastDate = new Date(sikkaLastSyncedAt);
      const lastMonthName =
        BOOKCLOSURE_CONSTANTS.MONTH_NAMES.FULL[lastDate.getMonth()];
      const lastYear = lastDate.getFullYear();
      addToast(
        `Selected month (${selectedMonth}) must be the month after last Sikka sync (${lastMonthName} ${lastYear})`,
        "error"
      );
      return;
    }

    // Set loading state
    setLocalUploadStates((prev) => ({ ...prev, operational: "loading" }));

    try {
      const syncResult = await dispatch(
        syncOperationalData({
          office_id: officeId,
          month: selectedMonth,
          schema_name: schemaName,
          organization_id: id,
        })
      ).unwrap();

      if (syncResult?.success !== false) {
        addToast(BOOKCLOSURE_CONSTANTS.TOAST_MESSAGES.SYNC_SUCCESS, "success");

        // await triggerPowerBiWorkflow("operations");

        // Update the last synced timestamp and mark as synced
        const { endDate } = getMonthDateRange(selectedMonth);
        setSikkaLastSyncedAt(endDate);
        setLocalUploadStates((prev) => ({ ...prev, operational: "synced" }));
      } else {
        throw new Error(syncResult?.message || "Sync failed");
      }
    } catch (error) {
      addToast(`${BOOKCLOSURE_CONSTANTS.TOAST_MESSAGES.SYNC_ERROR}`, "error");
      setLocalUploadStates((prev) => ({ ...prev, operational: false }));
    }
  };

  const handlePayrollUploadClick = (cardKey) => {
    // Prevent the default click if conditions aren't met
    if (!isServiceSelected(cardKey) || !schemaName) {
      return;
    }

    // Check if the selected month is valid before opening file dialog
    if (adpLastSyncedAt && !isNextMonthAfter(adpLastSyncedAt, selectedMonth)) {
      const lastDate = new Date(adpLastSyncedAt);
      const lastMonthName =
        BOOKCLOSURE_CONSTANTS.MONTH_NAMES.FULL[lastDate.getMonth()];
      const lastYear = lastDate.getFullYear();
      addToast(
        `Selected month (${selectedMonth}) must be the month after last ADP sync (${lastMonthName} ${lastYear})`,
        "error"
      );
      return;
    }

    // Programmatically trigger file input click
    document.getElementById(`payroll-upload-input-${cardKey}`).click();
  };

  const getLastSync = (key) => {
    if (key === BOOKCLOSURE_CONSTANTS.SERVICES_VALUES.FINANCIAL) {
      return qbLastSyncedAt ? formatDateToMMDDYYYY(qbLastSyncedAt) : null;
    }
    if (key === BOOKCLOSURE_CONSTANTS.SERVICES_VALUES.OPERATIONS) {
      return sikkaLastSyncedAt ? formatDateToMMDDYYYY(sikkaLastSyncedAt) : null;
    }
    return null;
  };

  const getSyncHandler = (key) => {
    if (key === BOOKCLOSURE_CONSTANTS.SERVICES_VALUES.FINANCIAL)
      return handleFinancialSync;
    if (key === BOOKCLOSURE_CONSTANTS.SERVICES_VALUES.OPERATIONS)
      return handleOperationalSync;
    return (files) => handleFileUpload(key, files);
  };

  return (
    <div className="bookkeeping-upload-section">
      <div className="bookkeeping-upload-section-header">
        <div className="bookkeeping-upload-section-accent"></div>
        <h2 className="bookkeeping-upload-section-title">
          {BOOKCLOSURE_CONSTANTS.UPLOAD_SECTION.TITLE}
        </h2>
      </div>
      <div className="bookkeeping-upload-grid">
        {getFilteredCards().map((card) => {
          const lastSync = getLastSync(card.key);
          const syncHandler = getSyncHandler(card.key);
          const serviceErrorKey =
            card.key === "financial"
              ? "finance"
              : card.key === "operational"
                ? "operations"
                : card.key === "payroll"
                  ? "payroll"
                  : card.key;

          // Determine status for payroll card border color
          const getPayrollStatus = () => {
            if (!isServiceSelected(card.key)) return "not-subscribed";
            if (adpLastSyncedAt) return "synced";
            return "not-synced";
          };

          return (
            <div key={card.key}>
              {card.key === "payroll" ? (
                <div className="bookkeeping-upload-card" data-status={getPayrollStatus()}>
                  <div className="bookkeeping-upload-card-icon">
                    {card.icon}
                  </div>
                  <h3 className="bookkeeping-upload-card-title">
                    {card.title}
                  </h3>
                  <div className="bookkeeping-upload-card-status">
                    {isServiceSelected(card.key) ? (
                      <>
                        <span
                          className={`bookkeeping-upload-card-status-dot ${
                            adpLastSyncedAt ? "green" : "gray"
                          }`}
                        ></span>
                        {adpLastSyncedAt ? (
                          <span className="bookkeeping-upload-card-status-badge green">
                            {
                              BOOKCLOSURE_CONSTANTS.UPLOAD_CARD
                                .LAST_SYNCED_LABEL
                            }{" "}
                            {formatDateToMMDDYYYY(adpLastSyncedAt)}
                          </span>
                        ) : (
                          <span className="bookkeeping-upload-card-status-badge gray">
                            Not synced yet
                          </span>
                        )}
                      </>
                    ) : (
                      <>
                        <span className="bookkeeping-upload-card-status-dot red"></span>
                        <span className="bookkeeping-upload-card-status-badge red">
                          {
                            BOOKCLOSURE_CONSTANTS.UPLOAD_SECTION
                              .NOT_SELECTED_LABEL
                          }
                        </span>
                      </>
                    )}
                  </div>
                  <button
                    type="button"
                    onClick={() => handlePayrollUploadClick(card.key)}
                    className={`bookkeeping-payroll-button ${
                      isServiceSelected(card.key) &&
                      schemaName &&
                      localUploadStates[card.key] !== "synced"
                        ? "active"
                        : "disabled"
                    }`}
                    disabled={
                      !isServiceSelected(card.key) ||
                      !schemaName ||
                      localUploadStates[card.key] === "synced" ||
                      localUploadStates[card.key] === "loading"
                    }
                    aria-label="Upload XLSX"
                  >
                    <Upload className="w-4 h-4" />
                    {(() => {
                      const state = localUploadStates[card.key];
                      if (state === "loading") return "Uploading...";
                      if (state === "synced") return "Uploaded";
                      return "Upload XLSX";
                    })()}
                  </button>
                  <input
                    id={`payroll-upload-input-${card.key}`}
                    type="file"
                    accept=".xlsx, .xls"
                    className="hidden"
                    disabled={
                      !isServiceSelected(card.key) ||
                      !schemaName ||
                      localUploadStates[card.key] === "synced" ||
                      localUploadStates[card.key] === "loading"
                    }
                    onChange={(e) =>
                      handleFileUpload(
                        card.key,
                        e.target.files[0],
                        e.target,
                        selectedMonth
                      )
                    }
                    tabIndex={-1}
                  />
                  {workflowErrors[serviceErrorKey] && (
                    <p className="bookkeeping-upload-card-error" role="alert">
                      {workflowErrors[serviceErrorKey]}
                    </p>
                  )}
                </div>
              ) : (
                <UploadCard
                  title={card.title}
                  description={card.description}
                  icon={card.icon}
                  onSync={syncHandler}
                  isLoading={localUploadStates[card.key] === "loading"}
                  lastSync={lastSync}
                  isSynced={localUploadStates[card.key] === "synced"}
                  isFinancial={card.key === "financial"}
                  realmId={realmId}
                  schemaName={schemaName}
                  selectedMonth={selectedMonth}
                  isSelected={isServiceSelected(card.key)}
                  errorMessage={workflowErrors[serviceErrorKey] || null}
                />
              )}
            </div>
          );
        })}
      </div>
    </div>
  );
}

UploadSection.propTypes = {
  uploadStates: PropTypes.object.isRequired,
  id: PropTypes.string.isRequired,
  selectedMonth: PropTypes.string.isRequired,
};
